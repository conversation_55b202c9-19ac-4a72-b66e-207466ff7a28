import { Command } from "commander";
import { executePrompt } from "../src/copilot/helpers/llm/prompt";
import path from "path";

const program = new Command();

program.name("prompt-cli").description("A CLI tool for applying templates with interpolations").version("1.0.0");

program
  .command("template")
  .description("Apply a template with interpolations from a file")
  .argument("<file>", "path to the template file")
  .option("-d, --data <key=value...>", "Key-value pairs for interpolation")
  .action(async (file, options) => {
    try {
      const data = options.data
        ? Object.fromEntries(
            options.data.map((pair: string) => {
              const [key, value] = pair.split("=");
              return [key.trim(), value.trim()];
            })
          )
        : {};
      file = path.relative(process.cwd() + "/prompts", file);
      const stream = await executePrompt(file.replace(/.md$/, ""), data);
      for await (const chunk of stream) {
        process.stdout.write(chunk.content.toString());
      }
    } catch (error) {
      console.error("Error processing template:", error.message);
    }
    process.stdout.write("\n");
    process.exit(0);
  });

program.parse(process.argv);
