/* eslint-disable @typescript-eslint/no-explicit-any */
import questions from "../data/question_answer.json";
import extraQuestions from "../data/question_answer_extra.json";
import { qdrantClient } from "../src/clients/qdrant";
import mongoose, { Types } from "mongoose";
import { v4 } from "uuid";

const mongoUrl =
  "************************************************************************************************************";
// const mongoUrl = "*******************************************************************************************************************";

const questionDatas: any[] = [];
const questionPoints: any[] = [];
const answerDatas: any[] = [];
const answerPoints: any[] = [];

const filterQuestion = questions.filter((item) => {
  if (
    item.answer.indexOf("提供订单号") > -1 ||
    item.answer.indexOf("麻烦您提供") > -1 ||
    item.answer.indexOf("麻烦提供") > -1
  ) {
    return false;
  }
  if (item.orgType === "CASS") {
    return true;
  }
  return false;
});

const uniqueRecord = {};
[...filterQuestion, ...extraQuestions].forEach((qa) => {
  const { question, answer, question_embedding, answer_embedding } = qa;
  if (uniqueRecord[question + answer]) {
    return;
  }
  uniqueRecord[question + answer] = true;
  const questionObjectId = Types.ObjectId();
  const answerObjectId = Types.ObjectId();
  const questionId = v4();
  const answerId = v4();
  questionDatas.push({
    _id: questionObjectId,
    question,
    answerId: answerObjectId,
    enabled: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    priority: 100,
    vectorId: questionId,
  });
  answerDatas.push({
    _id: answerObjectId,
    answer,
    enabled: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    vectorId: answerId,
  });
  questionPoints.push({
    id: questionId,
    vector: question_embedding,
    payload: {
      mongoId: questionObjectId.toString(),
    },
  });
  answerPoints.push({
    id: answerId,
    vector: answer_embedding,
    payload: {
      mongoId: answerObjectId.toString(),
    },
  });
});

async function start() {
  const mongo = await mongoose.connect(mongoUrl);
  const db = mongo.connection.useDb("copilot");
  const questioncollection = db.collection("qaquestions");
  const answercollection = db.collection("qaanswers");
  await questioncollection.createIndex({ answerId: 1 });
  await questioncollection.createIndex({ vectorId: 1 });
  await answercollection.createIndex({ vectorId: 1 });
  await qdrantClient.qdrant.upsert(qdrantClient.questionCollectionName, {
    wait: true,
    points: questionPoints,
  });
  await qdrantClient.qdrant.upsert(qdrantClient.answerCollectionName, {
    wait: true,
    points: answerPoints,
  });
  await questioncollection.insertMany(questionDatas);
  await answercollection.insertMany(answerDatas);
  console.log("执行完成");
  process.exit(0);
}

start();
