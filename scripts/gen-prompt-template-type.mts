import fs from "fs";
import prettier from "prettier";

function traverseDir(dir: string): string[] {
  return fs.readdirSync(dir).reduce((acc, file) => {
    const filePath = `${dir}/${file}`;
    const fileStat = fs.statSync(filePath);
    if (fileStat.isDirectory()) {
      return [...acc, ...traverseDir(filePath)];
    } else {
      return [...acc, filePath];
    }
  }, [] as string[]);
}

const promptFileNames = traverseDir("./prompts")
  .filter((file) => file.endsWith(".md"))
  .map((file) => file.replace("./prompts/", ""));

const typeFile = "./src/copilot/helpers/llm/PromptTemplate.ts";

const typeContent = `// 此文件由 \`scripts/gen-prompt-template-type.mts\` 生成
// 不要手动修改此文件

export type PromptTemplate = ${promptFileNames.map((fileName) => `"${fileName.replace(".md", "")}"`).join(" | ")};
`;

const output = await prettier.format(typeContent, { parser: "typescript" });

console.log(`生成模板类型文件:${typeFile}\n`, output);

fs.writeFileSync(typeFile, output);
