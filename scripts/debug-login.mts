import fs from "fs";
import path from "path";

// 登录接口URL
const LOGIN_URL = "https://ec-hwbeta.casstime.com/terminal-api-v2/public/auth/ecapp/login/password";

// 读取debug.json文件
const readDebugFile = () => {
  const debugFilePath = path.resolve(process.cwd(), "data/debug.json");
  const fileContent = fs.readFileSync(debugFilePath, "utf-8");
  return JSON.parse(fileContent);
};

// 更新debug.json文件
const updateDebugFile = (data: unknown) => {
  const debugFilePath = path.resolve(process.cwd(), "data/debug.json");
  fs.writeFileSync(debugFilePath, JSON.stringify(data, null, 2), "utf-8");
  console.log("Cookie已更新到debug.json文件");
};

// 主函数
const main = async () => {
  try {
    // 读取debug.json文件
    const debugData = readDebugFile();
    const { userLoginName, password } = debugData;

    if (!userLoginName || !password) {
      throw new Error("用户名或密码不存在");
    }

    console.log(`正在使用账号 ${userLoginName} 登录...`);

    // 发送登录请求
    const response = await fetch(LOGIN_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "user-agent": debugData.headers["user-agent"],
      },
      body: JSON.stringify({ userLoginName, password }),
    });

    if (!response.ok) {
      throw new Error(`登录失败: ${response.status} ${response.statusText}`);
    }
    const cookies = response.headers.get("set-cookie");
    if (!cookies) {
      throw new Error("登录成功但未返回cookie");
    }

    // 提取GLOBAL_COMPANY_INFO和security_context
    const cookieString = Array.isArray(cookies) ? cookies.join("; ") : cookies;
    const globalCompanyInfo = cookieString.match(/GLOBAL_COMPANY_INFO=[^;]+/);
    const securityContext = cookieString.match(/security_context=[^;]+/);

    if (!globalCompanyInfo || !securityContext) {
      throw new Error("未找到必要的cookie信息");
    }
    const companyInfo = Buffer.from(globalCompanyInfo?.[0]?.split("=")[1], "base64");
    const result = await response.json();
    const username = result.data.userLoginId;
    const garageCompanyId = companyInfo.toString().split(",")[1];
    console.log("username", username);
    console.log("garageCompanyId", garageCompanyId);

    Object.assign(debugData, result.data, {
      companyId: companyInfo.toString().split(",")[1],
    });

    // 只保留关键cookie
    const essentialCookies = `${globalCompanyInfo[0]}; ${securityContext[0]}`;

    // 更新debug.json中的cookie
    debugData.headers.cookie = essentialCookies;

    // 设置 ec-userinfo
    const appKey = "59ab3a3803eb4b76d061dcc67a18ec88";
    const appSecret = "8bdbf83737e86cc804d1e3d6f9ffa8bae09296687c647cebc704398d38045bf6";
    const userInfo = {
      appKey,
      appSecret,
      userInfo: {
        username,
      },
      companyInfo: {
        garageCompanyId,
      },
    };
    debugData.headers["ec-userinfo"] = Buffer.from(JSON.stringify(userInfo)).toString("base64");
    updateDebugFile(debugData);

    console.log("登录成功，cookie已更新");
  } catch (error) {
    console.error("执行失败:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
};

// 执行主函数
main();
