import mongoose from "mongoose";
import caches from "./recommend-plan-cache.json";

const mongoUrl =
  "************************************************************************************************************";

const mongo = await mongoose.connect(mongoUrl);
const db = mongo.connection.useDb("copilot");
const datacachcollection = db.collection("datacaches");
await datacachcollection.insertMany(caches as unknown[]);
console.log("写入完成");
process.exit(0);
