import fs from "fs/promises";
import { parse } from "csv-parse/sync";

import { config } from "@casstime/apollo-config";

const content = await fs.readFile("data/question_answer.csv", "utf8");

const records = parse(content, {
  columns: true,
  skip_empty_lines: true,
  trim: true,
});

const embedQuery = async (query: string) => {
  const response = await fetch("http://************:3000/v1/embeddings", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${config.get("LLM_API_KEY")}`,
    },
    body: JSON.stringify({
      model: "m3e-large",
      input: [query],
    }),
  });
  const data = await response.json();
  return data.data[0].embedding;
};

const questions: any[] = [];
for (const record of records) {
  const answer = eval("(" + record.answer + ")").content.data;
  const embedItem = {
    ...record,
    answer: answer,
    question_embedding: await embed<PERSON><PERSON>y(record.question),
    answer_embedding: await embed<PERSON><PERSON><PERSON>(answer),
  };
  questions.push(embedItem);

  console.log(questions.length + "/" + records.length, embedItem.question);
}

fs.writeFile("data/question_answer.json", JSON.stringify(questions, null, 2), "utf8");
