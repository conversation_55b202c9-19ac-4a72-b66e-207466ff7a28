import { defineFlatConfig } from "eslint-define-config";

export default defineFlatConfig({
  plugins: {
    custom: {
      rules: {
        "require-await-or-return": {
          meta: {
            type: "problem",
            docs: {
              description: "Ensure replyStream、routeTo、reRoute、next is preceded by await or return",
            },
            fixable: "code",
          },
          create(context) {
            return {
              CallExpression(node) {
                if (
                  node.callee.type === "MemberExpression" &&
                  node.callee.object.name === "context" &&
                  ["routeTo", "reRoute", "next", "replyStream"].includes(node.callee.property.name)
                ) {
                  const parent = node.parent;

                  if (parent.type !== "AwaitExpression" && parent.type !== "ReturnStatement") {
                    context.report({
                      node,
                      message: `context.${node.callee.property.name}() 前面必须是await或return`,
                      fix(fixer) {
                        if (parent.type === "ExpressionStatement") {
                          return fixer.insertTextBefore(node, "await ");
                        }
                      },
                    });
                  }
                }
              },
            };
          },
        },
      },
    },
  },
  rules: {
    "custom/require-await-or-return": "error",
  },
});
