FROM swr.cn-south-1.myhuaweicloud.com/cassopen/node:20.18.0

ENV TZ=Asia/Shanghai
RUN set -eux; \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime; \
    echo $TZ > /etc/timezone;

# 换源
RUN echo "deb http://mirrors.163.com/debian-archive/debian/ stretch main non-free contrib\n\
deb http://mirrors.163.com/debian-archive/debian-security/ stretch/updates main non-free contrib\n\
deb-src http://mirrors.163.com/debian-archive/debian/ stretch main non-free contrib\n\
deb-src http://mirrors.163.com/debian-archive/debian/ stretch-backports main non-free contrib\n\
deb http://mirrors.163.com/debian-archive/debian/ stretch-backports main non-free contrib\n\
deb-src http://mirrors.163.com/debian-archive/debian-security/ stretch/updates main non-free contrib\n\
" > /etc/apt/sources.list

# 配置私服
RUN npm config set registry http://npm.dev.casstime.com/

RUN npm config set //npm.dev.casstime.com/:_authToken eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWFsX2dyb3VwcyI6WyJjYXNzdGltZSIsIiRhbGwiLCIkYXV0aGVudGljYXRlZCIsIkBhbGwiLCJAYXV0aGVudGljYXRlZCIsImFsbCJdLCJuYW1lIjoiY2Fzc3RpbWUiLCJncm91cHMiOlsiY2Fzc3RpbWUiLCIkYWxsIiwiJGF1dGhlbnRpY2F0ZWQiLCJAYWxsIiwiQGF1dGhlbnRpY2F0ZWQiLCJhbGwiLCIkYWxsIiwiJGF1dGhlbnRpY2F0ZWQiLCJAYWxsIiwiQGF1dGhlbnRpY2F0ZWQiLCJhbGwiLCJjYXNzdGltZSIsIiRhbGwiLCIkYXV0aGVudGljYXRlZCIsIkBhbGwiLCJAYXV0aGVudGljYXRlZCIsImFsbCJdLCJpYXQiOjE1NzEwMzI4MTAsIm5iZiI6MTU3MTAzMjgxMX0.m6u-yYXUxqWP1Ozo6S7_wA6EKSjCVIkEmlTtJNeubRw

RUN echo "\nalways-auth=true" >> ~/.npmrc

RUN  echo "" > /etc/apt/sources.list.d/debian.sources   &&  apt-get update 

RUN apt-get install python3 -y

WORKDIR /usr/src/app

COPY package.json yarn.lock ./

RUN yarn

COPY . .

RUN yarn build

CMD ["node", "dist/index.js"]
