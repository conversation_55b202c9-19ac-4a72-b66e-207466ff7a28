{
  // 使用 IntelliSense 了解相关属性。
  // 悬停以查看现有属性的描述。
  // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "command": "npm start",
      "name": "Run npm start",
      "request": "launch",
      "type": "node-terminal"
    },
    {
      "experimentalNetworking": "off",
      "type": "node",
      "request": "launch",
      "name": "tsx",
      "runtimeExecutable": "tsx",
      "env": {
        "NODE_ENV": "development"
      },
      "args": ["watch", "${workspaceFolder}/src/main.ts"],
      "skipFiles": ["<node_internals>/**"],
      "internalConsoleOptions": "neverOpen",
      "console": "integratedTerminal",
      "outFiles": ["${workspaceFolder}/**/*.js"]
    }
  ]
}
