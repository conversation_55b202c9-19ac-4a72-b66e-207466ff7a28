# 询报价流程图

## 整体流程
```mermaid
graph TD
    A[用户输入] --> B{表单验证}
    B -->|验证失败| C[提示错误]
    C --> A
    B -->|验证通过| D[询价发布]
    
    D --> E{询价类型}
    E -->|普通配件| F[创建询价单]
    E -->|追加询价| G[追加需求]
    E -->|轮胎询价| H[轮胎询价]
    
    F --> I[等待报价]
    G --> I
    H --> I
    
    I --> J[获取报价结果]
    J --> K[展示报价]
```

## 表单验证流程
```mermaid
graph TD
    A[开始验证] --> B{检查VIN码}
    B -->|无效| C[提示输入VIN]
    B -->|有效| D{检查配件}
    
    D -->|无配件| E[提示输入配件]
    D -->|有配件| F{检查车型}
    
    F -->|解析失败| G[提示选择车型]
    F -->|解析成功| H{检查必要信息}
    
    H -->|缺少发票信息| I[提示选择发票]
    H -->|缺少地址| J[提示填写地址]
    H -->|信息完整| K[验证通过]
```

## 询价发布流程
```mermaid
graph TD
    A[开始发布] --> B{是否有询价ID}
    B -->|是| C[追加询价]
    B -->|否| D[新建询价]
    
    D --> E{配件类型}
    E -->|普通配件| F[创建询价单]
    E -->|轮胎| G[创建轮胎询价]
    
    F --> H[处理品质信息]
    G --> I[处理规格信息]
    
    H --> J[添加图片信息]
    I --> J
    
    J --> K[发送询价请求]
    C --> K
    
    K --> L[返回询价结果]
```

## 报价处理流程
```mermaid
graph TD
    A[获取报价] --> B[查询询价详情]
    B --> C[获取商家信息]
    C --> D[处理译码结果]
    
    D --> E{报价状态}
    E -->|报价中| F[等待报价]
    E -->|已报价| G[整理报价信息]
    E -->|已过期| H[提示过期]
    
    G --> I[处理库存信息]
    I --> J[排序报价结果]
    J --> K[展示报价信息]
```

## 特殊场景处理
```mermaid
graph TD
    A[特殊场景] --> B[短VIN码处理]
    A --> C[多车型选择]
    A --> D[轮胎规格识别]
    A --> E[发票信息处理]
    
    B --> F[香港/澳门车型]
    C --> G[车型确认]
    D --> H[规格解析]
    E --> I[发票类型确认]
    
    F --> J[特殊询价]
    G --> K[更新车型]
    H --> L[轮胎询价]
    I --> M[更新发票信息]
```
