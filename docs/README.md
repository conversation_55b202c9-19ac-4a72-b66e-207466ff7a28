# Cassec Copilot Server 项目文档

## 项目概述
Cassec Copilot Server 是一个基于 TypeScript 开发的智能助手服务端项目，提供多场景的智能对话和业务处理能力。

## 项目架构

### 1. 核心功能模块 (src/copilot/)
- **apps/**: 应用程序逻辑实现
- **server/**: 服务器相关功能
- **copilot.ts**: 智能助手核心实现
- **richtext/**: 富文本处理相关功能

### 2. 客户端集成模块 (src/clients/)
- **llm/**: 大语言模型集成
- **nlp/**: 自然语言处理
- **inquiry/**: 询价相关
- **product/**: 产品相关
- **quote/**: 报价相关
- **message/**: 消息处理

### 3. 业务服务模块 (src/service/)
- **PurchasePlanService**: 采购方案服务
- **InterceptRuleService**: 拦截规则服务

### 4. 提示词模板 (prompts/)
- **配件专家/**: 配件相关提示词
- **采购助手/**: 采购相关提示词
- **采购方案/**: 方案推荐相关提示词

### 5. 数据模型 (src/models/)
- **CopilotMessage**: 智能助手消息模型
- **ConfigureReply**: 配置回复
- **RecommendPlanGroup**: 推荐方案组
- **Dialogue**: 对话模型

### 6. 路由接口 (src/routes/)
- **messages**: 消息相关接口
- **makePlan**: 方案生成接口
- **quoteTrigger**: 报价触发接口
- **qa/**: 问答相关接口

### 7. 公共模块 (src/common/)
- **utils/**: 工具函数
- **error/**: 错误处理
- **logger/**: 日志处理
- **hooks/**: 钩子函数

## 核心实现

### Copilot 模块

#### 架构设计
- 基于 @casstime/copilot-core 框架开发
- 采用应用（Application）+ 代理（Agent）的分层架构
- 使用场景分类（Scene Classification）和路由（Route）机制

#### 已集成的智能助手应用
1. **智能采购助手 (GarageAssistant)**
   - 会话管理：60分钟会话过期时间
   - 中间件机制：路由前处理
   - 后台代理：后台任务处理
   - 场景分类：用户意图识别

2. **共享仓助手 (StoreHouse)**
   - 仓储管理智能服务

3. **汽配信息问答 (AutoPartsFAQ)**
   - 提供汽配知识服务

4. **智能文档助手 (KnowledgeAgent)**
   - 文档处理和查询服务

5. **配件专家问答 (PartExpertFAQ)**
   - 专业配件咨询服务

6. **定损专家 (DamageAssessmentExpert)**
   - 车辆定损评估服务

7. **AI采购方案助手 (AIPurchasePlanAssistant)**
   - 智能采购决策支持

#### 核心业务场景
- **询报价**：处理价格查询相关对话
- **汽配知识问答**：回答汽配相关问题
- **商品推荐**：提供商品推荐服务
- **配件专家**：专业配件咨询服务
- **兜底回复**：处理无法匹配的对话

#### 技术特点
- **模块化设计**：各个功能模块独立封装
- **可扩展性**：支持动态注册新的应用和代理
- **中间件支持**：提供请求处理的扩展点
- **场景分类**：智能识别用户意图并路由到对应处理器

### 询价表单实现（InquiryForm）

#### 1. 数据结构设计
InquiryFormData 包含以下主要字段：
- **VIN码相关**
  * vinCode: 当前VIN码
  * vinCodes: 多个候选VIN码
  * vinImages: VIN码图片
- **车型信息**
  * carModel: 当前车型
  * prevCarModel: 前一个车型
  * carModels: 多个候选车型
- **配件信息**
  * partNames: 配件名称列表
  * qualities: 配件品质要求
  * partNamesImages: 配件图片
- **用户信息**
  * invoice: 发票信息
  * address: 地址信息
  * user: 用户基本信息
  * source: 来源信息
- **询价信息**
  * inquiryId: 询价单ID
  * inquiryCarModel: 询价车型信息
  * appendInquiry: 是否追加询价
- **轮胎信息**
  * originalItems: 轮胎规格信息

#### 2. 表单验证流程
表单验证（doValidate）包含以下步骤：
- **车架号验证**
  * 检查长度（要求17位）
  * 验证车架号有效性
  * 处理多车架号场景
- **配件验证**
  * 检查配件数量限制
  * 特殊处理轮胎配件
  * 验证配件有效性
- **车型验证**
  * 支持单车型场景
  * 处理多车型选择
  * 车型变更确认
- **必要信息验证**
  * 发票信息完整性
  * 地址信息验证
  * 用户信息校验

#### 3. 表单提交处理
提交处理（doSubmit）包含：
- **询价发布**
  * 普通配件询价处理
  * 轮胎询价特殊处理
  * 询价优惠券发放
  * 询价卡片生成
- **任务管理**
  * 创建轮询任务
  * 全车件询价任务
  * 轮胎询价任务
  * 推荐方案任务

#### 4. 特殊功能支持
- **智能轮胎处理**
  * 轮胎规格识别
  * 规格有效性验证
  * 品牌匹配处理
- **多车型支持**
  * 车型历史记录
  * 车型选择引导
  * 车型切换确认
- **询价追加**
  * 已有询价检测
  * 追加配件验证
  * 合并询价处理
- **发票管理**
  * 发票信息收集
  * 发票类型确认
  * 发票状态追踪

#### 5. 错误处理机制
- **验证错误**
  * ValidateError 错误类型
  * 清晰的错误提示
  * 用户友好的引导
- **恢复机制**
  * 状态保存
  * 场景恢复
  * 上下文维护

#### 6. 系统集成
- **消息系统**：处理用户交互消息
- **任务系统**：管理异步任务
- **追踪系统**：记录用户行为
- **配置系统**：管理系统配置