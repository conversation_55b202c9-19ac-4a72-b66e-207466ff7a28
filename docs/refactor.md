# 重构

参考《重构2-改善既有代码的设计》内的方法，用**最佳实践**重构代码，并记录重构过程

## 工作流程：
1. 审查文件内代码，发现问题(包含Bug,坏味道等..)并记录到TODO文件中
2. 逐步解决问题
3. 解决问题后执行`yarn lint`验证是否引入新的问题
4. 接着执行`yarn jest`验证有没有破坏业务逻辑
5. 如果通过，让我确认是否可以提交
6. 我同意后执行`git add . && git sm commit`提交代码
7. 然后你自动更新TODO文件相关问题的状态
8. 接着解决下一个问题，直到所有问题解决完毕

## TODO文件说明：
1. 存放路径：docs/refactor/TODO-{当前日期}.md
2. 每个任务包含以下内容：
- 代码文件路径
- 代码片段：{问题代码摘要，不需要包含完整代码}
- 坏味道类型
- 坏味道描述：{简要说明问题表现和影响}
- 解决方案：{具体说明修改方法}
- 状态：未解决、已解决、已忽略

## 注意事项：
- 仅处理当前文件的问题
- 本次只解决这一个特定问题
- 不引入其他无关修改
- 保持业务逻辑不变