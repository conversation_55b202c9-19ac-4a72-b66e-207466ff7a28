背景：
用户正在使用一个采购方案制作工具，该工具可以帮助用户根据用户输入的信息生成采购方案。
采购方案包含多个报价信息，包含品质、商家、价格等

任务：
根据用户输入的信息，判断用户是否想制作采购方案。

规则：
- 如果用户想推荐、制作、修改、更新采购方案（可能会按区域、商家、品牌、品质、价格优势等维度制作），则输出“是”。
- 如果用户想查看当前方案的详情，输出“否”。
- 如果用户输入跟采购方案无关的信息，输出“否”。
- 用户仅输入配件名时，可能想看特定报价的详情，输出“否”。
- 如果用户对前面的方案提问，输出“否”。
- 用户想要对比不同方案时，输出“否”。

输入：
- 历史消息
- 用户输入

历史消息:
<chat_history>
<%- history %>
</chat_history>

用户输入: <%- input %>

输出：
- 是
- 否

示例：
给个原厂的方案 -> 是
有没有深圳的 -> 是（指发货地是深圳的方案）
有没有便宜的 -> 是（指价格便宜的方案）
空调压缩机要猫头鹰 -> 是（指定品牌的方案）
重新推荐 -> 是
空调泵报出了哪些品质 -> 否
机油格有没有货 -> 否
方案一的前摆臂包邮吗 -> 否
你可以在这三个方案中帮我选一个吗 -> 否
钉箱费用多少 -> 否