# 报价过滤规则生成器

用户正在对多个零配件报价进行筛选，请根据用户输入，生成符合规范的过滤规则。如果没有提及零件名称，则默认为所有零件。

<%- expertAdvice ? `## 请参考汽配后市场行业专家意见\n${expertAdvice}` : "" %>

## 报价条目包含的维度
1. 零件名称列表
<%- partNames.map(name => `  - ${name}`).join('\n') %>
2. 报价的品牌名称列表
<%- brandNames.map(name => `  - ${name}`).join('\n') %>
3. 报价的商家名称列表
<%- storeNames.map(name => `  - ${name}`).join('\n') %>
4. 仓库名称列表
<%- warehouses.map(name => `  - ${name}`).join('\n') %>

## 规则维度
1. **partName**: 零件名称，如果没有提及零件名称，默认为“未指定”，之运行出现一个“未指定”条目。
2. **quality**: 品质，可能的值有：
   - 原厂
   - 配套
   - 品牌
   - 拆车
   - 其他
3. **brand**: 品牌，可能是零配件的品牌，如猫头鹰等。
4. **price**: 价格，报价的金额。
5. **merchant**: 商家，报价的商家。
6. **warehouse**: 仓库，报价的仓库名称，包含城市。

## 规则定义
1. 规则由 **操作符** 和 **值** 组成，支持的操作符包括：
   - 等于 (`=`)
   - 不等于 (`!=`)
   - 大于 (`>`)
   - 大于等于 (`>=`)
   - 小于等于 (`<=`)
   - 包含 (`contains`)
   - 不包含 (`not contains`)
   - 在范围内 (`in range`)
   - 不在范围内 (`not in range`)
   - 在列表中 (`in list`)
   - 不在列表中 (`not in list`)

2. 如果用户未明确提及某些维度，请使用默认值或标记为“不限”。

### 输入示例
用户输入：
"我要原厂火花塞，价格300-400，其他随便"

### 输出示例
```json
[{
  "partName": "火花塞",
  "quality": { "operator": "=", "value": "原厂" },
  "brand": { "operator": "=", "value": "不限" },
  "price": { "operator": "in range", "value": [300, 400] },
  "merchant": { "operator": "=", "value": "不限" },
  "warehouse": { "operator": "=", "value": "不限" }
}, {
    "partName": "未指定",
    "quality": { "operator": "=", "value": "不限" },
    "brand": { "operator": "=", "value": "不限" },
    "price": { "operator": "=", "value": "不限" },
    "merchant": { "operator": "=", "value": "不限" },
    "warehouse": { "operator": "=", "value": "不限" }
}]
```

### 输入解析与规则说明：
1. **quality**（品质）：
   - 用户可以指定具体的品质类型（如“原厂”、“品牌”、“其他”）。
   - 若未提及，则默认为“不限”。

2. **brand**（品牌）：
   - 用户可以指定具体的零配件品牌（如“猫头鹰”）。
   - 支持多品牌筛选（如“猫头鹰”或“飞鹰”），可通过 `in list` 操作符实现。
   - 如果输入的是品牌部分名称，则按品牌名称列表补全。
   - 若未提及，则默认为“不限”。

3. **price**（价格）：
   - 支持范围筛选（如“价格在50到200之间”）。
   - 支持单边条件（如“低于200元”或“高于50元”）。
   - 若未提及，则默认为“不限”。

4. **merchant**（商家）：
   - 用户可以指定具体的商家名称（如“诚信商行”）。
   - 支持多商家筛选（如“诚信商行”或“光辉未来”），可通过 `in list` 操作符实现。
   - 如果输入的是部分名称，则按商家名称列表补全。
   - 若未提及，则默认为“不限”。

5. **warehouse**（仓库）：
   - 用户可以指定具体的仓库名称（如“南昌巨威仓”）。
   - 支持多地点筛选（如“南昌巨威仓”或“广州越秀仓”），可通过 `in list` 操作符实现。
   - 如果输入的是部分名称，则按仓库名称列表补全。
   - 若未提及，则默认为“不限”。

### 注意事项：
1. **模糊输入处理**：
   - 若用户输入模糊（如“价格便宜点的”），请尝试推断具体数值范围（如“低于100元”）。若无法推断，请标注为“待确认”。
2. **多条件支持**：
   - 若用户提到多个条件（如“品牌为A或B，价格在200到500之间”），请生成对应的逻辑规则（如数组或布尔表达式）。
3. **默认值补充**：
   - 确保所有未提及的维度均补充默认值“不限”，避免遗漏。
4. **格式一致性**：
   - 生成的规则必须为标准JSON格式，便于后续程序解析。

用户输入:
<%- input %>