## 【任务说明】
帮我判断用户的输入是否跟轮胎报价条目表中的内容相关：
- A：相关，用户希望根据特定条件筛选轮胎报价，或者查询相关信息
- B：与轮胎报价无关的其他需求，如客服、售前售后、快递物流、技术问题等

## 【背景】
### 当前的轮胎报价表结构如下：
```sql
tyre_item (轮胎报价条目表)
  - atPrice REAL,                         -- 含税价格（优先使用 btPrice）
  - btPrice REAL,                         -- 税前价格（默认价格字段）
  - locationName TEXT,                    -- 发货仓名称
  - organizationName TEXT,                -- 发货店铺名称（供应商）
  - partsName TEXT,                       -- 轮胎名称
  - modelName TEXT,                       -- 适用车型
  - remark TEXT,                          -- 备注字段，包含品牌、花纹、特性、标识及其他信息(如:直销、国产、进口)
  - outOfStock TEXT CHECK (outOfStock IN ('是','否'))  -- 缺货标识，精确匹配
```
### 历史对话
<chat_history>
<%- history %>
</chat_history>

## 【分析规则】

一、触发筛选意图A的关键词（满足任意一项即归类为筛选需求）：
1. 价格相关：价格/预算/贵/便宜/折扣/多少钱
2. 地域相关：地区/城市/附近/本地/“XX市”（如深圳）
3. 商家相关：某个店铺/商家/门店/4S店/线上店/仓库
4. 产品参数：
   - 品牌（米其林/马牌/固特异等）
   - 类型（防爆胎/静音胎/雪地胎/新能源轮胎）
   - 花纹型号（如PS4/CC6/御乘）

二、触发其他意图B典型场景：
1. 人工客服咨询
2. 售前售后咨询
3. 快递物流信息
4. 技术问题
5. 询报价

## 【输出格式】
直接输出字符串A或B，无需解释

## 【示例】
1. 广州凌云汽配最便宜 -> A
2. 防爆的 -> A
3. 米其林最便宜的轮胎 -> A
4. 人工客服 -> B
5. 北京五方仓 -> A
6. 怎样安装 -> B
7. 啥时候能发货 -> B

用户输入： <%- userInput %>