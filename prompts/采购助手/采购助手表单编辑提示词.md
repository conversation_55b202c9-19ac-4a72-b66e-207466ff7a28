## 背景：
用户正在使用采购助手进行配件采购，需要对表单进行操作。

## 任务：
请根据表单内容、用户输入，分析用户正在对表单进行哪些操作，将结果输出成JSON。

## 规则：
1. 实体类型(type)包含 "quality"（品质）、"partName"（配件名称）
2. 品质(quality)可能的值只能是 "原厂件"、"品牌件"、"拆车件"、"原厂再制造件"，
3. 配件名称(partName)可以是配件名、零件号、轮胎规格及汽车用品等，不能是车型、品牌等非配件实体。
4. 用户如果说“原厂”、 "正品"、“正厂”，则将品质设置称“原厂件”。如果说 "品牌"、“副厂”，则将品质设置成“品牌件”。如果说 "拆车"、“二手”，则将品质设置称“拆车件”。
5. action 可能的值只能是 "add" 及 "delete"，不能有其他action，如果是替换,由"delete"和"add"组合而成。
6. 默认添加配件，action 为 "add"
7. 如果用户强调只要某个配件，则需要将其他未提及的配件删除；
8. 如果用户强调只要某个品质，则需要将其他未提及的品质删除;
9. 用户删除(或不要)的实体必须包含在表单中，否则不进行操作;
10. 注意用户输入中的序号，需要关联到数组中正确条目;
11. 用户如果没有指定实体类型时，默认指的是`partName`;
12. 直接输出JSON数组，一定要确保格式正确，禁止输出其他代码;

## 推理示例
1. 不要第一个 -> 删除partNames中序号为1的条目
2. 将火花塞改称机油格 -> 删除partNames中的“火花塞”，添加一个名为"机油格"的条目
3. 原厂的 -> 添加quality为"原厂件"的条目

## 输出格式：
  [
    {{"action": "<action>", "type": "<type>", "entities": ["<entity>"]}}
  ]

## 表单内容：
### partNames
<% formData.partNames.forEach((item, index) => { %>
<%- index + 1 %>. <%- item _%>
<% }) %>

### qualities:
```json
<%- JSON.stringify(formData.qualities, null, 2) %>
```


## 用户输入：
<%- message %>