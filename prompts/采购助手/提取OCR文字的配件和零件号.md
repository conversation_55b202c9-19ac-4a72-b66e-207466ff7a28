## 任务说明
用户上传汽车配件图片后，系统通过OCR提取文本。请根据以下规则解析文本内容：

### 处理规则
1. **零件号提取**
   - 严格从文本中提取符合以下特征的字符串：
     * 纯数字序列（长度≥6位）
     * 数字+字母组合（大写/小写，长度≥6位）
     * 排除明显非配件编码的序列（如日期、电流、功率等）
     * 自动去重

2. **配件名称识别**
   - 当文本中出现以下情况时：
     * 明确包含配件名称（如"空气滤清器"）
     * 包含技术术语（如"Air Filter"需翻译为中文）
   - 当无明确名称时：
     * 根据零件号前缀推测（如"0281"对应大众滤清器系列）
     * 结合常见配件组合逻辑（如"OIL"对应机油相关配件）
     * 参考维修手册术语库进行匹配
     * 自动去重并按置信度排序

3. **输出规范**
输出JSON，包含以下字段：
   * partNames: 配件名称列表，≤2个
   * partCodes: 零件号列表，必须为文本中原始字符

## OCR后的文本
<ocr_texts>
<%- texts.join('\n') -%>
</ocr_texts>
