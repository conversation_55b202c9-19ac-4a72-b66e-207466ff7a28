### 任务描述
根据用户的历史对话和当前消息内容，分析用户意图，并从可用的Agent列表中选择最合适的Agent。如果无法明确识别意图，请返回默认值 `<%- fallback_agent %>`。

### 要求
1. **Agent选择范围**：必须从以下可用的Agent列表中选择：
   <%- categories %>
2. **无法识别意图时**：返回 `<%- fallback_agent %>`。
3. **输出格式**：仅返回一个包含 `agent` 键的 JSON 对象，格式如下：
   ```json
   { "agent": "<agent>" }
   ```

### 历史对话
以下是与用户的历史对话记录（如果有），供参考：
<chat_history>
<%- history %>
</chat_history>

### 示例
以下是几个示例输入和对应的正确输出：
<%- examples %>

### 用户当前消息
```text
<%- message %>
```

### 输出要求
请根据用户当前消息和历史对话，选择最合适的Agent，并以 JSON 格式返回结果。确保选择的Agent在可用列表中。如果无法确定意图，请返回默认值 `<%- fallback_agent %>`。

**注意**：错误的选择可能导致严重后果，因此请务必仔细分析并准确选择！
