{"name": "@casstime/copilot-server", "version": "1.0.0", "description": "助手类应用服务，主要服务于采购助手.", "main": "app.js", "scripts": {"login:debug": "tsx scripts/debug-login.mts", "generate:type": "tsx scripts/gen-prompt-template-type.mts", "build": "yarn generate:type && tsc -p tsconfig.json", "initqa": "NODE_ENV=hwbeta tsx scripts/initqa.ts", "start": "node dist/index.js", "dev": "yarn login:debug && LOG_LEVEL=debug tsx watch src/main.ts | bunyan -L", "gen-deps": "dependency-cruise -x \"^node_modules\" -T dot src | dot -T svg > dependencygraph.svg", "lint": "yarn tsc --noEmit", "e2e": "jest -c jest.e2e.config.js", "test": "jest -c jest.config.js", "prepare": "husky"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@casstime/apollo-config": "^1.2.6", "@casstime/config": "^1.1.2", "@casstime/copilot-core": "^2.8.9", "@casstime/copilot-xml": "^1.5.3", "@casstime/eureka": "^1.1.0-2.0", "@casstime/node-http-client": "^1.5.2", "@casstime/node-http-cls": "^1.5.2", "@casstime/node-logger": "^1.5.2", "@fastify/autoload": "^5.0.0", "@fastify/sensible": "^5.0.0", "@langchain/core": "^0.3.32", "@langchain/openai": "^0.3.17", "@qdrant/js-client-rest": "^1.13.0", "@types/ejs": "^3.1.5", "@types/mongoose": "^5.11.97", "config": "^3.3.9", "csv": "^6.3.10", "dayjs": "^1.11.13", "duckdb": "^1.1.1", "echarts": "^5.5.1", "ejs": "^3.1.10", "fastify": "^4.28.1", "fastify-cli": "^5.8.0", "fastify-plugin": "^4.0.0", "jsonrepair": "^3.12.0", "langchain": "^0.3.12", "lodash": "^4.17.21", "marked": "^15.0.11", "module-alias": "^2.2.3", "mongoose": "^5.4.0", "nzh": "^1.0.14", "openai": "^4.10.0", "p-limit": "3", "piscina": "^5.0.0", "sqlite3": "^5.1.7", "undici": "^6.21.0", "zod": "^3.22.4"}, "devDependencies": {"@eslint/js": "^9.10.0", "@types/eslint__js": "^8.42.3", "@types/jest": "^29.5.4", "@types/lodash": "^4.14.197", "@types/node": "^20.5.3", "commander": "^13.1.0", "dependency-cruiser": "^16.4.1", "eslint": "^9.10.0", "eslint-define-config": "^2.1.0", "husky": "^9.1.7", "jest": "^29.6.4", "node-pre-gyp": "^0.17.0", "nodemon": "^3.0.1", "prettier": "^3.5.3", "tap": "^16.1.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsx": "^4.7.1", "typescript": "^5.6.2", "typescript-eslint": "^8.6.0"}, "_moduleAliases": {"@": "./dist"}, "engines": {"node": ">=20.0.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}