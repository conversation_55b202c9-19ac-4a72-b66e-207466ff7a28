import { inquiryClient } from "@/clients/inquiry";
import { AppMap, AppName } from "@/common/enums";
import {
  IContentSource,
  IFetchMakePurchasePlanPayload,
  IScenarioType,
  IStoreFacilityInfo,
} from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/interface";
import { PlanRecommender } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender";
import { createDataProvider } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/factory";
import { purchaseService } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/services/PurchaseService";
import {
  IInquiryInfo,
  IIntelligentPlan,
  IIntelligentPlanTaskParams,
  IIntelligentProgrammeItem,
  IntelligentPlanType,
  TaskType,
} from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { taskService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/TaskService";
import { Dialogue, IPurchasePlan, ITaskPollingStatus, RecommendPlanGroup } from "@/models";
import { Types, Document } from "mongoose";
import { config } from "@casstime/apollo-config";
import logger from "@/common/logger";
import {
  IPurchasePlanItem,
  MarkerType,
  PurchasePlanFeatureEnum,
  PurchasePlanFeatureLabel,
} from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/interfaces/plan";
import { DataProviderKey } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/interfaces";
import { RecommendPlanGroupRead } from "@/models/RecommendPlanGroupRead";
import { inquiryClient as garageAssistantInquiryClient } from "@/copilot/apps/GarageAssistant/agents/inquiry/clients/InquiryClient";
import { inquiryService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/InquiryService";
import aggregationInquiryClient from "@/clients/aggregationInquiry";
import { messageClient } from "@/clients/message";
import { MessageReporter } from "@/messages";
import productClient from "@/clients/product";
import { IPlanQuoteItem } from "@/clients/quote/interface";

export class PurchasePlanService {
  async makeRecommendPlan(inquiryId: string, turn: number, _makerType?: string) {
    let makerType = _makerType || "";
    const inquiryResponse = await inquiryClient.getInquiryInfo(inquiryId);
    const { inquiryBaseInfos, inquiryUserInfos } = inquiryResponse || {};
    const userId = inquiryBaseInfos?.createdBy || "";
    const companyId = inquiryUserInfos?.garageCompanyId || "";
    const app = AppName.AIPurchasePlanAssistant;
    const owner = `${app}:${userId}:${companyId}`;
    const messageId = new Types.ObjectId().toString();

    const dialogue = await Dialogue.findOneAndUpdate(
      { userId, app, companyId, businessId: inquiryId },
      { $set: { displayName: AppMap.GARAGE_ASSISTANT } },
      { upsert: true, new: true }
    )
      .sort({ updatedAt: -1 })
      .exec();

    try {
      if (!makerType) {
        makerType = config.get("PLAN_MAKER_TYPE");
      }
    } catch (e) {
      makerType = "MACHINE_LEARNING";
      logger.info("查询阿波罗配置 PLAN_MAKER_TYPE 失败", e);
    }

    const dataProvider = createDataProvider(inquiryId);
    await dataProvider.prefetch();
    const inquiryDetail = await dataProvider.getItem(DataProviderKey.inquiryDetail);
    const quoteDetail = await dataProvider.getItem(DataProviderKey.quoteDetail);
    if (!quoteDetail?.quotes?.length) {
      return;
    }
    if (["LLM", "MACHINE_LEARNING"].includes(makerType)) {
      const purchaseTask = {
        taskId: inquiryId,
        taskType: TaskType.QUOTE_PURCHASE_PLAN,
        owner,
        app,
        companyId,
        userId,
        done: false,
        params: {
          makerType,
          inquiryId,
          source: "ANDROID",
          fromPage: "AIChatScreen",
          isOpenInvoice: true,
          messageId,
          hasSentTurn: 0,
          turns: 0,
          expiration: (inquiryDetail.createdStamp || Date.now()) + 24 * 60 * 60 * 1000,
          inquiryDetail,
        },
      };
      const task = await taskService.findOneAndUpdateTaskPolling(
        {
          owner,
          taskId: inquiryId,
          taskType: TaskType.QUOTE_PURCHASE_PLAN,
          dialogueId: dialogue._id.toString(),
          done: false,
        },
        purchaseTask
      );
      const json: IFetchMakePurchasePlanPayload = {
        demandId: inquiryId,
        scenario: IScenarioType.GARAGE_ASSISTANT_SENTINEL,
        operatorId: userId,
        identifyId: task?.id,
        callbackUrl: `${config.get("API_INTRA_BASE_URL")}copilot-server/copilot/purchase_plan`,
        contentSource: makerType as IContentSource,
      };
      await purchaseService.fetchMakePurchasePlan(json);
      return;
    }

    const input = "优先考虑品质、品牌，其次考虑时效、价格，给我推荐最优方案";
    const id = new Types.ObjectId().toString();
    const messageReporter = new MessageReporter(id);

    const planRecommender = await PlanRecommender.create({
      reporter: messageReporter,
      inquiryId,
      makerType: makerType as MarkerType,
      dataProvider,
    });

    const { recommendPurchasePlans, quoteMatches, planWeight } = await planRecommender.recommend({
      input,
    });
    if (!recommendPurchasePlans.length) {
      return;
    }
    let plans = recommendPurchasePlans.slice(0, 3).map((item, index) => ({ ...item, name: `方案${index + 1}` }));
    plans = await planRecommender.planReasonGenerator.generate({
      input,
      recommendPurchasePlans: plans,
      quoteMatches,
      planWeight,
    });
    await this.fillAndSaveRecommendPlan({
      recommendContentsGroupId: "",
      plans,
      userLoginId: userId,
      companyId,
      makerType,
      inquiryId,
      firstRecommend: true,
    });
    await this.sendRecommendMessage(inquiryId, userId);
  }
  // 保存LLM、MACHINE_LEARNING方案
  async saveLlmOrMlPlan(taskPollingItem: ITaskPollingStatus, params: IIntelligentPlanTaskParams) {
    const { userId, companyId, taskType } = taskPollingItem;
    const { recommendContentsGroupId, makerType, inquiryId, turns } = params;

    const { statusCode, result } = await garageAssistantInquiryClient.getIntelligentPlanDetailById({
      turns,
      recommendContentsGroupId,
    });
    if (statusCode !== 200 || !result) {
      logger.info(
        `保存recommendplan，查询方案内容失败,${statusCode}。taskType:${taskType},params: ${JSON.stringify(params)}`
      );
      return;
    }
    // 使用postProcessor后处理ml和llm方案
    const dataProvider = createDataProvider(inquiryId);
    await dataProvider.prefetch();
    const messageReporterNotListen = new MessageReporter(taskPollingItem.id || "");
    const planRecommenderSection = await PlanRecommender.create({
      inquiryId,
      reporter: messageReporterNotListen,
      dataProvider,
      makerType: makerType as MarkerType,
    });
    const processedPlans = await planRecommenderSection.postProcessor.process({
      inquiryId,
      plans: result.recommendProgrammes,
    });
    await this.fillAndSaveRecommendPlan({
      recommendContentsGroupId,
      plans: processedPlans,
      userLoginId: userId || "",
      companyId: companyId || "",
      makerType: makerType || "",
      inquiryId,
      firstRecommend: taskType === TaskType.QUOTE_PURCHASE_PLAN,
    });
    if (taskPollingItem.taskType !== TaskType.QUOTE_PURCHASE_PLAN) {
      return;
    }
    await this.sendRecommendMessage(inquiryId, userId || "");
  }
  async fillAndSaveRecommendPlan({
    plans,
    userLoginId,
    companyId,
    makerType,
    inquiryId = "",
    firstRecommend,
    recommendContentsGroupId,
  }: {
    plans: IIntelligentPlan[];
    userLoginId: string;
    companyId: string;
    makerType: string;
    inquiryId?: string;
    recommendContentsGroupId?: string;
    firstRecommend?: boolean;
  }) {
    const dataProvider = createDataProvider(inquiryId);
    const inquiryDetail = await dataProvider.getItem(DataProviderKey.inquiryDetail);
    const { quotes } = await dataProvider.getItem(DataProviderKey.quoteDetail);

    const plans1 = await inquiryService.getInquiryQuoteByPurchase(
      {
        demandId: inquiryId,
        scenario: "",
        createdDate: Date.now(),
        recommendProgrammes: plans as IIntelligentProgrammeItem[],
        recommendStores: [],
        turns: 1,
      },
      inquiryDetail,
      quotes
    );
    const plans2 = await this.fillRecommendPlanPriceAndFeature({
      plans: plans1,
      userLoginId,
      companyId,
      inquiryId,
    });
    const recommendPlanItem = await RecommendPlanGroup.create({
      recommendContentsGroupId,
      makerType,
      inquiryId,
      plans: plans2,
      firstRecommend,
    });
    if (firstRecommend) {
      await RecommendPlanGroupRead.updateOne(
        { inquiryId, recommendPlanGroupId: recommendPlanItem._id },
        {
          $set: {
            inquiryId,
            dialogueIds: [],
          },
        },
        { upsert: true }
      );
    }
    return recommendPlanItem.toJSON();
  }

  // 发送推荐方案的消息
  async sendRecommendMessage(inquiryId: string, userLoginId: string) {
    const { statusCode } = await messageClient.sendMessage({
      channel: "WS",
      platform: ["APP"],
      to: [userLoginId],
      type: "ACTION",
      group: "AI_CHAT_INQUIRY_RECOMMEND",
      title: "开思",
      content: "你有新的报价方案，前往查看～",
      url: `cassapp://route/native/inquiry/quotationResult?query=${JSON.stringify({ inquiryId })}`,
      timestamp: Date.now(),
      delay: 0,
      server: "copilot-server",
      extra: {
        inquiryId,
      },
    });
    if (statusCode !== 200) {
      logger.error("发送消息失败", inquiryId, userLoginId);
    }
  }

  // 查询报价推荐方案
  async getQuoteRecommendPlans(inquiryId: string) {
    const planGroup = await RecommendPlanGroup.findOne({ inquiryId, firstRecommend: true }).sort({ createdAt: -1 });
    if (!planGroup) {
      return { plans: [] };
    }
    return {
      plans: planGroup.plans.map((plan) => {
        const planDoc = (plan as unknown as Document).toObject() as IPurchasePlan;
        return {
          ...planDoc,
          id: planDoc._id?.toString(),
          feature: planDoc.showFeature,
          facility: planDoc.locationName,
        };
      }),
      recommendPlanGroupId: planGroup._id.toString(),
    };
  }
  // 制作推荐方案后，补充方案特征、价格等字段
  async fillRecommendPlanPriceAndFeature({
    plans,
    inquiryId,
  }: {
    plans: IIntelligentPlan[];
    userLoginId: string;
    companyId: string;
    inquiryId: string;
  }): Promise<IPurchasePlan[]> {
    const dataProvider = createDataProvider(inquiryId);
    const [
      {
        inquiryDetail,
        quoteDetail: { quotes },
        storesAddress,
        industryKnowledge,
      },
      quoteDetailV3,
      quoteStores,
    ] = await Promise.all([
      dataProvider.getAll(),
      aggregationInquiryClient.getInquiryDetailV3(inquiryId),
      productClient.getInquirySortStores(inquiryId),
    ]);
    const needInvoice = inquiryDetail.openInvoiceType === "YES";

    const storeFacilityMap: Record<string, Record<string, IStoreFacilityInfo>> = {};
    const storeFacilitys = storesAddress.storeFacilitys || [];
    storeFacilitys.map((item) => {
      if (!storeFacilityMap[item.storeId]) {
        storeFacilityMap[item.storeId] = {};
      }
      item.storeFacilityList.forEach((facility) => {
        storeFacilityMap[item.storeId][facility.facilityId] = facility;
      });
    });
    const filledPlansPromise = plans.map(async (plan, idx) => {
      const featuresMap: Partial<Record<PurchasePlanFeatureEnum, boolean>> = {};
      const { standardItemResults } = plan as IInquiryInfo & IPurchasePlanItem;

      const planProductIds = standardItemResults.map(({ quotationProductIds }) => quotationProductIds).flat();

      let locationName = ""; // 发货地

      const traverseUserNeedsQuotes = <T>(callback: (quote: IPlanQuoteItem, prevResult: T) => T, initResult: T) => {
        let result = initResult;
        standardItemResults.forEach(({ quotationProductIds }) => {
          quotationProductIds.forEach((planProductId) => {
            const quote = quotes.find((quote) => quote.quotationProductId === planProductId);
            if (!quote) {
              return;
            }
            result = callback(quote, result);
          });
        });
        return result;
      };
      // 全部原厂
      featuresMap.ORIGINAL_BRAND = traverseUserNeedsQuotes(
        (quote, prev) =>
          prev &&
          ["ORIGINAL_BRAND", "ORIGINAL_CURRENCY", "ORIGINAL_INLAND_4S", "ORIGINAL_OTHERS"].includes(
            quote.partsBrandQuality || ""
          ),
        true
      );
      // 全部国际品牌
      featuresMap.EXTERNAL_BRAND = traverseUserNeedsQuotes(
        (quote, prev) => prev && quote.partsBrandQuality === "EXTERNAL_BRAND",
        true
      );
      const storeNames = traverseUserNeedsQuotes((quote, prev) => {
        prev.add(quote.storeName || "");
        locationName = quote.locationName || "";
        return prev;
      }, new Set<string>());
      // 整单
      featuresMap.WHOLE_DISTRIBUTE = storeNames.size === 1;
      // 全部云仓
      featuresMap.YUN_FACILITY = plan.type === IntelligentPlanType.YUN_FACILITY;
      // 全部配套品牌
      featuresMap.OEM = traverseUserNeedsQuotes((quote, prev) => {
        const oemBrandIds =
          industryKnowledge?.standardItemKnowledgeList.find((list) => list.standardItemId === quote.standardItemId)
            ?.oemBrandIds || [];
        return prev && oemBrandIds.includes(quote?.brandId || "");
      }, true);
      // 方案价格
      const price = traverseUserNeedsQuotes((quote, prev) => {
        return prev + (needInvoice ? Number(quote.price) : Number(quote.btPrice));
      }, 0);

      quoteDetailV3?.userNeeds?.forEach((userNeed) => {
        const { decodeResults } = userNeed;
        decodeResults?.forEach((decodeResult) => {
          decodeResult.layers?.forEach((layer) => {
            const isStrict = layer.layerCode === "STRICT_LAYER";
            layer.subLayers?.forEach((subLayer) => {
              subLayer.storeLayers?.forEach((storeLayer) => {
                storeLayer.quotationProductIds.forEach((productId) => {
                  if (planProductIds.includes(productId)) {
                    if (isStrict && featuresMap.STRICT !== false) {
                      featuresMap.STRICT = true;
                    } else {
                      featuresMap.STRICT = false;
                    }
                  }
                });
              });
            });
          });
        });
      });
      const features: PurchasePlanFeatureEnum[] = [];
      let showFeature = "多商家";

      if (featuresMap.EXTERNAL_BRAND) {
        features.push(PurchasePlanFeatureEnum.EXTERNAL_BRAND);
        showFeature = PurchasePlanFeatureLabel.EXTERNAL_BRAND;
      }
      if (featuresMap.OEM) {
        features.push(PurchasePlanFeatureEnum.OEM);
        showFeature = PurchasePlanFeatureLabel.OEM;
      }
      if (featuresMap.ORIGINAL_BRAND) {
        features.push(PurchasePlanFeatureEnum.ORIGINAL_BRAND);
        showFeature = PurchasePlanFeatureLabel.ORIGINAL_BRAND;
      }
      if (featuresMap.WHOLE_DISTRIBUTE) {
        const storeName = [...storeNames][0];
        const inquiryStore = quoteStores.find((inquiryStore) => inquiryStore.storeName === storeName);
        features.push(PurchasePlanFeatureEnum.WHOLE_DISTRIBUTE);
        showFeature = inquiryStore?.positioningName || storeName;
      }
      if (featuresMap.STRICT) {
        features.push(PurchasePlanFeatureEnum.STRICT);
        showFeature = PurchasePlanFeatureLabel.STRICT;
      }
      if (featuresMap.YUN_FACILITY) {
        features.push(PurchasePlanFeatureEnum.YUN_FACILITY);
        showFeature = PurchasePlanFeatureLabel.YUN_FACILITY;
      }

      return {
        ...plan,
        name: plan.name || `方案${idx + 1}`,
        durationDesc: plan.defaultEta,
        showFeature,
        locationName: featuresMap.WHOLE_DISTRIBUTE ? locationName : "多仓发货",
        price,
        features,
      };
    });
    const filledPlans = await Promise.all(filledPlansPromise);

    return filledPlans as IPurchasePlan[];
  }

  // 根据方案组id查询未读的方案
  public async getUnreadPurchasePlanById(planGroupId: string, dialogueId: string) {
    const planGroup = await RecommendPlanGroup.findById(planGroupId);
    if (!planGroup) {
      return;
    }
    const planUnRead = await RecommendPlanGroupRead.findOneAndUpdate(
      {
        recommendPlanGroupId: planGroup._id,
        dialogueIds: { $nin: [dialogueId] },
      },
      {
        $addToSet: { dialogueIds: dialogueId },
      },
      { new: true }
    );
    if (planUnRead) {
      return planGroup.toJSON();
    }
    return;
  }
}
