import logger from "@/common/logger";
import { inquiryClient } from "@/clients/inquiry";
import { INeedDecodeItem, IGetNeedDecodeListRes } from "./interface";
import _ from "lodash";

export class InquiryService {
  /**
   * 需求和译码列表
   * @param inquiryId
   * @returns
   */
  async getNeedDecodeList(inquiryId: string): Promise<IGetNeedDecodeListRes> {
    const needDecodeList: INeedDecodeItem[] = [];
    try {
      const [needsListRes, decodeListRes] = await Promise.all([
        inquiryClient.getUserNeedsList(inquiryId),
        inquiryClient.getDecodeResultList(inquiryId),
      ]);
      needsListRes?.forEach((need) => {
        const { needId, needsName } = need;
        const decodes = decodeListRes?.filter((decode) => decode.userNeedsId === needId);
        if (decodes?.length) {
          decodes.forEach((decode) => {
            needDecodeList.push({
              needId: needId,
              needsName: needsName,
              decodeResultId: decode.decodeResultId,
              partsName: decode.partsName,
            });
          });
        } else {
          needDecodeList.push({
            needId: needId,
            needsName: needsName,
          });
        }
      });
    } catch (error) {
      logger.error("获取需求和译码列表失败", error);
    }
    const needsNames: string[] = _.uniqBy(needDecodeList, (item) => item.needId).map((item) => item.needsName);

    return { needDecodeList, needsNames };
  }
}
