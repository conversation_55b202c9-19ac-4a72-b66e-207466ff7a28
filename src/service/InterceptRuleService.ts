import logger from "@/common/logger";
import { IInterceptRule, InterceptRule } from "@/models";
import _ from "lodash";

export class InterceptRuleService {
  /**
   * 英文数字保持连续，中文及其他按字符切分
   * @param text
   * @returns
   */
  private splitChars(text: string) {
    return text
      .toLocaleLowerCase()
      .split(/\b/)
      .map((text) => (/^[a-b0-9_]+$/.test(text) ? [text] : text.split("")))
      .flat()
      .filter((text) => !/^\s+$/.test(text));
  }
  /**
   * 根据内容查询拦截规则
   * @param content
   * @returns
   */
  async findRules(app: string, content: string) {
    const rules = await InterceptRule.find({
      $text: {
        $search: this.splitChars(content).join(" "),
      },
      app,
      enabled: true,
    });
    return rules;
  }

  /**
   * 内容是否跟当前规则匹配
   * @param content
   * @param rule
   * @returns
   */
  isMatched(content: string, rule: IInterceptRule) {
    const { operator, value } = rule;
    switch (operator) {
      case "keywordsIn":
        return value.some((keyword) => content.includes(keyword));
      case "textIn":
        return value.includes(content);
      case "regexIn":
        return value.some((regex) => new RegExp(regex).test(content));
      default:
        return false;
    }
  }

  /**
   * 匹配规则
   * @param content
   */
  async findMatchedRule(app: string, content: string): Promise<IInterceptRule | undefined> {
    const rules = await this.findRules(app, content);
    logger.info(`查找到 ${rules.length} 条拦截规则 --> ${content}`);
    const sortedRules = _.orderBy(rules, "priority", "desc");
    for (const rule of sortedRules) {
      if (this.isMatched(content, rule)) {
        return rule;
      }
    }
  }
}
