import { IMessage, ISocket, MessageFactory, ReplyMode } from "@casstime/copilot-core";
import { FastifyReply } from "fastify";
import { CopilotMessage, CopilotMessageDoc } from "@/models";
import { Types } from "mongoose";
import logger from "@/common/logger";
import MessageQueue from "./MessageQueue";
import { AbstractTransport, TransportOptions } from "./transports/AbstractTransport";
import { PollingTransport } from "./transports/PollingTransport";
import { EventSourceTransport } from "./transports/EventSourceTransport";
import { BlockTransport } from "./transports/BlockTransport";
import MessageProcessor from "./MessageProcessor";

interface ISocketOptions {
  sessionId: string;
  fromMessage: CopilotMessageDoc;
  reply: FastifyReply;
  replyMode: ReplyMode;
  requestId: string;
  messageProcessor: MessageProcessor;
}

class CopilotSocket implements ISocket {
  id: string;
  private fromMessage: CopilotMessageDoc;
  private reply: FastifyReply;
  private transport!: AbstractTransport;
  private requestId: string;
  private messageQueue: MessageQueue<unknown>;
  private messageProcessor: MessageProcessor;

  constructor(options: ISocketOptions) {
    this.id = options.sessionId;
    this.fromMessage = options.fromMessage;
    this.reply = options.reply;
    this.requestId = options.requestId;
    this.messageProcessor = options.messageProcessor;
    const transportName = options.replyMode === "stream" ? "event-source" : undefined;
    this.setTransport(transportName || "block");
    this.messageQueue = new MessageQueue(this.consumer);
  }

  private setTransport(transport: "polling" | "event-source" | "block") {
    const options: TransportOptions = {
      reply: this.reply,
      waitSentMessages: this.transport?.waitSentMessages,
      fromMessage: this.fromMessage,
      sessionId: this.id,
      requestId: this.requestId,
    };

    switch (transport) {
      case "polling":
        this.transport = new PollingTransport(options);
        break;
      case "event-source":
        this.transport = new EventSourceTransport(options);
        break;
      case "block":
        this.transport = new BlockTransport(options);
        break;
      default:
        throw new Error("Unknown transport");
    }
  }

  // 消费消息队列，保存到数据库并通过transport发送消息
  private consumer = async (message: IMessage) => {
    // 处理兼容性问题
    const messages = this.messageProcessor.process(message);
    const msgs = [];
    for (const msg of messages) {
      let waitSentMsg = msg;
      if (msg.persistent !== false) {
        waitSentMsg = await CopilotMessage.findOneAndUpdate(
          { _id: msg.id ? Types.ObjectId(msg.id) : new Types.ObjectId() },
          Object.assign(
            { sessionId: this.id, embed: undefined, indicator: undefined, tips: undefined, actions: [] },
            msg,
            {
              // ...MessageFactory.base(),
              id: undefined,
              toUser: this.fromMessage.fromUser,
              fromUser: "system",
              owner: this.fromMessage.owner,
              requestId: this.requestId,
            }
          ),
          { upsert: true, new: true }
        );
      }
      msgs.push(waitSentMsg);
    }
    this.transport.send(msgs);
  };

  // 将消息放到消息队列供消费
  send(message: CopilotMessageDoc) {
    this.messageQueue.push(message);
  }

  async end() {
    // 等待队列清空
    await this.messageQueue.waitQueueEmpty();
    this.transport.close();
  }

  // 设置输出模式，暂时只支持从block切换到stream
  setReplyMode(replyMode: ReplyMode) {
    if (this.transport.name === "block" && replyMode === "stream") {
      this.setTransport("polling");
      return;
    }
    logger.warn("replyMode 只允许从 block 切换到 stream");
  }
}

export default CopilotSocket;
