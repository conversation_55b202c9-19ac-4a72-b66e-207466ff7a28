import { VersionEnum } from "@/common/enums";
import Version from "@/common/utils/Version";
import { ITextMessage, IMessage } from "@casstime/copilot-core";

interface IMessageProcessOptions {
  appVersion: Version;
  containerId: string;
}

/**
 * 用于处理消息的兼容性问题
 */
class MessageProcessor {
  private appVersion: Version;
  private containerId: string;
  constructor(options: IMessageProcessOptions) {
    this.appVersion = options.appVersion;
    this.containerId = options.containerId;
  }

  markdownToText(text: string) {
    return text
      .replace(/^#{1,6}\s+/gm, "") // 去掉标题
      .replace(/(\*\*|__)(.*?)\1/g, "$2") // 去掉加粗
      .replace(/(\*|_)(.*?)\1/g, "$2") // 去掉斜体
      .replace(/-\s*/g, ""); // 去掉列表
  }

  process(message: IMessage): IMessage[] {
    if (this.containerId === "cassapp") {
      const isLessThan051700 = this.appVersion.isLessThan(VersionEnum.FIVE_17_0);
      // 兼容旧版本不支持markdown消息
      if (message.type === "markdown") {
        if (isLessThan051700) {
          let content = this.markdownToText(message.content);
          if (message.disclaimer) {
            content += `\n<font color="#646566" fontSize="26">${message.disclaimer}</font>`;
          }
          const MDMessage: ITextMessage = { ...message, type: "text", content, disclaimer: undefined };
          return [MDMessage];
        }
      }
      // 兼容旧版本不支持disclaimer
      if (isLessThan051700) {
        const { type, disclaimer } = message;
        if (type === "text" && disclaimer) {
          message.content += `\n<font color="#646566" fontSize="26">${message.disclaimer}</font>`;
        }
      }
    }
    return [message];
  }
}

export default MessageProcessor;
