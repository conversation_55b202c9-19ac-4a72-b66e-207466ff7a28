import { IMessage } from "@casstime/copilot-core";
import MessageQueue from "../MessageQueue";
import { sleep } from "@/common/utils";

it("队列能够顺序消费消息", async () => {
  const consumer = jest.fn();
  const queue = new MessageQueue(consumer);
  const msg1: IMessage = { type: "text", content: "hello1" };
  const msg2: IMessage = { type: "text", content: "hello2" };
  queue.push(msg1);
  queue.push(msg2);
  await queue.waitQueueEmpty();
  expect(consumer).toHaveBeenNthCalledWith(1, msg1);
  expect(consumer).toHaveBeenNthCalledWith(2, msg2);
});

it("如果队列中包含相同id的消息，则只保留最新的消息", async () => {
  const consumer = jest.fn().mockImplementation(async (msg: IMessage) => {
    await sleep(1);
  });
  const queue = new MessageQueue(consumer);
  const msg1: IMessage = { type: "text", content: "hello1", id: "1" };
  const msg2: IMessage = { type: "text", content: "hello2", id: "2" };
  const msg3: IMessage = { type: "text", content: "hello3", id: "2" };
  queue.push(msg1);
  queue.push(msg2);
  queue.push(msg3);
  await queue.waitQueueEmpty();
  expect(consumer).toHaveBeenNthCalledWith(2, msg3);
});

it("消费完毕后会触发 consumed 事件", async () => {
  const consumer = jest.fn().mockImplementation(async (msg: IMessage) => {
    return {
      ...msg,
      id: "test",
    };
  });
  const callback = jest.fn();
  const queue = new MessageQueue(consumer);
  queue.on("consumed", callback);
  const msg1: IMessage = { type: "text", content: "hello1" };
  queue.push(msg1);
  await queue.waitQueueEmpty();
  expect(callback).toHaveBeenCalledWith({
    ...msg1,
    id: "test",
  });
});
