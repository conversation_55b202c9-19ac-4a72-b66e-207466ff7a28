import { parseUserAgent, isError } from "@/common/utils";
import { IPayload, IServer, ISocket, MessageFactory, Session } from "@casstime/copilot-core";
import { FastifyInstance } from "fastify";
import { Dialogue, ContextValue, CopilotMessage, ICopilotMessage, IContextValue, RequestStatus } from "@/models";
import CopilotSocket from "./CopilotSocket";
import Version from "@/common/utils/Version";
import { AppMap, VersionEnum } from "@/common/enums";
import { storage } from "../storage";
import { AgentName, AppName, Intents } from "../constants";
import { sleep } from "@/common/utils";
import { Types } from "mongoose";
import MessageProcessor from "./MessageProcessor";
import dayjs from "dayjs";
import _ from "lodash";
import assert from "assert";
import logger from "@/common/logger";

type PayloadCallback = Parameters<IServer["onPayload"]>[0];

interface IDoPollingParams {
  sessionId: string;
  requestId: string;
  updatedAt: number;
}

export interface IParamsSchema {
  Body: {
    sessionId?: string;
    data: unknown;
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["data"],
      properties: {
        sessionId: { type: "string" },
        data: { type: "object" },
      },
    },
  },
};

class CopilotServer implements IServer {
  private payloadCallback?: PayloadCallback;

  async doPolling(params: IDoPollingParams) {
    const { sessionId, requestId, updatedAt } = params;
    // 只查3分钟之内的消息
    const TIME_LIMIT = 3 * 60 * 1000;
    const createdAfter = new Date().getTime() - TIME_LIMIT;
    await sleep(100);
    const [requestStatus, messageDocs] = await Promise.all([
      RequestStatus.findOne({ requestId }).lean(), // 查询请求是否结束
      CopilotMessage.find({
        sessionId,
        requestId,
        fromUser: "system",
        createdAt: { $gt: createdAfter },
      }),
    ]);
    const messages: ICopilotMessage[] = messageDocs.map((msgdoc) => msgdoc.toJSON());
    if (messages.length && requestStatus && !requestStatus?.done) {
      // 取出回复消息中最新一条消息的 updatedAt
      const maxUpdatedAt = _.maxBy(messages, "updatedAt")?.updatedAt;
      const hasChanged = dayjs(maxUpdatedAt).isAfter(dayjs(updatedAt));
      if (!hasChanged) {
        // 流式消息没有变化，多延迟 500ms
        await sleep(500);
      }
      // 请求未结束，继续增加触发 polling 的 reply 消息
      const { requestId, sessionId, fromUser } = messages[0];
      messages.push({
        id: new Types.ObjectId().toString(),
        type: "command",
        fromUser,
        command: Intents.pollingStreamMsg,
        reply: {
          type: "command",
          command: Intents.pollingStreamMsg,
          params: { requestId, sessionId, updatedAt: dayjs(maxUpdatedAt).valueOf() },
        },
        sessionId,
      } as ICopilotMessage);
    }
    return messages;
  }

  constructor(
    private server: FastifyInstance,
    path: string
  ) {
    this.server.post<IParamsSchema>(path, opts, async (req, reply) => {
      const payload = req.body as IPayload;
      // 默认为“采购助手”
      payload.app = payload.app || AppName.GarageAssistant;
      const { data: fromMessage, app, companyId, agentName } = payload;
      let sessionId = payload.sessionId;
      const userId = fromMessage.fromUser;

      // 兼容采购助手 command 或者带 nlu 的消息缺失 agentName
      if (app === AppName.GarageAssistant && (fromMessage.type === "command" || fromMessage?.nlu)) {
        fromMessage.nlu = {
          ...(fromMessage?.nlu || {}),
          agentName: fromMessage?.nlu?.agentName || AgentName.inquiryAgent,
        };
      }

      // 将 app 和 agentName 放到消息体中
      fromMessage.app = app;
      if (agentName) {
        fromMessage.nlu = {
          ...(fromMessage?.nlu || {}),
          agentName,
        };
      }
      if (fromMessage.nlu?.agentName) {
        fromMessage.agent = fromMessage.nlu?.agentName;
      }
      // 组装 owner
      if (app && userId && companyId) {
        fromMessage.owner = `${app}:${userId}:${companyId}`;
      }

      // 获取app版本号
      payload.headers = req.headers;
      const userAgent = payload.headers["user-agent"];
      const { appVersion, containerId, platform } = parseUserAgent(userAgent || "");
      payload.appVersion = new Version(appVersion || VersionEnum.MAX_VERSION);
      payload.containerId = containerId;
      payload.platform = platform;
      let socket: ISocket | undefined = undefined;
      try {
        // 补充dialogueId
        if (!fromMessage.dialogueId) {
          const displayName = AppMap[app as keyof typeof AppMap];
          const dialogue = await Dialogue.findOneAndUpdate(
            { userId, app, companyId, businessId: "DEFAULT" },
            { $set: { displayName } },
            { upsert: true, new: true }
          )
            .sort({ updatedAt: -1 })
            .exec();
          fromMessage.dialogueId = dialogue.id.toString();
        }

        // 处理流式消息
        if (
          fromMessage.type === "command" &&
          fromMessage.command === Intents.pollingStreamMsg &&
          fromMessage.params?.requestId
        ) {
          const messages = await this.doPolling(fromMessage.params);
          reply.send({
            sessionId,
            messages,
          });

          return reply;
        }

        // 生成 requestId
        const requestId = new Types.ObjectId().toString();
        if (!userId) throw new Error("缺少 fromUser");

        const dialogueId = fromMessage.dialogueId;
        let condition: Partial<IContextValue> = { userId, app };
        if (dialogueId) {
          condition = { dialogueId };
        }
        // 查询最新的sessionId
        const beforeContextValue = await ContextValue.findOne(condition, { _id: 0, id: 0 })
          .sort({ updatedAt: -1 })
          .exec();
        sessionId = beforeContextValue?.sessionId;
        const session = new Session({ app, userId, sessionId, dialogueId, storage });
        await session.loadContextData();
        sessionId = session.sessionId;

        // 保存用户消息
        const msg = await CopilotMessage.create({
          ...fromMessage,
          sessionId,
          dialogueId,
          toUser: "system",
          id: undefined,
          _id: undefined,
          createdAt: undefined,
          requestId,
        });
        // 保存后的消息包含id，便于后续业务使用
        payload.data = msg.toJSON();
        const appVersion = payload.appVersion as Version;
        const containerId = payload.containerId as string;
        socket = new CopilotSocket({
          sessionId,
          fromMessage: msg,
          reply,
          replyMode: payload.replyMode || "block",
          requestId,
          messageProcessor: new MessageProcessor({ appVersion, containerId }),
        });
        await this.payloadCallback?.(payload, socket, session);
      } catch (err) {
        logger.error("Copilot Server 出错了", err);
        assert(isError<{ extra?: { message?: string } }>(err), "err must be an instance of Error");
        const errMsg = err.extra?.message || err.message || "未知错误";
        if (socket) {
          socket.send(
            MessageFactory.system(errMsg, {
              fromUser: "system",
              toUser: fromMessage.fromUser,
            })
          );
          socket.end();
        }
      }
      return reply;
    });
  }

  onPayload(callback: PayloadCallback): void {
    this.payloadCallback = callback;
  }
}

export default CopilotServer;
