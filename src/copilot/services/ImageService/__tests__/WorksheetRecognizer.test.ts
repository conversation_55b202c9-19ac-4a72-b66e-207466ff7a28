import { WorksheetRecognizer } from "../WorksheetRecognizer";
import { imageClient } from "../../../apps/GarageAssistant/agents/inquiry/clients/ImageClient";
import { useRequestMeta } from "../../../../common/asyncStore";

// Mock外部依赖
jest.mock("@/copilot/apps/GarageAssistant/agents/inquiry/clients/ImageClient");
// jest.mock("@/copilot/helpers");
jest.mock("@/common/asyncStore", () => ({
  useRequestMeta: jest.fn(),
}));

describe("WorksheetRecognizer 测试", () => {
  let recognizer: WorksheetRecognizer;

  beforeEach(() => {
    recognizer = new WorksheetRecognizer("test.jpg");
    jest.clearAllMocks();
  });

  describe("shouldRotateImage 方法", () => {
    it("当分数超过阈值且标签不为0时应返回true", () => {
      const result = recognizer["shouldRotateImage"]({
        score: 0.6,
        label: "90",
      });
      expect(result).toBe(true);
    });

    it("当分数低于阈值时应返回false", () => {
      const result = recognizer["shouldRotateImage"]({
        score: 0.4,
        label: "90",
      });
      expect(result).toBe(false);
    });

    it("当标签为0时应返回false", () => {
      const result = recognizer["shouldRotateImage"]({
        score: 0.6,
        label: "0",
      });
      expect(result).toBe(false);
    });
  });

  describe("selectBestOcrResult 方法", () => {
    it("当只有一个结果时应直接返回", () => {
      const results = [[{ words: "test" }]];
      const merged = recognizer["selectBestOcrResult"](results);
      expect(merged).toEqual([{ words: "test" }]);
    });

    it("应选择文本更长的结果", () => {
      const results = [[{ words: "short" }], [{ words: "longer text" }]];
      const merged = recognizer["selectBestOcrResult"](results);
      expect(merged).toEqual([{ words: "longer text" }]);
    });
  });

  describe("calculateTotalTextLength 方法", () => {
    it("应正确计算文本总长度", () => {
      const items = [{ words: "test" }, { words: "123" }, { words: "" }];
      const length = recognizer["calculateTotalTextLength"](items);
      expect(length).toBe(7);
    });

    it("当没有文字时应返回0", () => {
      const items = [{ words: "" }, { words: undefined }];
      const length = recognizer["calculateTotalTextLength"](items);
      expect(length).toBe(0);
    });
  });

  describe("extractRequiredPartNames 方法", () => {
    it("应只提取标记为需要的配件名", () => {
      const result = [
        { words: "part1", isNeed: true },
        { words: "part2", isNeed: false },
        { words: "part3", isNeed: true },
      ];
      const names = recognizer["extractNeedNames"](result);
      expect(names).toEqual(["part1", "part3"]);
    });
  });

  describe("indexPartNamesWithSerialNumbers 方法", () => {
    it("应正确为配件名称添加序号", () => {
      const partNames = ["part1", "part2"];
      const indexed = recognizer["indexPartNamesWithSerialNumbers"](partNames);
      expect(indexed).toEqual([
        { name: "part1", serialNum: 1 },
        { name: "part2", serialNum: 2 },
      ]);
    });
  });

  describe("recognize 方法", () => {
    beforeEach(() => {
      (useRequestMeta as jest.Mock).mockReturnValue({ platform: "ios" });
    });

    it("应正确处理工单图片", async () => {
      // 设置mock返回值
      (imageClient.getImageRotate as jest.Mock).mockResolvedValue({
        score: 0,
        label: "0",
      });
      (imageClient.ocrRecognizePartsNames as jest.Mock).mockResolvedValue([
        { words: "LVSHCAAE99F408357", isNeed: false },
        { words: "机油格", isNeed: true },
      ]);
      (imageClient.getMatchNeeds as jest.Mock).mockResolvedValue([]);

      const result = await recognizer.recognize();
      expect(result).toEqual({
        vinCodes: ["LVSHCAAE99F408357"],
        partNames: [],
        texts: ["LVSHCAAE99F408357", "机油格"],
      });
    });

    it("当平台信息缺失时应抛出错误", async () => {
      (useRequestMeta as jest.Mock).mockReturnValue({});
      await expect(recognizer.recognize()).rejects.toThrow("Platform is required");
    });
  });

  describe("processPartNames 方法", () => {
    it("应正确处理配件名称", async () => {
      const mockBatchResponse = [
        { name: "part1", serialNum: 1, similar: 0.8, threshold: 0.7, stdName: "标准配件1" },
        { name: "part2", serialNum: 2, similar: 0.6, threshold: 0.7, stdName: "标准配件2" },
      ];
      (imageClient.getMatchNeeds as jest.Mock).mockResolvedValue(mockBatchResponse);

      const result = await recognizer["processPartNames"](["part1", "part2"]);
      expect(result).toEqual(["标准配件1", "part2"]);
    });
  });

  describe("filterAndValidateShortPartNames 方法", () => {
    const data = [
      {
        input: ["前杠", "张三", "前下笔支架", "后杠喝"],
        output: ["前杠", "前下笔支架", "后杠喝"],
      },
    ];
    data.forEach(({ input, output }) => {
      it(`should return ${output} for input ${input}`, async () => {
        const result = await recognizer["filterAndValidateShortPartNames"](input);
        expect(result).toEqual(output);
      });
    });
  });
});
