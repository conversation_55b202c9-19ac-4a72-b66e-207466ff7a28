import { imageClient } from "../../apps/GarageAssistant/agents/inquiry/clients/ImageClient";
import { useRequestMeta } from "@/common/asyncStore";
import { rotateOssImage } from "@/common/utils";
import { extractPartNameEntities, extractVinCodeEntities } from "../../helpers";
import type { IHandleWritingDto } from "@/interfaces/client";

// ======================
// 类型定义
// ======================

// 图片处理相关类型
interface ImageRotationResult {
  score?: number;
  label?: string;
}

interface ImageParams {
  imageUrl: string;
  application: string;
  channel: string;
  scene: string;
  os: string;
}

// OCR识别结果类型
type OcrResultItem = IHandleWritingDto;

// 工作表识别结果
export interface IWorksheet {
  vinCodes?: string[];
  partNames: string[];
  texts: string[];
}

// 配件名称处理相关类型
interface PartNameWithSerial {
  name: string;
  serialNum: number;
}

/**
 * @type 用于standardizePartNames方法的返回类型
 */
interface StandardizedPartName extends PartNameWithSerial {
  threshold?: number;
  similar?: number;
  stdName?: string;
}

// ======================
// 配置常量
// ======================
const Config = {
  BATCH_SIZE: 20,
  ROTATION: {
    SCORE_THRESHOLD: 0.5,
  },
  IMAGE_API: {
    APPLICATION: "MALL_MAINTENANCE_SHOP_APP",
    CHANNEL: "CASSMALL",
    SCENE: "INTELLIGENT_PURCHASE",
  },
};

export class WorksheetRecognizer {
  private readonly imageUrl: string;

  constructor(imageUrl: string) {
    this.imageUrl = imageUrl;
  }

  /**
   * 识别工单图片
   * @param imageUrl 图片URL
   * @returns 识别结果
   * @throws 当平台信息缺失或识别失败时抛出错误
   */
  async recognize(): Promise<IWorksheet> {
    let imageParams: ImageParams;

    try {
      imageParams = this.getImageParams(); // 获取图片识别参数
      const results = await this.getOcrResults(imageParams);
      const bestResult = this.selectBestOcrResult(results);
      const needNames = this.extractNeedNames(bestResult);
      const partNames = await this.processPartNames(needNames);
      const vinCodeEntities = extractVinCodeEntities(bestResult.map((item) => item.words || "").join("\n"));
      return {
        vinCodes: vinCodeEntities.map((entity) => entity.value),
        partNames,
        texts: bestResult.map((item) => item.words || "").filter(Boolean),
      };
    } catch (error) {
      throw new Error(`Worksheet recognition failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取图片参数
   * @param imageUrl 图片URL
   * @param platform 平台信息
   * @returns 图片参数对象
   */

  private getImageParams(): ImageParams {
    const { platform } = useRequestMeta();
    if (!platform) {
      throw new Error("Platform is required for getImageParams");
    }

    return {
      imageUrl: this.imageUrl,
      application: Config.IMAGE_API.APPLICATION,
      channel: Config.IMAGE_API.CHANNEL,
      scene: Config.IMAGE_API.SCENE,
      os: platform,
    };
  }

  /**
   * 获取OCR识别结果
   * @param imageUrl 图片URL
   * @param imageParams 图片参数
   * @returns 返回OCR识别结果数组
   */

  private async getOcrResults(imageParams: ImageParams): Promise<OcrResultItem[][]> {
    const ocrRecognizePromises = [imageClient.ocrRecognizePartsNames(imageParams)];
    const rotationResult = await imageClient.getImageRotate(this.imageUrl);

    if (this.shouldRotateImage(rotationResult)) {
      const rotateImageUrl = rotateOssImage(this.imageUrl, 360 - Number(rotationResult.label));
      ocrRecognizePromises.push(imageClient.ocrRecognizePartsNames({ ...imageParams, imageUrl: rotateImageUrl }));
    }
    return Promise.all(ocrRecognizePromises);
  }

  /**
   * 判断图片是否需要旋转
   * @param rotationResult 旋转检测结果
   * @returns 是否需要旋转图片
   */
  private shouldRotateImage(rotationResult: ImageRotationResult): boolean {
    return !!(
      rotationResult.score &&
      rotationResult.score >= Config.ROTATION.SCORE_THRESHOLD &&
      rotationResult.label !== "0"
    );
  }

  /**
   * 选择最佳的识别结果
   * @param results 多个OCR识别结果
   * @returns 最佳识别结果
   */
  private selectBestOcrResult(results: OcrResultItem[][]): OcrResultItem[] {
    if (results.length === 1) return results[0];

    // 从两个角度识别结果中，选择包含更多文字的识别结果
    return results.reduce((longest: OcrResultItem[], current: OcrResultItem[]) => {
      const currentTextLength = this.calculateTotalTextLength(current);
      const longestTextLength = this.calculateTotalTextLength(longest);
      return currentTextLength > longestTextLength ? current : longest;
    }, []);
  }

  /**
   * 计算OCR结果中所有文字的总长度
   * @param items OCR识别结果项数组
   * @returns 文字总长度
   */
  private calculateTotalTextLength(items: OcrResultItem[]): number {
    return items.reduce((sum, item) => sum + (item.words?.length || 0), 0);
  }

  /**
   * 提取需要识别的配件名称
   * @param result OCR识别结果
   * @returns 配件名称数组
   */
  private extractNeedNames(result: OcrResultItem[]): string[] {
    return result
      .filter((item): item is { words: string; isNeed: true } => !!item.isNeed && !!item.words)
      .map((item) => item.words);
  }

  /**
   * 配件名称纠错处理
   * @param rawPartNames 原始配件名称列表
   * @returns 标准化后的配件名称列表
   * @throws 当标准化处理失败时抛出错误
   */

  private async processPartNames(rawPartNames: string[]): Promise<string[]> {
    const indexedNames = this.indexPartNamesWithSerialNumbers(rawPartNames);
    const batches = this.createBatches(indexedNames);
    const standardizedNames = await this.standardizePartNames(batches);
    const correctedNames = this.correctNamesBasedOnThreshold(standardizedNames);
    return this.filterAndValidateShortPartNames(correctedNames);
  }

  /**
   * 为配件名称添加序号
   * @param partNames 配件名称数组
   * @returns 带序号的配件名称数组
   */
  private indexPartNamesWithSerialNumbers(partNames: string[]): Array<{ name: string; serialNum: number }> {
    return partNames.map((name, index) => ({
      name: name || "",
      serialNum: index + 1,
    }));
  }

  /**
   * 将配件名称分批处理
   * @param partNames 配件名称数组
   * @returns 分批后的配件名称数组
   */
  private createBatches(
    partNames: Array<{ name: string; serialNum: number }>
  ): Array<Array<{ name: string; serialNum: number }>> {
    return Array.from({ length: Math.ceil(partNames.length / Config.BATCH_SIZE) }, (_, i) =>
      partNames.slice(i * Config.BATCH_SIZE, (i + 1) * Config.BATCH_SIZE)
    );
  }

  /**
   * 标准化配件名称
   * @param batches 分批的配件名称
   * @returns 标准化后的配件名称数组
   */
  /**
   * @type {StandardizedPartName[]} 返回标准化后的配件名称数组
   */
  private async standardizePartNames(
    batches: Array<Array<{ name: string; serialNum: number }>>
  ): Promise<StandardizedPartName[]> {
    const requests = batches.map((batch) => imageClient.getMatchNeeds({ needs: batch }));
    const batchResults = await Promise.all(requests);
    return batchResults.flat();
  }

  /**
   * 根据阈值修正配件名称
   * @param standardizedNames 标准化后的配件名称
   * @returns 修正后的配件名称数组
   */
  private correctNamesBasedOnThreshold(
    standardizedNames: Array<{
      name: string;
      serialNum: number;
      threshold?: number;
      similar?: number;
      stdName?: string;
    }>
  ): string[] {
    return standardizedNames
      .sort((a, b) => a.serialNum - b.serialNum)
      .map((item) => {
        const threshold = item.threshold ?? 0;
        const similar = item.similar ?? 0;
        const stdName = item.stdName ?? "";
        const name = item.name ?? "";
        return similar > threshold ? stdName : name;
      });
  }

  /**
   * 验证短配件名称的有效性，返回过滤后的有效配件名称数组
   * @param partNames 配件名称数组
   * @returns 过滤后的有效配件名称数组
   */
  private async filterAndValidateShortPartNames(partNames: string[]): Promise<string[]> {
    const shortNames = partNames.filter((name) => name.length <= 3);
    if (shortNames.length === 0) return partNames;

    const recognizedEntities = await extractPartNameEntities(shortNames.join(","));
    const validShortNames = recognizedEntities.map((entity) => entity.value);

    return partNames.filter(
      (partName) => partName.length > 3 || validShortNames.some((validName) => partName.includes(validName))
    );
  }
}
