import logger from "@/common/logger";
import { executePrompt, waitStreamContent } from "@/copilot/helpers";
import { Service } from "@casstime/copilot-core";

export class PromptService extends Service {
  /**
   * 快速解析用户意图
   * @param dialog 对话内容
   * @param intents 意图列表
   * @returns {string} 解析后的意图
   */
  async parseUserIntentQuickly(dialog: string, intents: string[]): Promise<string | undefined> {
    try {
      const startOptionCode = "A".charCodeAt(0);
      const options = intents.map((intent, index) => `${String.fromCharCode(startOptionCode + index)}、${intent}`);
      const stream = await executePrompt("common/用户意图快速分类提示词", {
        dialog: dialog,
        options: options.join("\n"),
      });
      const content = await waitStreamContent(stream);
      const answerCode = content.trim().charCodeAt(0);
      const index = answerCode - startOptionCode;
      return intents[index];
    } catch (err) {
      logger.warn("PromptService: parseUserIntentQuickly error", err);
      return undefined;
    }
  }
}
