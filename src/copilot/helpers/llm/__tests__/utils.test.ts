import { removeUselessTokens } from "../utils";

describe(removeUselessTokens.name, () => {
  const data = [
    {
      input:
        '这属于具体的联系方式啦，我没办法直接提供呢😔 您可以联系人工客服，他们会帮您解决这个问题哒~ <|FunctionCallBegin|>[{"name": "send_button", "parameters": {"button_text": "人工客服", "button_type": 1}}]<|FunctionCallEnd|>',
      output: "这属于具体的联系方式啦，我没办法直接提供呢😔 您可以联系人工客服，他们会帮您解决这个问题哒~ ",
    },
    {
      input: '不太明白您说的重新推荐是什么意思？<br/>你可以咨询人工客服<button onclick="contact">人工客服</button>',
      output: "不太明白您说的重新推荐是什么意思？你可以咨询人工客服",
    },
  ];
  test.each(data)("removeFunctionCallTokens", async (testCase) => {
    expect(removeUselessTokens(testCase.input)).toBe(testCase.output);
  });
});
