import { Context } from "@casstime/copilot-core";
import { Category } from "./Category";
import { ChatPromptTemplate, PromptTemplate } from "@langchain/core/prompts";
import { JsonOutputParser } from "@langchain/core/output_parsers";
import { chatglm_chat, doubao_chat } from "@/clients/llm";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
import { stringifyHistory, stringifyMessage } from "@/common/utils/message";

const dialogSummaryPrompt = PromptTemplate.fromTemplate(
  `
任务说明：
请根据提供的对话对话文本，生成一个简洁的对话摘要。摘要应包含对话中的关键信息和主要意图。

对话文本：
{history}

请根据以上对话，生成一个简洁的对话摘要，直接输出摘要内容。
  `.trim()
);

const prompt = ChatPromptTemplate.fromMessages([
  [
    "system",
    `
任务：{desc}请从以下意图列表中选择合适的意图：
{intents}
{summary}
指导原则：
- 确保返回的意图属于提供的意图列表。
- 如果意图不明确，应默认选择“其他”。
- 只需返回一个包含\`intent\`键的JSON对象。

{example}
    `.trim(),
  ],
  [
    "user",
    `
当前消息：
{input}
输出：`.trim(),
  ],
]);

const chain = prompt.pipe(doubao_chat).pipe(new JsonOutputParser<{ intent: string }>());
const dialogSummaryChain = dialogSummaryPrompt.pipe(chatglm_chat);

export class IntentClassifier {
  categories: Category[] = [];

  _task = "根据用户的历史对话摘要和当前消息，分析用户的意图，并以JSON格式返回。";

  task(desc: string) {
    this._task = desc;
    return this;
  }

  static create() {
    return new IntentClassifier();
  }

  addCategory(category: Category) {
    this.categories.push(category);
    return this;
  }

  private list(candidates: string[]) {
    return this.categories
      .filter((category) => {
        return candidates.includes(category.name);
      })
      .map((category) => {
        let description = category.description;
        if (category.aliases?.length) {
          description += ` ${category.aliases.join("、")}都归类到${category.name}。`;
        }
        return `- ${category.name}: ${description}`;
      })
      .join("\n")
      .trim();
  }

  private example(candidates: string[]) {
    const examples = this.categories
      .filter((category) => {
        return candidates.includes(category.name);
      })
      .map((intent) => {
        return intent.examples;
      })
      .flat();
    return examples
      .map((example, index) => {
        return `示例${index + 1}: \n${example.trim()}`;
      })
      .join("\n\n");
  }

  async classify(context: Context, candidates?: string[]) {
    if (!candidates) {
      candidates = this.categories.map((category) => category.name);
    }
    const example = this.example(candidates) + "\n";
    const intents = this.list(candidates) + "\n";
    try {
      const history = stringifyHistory(context.historyMessages);
      // let summary = "";
      // if (history) {
      //   summary = (await dialogSummaryChain.invoke({ history })).content.toString().trim() + "\n";
      // }
      const res = await chain.invoke(
        {
          desc: this._task,
          intents,
          summary: history ? `历史对话如下:\n <history_chat>${history}</history_chat>` : "",
          input: stringifyMessage(context.lastMessage),
          example: example ? "示例：\n" + example : "",
        },
        { callbacks: [llmLogCallback] }
      );
      return res.intent || "其他";
    } catch (err) {
      console.error(err);
      return "其他";
    }
  }
}
