export class Category {
  aliases: string[] = [];
  description: string = "";
  examples: string[] = [];

  static create(name: string) {
    return new Category(name);
  }

  constructor(public name: string) {}

  describe(description: string) {
    this.description = description;
    return this;
  }

  alias(...aliases: string[]) {
    this.aliases.push(...aliases);
    return this;
  }

  example(example: string) {
    this.examples.push(example);
    return this;
  }
}
