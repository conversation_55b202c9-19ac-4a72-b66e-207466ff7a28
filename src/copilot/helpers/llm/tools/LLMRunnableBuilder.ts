import { ChatPromptTemplate } from "@langchain/core/prompts";
import { doubao_chat } from "@/clients/llm";
import { JsonOutputParser, StringOutputParser } from "@langchain/core/output_parsers";
import { LLMMessageTemplate } from "./LLMMessageTemplate";
import { Runnable, RunnableConfig } from "@langchain/core/runnables";
import type { ChatOpenAI, ChatOpenAICallOptions } from "@langchain/openai";

export type OutputOptions = {
  type: "string" | "json";
};

export class LLMRunnableBuilder {
  private messages: LLMMessageTemplate[] = [];
  private chat: ChatOpenAI<ChatOpenAICallOptions>;

  static create(chat = doubao_chat) {
    return new LLMRunnableBuilder(chat, []);
  }

  private constructor(chat: ChatOpenAI<ChatOpenAICallOptions>, prompts: LLMMessageTemplate[] = []) {
    this.messages = prompts;
    this.chat = chat;
  }

  addPrompt(prompt: LLMMessageTemplate) {
    this.messages.push(prompt);
    return this;
  }

  build({ type }: { type: "string" }): Runnable<any, string, RunnableConfig>;
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  build<T extends Record<string, any> = {}>({ type }: { type: "json" }): Runnable<any, T, RunnableConfig>;
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  build<T extends Record<string, any> = {}>({ type }: OutputOptions = { type: "string" }): Runnable<any, any, any> {
    const prompt = ChatPromptTemplate.fromMessages(
      this.messages.map((m) => {
        return [m.role, m.content];
      })
    );
    if (type === "string") {
      return prompt.pipe(this.chat).pipe(new StringOutputParser());
    }
    return prompt.pipe(this.chat).pipe(new JsonOutputParser<T>());
  }
}
