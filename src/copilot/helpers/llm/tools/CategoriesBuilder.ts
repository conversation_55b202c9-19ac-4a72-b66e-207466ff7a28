import { Category } from "./Category";

export interface CategoryPromptBlocks {
  examples: string;
  categories: string;
}

export class CategoriesBuilder {
  categories: Category[] = [];

  private constructor(categories: Category[] = []) {
    this.categories = categories;
  }

  static create(categories: Category[] = []): CategoriesBuilder {
    return new CategoriesBuilder(categories);
  }

  addCategory(category: Category) {
    this.categories.push(category);
    return CategoriesBuilder.create([...this.categories]);
  }

  filter(predicate: (category: Category) => boolean) {
    return CategoriesBuilder.create(this.categories.filter(predicate));
  }

  build(): CategoryPromptBlocks {
    return {
      examples: this.categories
        .map((category) => category.examples)
        .flat()
        .map((example, index) => `示例${index + 1}:\n${example}`)
        .join("\n\n"),
      categories: this.categories
        .map((category) => {
          let description = category.description;
          if (category.aliases?.length) {
            description += ` ${category.aliases.join("、")}都归类到${category.name}。`;
          }
          let line = `- ${category.name}`;
          if (description) {
            line += `: ${description}`;
          }
          return line;
        })
        .join("\n"),
    };
  }
}
