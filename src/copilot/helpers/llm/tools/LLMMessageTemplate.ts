type Role = "system" | "user" | "assistant";

export class LLMMessageTemplate {
  static create(role: Role, message = "") {
    return new LLMMessageTemplate(role, message);
  }

  addTitle(title: string, level: number = 1) {
    this.content += "#".repeat(level) + " " + title + "\n";
    return new LLMMessageTemplate(this.role, this.content);
  }

  addText(text: string) {
    this.content += text + "\n";
    return new LLMMessageTemplate(this.role, this.content);
  }

  addBlock(title: string, content: string) {
    this.content += `## ${title}\n${content}\n\n`;
    return new LLMMessageTemplate(this.role, this.content);
  }

  private constructor(public role: Role, public content: string) {}
}
