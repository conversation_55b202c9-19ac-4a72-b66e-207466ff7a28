import { CategoriesBuilder } from "../CategoriesBuilder";
import { Category } from "../Category";

it("addCategory", () => {
  const builder = CategoriesBuilder.create().addCategory(Category.create("test1").example("example1"));
  expect(builder.categories.length).toBe(1);
  expect(builder.build()).toEqual({
    examples: "示例1:\nexample1",
    categories: "- test1",
  });
});

it("filter", () => {
  const builder = CategoriesBuilder.create()
    .addCategory(Category.create("test1").describe("test1 is test1").example("example1"))
    .addCategory(Category.create("test2").example("example2"));

  expect(builder.categories.length).toBe(2);
  const filtered = builder.filter((category) => category.name === "test1");
  expect(filtered.categories.length).toBe(1);
  expect(filtered.build()).toEqual({
    examples: "示例1:\nexample1",
    categories: "- test1: test1 is test1",
  });
});
