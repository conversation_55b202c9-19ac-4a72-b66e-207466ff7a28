import { LLMRunnableBuilder } from "../LLMRunnableBuilder";
import { LLMMessageTemplate } from "../LLMMessageTemplate";

test("create an LLMGenerator instance", () => {
  const generator = LLMRunnableBuilder.create();
  expect(generator).toBeInstanceOf(LLMRunnableBuilder);
});

test("build output from generator", async () => {
  const generator = LLMRunnableBuilder.create().addPrompt(
    LLMMessageTemplate.create("user").addText("你直接回复 'hello'")
  );
  const output = await generator.build({ type: "string" });
  expect(typeof (await output.invoke({}))).toBe("string");
});

test("build json output from generator", async () => {
  const generator = LLMRunnableBuilder.create().addPrompt(
    LLMMessageTemplate.create("user").addText('直接回复后面的JSON => {{"hello": "{value}"}}')
  );
  const output = generator.build({ type: "json" });
  expect(await output.invoke({ value: "aaa" })).toStrictEqual({
    hello: "aaa",
  });
});
