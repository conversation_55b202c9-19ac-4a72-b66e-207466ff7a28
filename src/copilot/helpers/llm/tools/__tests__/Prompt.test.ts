import { LLMMessageTemplate } from "../LLMMessageTemplate";

test("create a Prompt instance", () => {
  const prompt = LLMMessageTemplate.create("user");
  expect(prompt).toBeInstanceOf(LLMMessageTemplate);
});

test("add title and text to prompt", () => {
  const prompt = LLMMessageTemplate.create("user").addTitle("Hello", 2).addText("This is a test");
  expect(prompt.content).toBe("## Hello\nThis is a test\n");
});

test("add block to prompt", () => {
  const prompt = LLMMessageTemplate.create("user").addTitle("Hello", 2).addBlock("This is a test", "Lorem ipsum");
  expect(prompt.content).toBe("## Hello\n## This is a test\nLorem ipsum\n\n");
});
