import { ChatOpenAI } from "@langchain/openai";
import { DynamicStructuredTool } from "@langchain/core/tools";
import { AIMessage, BaseMessage, AIMessageChunk } from "@langchain/core/messages";
import type { ToolCall } from "@langchain/core/messages/tool";
import logger from "@/common/logger";

import { ToolMessage } from "@langchain/core/messages";
import { doubaoDeepSeek_chat } from "@/clients/llm";
import { concat } from "@langchain/core/utils/stream";

export interface ToolsExecutorOptions {
  model?: ChatOpenAI;
  maxIterations?: number;
  toolTimeout?: number;
}

/**
 * 工具执行器，负责管理LLM工具调用流程
 *
 * 功能：
 * 1. 绑定工具集到LLM模型
 * 2. 流式处理工具调用
 * 3. 超时和重试机制
 * 4. 执行状态跟踪
 *
 * 性能考虑：
 * - 每个工具调用有独立超时控制
 * - 最大迭代次数防止无限循环
 *
 * 并发安全：
 * - 非线程安全，每个实例应独立使用
 */
export class ToolsExecutor {
  private modelWithTools: ReturnType<ChatOpenAI["bindTools"]>;
  private maxIterations = 10;
  private toolTimeout = 5000; // 5秒工具调用超时
  private _messages: BaseMessage[] = [];
  private activeStreams = new Set<AbortController>();

  /**
   * 获取当前消息历史（只读）
   * @returns 消息数组的深拷贝
   * @remarks 该方法保证返回的是消息数组的深拷贝，防止外部修改内部状态
   */
  get messages(): ReadonlyArray<BaseMessage> {
    return [...this._messages];
  }

  /**
   * @param tools 可用的工具集
   * @param options 配置选项
   * @param options.model 模型
   * @param options.maxIterations 最大迭代次数，默认10
   * @param options.toolTimeout 工具超时时间(ms)，默认5000
   */
  constructor(
    private tools: DynamicStructuredTool[],
    { model = doubaoDeepSeek_chat, maxIterations = 10, toolTimeout = 5000 }: ToolsExecutorOptions = {}
  ) {
    this.modelWithTools = model.bindTools(tools);
    this.maxIterations = maxIterations ?? this.maxIterations;
    this.toolTimeout = toolTimeout ?? this.toolTimeout;
  }

  /**
   * 根据名称查找工具
   * @param name 工具名称
   * @returns 找到的工具或undefined
   */
  getTool(name: string): DynamicStructuredTool | undefined {
    return this.tools.find((tool) => tool.name === name);
  }

  /**
   * 重置消息历史
   */
  reset(): void {
    this._messages = [];
  }

  /**
   * 执行工具流式处理
   * @returns 是否完成执行 (true=完成, false=需要继续)
   *
   * 实现说明：
   * 1. 从LLM获取流式响应
   * 2. 收集内容和工具调用
   * 3. 对每个工具调用并行执行
   * 4. 处理执行结果或错误
   *
   * 错误处理策略：
   * - 单个工具失败不影响其他工具执行
   * - 主流程错误会终止整个执行
   *
   * 单元测试要点：
   * - 模拟工具调用超时
   * - 验证消息历史更新
   * - 测试多工具并行执行
   */
  /**
   * 执行工具流式处理
   * @param controller ReadableStream控制器
   * @returns 是否完成执行 (true=完成, false=需要继续)
   * @throws 当发生严重错误时抛出异常
   *
   * 算法步骤：
   * 1. 创建AbortController用于控制流式请求
   * 2. 收集流式响应内容和工具调用
   * 3. 如果没有工具调用则直接返回完成
   * 4. 并行执行所有工具调用
   * 5. 处理执行结果或错误
   */
  private async runTools(controller: ReadableStreamDefaultController): Promise<boolean> {
    const abortController = new AbortController();
    this.activeStreams.add(abortController);

    try {
      const stream = await this.modelWithTools.stream(this._messages, {
        signal: abortController.signal,
      });

      const { content, toolCalls } = await this.collectStreamResponse(stream, controller);
      this.addAIMessage(content, toolCalls);

      if (toolCalls.length === 0) {
        logger.info("No tool calls detected, execution completed");
        return true;
      }

      logger.debug(`Processing ${toolCalls.length} tool calls`, {
        toolNames: toolCalls.map((t) => t.name),
      });

      await this.executeToolsInParallel(toolCalls, controller);
      return false;
    } catch (error) {
      this.handleExecutionError(error);
      return true; // 发生错误则终止
    } finally {
      this.activeStreams.delete(abortController);
    }
  }

  /**
   * 收集流式响应内容
   * @param stream 流式响应
   * @param controller ReadableStream控制器
   * @returns 收集到的内容和工具调用
   */
  private async collectStreamResponse(
    stream: AsyncIterable<AIMessageChunk>,
    controller: ReadableStreamDefaultController
  ): Promise<{ content: string; toolCalls: ToolCall[] }> {
    let content = "";
    let gathered: AIMessageChunk | undefined = undefined;

    for await (const chunk of stream) {
      const aiChunk = chunk;

      gathered = gathered ? concat(gathered, chunk) : chunk;

      controller.enqueue(aiChunk);
      if (aiChunk.content) {
        content += aiChunk.content;
      }
    }

    return { content, toolCalls: gathered?.tool_calls ?? [] };
  }

  /**
   * 添加AI消息到历史
   * @param content 消息内容
   * @param toolCalls 工具调用列表
   */
  private addAIMessage(content: string, toolCalls: ToolCall[]): void {
    const aiMessage = new AIMessage({
      content,
      tool_calls: toolCalls.length > 0 ? toolCalls : undefined,
    });
    this._messages.push(aiMessage);
  }

  /**
   * 并行执行工具
   * @param toolCalls 工具调用列表
   * @param controller ReadableStream控制器
   */
  private async executeToolsInParallel(
    toolCalls: ToolCall[],
    controller: ReadableStreamDefaultController
  ): Promise<void> {
    await Promise.all(
      toolCalls.map(async (toolCall) => {
        const tool = this.getTool(toolCall.name);
        if (!tool) {
          this.handleMissingTool(toolCall);
          return;
        }

        try {
          const result = await this.executeToolWithTimeout(tool, toolCall);
          this.handleToolSuccess(toolCall, result, controller);
        } catch (error) {
          this.handleToolError(toolCall, error, controller);
        }
      })
    );
  }

  /**
   * 带超时的工具执行
   * @param tool 工具实例
   * @param toolCall 工具调用信息
   * @returns 工具执行结果
   * @throws 当工具执行超时或失败时抛出异常
   */
  private async executeToolWithTimeout(tool: DynamicStructuredTool, toolCall: ToolCall): Promise<unknown> {
    return Promise.race([
      tool.invoke(toolCall.args),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error(`Tool timeout after ${this.toolTimeout}ms`)), this.toolTimeout)
      ),
    ]);
  }

  /**
   * 处理工具执行成功
   * @param toolCall 工具调用信息
   * @param result 执行结果
   * @param controller ReadableStream控制器
   */
  private handleToolSuccess(toolCall: ToolCall, result: unknown, controller: ReadableStreamDefaultController): void {
    logger.debug(`Tool executed`, {
      toolName: toolCall.name,
      duration: `${this.toolTimeout}ms`,
    });

    if (!toolCall.id) {
      logger.warn(`Tool call missing ID`, { toolName: toolCall.name });
      return;
    }

    const toolMessage = new ToolMessage({
      tool_call_id: toolCall.id,
      name: toolCall.name,
      content: typeof result === "string" ? result : JSON.stringify(result),
    });
    this._messages.push(toolMessage);
    controller.enqueue(toolMessage);
  }

  /**
   * 处理工具执行错误
   * @param toolCall 工具调用信息
   * @param error 错误对象
   * @param controller ReadableStream控制器
   */
  private handleToolError(toolCall: ToolCall, error: unknown, controller: ReadableStreamDefaultController): void {
    const errorMsg = error instanceof Error ? error.message : "Unknown error";
    logger.error(`Tool execution failed`, {
      toolName: toolCall.name,
      error: errorMsg,
      stack: error instanceof Error ? error.stack : undefined,
    });

    if (toolCall.id) {
      const toolMessage = new ToolMessage({
        tool_call_id: toolCall.id,
        name: toolCall.name,
        content: `ERROR: ${errorMsg}`,
      });
      this._messages.push(toolMessage);
      controller.enqueue(toolMessage);
    }
  }

  /**
   * 处理工具缺失情况
   * @param toolCall 工具调用信息
   */
  private handleMissingTool(toolCall: ToolCall): void {
    logger.warn(`Tool not found`, {
      toolName: toolCall.name,
      availableTools: this.tools.map((t) => t.name),
    });
  }

  /**
   * 处理执行错误
   * @param error 错误对象
   */
  private handleExecutionError(error: unknown): void {
    const errorMsg = error instanceof Error ? error.message : String(error);
    logger.error("Execution failed", {
      error: errorMsg,
      stack: error instanceof Error ? error.stack : undefined,
      messageCount: this._messages.length,
      lastMessage: this._messages[this._messages.length - 1]?.content?.slice(0, 100),
    });
  }

  /**
   * 创建并返回可读流
   * @param messages 初始消息数组
   * @returns 可读流实例
   * @remarks 该方法会创建一个新的ReadableStream，并在内部管理其生命周期
   */
  execute(messages: BaseMessage[]): ReadableStream {
    const stream = new ReadableStream({
      start: async (controller) => {
        try {
          await this._execute(messages, controller);
        } finally {
          controller.close();
        }
      },
      cancel: () => {
        // 取消时中止所有活跃的流
        this.activeStreams.forEach((ac) => ac.abort());
        this.activeStreams.clear();
      },
    });

    return stream;
  }

  /**
   * 执行工具调用流程
   * @param messages 初始消息数组
   * @returns 最终的消息历史(只读)
   *
   * 执行流程：
   * 1. 初始化消息历史
   * 2. 循环执行直到完成或达到最大迭代
   * 3. 记录执行统计信息
   */
  async _execute(
    messages: BaseMessage[],
    controller: ReadableStreamDefaultController
  ): Promise<ReadonlyArray<BaseMessage>> {
    this._messages = [...messages];
    let done = false;
    let iteration = 0;

    logger.info("Starting execution", {
      initialMessages: messages.length,
      maxIterations: this.maxIterations,
      toolCount: this.tools.length,
    });

    const startTime = Date.now();

    while (!done && iteration++ < this.maxIterations) {
      logger.debug(`Iteration ${iteration}/${this.maxIterations}`);
      done = await this.runTools(controller);
    }

    const duration = Date.now() - startTime;

    if (!done) {
      logger.warn("Max iterations reached", {
        iterations: iteration,
        duration: `${duration}ms`,
        finalMessages: this._messages.length,
      });
    } else {
      logger.info("Execution completed", {
        iterations: iteration,
        duration: `${duration}ms`,
        messageCount: this._messages.length,
      });
    }

    return this.messages;
  }
}
