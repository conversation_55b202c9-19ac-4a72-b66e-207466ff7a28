import { ToolsExecutor, ToolsExecutorOptions } from "./tools/ToolsExecutor";
import type { DynamicStructuredTool } from "@langchain/core/tools";

/**
 * 创建 ToolsExecutor 实例的工厂函数
 * @param tools 工具数组
 * @param options 配置选项
 * @returns ToolsExecutor 实例
 *
 * 使用示例：
 * ```ts
 * const executor = createToolsExecutor(tools, {
 *   model: customModel,
 *   maxIterations: 5
 * });
 * ```
 */
export function createToolsExecutor(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  tools: DynamicStructuredTool<any>[],
  options?: ToolsExecutorOptions
): ToolsExecutor {
  return new ToolsExecutor(tools, options);
}
