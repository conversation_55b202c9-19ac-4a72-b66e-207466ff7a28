import { parseJsonMarkdown } from "@/common/utils";
import { waitAsyncIterableStream } from "@/common/utils/async";
import { IterableReadableStream } from "@casstime/copilot-core/dist/replier/StreamReplier";
import { AIMessageChunk, MessageType } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { chatglm_chat, doubao_chat } from "../../../clients/llm";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";

/**
 * 等待llm流式输出完成，返回文本内容
 * @param stream 流式输出
 * @returns
 */
export async function waitStreamContent(stream: IterableReadableStream<AIMessageChunk>) {
  const chunks = await waitAsyncIterableStream(stream);
  return chunks.map((chunk) => chunk.content).join("");
}

/**
 * 从流中提取JSON
 * @param stream
 * @returns
 */
export async function extractJsonFromStream<T>(stream: IterableReadableStream<AIMessageChunk>) {
  const content = await waitStreamContent(stream);
  return parseJsonMarkdown(content) as T;
}

/**
 * 从生成的文本中，移除无用的token
 * @param stream
 * @returns
 */
export function removeUselessTokens(text: string) {
  const trimRules = [
    /<\|FunctionCallBegin\|>.*?<\|FunctionCallEnd\|>/g,
    /<\/?richtext_content>/g,
    /<[^>]+>[^<]*<\/[^>]+>/g, // 匹配HTML标签及其内容
    /<[^>]+\/?>/g, // 匹配单个HTML标签;
  ];
  return trimRules.reduce((acc, rule) => {
    return acc.replace(rule, "");
  }, text);
}

export type ChatMessage = { role: MessageType | "user" | "assistant" | "placeholder"; content: string };

/**
 * 封装多轮消息调用
 * @param messages
 * @param param1
 * @returns
 */
export async function createChatCompletion(
  messages: ChatMessage[],
  { model }: { model: ChatOpenAI } = { model: doubao_chat }
) {
  return model.stream(messages, { callbacks: [llmLogCallback] });
}

/**
 * 封装IM问答多轮消息调用
 * @param messages
 * @param param1
 * @returns
 */
export async function createIMChatCompletion(
  messages: ChatMessage[],
  { model }: { model: ChatOpenAI } = { model: chatglm_chat }
) {
  return await model.invoke(messages, { callbacks: [llmLogCallback] });
}
