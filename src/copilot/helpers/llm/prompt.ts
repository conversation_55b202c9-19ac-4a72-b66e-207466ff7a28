import ejs from "ejs";
import { doubao_chat } from "../../../clients/llm";
import { PromptTemplate } from "./PromptTemplate";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
import { isDev } from "@/common/utils";
import { ChatOpenAI } from "@langchain/openai";

/**
 * 渲染模板
 * @param template
 * @param data
 * @returns
 */
export function renderPrompt<T extends ejs.Data>(template: PromptTemplate, data: T) {
  return ejs.renderFile("./prompts/" + template + ".md", data, { async: true, cache: !isDev() });
}

/**
 * 执行提示词
 * @param template
 * @param data
 * @param param2
 * @returns
 */
export async function executePrompt<T extends ejs.Data>(
  template: PromptTemplate,
  data: T,
  { model, temperature }: { model?: ChatOpenAI; temperature?: number } = {}
) {
  const prompt = await renderPrompt(template, data);
  model = model || doubao_chat;
  temperature = temperature || 0.5;
  return model.stream(prompt, {
    callbacks: [llmLogCallback],
    configurable: { temperature },
  });
}
