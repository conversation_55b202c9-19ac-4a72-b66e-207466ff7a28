import { LLMRecord } from "@/models/LLMRecord";
import logger from "@/common/logger";
import { CallbackHandlerMethods } from "@langchain/core/dist/callbacks/base";

export const llmLogCallback: CallbackHandlerMethods = {
  handleLLMStart(_, prompts, runId) {
    logger.debug(`LLM Start: ${runId}`, prompts);
    LLMRecord.create({ runId, prompts: JSON.stringify(prompts) }).catch((err) => {
      logger.error("Failed to save LLM record:", err);
    });
  },
  handleLLMEnd(output, runId) {
    logger.debug(
      `LLM End: ${runId}\n`,
      output.generations.map((val) => val.map((item) => item.text))
    );
    LLMRecord.findOneAndUpdate({ runId }, { $set: { generations: JSON.stringify(output.generations) } })
      .exec()
      .catch((err) => {
        logger.error("Failed to update LLM record:", err);
      });
  },
};
