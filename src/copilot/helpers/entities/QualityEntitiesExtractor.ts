import { EntityNames, IQuality, qualities } from "../../constants";
import { AbstractEntitiesExtractor } from "./AbstractEntitiesExtractor";

export class QualityEntitiesExtractor extends AbstractEntitiesExtractor {
  constructor(text: string) {
    super(text);
  }
  extract() {
    let modifiedTxt = this.text;
    const qualityEntities = [];
    for (const str of Object.keys(qualities)) {
      const regex = new RegExp(str, "g");
      let match;
      while ((match = regex.exec(modifiedTxt)) !== null) {
        const startIndex = match.index;
        const endIndex = startIndex + str.length;
        modifiedTxt = modifiedTxt.substring(0, startIndex) + "*".repeat(str.length) + modifiedTxt.substring(endIndex);
        qualityEntities.push({
          value: IQuality[qualities[str]],
          name: EntityNames.qualities,
          offset: {
            start: startIndex,
            end: endIndex,
          },
        });
      }
    }
    return qualityEntities;
  }
}
