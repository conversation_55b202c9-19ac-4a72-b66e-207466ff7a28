import { EntityNames } from "@/copilot/constants";
import { AbstractEntitiesExtractor } from "./AbstractEntitiesExtractor";
import { Entity, TyreSpec } from "@/common/utils";
import { nlpClient } from "@/clients/nlp";
import logger from "@/common/logger";

export class PartNameEntitiesExtractor extends AbstractEntitiesExtractor {
  constructor(text: string) {
    super(text);
  }

  private trimInvalidPartNames<T extends { name: string }>(input: string, parts: T[]): T[] {
    const config = {
      车架: () => {
        return /车架(号|号?[\s:：]*[A-Z0-9]{17})/.test(input);
      },
      双离合: () => {
        return /\d\.\dT\s*双离合/.test(input);
      },
    } as Record<string, () => boolean>;
    return parts.filter((part) => {
      const { name } = part;
      const isInvalid = config[name]?.();
      return !isInvalid;
    });
  }

  private extractPartNo(line: string) {
    if (/手机|电话|联系|微信/.test(line)) {
      return "";
    }
    let partNo = "";
    if (/^[A-Z0-9][A-Z0-9 /-]{4,40}$/.test(line)) {
      partNo = line;
    } else {
      partNo = /([号码:]|配件)\s?([A-Z0-9][A-Z0-9 /-]{4,40})/.exec(line)?.[2] || "";
    }
    // 如果只有字母，大概率是单词，不是零件号
    if (/^[A-Z\s]+$/.test(partNo)) {
      partNo = "";
    }
    return partNo;
  }

  async extract(): Promise<Entity[]> {
    const text = this.text;
    // 左，右发动机支架垫 -> 左右发动机支架垫。邮箱 -> 油箱
    let txt = text.toUpperCase().replace(/^([前后左右上下]{1,2})[^\u4e00-\u9fa5a-zA-Z0-9]*/, "$1");
    // 去掉车架号部分内容
    txt = txt.replace(/\b[A-Za-z0-9]{17}\b/, "");
    const partNumbers = txt
      .split("\n")
      // 提取零件号
      .map((line) => this.extractPartNo(line.trim()))
      // 过滤非配件
      .filter(Boolean)
      // 排除VIN码
      .filter((line) => !/^[A-Z0-9]{17}$/.test(line))
      // 排除订单和询价单号
      .filter((line) => !/^[BS]\d+$/.test(line))
      // 排除轮胎规格
      .filter((line) => !TyreSpec.includesTyreSpec(line))
      // 排除手机号
      // .filter((line) => !isCellphone(line.trim()))
      .map((line) => ({
        name: EntityNames.partName,
        value: line,
      }));

    const parsePartNames: { name: string }[] = [];
    try {
      const { data } = await nlpClient.extractPartNameEntities(txt);
      parsePartNames.push(...this.trimInvalidPartNames(text, data));
    } catch (error) {
      logger.error(`解析配件接口失败：${error}`);
    }

    // 词库匹配
    let partsNlp = parsePartNames.map((result) => ({
      name: EntityNames.partName,
      value: TyreSpec.includesTyreSpec(result.name) ? `（轮胎）${result.name}` : result.name,
    }));

    // 匹配轮胎规格
    const tyreSize: Entity[] = TyreSpec.parse(txt).map((spec) => ({
      name: EntityNames.partName,
      value: `（轮胎）${TyreSpec.stringify(spec)}`,
    }));
    if (tyreSize?.length) {
      // 已有轮胎规格，不再匹配“轮胎”配件
      partsNlp = partsNlp.filter((part) => !/(轮|AT|前|后|山地|防爆|静音|雪地)胎$/i.test(part.value));
    }

    const partNames = [...partsNlp, ...partNumbers, ...tyreSize];
    return this.filterValidEntities(partNames);
  }

  /**
   * 过滤掉分错词的实体
   * @param entities
   * @returns
   */
  private filterValidEntities(entities: Entity[]) {
    const text = this.text.toUpperCase();
    return entities.filter((entity) => {
      const index = text.indexOf(entity.value.toUpperCase());
      if (index < 0) {
        return true;
      }
      const startWithEnOrNumChar = /^[A-Z0-9]/.test(entity.value);
      const endWithEnOrNumChar = /[A-Z0-9]$/.test(entity.value);
      const EN_NUM_REG = /[A-Z0-9]/;
      const prevChar = text[index - 1];
      const nextChar = text[index + entity.value.length];
      if (startWithEnOrNumChar && EN_NUM_REG.test(prevChar)) {
        return false;
      }
      if (endWithEnOrNumChar && EN_NUM_REG.test(nextChar)) {
        return false;
      }
      return true;
    });
  }
}
