import { TyreSpec } from "@/common/utils";
import { AbstractEntitiesExtractor } from "./AbstractEntitiesExtractor";
import { Entity } from "@casstime/copilot-core";
import { extractTyreBrands } from "@/common/utils/tyre";

export type TyreEntityName = "tyreBrand" | "tyrePattern" | "tyreIdentification" | "tyreSpec";

/**
 * TyreEntitiesExtractor 类用于从输入文本中提取轮胎相关的实体，
 * 包括轮胎品牌、轮胎花纹、轮胎标识、轮胎规格。
 */
export class TyreEntitiesExtractor extends AbstractEntitiesExtractor {
  /**
   * 构造函数，用于初始化输入文本。
   * @param text 要解析的输入文本。
   */
  constructor(text: string) {
    super(text);
  }
  /**
   * 提取轮胎规格实体。
   * @returns 提取的轮胎规格实体数组。
   */
  private extractTyreSpecEntities(): Entity[] {
    return TyreSpec.parse(this.text).map((spec) => {
      return {
        name: "tyreSpec",
        value: TyreSpec.stringify(spec),
      };
    });
  }

  private extractTyreBrandEntities(): Entity[] {
    const brands = extractTyreBrands(this.text);
    return brands.map((brand) => {
      return {
        name: "tyreBrand",
        value: brand.name,
      };
    });
  }

  private extractTyreIdentificationEntities(): Entity[] {
    const matchers = [
      [/[^非不]防爆/, "防爆"],
      [/[非不]防爆/, "非防爆"],
      [/(静音棉?)/, "$1"],
      [/(雪地胎?)/, "雪地胎"],
      [/(冬季胎?)/, "冬季胎"],
      [/(四季胎?)/, "四季胎"],
      [/(越野胎?)/, "越野胎"],
      [/(全地形胎?)/, "全地形胎"],
      [/(公路胎?)/, "公路胎"],
      [/(AT胎)/, "$1"],
      [/\b(DSST|HRS|R-F|RFT|ROF|SSR|XRP|ZP|ZRF|ZRT|RSC)\b/, "$1"],
    ];
    const entities: Entity[] = [];
    for (const [regex, name] of matchers) {
      const matches = this.text.match(regex);
      if (matches) {
        entities.push({
          name: "tyreIdentification",
          value: name,
        });
      }
    }
    return entities;
  }

  /**
   * 解析输入文本并提取轮胎相关的实体。
   * @returns 提取的轮胎实体数组。
   */
  extract(): Entity[] {
    const entities: Entity[] = [];
    entities.push(...this.extractTyreBrandEntities());
    entities.push(...this.extractTyreSpecEntities());
    entities.push(...this.extractTyreIdentificationEntities());
    return entities;
  }
}
