import { Entity, validateVINCheckBit } from "@/common/utils";
import { AbstractEntitiesExtractor } from "./AbstractEntitiesExtractor";
import { EntityNames } from "@/copilot/constants";
import _ from "lodash";

export class VinCodeEntitiesExtractor extends AbstractEntitiesExtractor {
  constructor(text: string) {
    super(text);
  }
  extract(): Entity[] {
    const text = this.text;
    // 有些用户输入车架号时，中间会带空格
    const textWithoutSpace = text.replace(/\s/g, "");
    if (textWithoutSpace.length === 17 && validateVINCheckBit(textWithoutSpace)) {
      return [
        {
          value: textWithoutSpace,
          name: EntityNames.vinCode,
          offset: {
            start: 0,
            end: text.length,
          },
        },
      ];
    }
    const vinReg = /\b[A-Z0-9]{17}\b/gi;
    const arr = [...text.matchAll(vinReg)];
    const vins = arr
      .filter((result) => {
        const vinLike = result["0"];
        // 去掉纯字母
        const allAlphabet = /^[A-Za-z]+$/.test(vinLike);
        const allNumber = /^[0-9]+$/.test(vinLike);
        if (allAlphabet || allNumber) {
          return false;
        }
        return true;
      })
      .map((result) => ({
        // VIN中不存在 O|Q｜I
        value: result["0"]?.toUpperCase()?.replace(/O|Q/g, "0")?.replace(/I/g, "1"),
        name: EntityNames.vinCode,
        offset: {
          start: result.index!,
          end: result.index! + result[0].length,
        },
      }))
      .filter((item) => {
        const before = item.offset.start - 1;
        const after = item.offset.end;
        const beforeChar = text[before] || "";
        const afterChar = text[after] || "";
        const notValidReg = /[A-Za-z0-9]/;
        // 如果前后有字母或数字，那么它不是VIN码
        const isValid = !notValidReg.test(beforeChar) && !notValidReg.test(afterChar);
        return isValid;
      });

    // 短车架号识别
    const shortVinReg = /\b[A-Za-z]{1,3}\d{1,4}(-\d{5,7})(?![A-Za-z0-9])\b/g;
    const shortVins = [...text.matchAll(shortVinReg)].map((result) => ({
      value: result["0"].toUpperCase().replace(/O|Q/g, "0")?.replace(/I/g, "1"),
      name: EntityNames.vinCode,
      offset: {
        start: result.index!,
        end: result.index! + result[0].length,
      },
    }));

    // 16或18位车架号识别
    let suspectedVins: Entity[] = [];
    if (vins.length === 0 && shortVins.length === 0) {
      const suspectedVinReg = /\b[A-HJ-NPR-Z0-9]{16,18}\b/gi;
      suspectedVins = [...text.matchAll(suspectedVinReg)]
        .filter((result) => {
          const vinLike = result["0"];
          // 去掉纯字母或纯数字
          const allAlphabet = /^[A-Za-z]+$/.test(vinLike);
          const allNumber = /^[0-9]+$/.test(vinLike);
          if (allAlphabet || allNumber) {
            return false;
          }
          return true;
        })
        .map((result) => ({
          value: result["0"].toUpperCase(),
          name: EntityNames.vinCode,
          offset: {
            start: result.index!,
            end: result.index! + result[0].length,
          },
        }));
    }
    return _.concat(vins, shortVins, suspectedVins);
  }
}
