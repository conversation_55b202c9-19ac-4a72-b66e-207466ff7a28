import { Entity } from "@casstime/copilot-core";

/**
 * EntityExtractor 类用于从输入文本中提取实体。
 * 子类需要实现具体的 extract 方法来完成实体提取。
 */
export abstract class AbstractEntitiesExtractor {
  protected text: string;
  /**
   * 构造函数，用于初始化输入文本。
   * @param text 要解析的输入文本。
   */
  constructor(text: string) {
    this.text = text;
  }

  /**
   * 解析输入文本并提取实体。
   * @returns 提取的实体数组。
   */
  abstract extract(): Entity[] | Promise<Entity[]>;
}
