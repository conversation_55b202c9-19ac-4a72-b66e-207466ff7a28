import { TyreEntitiesExtractor } from "../TyreEntitiesExtractor";

describe("TyreEntitiesExtractor", () => {
  const data = [
    {
      text: "米其林 205/55R17",
      output: [
        { name: "tyreBrand", value: "米其林" },
        { name: "tyreSpec", value: "205/55R17" },
      ],
    },
    {
      text: "马牌和PIREllI防爆",
      output: [
        { name: "tyreBrand", value: "马牌" },
        { name: "tyreBrand", value: "倍耐力" },
        { name: "tyreIdentification", value: "防爆" },
      ],
    },
  ];
  test.each(data)("should extract tyre entities", ({ text, output }) => {
    const extractor = new TyreEntitiesExtractor(text);
    const result = extractor.extract();
    expect(result).toEqual(output);
  });
});
