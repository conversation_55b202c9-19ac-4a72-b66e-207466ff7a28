import { EntityNames } from "../../../constants";
import { extractPartNameEntities, extractVinCodeEntities } from "..";

describe("parseVinCodeEntities", () => {
  test("should return an empty array if the input text does not contain any VIN codes", () => {
    const text = "Hello, world!";
    const result = extractVinCodeEntities(text);
    expect(result).toEqual([]);
  });

  test("should return an array of entities if the input text contains VIN codes", () => {
    const text = "The VIN code is 1HGCM8263YA123456 and the other VIN code is 2HGCM8263YA123456";
    const result = extractVinCodeEntities(text);
    expect(result).toHaveLength(2);
    expect(result[0].value).toBe("1HGCM8263YA123456");
    expect(result[1].value).toBe("2HGCM8263YA123456");
  });

  test("should return an array of entities with correct offsets", () => {
    const text = "The VIN code is 1HGCM8263YA123456 and the other VIN code is 2HGCM8263YA123456";
    const result = extractVinCodeEntities(text);
    expect(result).toHaveLength(2);
    expect(result[0].offset?.start).toBe(16);
    expect(result[0].offset?.end).toBe(33);
    expect(result[1].offset?.start).toBe(60);
    expect(result[1].offset?.end).toBe(77);
  });

  test("should return an array of entities with correct names", () => {
    const text = "The VIN code is 1HGCM8263YA123456 and the other VIN code is 2HGCM8263YA123456";
    const result = extractVinCodeEntities(text);
    expect(result).toHaveLength(2);
    expect(result[0].name).toBe(EntityNames.vinCode);
    expect(result[1].name).toBe(EntityNames.vinCode);
  });

  test("should return no vinCode", () => {
    const text = "ContiMaxContactTM MC6";
    const result = extractVinCodeEntities(text);
    expect(result).toHaveLength(0);
  });

  test("车架号内容不识别配件", () => {
    const text = "宝马WAUACC8P0BA073474";
    const result = extractVinCodeEntities(text);
    expect(result).toHaveLength(1);
    expect(result[0].name).toBe(EntityNames.vinCode);
  });
  test("纯数字不能当成VIN码", () => {
    const text = "12345678901234567";
    const result = extractVinCodeEntities(text);
    expect(result).toHaveLength(0);
  });
  test("短车架号", () => {
    const text = "RK5-1031172火花塞";
    const result = extractVinCodeEntities(text);
    expect(result).toHaveLength(1);
    expect(result[0].value).toBe("RK5-1031172");
  });
  test("非法短车架号", () => {
    const text = "RK5-1031172888";
    const result = extractVinCodeEntities(text);
    expect(result).toHaveLength(0);
  });
  test("超长字符串不能当成VIN码", () => {
    const text = "64a85730b04bfeb3b1032f";
    const result = extractVinCodeEntities(text);
    expect(result).toHaveLength(0);
  });

  const data = [
    {
      input: "ZZANH20-8106076",
      output: [],
    },
    {
      input: ".ANH20-8106076~",
      output: ["ANH20-8106076"],
    },
    {
      input: "WBAWX3109 DL907353",
      output: ["WBAWX3109DL907353"],
    },
  ];
  for (const { input, output } of data) {
    test(`parsePartNameEntities: ${input}`, async () => {
      const result = await extractVinCodeEntities(input);
      console.log(input, result);
      expect(result).toHaveLength(output.length);
      expect(result.map((item) => item.value)).toEqual(output);
    });
  }
});

describe("parsePartNameEntities", () => {
  test("解析单行编码", async () => {
    const text = "0280158040";
    const result = await extractPartNameEntities(text);
    expect(result).toHaveLength(1);
    expect(result[0].value).toBe("0280158040");
  });
  test("多行", async () => {
    const text = "0280158040\n13800138000";
    const result = await extractPartNameEntities(text);
    expect(result).toHaveLength(2);
    expect(result[0].value).toBe("0280158040");
  });
  test("排除日期", async () => {
    const text = "2025-01-11 14:55:31";
    const result = await extractPartNameEntities(text);
    expect(result).toHaveLength(0);
  });
  test("标名纠错", async () => {
    const result = await extractPartNameEntities("邮箱");
    expect(result).toHaveLength(1);
    expect(result[0].value).toBe("油箱");
  });

  const data = [
    { input: "车架", output: ["车架"] },
    { input: "车架号", output: [] },
    { input: "车架:WAUACC8P0BA073474", output: [] },
    { input: "奔驰车配件号0008207703", output: ["0008207703"] },
    { input: "配件13800138000", output: ["13800138000"] },
    { input: "continental", output: [] },
    { input: "捷途 大圣 2022 1.6T 双离合(HJRPBGGB5NB493478)", output: [] },
    { input: "ABS", output: ["ABS"] },
    { input: "ABSE 机油格", output: ["机油格"] },
  ];
  for (const { input, output } of data) {
    test(`parsePartNameEntities: ${input}`, async () => {
      const result = await extractPartNameEntities(input);
      console.log(input, result);
      expect(result).toHaveLength(output.length);
      expect(result.map((item) => item.value)).toEqual(output);
    });
  }
});
