import { Entity } from "@/common/utils";
import { PartNameEntitiesExtractor } from "./PartNameEntitiesExtractor";
import { QualityEntitiesExtractor } from "./QualityEntitiesExtractor";
import { TyreEntitiesExtractor } from "./TyreEntitiesExtractor";
import { VinCodeEntitiesExtractor } from "./VinCodeEntitiesExtractor";

export function extractTyreEntities(text: string) {
  return new TyreEntitiesExtractor(text).extract();
}

export function extractVinCodeEntities(text: string): Entity[] {
  return new VinCodeEntitiesExtractor(text).extract();
}

export async function extractPartNameEntities(text: string): Promise<Entity[]> {
  return new PartNameEntitiesExtractor(text).extract();
}

export function extractQualityEntities(txt: string): Entity[] {
  return new QualityEntitiesExtractor(txt).extract();
}
