import { Copilot } from "@casstime/copilot-core";
import { storage } from "./storage";
import {
  autoPartsFAQApp,
  garageAssistantApp,
  knowledgeAgentApp,
  storeHouseApp,
  partExpertFAQApp,
  monkeyApp,
  damageAssessmentExpertApp,
  AIPurchasePlanAssistantApp,
  proxyInquiryApp,
  AIVehicleCheckAssistantApp,
} from "./apps";

const copilot = new Copilot({ storage });

// 智能采购助手
copilot.registerApplication(garageAssistantApp);

// 共享仓助手
copilot.registerApplication(storeHouseApp);

// 汽配信息问答
copilot.registerApplication(autoPartsFAQApp);

// 智能文档助手
copilot.registerApplication(knowledgeAgentApp);

// 配件专家问答
copilot.registerApplication(partExpertFAQApp);

copilot.registerApplication(monkeyApp);

// 定损专家
copilot.registerApplication(damageAssessmentExpertApp);

// AI采购方案助手
copilot.registerApplication(AIPurchasePlanAssistantApp);

// 代客询价
copilot.registerApplication(proxyInquiryApp);
// AI接车助手
copilot.registerApplication(AIVehicleCheckAssistantApp);

export default copilot;
