import { doubaoDeepSeek_chat } from "@/clients/llm";
import { imageClient } from "@/copilot/apps/GarageAssistant/agents/inquiry/clients/ImageClient";
import { getOrCreateReplyMsgId } from "@/copilot/helpers";
import { renderRichtext, ThinkingSteps } from "@/copilot/richtext";
import { ImageService } from "@/copilot/services/ImageService/ImageService";
import { MessageFactory, TaskHandler } from "@casstime/copilot-core";
import { ToolCall } from "@langchain/core/dist/messages/tool";
import { DynamicStructuredTool } from "@langchain/core/tools";
import { concat } from "@langchain/core/utils/stream";

import _ from "lodash";

export type ImageTaskParams = {
  imageUrl: string;
};
import { z } from "zod";

export class ImageTaskHandler extends TaskHandler<ImageTaskParams> {
  private imageService = this.context.getService(ImageService);

  messages: { role: string; content: string; tool_calls?: ToolCall[] }[] = [
    {
      role: "system",
      content: `用户上传了一张图片，请根据识别过程识别图片信息。
识别过程：
    1. 先对图片分类，按不同类别调用相关工具进行识别。
    2. 如果分类是铭牌或VIN码图片，调用VIN码识别。
    3. 如果分类是轮胎，调用轮胎识别。
    4. 如果分类是截图或者工单，调用工单识别。
    5. 如果分类是配件，调用配件识别。
    6. 如果VIN码、轮胎、工单识别失败，调用配件识别兜底。
    7. 识别出结果后输出识别结果。
    8. 处理完成后**直接**调用terminal工具，结束流程。
注意：
    1. **禁止**对用户输出工具名称，不要输出识别过程
    2. 需要调用terminal工具时，禁止输出“流程结束”，“识别完成”等字样
    3. 使用主动语序，不要使用被动语序
    4. 描述这张图片，并输出识别结果
    5. 输出不超过500字`.trim(),
    },
    {
      role: "user",
      content: "![](a.png)",
    },
  ];

  isDone = false;

  private createTools = () => {
    const { imageUrl } = this.task.params;
    // 图片分类
    const imageClassifier = new DynamicStructuredTool({
      name: "classify_image",
      description: "对当前图片进行分类，识别图片类别",
      schema: z.object({}),
      func: async () => {
        const result = await imageClient.getImageClassify(imageUrl);
        return result.label;
      },
    });

    const vinRecognizer = new DynamicStructuredTool({
      name: "recognize_vin",
      description: "对当前图片进行VIN码识别，识别车型信息",
      schema: z.object({}),
      func: async () => {
        const vinCode = await imageClient.ocrRecognizeVin(imageUrl);
        if (vinCode.length) {
          return `从图片中识别出VIN码: ${vinCode}`;
        }
        const result = await imageClient.recognizeImageByProOcr(imageUrl, ["text"]);
        if (!result.length) {
          return "没有从图片中识别出VIN码";
        }
        return `从图片中识别出以下文本：\n${result.map((item) => item.text).join("\n")}`;
      },
    });

    const tyreRecognizer = new DynamicStructuredTool({
      name: "recognize_tyre",
      description: "对当前图片进行轮胎识别，识别轮胎信息",
      schema: z.object({}),
      func: async () => {
        const result = await imageClient.getTyreSize(imageUrl);
        delete (result as any).textPolygons;
        return JSON.stringify(result, null, 2);
      },
    });

    const worksheetRecognizer = new DynamicStructuredTool({
      name: "recognize_worksheet",
      description: "对当前图片进行工单识别，识别工单信息",
      schema: z.object({}),
      func: async () => {
        const result = this.imageService.recognizeWorksheet(imageUrl);
        return JSON.stringify(result, null, 2);
      },
    });

    const partsRecognizer = new DynamicStructuredTool({
      name: "recognize_parts",
      description: "对当前图片进行零件识别，识别零件信息",
      schema: z.object({}),
      func: async () => {
        const result = await imageClient.recognizeImageByProOcr(imageUrl, ["qr_code", "text"]);
        return JSON.stringify(result.map((item) => ({ content: item.text, type: item.type })));
      },
    });

    const done = new DynamicStructuredTool({
      name: "terminal",
      description: "完成图片识别",
      schema: z.object({}),
      func: async () => {
        this.isDone = true;
        return "";
      },
    });

    return [imageClassifier, vinRecognizer, worksheetRecognizer, tyreRecognizer, partsRecognizer, done];
  };

  private tools = this.createTools();

  modelWithTools = doubaoDeepSeek_chat.bindTools(this.tools);

  private renderIndicator() {
    const toolCallMessage = _.findLast(this.messages, (message) => _.get(message, "tool_calls")?.length);
    if (toolCallMessage) {
      const tool = _.get(toolCallMessage, "tool_calls[0]") as unknown as { name: string; id: string };
      if (tool?.name) {
        const doneIds = this.messages
          .map((message) => _.get(message, "tool_call_id"))
          .filter(Boolean) as unknown as string[];
        const description = this.tools.find((t) => t.name === tool.name)?.description;
        const done = doneIds.includes(tool.id);
        return renderRichtext(ThinkingSteps, {
          steps: [{ label: description || "识别图片", done }],
        });
      }
    }
  }

  private reply(message: string = "") {
    const id = getOrCreateReplyMsgId(this.context);
    const parts = this.messages
      .slice(2)
      .filter((item) => !_.get(item, "tool_call_id"))
      .map((item) => {
        return item.content;
      });
    this.context.reply(
      MessageFactory.markdown(parts.join("\n\n") + message, {
        id,
        indicator: { type: "richtext", content: this.renderIndicator() },
      })
    );
  }

  private async runTools() {
    const stream = await this.modelWithTools.stream(this.messages, { tools: this.tools });
    let gathered = undefined;
    let content = "";
    for await (const chunk of stream) {
      if (chunk.content) {
        console.log(JSON.stringify(chunk.content));
        content += chunk.content.toString();
        this.reply(content);
      }
      gathered = gathered !== undefined ? concat(gathered, chunk) : chunk;
    }
    console.log(gathered);
    this.messages.push({ role: "assistant", content, tool_calls: gathered?.tool_calls });
    this.reply();
    if (gathered?.tool_calls) {
      for (const toolCall of gathered.tool_calls) {
        const selectedTool = this.tools.find((tool) => tool.name === toolCall.name);
        if (selectedTool) {
          const toolMessage = await selectedTool.invoke(toolCall);
          console.log(toolMessage);
          this.messages.push(toolMessage);
        }
      }
    }
  }
  async handle(): Promise<void> {
    while (!this.isDone) {
      await this.runTools();
    }
    this.reply();
  }
}
