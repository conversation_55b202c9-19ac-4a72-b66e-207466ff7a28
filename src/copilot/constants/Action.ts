/** ---------------- commandAction start  ---------------- */

import { ActionFactory } from "@/common/factory/ActionFactory";
import { commandIntents, IMTeamId, Intents, NluActionIntents } from "./Intents";
import { AgentName } from "../../common/enums/AgentName";
import { EntityNames } from "./EntityNames";
import { InquiryIntents } from "../apps/GarageAssistant/agents/inquiry/parsers/inquiryIntentClassifier";
import { AI_CHAT_SCREEN, FROM_AI_CHAT } from "./Texts";

export const CameraIcon = "https://fe-static.cassmall.com/assets/images/a18730b7e1a56877b221f3eb1d1d0963.png";

export const SendTyreImageAction = ActionFactory.command("发送轮胎图片", Intents.inquiryPicture, {
  iconUrl: CameraIcon,
  params: {
    field: "tyreImage",
  },
});

export const EditPartNameAction = ActionFactory.command("修改配件", Intents.inquiryUpdateField, {
  params: {
    field: EntityNames.partName,
  },
});

export const ScanVinCodeAction = ActionFactory.command("扫描车架号", Intents.inquiryPicture, {
  iconUrl: CameraIcon,
  params: {
    field: EntityNames.vinCode,
  },
});

export const WorkOrderRecognizeAction = ActionFactory.command("工单识别", Intents.inquiryPicture, {
  iconUrl: CameraIcon,
  params: {
    field: EntityNames.partName,
  },
});

export const MaintainAddressAction = ActionFactory.command("添加收货地址", Intents.inquiryAddress);

export const AppendNeedAction = ActionFactory.command("追加配件", Intents.inquiryPressSubmitForm, { theme: "primary" });

export const OrderListAction = ActionFactory.command("我的订单", commandIntents.commonNavigate, {
  params: {
    navigate: `cassapp://route/native/order/OrderList`,
  },
});

export const ToCassServiceAction = ActionFactory.command("人工客服", Intents.commonNavigate, {
  theme: "primary",
  params: {
    navigate: "im/conversation",
    params: {
      teamId: IMTeamId.CASS_SERVICE,
      fromScreen: AI_CHAT_SCREEN,
      referer: AI_CHAT_SCREEN,
      messageText: FROM_AI_CHAT, // 文本消息
      isDialog: true,
    },
    platform: "native",
  },
});

export const CheckInquiryMoreQuotes = ActionFactory.command("查看更多报价>", Intents.inquiryMoreQuotes);

export const AddPartNameAction = ActionFactory.command("添加配件", commandIntents.inquiryUpdateField, {
  params: { field: EntityNames.partName },
});

export const ToQualificationAction = ActionFactory.command("去认证", Intents.commonNavigate, {
  params: {
    navigate: "cassapp://route/rn/Qualification",
  },
  theme: "primary",
});

/** ---------------- commandAction end  ---------------- */

/** ---------------- nluAction start  ---------------- */

export const NeedInvoiceAction = ActionFactory.nlu("需要发票", {
  intent: InquiryIntents.修改开票信息,
  agentName: AgentName.inquiryAgent,
  slots: {
    invoice: {
      openInvoiceType: "YES",
      isRequireItemInvoice: false,
    },
  },
});

export const NoNeedInvoiceAction = ActionFactory.nlu("不需要发票", {
  intent: InquiryIntents.修改开票信息,
  agentName: AgentName.inquiryAgent,
  slots: {
    invoice: {
      openInvoiceType: "NO",
      isRequireItemInvoice: false,
    },
  },
});

export const createNewInquiryAction = (partNames: string[]) =>
  ActionFactory.nlu("发布新询价单", {
    intent: Intents.newInquiry,
    slots: {
      partNames,
    },
    agentName: AgentName.inquiryAgent,
  });

export const GoToInquiryAction = ActionFactory.nlu("我想询价", {
  intent: Intents.inquiry,
  agentName: AgentName.inquiryAgent,
});

export const BuyPartAction = ActionFactory.nlu("买配件", {
  intent: InquiryIntents.询报价,
  agentName: AgentName.inquiryAgent,
});

export const BuyTyreAction = ActionFactory.nlu("买轮胎", {
  intent: InquiryIntents.买轮胎,
  agentName: AgentName.inquiryAgent,
});

export const GrantCouponAction = ActionFactory.nlu(
  "领福利",
  {
    intent: InquiryIntents.领福利,
    agentName: AgentName.inquiryAgent,
  },
  "primary"
);

export const AppendNeedNluAction = ActionFactory.nlu(
  "追加配件",
  {
    intent: NluActionIntents.APPEND_PARTS,
    agentName: AgentName.inquiryAgent,
  },
  "primary"
);

/** ---------------- nluAction end  ---------------- */
