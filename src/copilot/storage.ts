import { IContextData, IStorage } from "@casstime/copilot-core";
import { ConfigureReply, ContextValue, CopilotMessage, ICopilotMessage, ReplyTime } from "../models";
import _ from "lodash";
import logger from "@/common/logger";
import { FilterQuery, Types } from "mongoose";
import { AppName } from "./constants";

export const storage: IStorage = {
  generateSessionId() {
    return new Types.ObjectId().toString();
  },
  async getItemAsync(id) {
    // TODO 裁剪不需要的字段
    const [contextValue, historyMessages] = await Promise.all([
      ContextValue.findOne({ sessionId: id }, { _id: 0, id: 0 }),
      CopilotMessage.find({ sessionId: id }, { amrBase64Content: 0 }).sort({ createdAt: -1 }).limit(100),
    ]);
    return {
      app: AppName.GarageAssistant,
      ...contextValue?.toJSON(),
      historyMessages: _.orderBy(
        historyMessages.map((item) => item.toJSON()),
        "createdAt",
        "asc"
      ).filter((message) => !["record", "command"].includes(message.type)),
    } as IContextData;
  },
  async setItemAsync(id, data) {
    await ContextValue.updateOne(
      { sessionId: id },
      {
        $set: {
          ...data,
          sessionId: id,
        },
      },
      { upsert: true }
    );
  },
};

// 获取全部有效意图
export const getConfigureIntents = async () => {
  try {
    const result = await ConfigureReply.aggregate([
      { $match: { isValid: true } },
      { $group: { _id: "$intent" } },
      { $project: { intent: "$_id", _id: 0 } },
    ]);
    return (
      result?.map((item) => item.intent)?.filter((item) => /[\u4e00-\u9fa5]/.test(item)) || [] //过滤掉不含中文的意图
    );
  } catch (error) {
    logger.warn(`查询配置的意图失败：${error}`);
    return [];
  }
};

// 更新配置消息次数
export const updateReplyTime = async (userLoginId: string, configureReplyId: Types.ObjectId) => {
  try {
    // 是否有配置记录
    const item = await ReplyTime.findOne({ userLoginId, configureReplyId });
    if (item) {
      // 找到了文档，更新 time 字段的值
      item.time += 1;
      await item.save(); // 保存更新后的文档
    } else {
      // 如果没有匹配的文档，插入新文档
      const { _id: replyTime } = await ReplyTime.create({
        userLoginId,
        configureReplyId,
        time: 1,
      });
      await ConfigureReply.updateOne({ _id: configureReplyId }, { $push: { replyTimes: replyTime } });
    }
  } catch (error) {
    logger.warn(`更新配置回复消息次数失败：${error}`);
  }
};

// 最后一条消息
export const getLastShowMessage = async (userId: string) => {
  try {
    const condition: FilterQuery<ICopilotMessage> = {
      // 默认过滤不展示的消息
      type: {
        $nin: ["command", "echo", "system"] as any,
      },
      $or: [{ fromUser: userId }, { toUser: userId }],
    };
    const result = await CopilotMessage.findOne(condition).sort({ createdAt: -1 }).lean().exec();
    return result;
  } catch (error) {
    logger.warn(`获取最后一条消息失败：${error}`);
    return {};
  }
};
