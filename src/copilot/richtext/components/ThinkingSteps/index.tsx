import { React, View, Image, Text } from "@casstime/copilot-xml";

interface LoadingProgressProps {
  steps: { label: string; done: boolean }[];
}

const THINKING_ICON = "https://cass-upload.oss-cn-shenzhen.aliyuncs.com/copilot/production/thinking.gif";
const COMPLETED_ICON = "https://cass-upload.oss-cn-shenzhen.aliyuncs.com/copilot/production/completed.png";

export function ThinkingSteps({ steps }: LoadingProgressProps) {
  return (
    <View style={{ marginTop: 12, backgroundColor: "#F7F8FA", borderRadius: 8 }}>
      {steps.map((step) => (
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <Image
            style={{ width: 30, height: 30, marginRight: 10, marginLeft: 10 }}
            uri={step.done ? COMPLETED_ICON : THINKING_ICON}
          ></Image>
          <Text style={{ color: "#979899" }}>{step.label}</Text>
        </View>
      ))}
    </View>
  );
}
