import { React, View, Text, StyleSheet } from "@casstime/copilot-xml";

import { IRepairScenariosRes } from "@/clients/parts-mind/interface";
import { AgentName } from "@/copilot/constants";

export interface AccidentTipsProps {
  carBrandName: string;
  brandCode: string;
  vehicleTypeCode: string;
  repairScenarios: IRepairScenariosRes["accidents"];
}

export function AccidentTips({ carBrandName, repairScenarios, brandCode, vehicleTypeCode }: AccidentTipsProps) {
  return (
    <>
      <View style={{ marginTop: 7, height: 1, backgroundColor: "#d8d9db" }}></View>
      <View style={{ marginTop: 12, borderRadius: 8 }}>
        <Text>你也可以通过故障选择配件</Text>
      </View>
      <View style={{ flexDirection: "row", alignItems: "center", flexWrap: "wrap" }}>
        {repairScenarios.slice(0, 4).map((scenario) => {
          return (
            <View
              style={styles.nomalView}
              action={{
                type: "nlu",
                text: scenario.accidentText,
                nlu: {
                  agentName: AgentName.partInfoAgent,
                  entities: [
                    { name: "accidentCode", value: scenario.accidentCode },
                    { name: "brandCode", value: brandCode },
                    { name: "vehicleTypeCode", value: vehicleTypeCode },
                  ],
                },
              }}
            >
              <Text style={styles.nomalText}> {scenario.accidentText}</Text>
            </View>
          );
        })}
      </View>
    </>
  );
}
const styles = StyleSheet.create({
  isSelectedView: {
    color: "#E51E1E",
    paddingTop: 7,
    paddingBottom: 7,
    paddingRight: 16,
    paddingLeft: 16,
    borderColor: "#E51E1E",
    borderRadius: 8,
    borderWidth: 1,
    backgroundColor: "#FFF4F4",
    marginTop: 10,
    marginBottom: 10,
    marginRight: 20,
  },
  nomalView: {
    color: "#2A2B2C",
    paddingTop: 7,
    paddingBottom: 7,
    paddingRight: 16,
    paddingLeft: 16,
    borderColor: "#C8C9CC",
    borderRadius: 8,
    borderWidth: 1,
    backgroundColor: "#ffffff",
    marginTop: 10,
    marginBottom: 10,
    marginRight: 20,
  },
  isSelectedText: {
    color: "#E51E1E",
  },
  nomalText: {
    color: "#2A2B2C",
  },
});
