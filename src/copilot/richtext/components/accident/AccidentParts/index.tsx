import { React, View, Text, StyleSheet } from "@casstime/copilot-xml";
import { SelectParts } from "./selectParts";

export interface AccidentPartsProps {
  partNames: string[];
  messageId: string;
  selectedPartNames: string[];
  content: string;
  source: "similar" | "accident";
}
export function AccidentParts({
  partNames,
  selectedPartNames,
  messageId,
  content,
  source = "accident",
}: AccidentPartsProps) {
  return (
    <>
      <View style={styles.title} showOnlyLast>
        <Text style={styles.text}>{content}可能需要以下配件，您需要哪些呢？</Text>
      </View>
      <SelectParts
        partNames={partNames}
        selectedPartNames={selectedPartNames}
        messageId={messageId}
        content={content}
        source={source}
      />
    </>
  );
}

const styles = StyleSheet.create({
  title: {
    marginTop: 12,
    borderRadius: 8,
  },
  text: {
    fontSize: 32,
    color: "#2A2B2C",
    lineHeight: 48,
  },
});
