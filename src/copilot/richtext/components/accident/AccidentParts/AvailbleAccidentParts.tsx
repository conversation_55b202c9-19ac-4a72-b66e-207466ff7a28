import { React, View, Text, Button } from "@casstime/copilot-xml";
import { commandIntents, EntityNames } from "@/copilot/constants";
import { EmbedContainer } from "../../Content/EmbedContainer";

interface IProps {
  hasPartNames: string[];
}
export function AvailbleAccidentParts(props: IProps) {
  const { hasPartNames } = props;
  return (
    <View>
      <PartsList hasPartNames={hasPartNames} />
      <Text style={{ fontSize: 32, marginBottom: 10 }}>识别到以下内容，请选择您需要的配件</Text>
    </View>
  );
}

export function PartsList({ hasPartNames = [] }: { hasPartNames: string[] }) {
  if (!hasPartNames.length) return <></>;
  return (
    <View
      style={{
        marginTop: 10,
        marginBottom: 10,
        backgroundColor: "#F7F8FA",
        paddingTop: 20,
        paddingBottom: 20,
        paddingLeft: 27,
        paddingRight: 27,
        width: 552,
        borderRadius: 16,
      }}
    >
      <Text style={{ fontSize: 30, fontWeight: "bold" }}>已录入配件：</Text>
      {renderPartNames(hasPartNames)}
    </View>
  );
}

export function renderPartNames(partNames: string[] = []) {
  if (partNames.length < 10) {
    return partNames.map((name: string, index: number) => (
      <Text key={index}>
        {index + 1}. {name}
      </Text>
    ));
  } else {
    // 超过10个配件，显示前5个和后4个，中间用...表示
    const partNamesMap = partNames.map((partName, index) => {
      return { partName, index };
    });
    const prefix = partNamesMap.slice(0, 5);
    const suffix = partNamesMap.slice(-4);
    const params = {
      type: "richtext",
      content: partNames
        .map((partName, index) => `<text style="font-weight: bold;">${index + 1}.${partName}</text>`)
        .join("\n"),
    };
    return (
      <View>
        {prefix.map(({ partName, index }) => (
          <Text style={{ fontWeight: "bold" }} key={index}>
            {index + 1}. {partName}
          </Text>
        ))}
        <Text style={{ fontWeight: "bold" }}>...</Text>
        {suffix.map(({ partName, index }) => (
          <Text style={{ fontWeight: "bold" }} key={index}>
            {index + 1}. {partName}
          </Text>
        ))}
        <View style={{ alignItems: "flex-end" }}>
          <Button
            type="command"
            command={commandIntents.showFullContent}
            params={params}
            textStyle={{ color: "#E51E1E" }}
            style={{ borderColor: "#E51E1E", backgroundColor: "#fff" }}
          >
            查看全部
          </Button>
        </View>
      </View>
    );
  }
}

interface IEditPartListProps {
  partNames: string[];
  showBtn?: boolean;
}
export function PartListWithEditBtn(props: IEditPartListProps) {
  const { partNames, showBtn = true } = props;
  return (
    <View>
      <Text style={{ fontSize: 30 }}>已录入配件：</Text>
      <Text>{partNames.join("、")}</Text>
      <View style={{ display: "flex", flexDirection: "row" /**, justifyContent: "space-between"  */ }}>
        {showBtn && (
          <View showType="onLast">
            <View
              style={{ display: "flex" }}
              action={{
                text: "修改配件",
                type: "command",
                command: commandIntents.inquiryUpdateField,
                params: { field: EntityNames.partName },
              }}
            >
              <Text style={{ color: "#979899" }}>修改配件</Text>
              <View style={{ top: -10, height: 1, backgroundColor: "#979899" }}></View>
            </View>
          </View>
        )}
      </View>
    </View>
  );
}

export function PartListWithEditBtnEmbed(props: IEditPartListProps) {
  if (!props.partNames?.length) {
    return <></>;
  }
  return (
    <EmbedContainer>
      <PartListWithEditBtn {...props}></PartListWithEditBtn>
    </EmbedContainer>
  );
}
