import { React, View, Text } from "@casstime/copilot-xml";
import { SelectParts } from "./selectParts";

export interface AccidentPartsProps {
  partNames: string[];
  messageId: string;
  selectedPartNames: string[];
  content: string;
  //   similar: 事故车图片推荐配件 accident: 维修知识问答推荐配件 related: 配件推荐关联配件
  source: "similar" | "accident" | "relatedParts";
}
export function SelectRelatedParts({
  partNames,
  selectedPartNames,
  messageId,
  content,
  source = "accident",
}: AccidentPartsProps) {
  return (
    <>
      <View>
        <Text>您可能还需要以下配件：</Text>
      </View>
      <SelectParts
        partNames={partNames}
        selectedPartNames={selectedPartNames}
        messageId={messageId}
        content={content}
        source={source}
      />
    </>
  );
}
