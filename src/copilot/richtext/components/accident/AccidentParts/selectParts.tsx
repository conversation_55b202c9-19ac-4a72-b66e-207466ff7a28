import { React, View, Text, Image, StyleSheet, Button } from "@casstime/copilot-xml";
import { UN_CHECK_ICON, CHECK_ICON, NluActionIntents, CommandActionIntents } from "@/copilot/constants";
import { AgentName, commandIntents } from "@/copilot/constants";
import { ActionFactory } from "@/common/factory/ActionFactory";
import { useRequestMeta } from "@/common/asyncStore";
import { InquiryIntents } from "@/copilot/apps/GarageAssistant/agents/inquiry/parsers/inquiryIntentClassifier";
import { VersionEnum } from "@/common/enums";

interface SelectPartsProps {
  partNames: string[];
  messageId: string;
  selectedPartNames: string[];
  content: string;
  source?: "similar" | "accident" | "relatedParts";
}
/** 没有想要的配件 */
function hasNoPartAction() {
  return ActionFactory.nlu("没有想要的配件", {
    intent: NluActionIntents.NO_WANT_PARTS,
    agentName: AgentName.inquiryAgent,
  });
}

function LogInPartNames(partNames: string[]) {
  return ActionFactory.nlu(partNames.join("、"), {
    intent: InquiryIntents.询报价,
    agentName: AgentName.inquiryAgent,
  });
}

export function SelectParts({
  partNames,
  messageId,
  selectedPartNames,
  content = "",
  source = "accident",
}: SelectPartsProps) {
  const isAllSelected = partNames.length > 0 && partNames.every((part) => selectedPartNames.includes(part));

  const { appVersion } = useRequestMeta();
  const noSupportTogglePart2Input = appVersion?.isLessThan(VersionEnum.SIX_9_0);

  return (
    <>
      <View style={styles.container}>
        <View
          style={styles.allflex}
          action={{
            text: "全选",
            type: "command",
            command: CommandActionIntents.ECHO_COMMAND,
            params: {
              agentName: AgentName.partInfoAgent,
              command: commandIntents.ACCIDENT_TOGGLE_PARTS,
              messageId,
              partNames,
              isAllSelected,
              content,
              source,
              selectedPartNames,
            },
          }}
        >
          <Image style={styles.icon} uri={isAllSelected ? CHECK_ICON : UN_CHECK_ICON}></Image>
          <Text style={styles.selectAll}>全选</Text>
        </View>
        <View style={styles.flex}>
          {partNames.map((partName) => {
            const isSelected = selectedPartNames.includes(partName);
            return (
              <View
                style={isSelected ? styles.selectBtn : styles.btn}
                action={{
                  text: partName,
                  type: "command",
                  command: CommandActionIntents.ECHO_COMMAND,
                  params: {
                    agentName: AgentName.partInfoAgent,
                    command: commandIntents.ACCIDENT_TOGGLE_PARTS,
                    messageId,
                    partNames,
                    partName,
                    content,
                    source,
                    selectedPartNames,
                  },
                }}
              >
                <Text style={isSelected ? styles.selectBtnText : styles.btnText}>{partName}</Text>
              </View>
            );
          })}
        </View>
      </View>
      {/* 按钮 */}
      <View style={styles.flex}>
        {selectedPartNames.length === 0 && (
          <Button textStyle={{ color: "#E51E1E" }} style={styles.changePart} action={hasNoPartAction()}>
            没有想要的配件
          </Button>
        )}
        {noSupportTogglePart2Input && selectedPartNames.length !== 0 && (
          <Button textStyle={{ color: "#E51E1E" }} style={styles.changePart} action={LogInPartNames(selectedPartNames)}>
            一键录入
          </Button>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  allflex: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
    marginBottom: 20,
  },
  flex: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
  },
  container: {
    paddingTop: 20,
    paddingBottom: 20,
    paddingLeft: 27,
    width: 552,
    backgroundColor: "#F7F8FA",
    borderRadius: 16,
  },
  icon: {
    width: 30,
    height: 30,
    marginRight: 15,
  },
  selectAll: {
    fontSize: 28,
    color: "#2A2B2C",
  },
  btn: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#C8C9CC",
    backgroundColor: "#fff",
    marginRight: 20,
    marginBottom: 10,
  },
  selectBtn: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#E51E1E",
    backgroundColor: "#FFF4F4",
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 7,
    paddingBottom: 7,
    marginRight: 20,
    marginBottom: 10,
  },
  btnText: {
    marginTop: 7,
    marginBottom: 7,
    marginLeft: 16,
    marginRight: 16,
  },
  selectBtnText: {
    fontSize: 28,
    color: "#E51E1E",
  },
  changePart: {
    color: "#E51E1E",
    borderColor: "#E51E1E",
    backgroundColor: "#fff",
    paddingTop: 8,
    paddingRight: 20,
    paddingBottom: 8,
    paddingLeft: 20,
    marginTop: 20,
    marginRight: 20,
    borderRadius: 44,
  },
  inquiryText: {
    color: "#fff",
    borderColor: "#fff",
    backgroundColor: "#E51E1E",
    paddingTop: 8,
    paddingRight: 20,
    paddingBottom: 8,
    paddingLeft: 20,
    marginTop: 20,
    borderRadius: 44,
  },
});
