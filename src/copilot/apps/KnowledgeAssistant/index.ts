import { Application } from "@casstime/copilot-core";
import { knowledgeAgent } from "./agents/fastgpt";
import { AppName } from "../../constants";

const app = new Application(AppName.KnowledgeAssistant);

const DEFAULT_SCENE = "FALLBACK";

app.setSceneClassifier({
  id: "KnowledgeAssistantClassifier",
  classify: async () => {
    return DEFAULT_SCENE;
  },
});

app.route(DEFAULT_SCENE, knowledgeAgent);

export default app;
