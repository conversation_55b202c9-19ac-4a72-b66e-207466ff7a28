import { IMessage, MessageFactory } from "@casstime/copilot-core";

const getMockCMDMessages = (lastMessage: IMessage) => {
    if (lastMessage.type === 'text') {
        const inputCMD = lastMessage.content || '';
        if (['image', 'video', 'inquiry'].includes(inputCMD)) {
            let msg;
            if (inputCMD === 'image') {
                msg = MessageFactory.image(
                    'https://www.casstime.com/uploads/20210926/67fde109b2c4e71a5ffde16b416366c8.png',
                    { id: 'test-img' });
            } else if (inputCMD === 'video') {
                msg = MessageFactory.video(
                    'http://www.1chejian.cn/m/Images/kaisiyihaochejian20180628.mp4',
                    {
                        id: 'test-video',
                    });
            } else if (inputCMD === 'inquiry') {
                const params = {
                    "inquiryId": "B129019283933",
                    "partNames": [
                        "火花塞",
                        "机油格",
                        "空气格"
                    ],
                    "inquiryTime": "1711988600000",
                    "userName": "peter蒋",
                    "inquiryStatus": "UNQUOTE",
                    "vin": "LSVUL25N9D2016097",
                    "carModelName": "宝马 740Li 2011 3.0T 手自一体"
                };
                msg = MessageFactory.form("inquiryResult", params, {});
            }
            return msg;
        }
    }

    return null;
};

export default getMockCMDMessages;