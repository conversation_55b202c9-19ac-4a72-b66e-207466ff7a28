import { fetchEventSource } from "@/common/infra/fetch-event-source";
import logger from "@/common/logger";
import { doubao_chat } from "@/clients/llm";
import { ChatPromptTemplate } from "@langchain/core/prompts";

interface IFastGptRequest {
  query: string;
  inputs: {};
  platform: "fastgptChat";
  conversationId: string;
  user: string;
}

type MessageEvent = {
  event: "message";
  answer: string;
  /** 单位是s */
  created_at: number;
};

type AgentMessageEvent = {
  event: "flowNodeStatus";
  answer: string;
};

type MessageEndEvent = {
  event: "message_end";
};

export interface IFastGptFlowNode {
  status: string;
  name: string;
}

//  建议获取
export interface IFastGptSuggestReq {
  shareId: string;
  outLinkUid: string;
  messages: IFastGptSuggestMessage[];
}

export interface IFastGptSuggestMessage {
  role: string;
  content: string;
}

export type ResponseEvent = MessageEvent | AgentMessageEvent | MessageEndEvent;

interface FastGptApiOptions {
  baseUrl: string;
  apiKey: string;
}

type Data<T = never> = Promise<{
  result: "success";
  data?: T;
}>;

class FastGptApi {
  private apiKey: string;
  private baseUrl: string;
  constructor(options: FastGptApiOptions) {
    this.apiKey = options.apiKey;
    this.baseUrl = options.baseUrl;
  }

  get headers() {
    return {
      "Content-Type": "application/json",
      Authorization: `Bearer ${this.apiKey}`,
    };
  }
  async sendChatMessage(request: IFastGptRequest, onEvent: (data: ResponseEvent) => void) {
    logger.info("sendChatMessage", request);
    return new Promise((resolve, reject) => {
      fetchEventSource(`${this.baseUrl}/chat`, {
        method: "POST",
        headers: {
          ...this.headers,
        },
        body: JSON.stringify(request),
        openWhenHidden: true,
        onmessage(ev) {
          logger.info("onmessage", ev.data);
          try {
            onEvent(JSON.parse(ev.data));
          } catch (err) {
            logger.error("onmessage error", err, ev);
          }
        },
        onerror(err) {
          console.error("EventSource error:", err);
          reject(err);
        },
        onclose() {
          console.log("EventSource closed");
        },
      })
        .then(resolve)
        .catch(reject);
    });
  }

  /**
   * 获取下一轮建议问题列表
   * @param messageId
   * @returns
   */
  async getSuggested(messages: IFastGptSuggestMessage[]): Promise<string[]> {
    const inputs = messages.map((item) => `${item.role}: ${item.content}`).join("\n");
    const prompt = ChatPromptTemplate.fromMessages([
      [
        "system",
        `你是一个AI智能助手，请结合前面的对话记录，帮我生成 3 个问题，引导我继续提问。 提问仅限对话提及的内容，不要超出对话谈及的关键字。`,
      ],
      ["assistant", '问题的长度应小于20个字符，按 JSON 格式返回: ["问题1", "问题2", "问题3"]'],
      ["user", inputs],
    ]);

    let answer = "";
    try {
      const suggestionChain = prompt.pipe(doubao_chat);
      const msg = await suggestionChain.invoke({
        content: inputs || "",
      });
      answer = msg.content as string;
    } catch (error) {
      answer = "";
    }

    const result = this.parseTextAnswer2Array(answer);
    return result;
  }

  parseTextAnswer2Array(answer: string) {
    // 解析文本
    const start = answer.indexOf("[");
    const end = answer.lastIndexOf("]");

    if (start === -1 || end === -1) {
      return [];
    }
    const jsonStr = answer
      .substring(start, end + 1)
      .replace(/(\\n|\\)/g, "")
      .replace(/ {2}/g, "");

    try {
      const textArr = JSON.parse(jsonStr);
      return textArr;
    } catch (error) {
      return [];
    }
  }
}

export default FastGptApi;
