import { Agent, MessageFactory } from "@casstime/copilot-core";
import FastGpt<PERSON>pi, { IFastGptFlowNode, ResponseEvent } from "../FastGptApi";
import { Types } from "mongoose";
import _ from "lodash";

interface ISuggestionMsg {
  agentName: string;
  msgId: string;
  fromUser: string;
  content: string;
}
export interface IFastGptAgentConfig {
  agentName: string;
  baseUrl: string;
  apiKey: string;
}

function createFastGptAgent(config: IFastGptAgentConfig) {
  const { agentName, baseUrl, apiKey } = config;

  const agent = new Agent(agentName);
  const fastGptApi = new FastGptApi({
    baseUrl,
    apiKey,
  });

  agent.setIntentParser(async (context) => {
    context.setIntent("default");
  });

  agent.handle("default", async (context) => {
    console.log("mock enter default->");
    const lastMessage = context.lastMessage;
    const ids: Record<string, string> = {};
    let msgForActions: ISuggestionMsg | undefined;
    const getCopilotMsgId = (msgId: string) => {
      if (!ids[msgId]) {
        ids[msgId] = new Types.ObjectId().toString();
      }
      return ids[msgId];
    };

    let content = "";
    if (lastMessage.type === "text") {
      let events: ResponseEvent[] = [];
      const query = lastMessage.content;
      const messageId = "";
      const conversationId = context.sessionId || "";
      const doSend = async (ev: ResponseEvent) => {
        console.log("ev_result->", JSON.stringify(ev));
        events.push(ev);

        console.log("解析flowNodeStatu->");
        let nodeNamePrev = "";
        const flowNodeStatus = events
          .filter((e) => e.event === "flowNodeStatus")
          .map((event) => event.answer);
        const flowNodeStatu =
          flowNodeStatus && flowNodeStatus[flowNodeStatus.length - 1];
        try {
          const flowObject = JSON.parse(flowNodeStatu) as IFastGptFlowNode;
          const { name, status } = flowObject;

          if (name) {
            const stautsDesc = status === "running" ? "处理中" : "完成";
            nodeNamePrev = `${stautsDesc}-${name}\n\n`;
          }
        } catch (error) {
          console.log("解析flowNodeStatu失败", error);
        }
        content = events
          .filter((e) => e.event === "message")
          .map((event) => event.answer)
          .join("");
        if (ev.event === "message" || ev.event === "flowNodeStatus") {
          msgForActions = {
            msgId: messageId, //ev.message_id
            fromUser: lastMessage.fromUser || "",
            agentName: lastMessage.nlu?.agentName || "",
            content,
          };
          const msg = MessageFactory.markdown(nodeNamePrev + content, {
            id: getCopilotMsgId(messageId),
          });
          context?.memory?.setExtra("conversationId", conversationId); // 存Id
          context.reply(msg);
        }
        if (ev.event === "message_end") {
          const msg = MessageFactory.markdown(content, {
            id: getCopilotMsgId(messageId),
          });
          context?.memory?.setExtra("conversationId", conversationId); // 存Id
          context.reply(msg);
        }
      };
      await fastGptApi.sendChatMessage(
        {
          inputs: {},
          query: query,
          platform: "fastgptChat",
          conversationId: conversationId,
          user: lastMessage.fromUser!,
        },
        (event) => {
          doSend(event);
        }
      );

      /* 已调通，生成的建议，知识库无法回答，暂不开放
      // 获取建议
      const historyMessages = context.historyMessages.slice(-6).map((messageItem) => {
        const content = _.get(messageItem, 'content', '');
        const role = (messageItem.fromUser === 'system') ? 'assistant' : 'user';
        return {content, role};
      }).reverse();
      const suggests = await fastGptApi.getSuggested(historyMessages);
      const actions = suggests && suggests.map((suggest: string) => createNLUAction(
        suggest, 
        { intent: Intents.commonTxtSend, agentName },
        "secondary"));

      // 返回结果
      const msg = MessageFactory.markdown(content, {
        id: getCopilotMsgId(messageId),
        actions: [actions]
      });
      context?.memory?.setExtra('conversationId', conversationId); // 存Id
      context.reply(msg);
      */
    }
  });

  return agent;
}

export default createFastGptAgent;
