import { Application } from "@casstime/copilot-core";
import autopartsAgent from "./agents/autoparts";
import { AppName } from "@/common/enums";

const app = new Application(AppName.AutopartsFAQ);

const DEFAULT_SCENE = "FALLBACK";

app.setSceneClassifier({
  id: "AutoPartsFAQClassifier",
  classify: async () => {
    return DEFAULT_SCENE;
  },
});

app.route(DEFAULT_SCENE, autopartsAgent);

export default app;
