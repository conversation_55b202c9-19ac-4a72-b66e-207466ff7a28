import { Agent<PERSON><PERSON>, AI_DISCLAIMER, commandIntents } from "@/copilot/constants";
import { Agent, IMessage, MarkdownStreamReplier, MessageFactory } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { Types } from "mongoose";
import FastGptApi, { ResponseEvent } from "@/common/clients/FastGptApi";
import config from "@casstime/config";
import { AutopartsIntents } from "./intent";
import { messageService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/MessageService";
import { createObjectId } from "@/common/utils";
import { executePrompt } from "@/copilot/helpers/llm";

/**
 * Agent
 */
const agent = new Agent(AgentName.autopartsAgent);

// 配置 fastgpt
const fastGptApi = new FastGptApi({
  baseUrl: config.get("FASTGPT_BASE_URL"),
  apiKey: config.get("FASTGPT_AUTOPARTSFAQ_KEY"),
});

/**
 * 第一次进入Agent
 */
agent.onEnter(() => {
  logger.info("进入 autopartsAgent");
});

/**
 * 离开当前Agent
 */
agent.onLeave(() => {
  logger.info("离开 autopartsAgent");
});

/**
 * 设置意图解析器
 */
agent.setIntentParser(async (context) => {
  context.setIntent(AutopartsIntents.问答);
});

agent.handleCommand(commandIntents.inquiryStart, async (context) => {
  // 查询最后一条展示的消息，避免重复发送欢迎语
  const lastShowMessage = (await messageService.getLastShowMessage(context.payload)) as IMessage;
  if (lastShowMessage?.extra?.isGreet) return;

  const messageId = createObjectId();
  try {
    // 设为流式输出
    context.setReplyMode("stream");
    const stream = await executePrompt("配件专家/配件专家欢迎语提示词", {});
    const replier = MarkdownStreamReplier.for(context);
    const msg = await replier.reply(stream, { id: messageId });
    context.reply(msg, {
      extra: { isGreet: true },
      disclaimer: AI_DISCLAIMER,
    });
  } catch (error) {
    logger.warn(`autoparts-agent 生成流式欢迎语消息失败：${error}`);
    const txt = "您好，我是汽修问答小助手～，可以问我汽配故障信息、维修方法等问题喔，我将全力为您解答";
    context.reply(
      MessageFactory.text(txt, {
        extra: { isGreet: true },
        disclaimer: AI_DISCLAIMER,
      })
    );
  }
});

agent.handle(AutopartsIntents.问答, async (context) => {
  const { lastMessage, sessionId } = context;
  if (lastMessage.type === "text" || lastMessage.type === "voice") {
    const messageId = new Types.ObjectId().toString();
    const factory = MessageFactory.with({ id: messageId });
    const tokens: string[] = [];
    context.setReplyMode("stream");
    const doSend = async (ev: ResponseEvent) => {
      console.log("ev_result->", JSON.stringify(ev));
      if (ev.event === "message") {
        tokens.push(ev.answer);
      }
      if (tokens.length > 2) {
        context.reply(factory.markdown(tokens.join("")));
      }
      if (ev.event === "message_end") {
        context.reply(
          factory.markdown(tokens.join(""), {
            disclaimer: AI_DISCLAIMER,
          })
        );
      }
    };
    try {
      await fastGptApi.sendChatMessage(
        {
          inputs: {},
          query: lastMessage.content!,
          platform: "fastgptChat",
          conversationId: sessionId,
          user: lastMessage.fromUser!,
        },
        (event) => {
          doSend(event);
        }
      );
    } catch (error) {
      logger.warn(`fastGptApi - autoparts—faq 出错：${error}`);
    }
    if (tokens.join("") === "") {
      context.reply(factory.text("服务器繁忙，请稍后再试"));
    }
  }
});

export default agent;
