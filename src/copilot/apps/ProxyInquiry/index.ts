import { Application } from "@casstime/copilot-core";
import proxyInquiryAgent from "./agents/proxyInquiryAgent";
import inquiryAgent from "../GarageAssistant/agents/inquiry";
import fallbackAgent from "../GarageAssistant/agents/fallback";
import backgroundAgent from "../GarageAssistant/agents/background";
import { AppName } from "@/common/enums";
import { SceneCodes } from "../GarageAssistant/constants";

const app = new Application(AppName.ProxyInquiry);

const DEFAULT_SCENE = "PROXY_INQUIRY";

app.setSceneClassifier({
  id: "proxyInquiryClassifier",
  classify: async () => {
    return DEFAULT_SCENE;
  },
});

// 设置后台Agent
app.setBackgroundAgent(backgroundAgent);

app.route(DEFAULT_SCENE, proxyInquiryAgent);

app.route(SceneCodes.询报价, inquiryAgent);

app.route(SceneCodes.FALLBACK, fallbackAgent);

export default app;
