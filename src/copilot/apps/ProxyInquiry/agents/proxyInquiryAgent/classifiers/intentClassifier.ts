import { hasVinCode, stringifyHistory, stringifyMessage } from "@/common/utils";
import { executePrompt, extractJsonFromStream } from "@/copilot/helpers";
import { IClassifier } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { CategoriesBuilder, Category } from "@/copilot/helpers/llm/tools";
import { Intents } from "../../../Intents";
import { IntentCodes } from "../../../constants/IntentCodes";
import { AgentName } from "@/copilot/constants";
import { InquiryIntents } from "@/copilot/apps/GarageAssistant/agents/inquiry/parsers/inquiryIntentClassifier";

const categoriesBuilder = CategoriesBuilder.create()
  .addCategory(
    Category.create(Intents.询报价.name)
      .describe(
        "为用户提供询价或报价能力，以及车架号识别、工单解析等，可以处理零配件信息查询、零配件编码输入、车架号输入、修改发票信息等。用户输入仅包含配件信息时，命中该意图"
      )
      .example(`我想询价 -> ${Intents.询报价.name}`)
      .example(`前大灯支架 -> ${Intents.询报价.name}`)
  )
  .addCategory(
    Category.create(Intents.其他.name)
      .describe("提供兜底能力，以上Agents均无法处理时，用其他意图兜底")
      .alias("售后", "人工服务", "投诉建议", "找供应商")
      .example(`如何退货 -> ${Intents.其他.name}`)
      .example(`这个中网不带标的 -> ${Intents.其他.name}`)
  );

export const intentClassifier: IClassifier = {
  id: "INTENT_CLASSIFIER",
  async classify(context, categories) {
    const message = context.lastMessage;
    // 如果用户输入了车架号，则直接选择询报价Agent
    if (message.type === "text" || message.type === "voice") {
      if (hasVinCode(message.content || "")) {
        const inquiryCode = categories.find((code) => code === Intents.询报价.code);
        if (inquiryCode) {
          return inquiryCode;
        }
      }
    }
    const intents = Object.values(Intents);
    const { examples, categories: newCategories } = categoriesBuilder
      .filter((c) => intents.some((intent) => intent.name === c.name))
      .build();

    try {
      const stream = await executePrompt(
        "代客询价/意图分类提示词",
        {
          history: stringifyHistory(context.historyMessages.slice(-5)),
          categories: newCategories,
          fallback_intent: Intents.其他.name,
          message: stringifyMessage(context.lastMessage, false),
          examples,
        },
        { temperature: 0.2 }
      );
      const { outIntent } = await extractJsonFromStream<{ outIntent: string }>(stream);
      return intents.find((intent) => intent.name === outIntent)?.code || IntentCodes.其他;
    } catch (error) {
      logger.warn(error, "意图分类出错，使用其他意图兜底");
      return IntentCodes.其他;
    }
  },
};
