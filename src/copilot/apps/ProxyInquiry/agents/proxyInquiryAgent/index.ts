import { AgentName, commandIntents } from "@/copilot/constants";
import { Agent } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { intentClassifier } from "./classifiers/intentClassifier";
import { IntentCodes } from "../../constants/IntentCodes";
import { InquiryIntents } from "@/copilot/apps/GarageAssistant/agents/inquiry/parsers/inquiryIntentClassifier";
/**
 * Agent
 */
const agent = new Agent(AgentName.proxyInquiryAgent);

/**
 * 第一次进入Agent
 */
agent.onEnter(() => {
  logger.info("进入 proxyInquiryAgent");
});

/**
 * 离开当前Agent
 */
agent.onLeave(() => {
  logger.info("离开 proxyInquiryAgent");
});

/**
 * 设置意图解析器
 */
agent.registerIntentClassifier(intentClassifier, true);

agent.handleCommand(commandIntents.inquiryUpdateField, async (context) => {
  return context.routeTo(AgentName.inquiryAgent);
});

agent.handleCommand(commandIntents.inquiryStart, async (context) => {
  return context.routeTo(AgentName.inquiryAgent);
});
agent.handleCommand(commandIntents.PROXY_MESSAGE_PARSE, async (context) => {
  const data = context.payload?.data as { params: { txts: string[]; images: string[] } };
  const { params } = data || {};
  const { txts = [], images = [] } = params;

  for (const image of images) {
    context.payload.data = {
      ...context.payload.data,
      type: "image",
      imageUrl: image,
      nlu: {},
    };
    await context.routeTo(AgentName.inquiryAgent);
    await context.activateAgent(agent);
    context.payload.data.imageUrl = "";
  }
  // 处理文字 - 只有在有文字内容时才处理
  if (txts.length > 0 && txts.some(txt => txt.trim())) {
    context.payload.data = {
      ...context.payload.data,
      type: "text",
      content: txts.join("\n"),
      nlu: {
        agentName: "",
        intent: "",
      },
    };
    return context.routeTo(AgentName.inquiryAgent, { intent: InquiryIntents.询报价 });
  }
});

agent.handle(IntentCodes.询报价, async (context) => {
  // 检查路由历史，避免循环路由
  const routeHistory = (context.getTempData("__ROUTE_HISTORY__") as string[]) || [];
  const currentRoute = `${AgentName.proxyInquiryAgent}:${IntentCodes.询报价}`;

  // 如果发现循环路由，直接路由到 fallbackAgent
  if (routeHistory.includes(currentRoute)) {
    logger.warn(`检测到循环路由，直接路由到 fallbackAgent: ${routeHistory.join(' -> ')} -> ${currentRoute}`);
    return context.routeTo(AgentName.fallbackAgent);
  }

  // 记录当前路由历史
  const updatedHistory = [...routeHistory, currentRoute];
  context.setTempData("__ROUTE_HISTORY__", updatedHistory);

  return context.routeTo(AgentName.inquiryAgent);
});

agent.handle(IntentCodes.其他, async (context) => {
  return context.routeTo(AgentName.fallbackAgent);
});

export default agent;
