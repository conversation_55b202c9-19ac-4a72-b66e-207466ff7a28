import { Agent, MessageFactory } from "@casstime/copilot-core";
import Dify<PERSON><PERSON>, { ResponseEvent } from "../DifyApi";
import { Types } from "mongoose";
import { AgentName, Intents } from "@/copilot/constants";
import getMockCMDMessages from "../__mock__/mockDifyBack";
import { ActionFactory } from "@/common/factory/ActionFactory";

interface ISuggestionMsg {
  agentName: string;
  msgId: string;
  fromUser: string;
  content: string;
}
export interface IDifyAgentConfig {
  agentName: AgentName;
  baseUrl: string;
  apiKey: string;
}

function createDifyAgent(config: IDifyAgentConfig) {
  const { agentName, baseUrl, apiKey } = config;

  const agent = new Agent(agentName);
  const difyApi = new DifyApi({
    baseUrl,
    apiKey,
  });

  agent.setIntentParser(async (context) => {
    context.setIntent("default");
  });

  agent.handle("default", async (context) => {
    const lastMessage = context.lastMessage;
    const ids: Record<string, string> = {};
    let msgForActions: ISuggestionMsg | undefined;
    const getCopilotMsgId = (msgId: string) => {
      if (!ids[msgId]) {
        ids[msgId] = new Types.ObjectId().toString();
      }
      return ids[msgId];
    };

    const mockRes = getMockCMDMessages(lastMessage);
    if (mockRes) {
      const suggests = await difyApi.getMockSuggested("", lastMessage.fromUser || "");
      const actions =
        suggests &&
        suggests.map((suggest: string) =>
          ActionFactory.nlu(suggest, { intent: Intents.commonTxtSend, agentName }, "secondary")
        );
      mockRes.actions = [actions];
      mockRes.id = new Types.ObjectId().toString();
      context.reply(mockRes);
      return;
    }

    if (lastMessage.type === "text") {
      const events: ResponseEvent[] = [];
      const query = lastMessage.content;
      const doSend = async (ev: ResponseEvent) => {
        events.push(ev);
        const content = events
          .filter((e) => e.event === "agent_message")
          .map((event) => event.answer)
          .join("");
        if (ev.event === "agent_message") {
          msgForActions = {
            msgId: ev.message_id,
            fromUser: lastMessage.fromUser || "",
            agentName: lastMessage.nlu?.agentName || "",
            content,
          };
          const msg = MessageFactory.text(content, {
            id: getCopilotMsgId(ev.message_id),
          });
          context?.memory?.setExtra("conversationId", ev.conversation_id || ""); // 存Id
          context.reply(msg);
        }
      };
      await difyApi.sendChatMessage(
        {
          inputs: {},
          query: query,
          response_mode: "streaming",
          conversation_id: "", // context?.memory?.extra?.conversationId  读Id
          user: lastMessage.fromUser!,
          files: [],
          auto_generate_name: false,
        },
        (event) => {
          doSend(event);
        }
      );

      /**
      if (msgForActions) {
        const {msgId, fromUser, content, agentName } = msgForActions;
        const suggests = await difyApi.getMockSuggested(msgId, fromUser);
        const actions = suggests && suggests.map((suggest: string) => createNLUAction(
          suggest, 
          { intent: Intents.commonTxtSend, agentName },
          "secondary"));
        const msg = MessageFactory.text(content, {
          id: getCopilotMsgId(msgId),
          actions: [actions]
        });
        context.reply(msg);
      }
      */
    }
  });

  return agent;
}

export default createDifyAgent;
