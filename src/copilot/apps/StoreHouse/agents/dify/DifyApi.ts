import querystring from "querystring";
import { fetch } from "undici";
import { fetchEventSource } from "@/common/infra/fetch-event-source";
import logger from "@/common/logger";



type DifyFile =
  | {
      type: "image";
      transfer_method: "remote_url";
      url: string;
    }
  | {
      type: "image";
      transfer_method: "local_file";
      upload_file_id: string;
    };

interface IDifyRequest {
  query: string;
  inputs: {};
  response_mode: "streaming";
  conversation_id: string;
  user: string;
  files: DifyFile[];
  auto_generate_name: boolean;
}

type MessageEvent = {
  event: "message";
  task_id: string;
  message_id: string;
  conversation_id: string;
  answer: string;
  /** 单位是s */
  created_at: number;
};

type AgentMessageEvent = {
  event: "agent_message";
  task_id: string;
  message_id: string;
  conversation_id: string;
  answer: string;
  /** 单位是s */
  created_at: number;
};

type AgentThoughtEvent = {
  event: "agent_thought";
  id: string;
  task_id: string;
  message_id: string;
  position: number;
  thought: string;
  observation: string;
  tool: string;
  tool_input: string;
  created_at: number;
  message_files: string[];
  conversation_id: string;
};

export type MessageFileEvent = {
  event: "message_file";
  id: string;
  type: "image";
  belongs_to: string;
  url: string;
  conversation_id: string;
};

export type MessageEndEvent = {
  event: "message_end";
  task_id: string;
  message_id: string;
  conversation_id: string;
  metadata: {
    usage: unknown;
    retriever_resources: unknown[];
  };
};

export type MessageReplaceEvent = {
  event: "message_replace";
  task_id: string;
  message_id: string;
  conversation_id: string;
  answer: string;
  created_at: number;
};

export type ErrorEvent = {
  event: "error";
  task_id: string;
  message_id: string;
  status: number;
  code: string;
  message: string;
};

export type PingEvent = {
  event: "ping";
};

export type ResponseEvent =
  | MessageEvent
  | AgentMessageEvent
  | AgentThoughtEvent
  | MessageFileEvent
  | MessageEndEvent
  | MessageReplaceEvent
  | ErrorEvent
  | PingEvent;

interface DifyApiOptions {
  baseUrl: string;
  apiKey: string;
}

type Data<T = never> = Promise<{
  result: "success";
  data?: T;
}>;

type MessagesResponse = {
  limit: number;
  has_more: boolean;
  data: Array<{
    id: string;
    conversation_id: string;
    inputs: Record<string, any>;
    query: string;
    answer: string;
    message_files: Array<{
      id: string;
      type: string;
      url: string;
      belongs_to: string;
    }>;
    feedback: null | {
      rating: "like" | "dislike";
    };
    retriever_resources: Array<{
      position: number;
      dataset_id: string;
      dataset_name: string;
      document_id: string;
      document_name: string;
      segment_id: string;
      score: number;
      content: string;
    }>;
    agent_thoughts: Array<{
      id: string;
      message_id: string;
      position: number;
      thought: string;
      observation: string;
      tool: string;
      tool_input: string;
      created_at: number;
      message_files: Array<string>;
    }>;
    created_at: number;
  }>;
};

interface ConversationsResponse {
  data: {
    id: string;
    name: string;
    inputs: { [key: string]: string };
    introduction: string;
    created_at: number;
  }[];
  limit: number;
  has_more: boolean;
}

class DifyApi {
  private apiKey: string;
  private baseUrl: string;
  constructor(options: DifyApiOptions) {
    this.apiKey = options.apiKey;
    this.baseUrl = options.baseUrl;
  }

  get headers() {
    return {
      "Content-Type": "application/json",
      Authorization: `Bearer ${this.apiKey}`,
    };
  }
  async sendChatMessage(request: IDifyRequest, onEvent: (data: ResponseEvent) => void) {
    logger.info("sendChatMessage", request);
    return new Promise((resolve, reject) => {
      fetchEventSource(`${this.baseUrl}/chat-messages`, {
        method: "POST",
        headers: {
          ...this.headers,
        },
        body: JSON.stringify(request),
        openWhenHidden: true,
        onmessage(ev) {
          logger.info("onmessage", ev.data);
          try {
            onEvent(JSON.parse(ev.data));
          } catch (err) {
            logger.error("onmessage error", err, ev);
          }
        },
        onerror(err) {
          console.error("EventSource error:", err);
          reject(err);
        },
        onclose() {
          console.log("EventSource closed");
        },
      })
        .then(resolve)
        .catch(reject);
    });
  }

  async stopTask(taskId: string, userId: string): Data {
    const res = await fetch(`${this.baseUrl}/chat-messages/${taskId}/stop`, {
      method: "POST",
      headers: this.headers,
      body: JSON.stringify({ user: userId }),
    });
    return res.json() as Data;
  }

  /**
   * 消息终端用户反馈、点赞，方便应用开发者优化输出预期。
   * @param messageId
   * @param param1
   * @returns
   */
  async feedback(messageId: string, { rating, user }: { rating: "like" | "dislike" | "null"; user: string }): Data {
    const res = await fetch(`${this.baseUrl}/messages/${messageId}/feedback`, {
      method: "POST",
      headers: this.headers,
      body: JSON.stringify({ rating, user }),
    });
    return res.json() as Promise<Data>;
  }

  async getMockSuggested(messageId: string, userId: string) {
    function delay(ms: number) {
      return new Promise((resolve) => {
        setTimeout(resolve, ms);
      });
    }
    await delay(1000);
    const suggests = [
      "沉闷的日子里，奔跑就有了风",
      "人生如蜡烛，从头亮到尾",
      "感谢这世界，有剥夺，也有馈赠",
      "若再许我少年时，一两黄金一两风",
      "如果所有人都能理解你，那你该是有多平庸",
      "当命运给你选择的那一刻，你只需要选择倒下还是站起来，然后剩下的痛苦就都不重要了",
    ];
    const sug1 = Date.now() % suggests.length;
    const sug2 = (Date.now() + 1) % suggests.length;
    return [suggests[sug1], suggests[sug2]];
  }

  /**
   * 获取下一轮建议问题列表
   * @param messageId
   * @returns
   */
  async getSuggested(messageId: string, userId: string) {
    const res = await fetch(`${this.baseUrl}/messages/${messageId}/suggested?user=${userId}`, {
      method: "GET",
      headers: this.headers,
    });
    return res.json() as Data<string[]>;
  }
  /**
   * 获取会话历史消息
   */
  async getMessages(query: { user: string; first_id: string; limit: number; conversation_id: string }) {
    const res = await fetch(`${this.baseUrl}/messages?${querystring.stringify(query)}`, {
      method: "GET",
      headers: this.headers,
    });
    return res.json() as Promise<MessagesResponse>;
  }

  /**
   * 获取当前用户的会话列表，默认返回最近的 20 条。
   * @param query
   * @returns
   */
  async getConversations(query: {
    user: string; // 用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
    last_id?: string; // 当前页最后一条记录的ID，默认null。
    limit?: number; // 一次请求返回多少条记录。
    pinned?: boolean; // 只返回置顶的会话，true表示只返回置顶，false表示只返回非置顶。
  }) {
    const res = await fetch(`${this.baseUrl}/conversations?${querystring.stringify(query)}`, {
      method: "GET",
      headers: this.headers,
    });
    return res.json() as Promise<ConversationsResponse>;
  }

  // 会话重命名
  async renameConversation(conversationId: string, body: { name: string; auto_generate?: string; user: string }) {
    const url = `${this.baseUrl}/conversations/${conversationId}/name`;

    const response = await fetch(url, {
      method: "POST",
      headers: this.headers,
      body: JSON.stringify(body),
    });

    return (await response.json()) as Promise<{
      id: string; // 会话 ID
      name: string; // 会话名称
      inputs: object[]; // 用户输入参数数组
      introduction: string; // 开场白
      created_at: number; // 创建时间（时间戳）
    }>;
  }

  async deleteConversation(conversationId: string, userId: string) {
    const res = await fetch(`${this.baseUrl}/conversations/${conversationId}`, {
      method: "DELETE",
      headers: this.headers,
      body: JSON.stringify({ user: userId }),
    });
    return res.json() as Data;
  }
}

export default DifyApi;
