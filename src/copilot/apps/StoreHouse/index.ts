import { Application } from "@casstime/copilot-core";
import { AppName } from "../../constants";
import { storeHouseAgent } from "@/copilot/apps/StoreHouse/agents/dify";

const app = new Application(AppName.StoreHouseAssistant);

const DEFAULT_SCENE = "FALLBACK";

app.setSceneClassifier({
  id: "StoreHouseSceneClassifier",
  classify: async () => {
    return DEFAULT_SCENE;
  },
});

app.route(DEFAULT_SCENE, storeHouseAgent);

export default app;
