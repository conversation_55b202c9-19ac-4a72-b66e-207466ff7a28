import { IFormConfig } from "@casstime/copilot-core";
import { SlotRecords } from "@casstime/copilot-core";

export interface PurchaseFormData {
  /** 指定品质 */
  qualities?: string[];
  /** 推荐品质 */
  industryQualities?: string[];
  /** 指定品牌 */
  brands: string[];
  /** 推荐品牌 */
  industryBrands: string[];
  /** 指定商家 */
  stores: string[];
  /** 推荐商家 */
  industryStores: string[];
  /** 指定地区 */
  regions: string[];
  /** 指定仓库 */
  warehouses: string[];
  /** 最大价格 */
  maxPrice: string;
  /** 最小价格 */
  minPrice: string;
  /** 最远距离 */
  maxDistance: string;
}

export const purchaseForm: IFormConfig<PurchaseFormData, { success: boolean }> = {
  doMapping(slots: SlotRecords) {
    return {
      // 品质
      qualities: slots["qualities"],
      industryQualities: slots["industryQualities"],
      // 品牌
      brands: slots["brands"],
      industryBrands: slots["industryBrands"],
      // 商家
      stores: slots["stores"],
      industryStores: slots["industryStores"],
      // 地区
      regions: slots["regions"],
      // 仓库
      warehouses: slots["warehouses"],
      // 价格
      maxPrice: slots["maxPrice"],
      minPrice: slots["minPrice"],
      // 时效
      maxDistance: slots["maxDistance"],
    };
  },
  doSubmit: async function () {
    return {
      success: true,
    };
  },
};
