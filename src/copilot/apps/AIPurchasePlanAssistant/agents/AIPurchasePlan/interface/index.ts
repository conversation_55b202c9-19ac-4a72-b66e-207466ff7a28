export interface EntityValue {
  displayName: string;
  code: string;
}

export interface EntityData {
  qualities: EntityValue[];
  brands: EntityValue[];
  stores: EntityValue[];
}

export interface IFetchMakePurchasePlanPayload {
  demandId: string;
  scenario: IScenarioType;
  operatorId: string;
  callbackUrl: string;
  identifyId: string;
  contentSource: IContentSource; // MACHINE_LEARNING:机器学习,LLM:大模型, 不传值时走后台配置;
}

export enum IScenarioType {
  GARAGE_ASSISTANT_SENTINEL = "GARAGE_ASSISTANT_SENTINEL",
}
export enum IContentSource {
  MACHINE_LEARNING = "MACHINE_LEARNING",
  LLM = "LLM",
}

export interface IStandardItemKnowledgeResponse {
  demandId: string; // 需求ID
  standardItemKnowledgeList: IStandardItemKnowledgeList[];
}

export interface IStandardItemKnowledgeList {
  standardItemId: string; // 标准品ID
  recommendQualityIds?: string[]; // 推荐品质ID列表
  oemBrandIds?: string[]; // OEM品牌ID列表
  originalRecommendStoreIds?: string[]; // 推荐门店ID列表
  oemBrandRecommendStores?: IStandardItemKnowledgeRecommendStores[]; // OEM品牌推荐门店列表
  brandRecommendStoreIds?: string[]; // 品牌推荐门店ID列表
  requiredDurationHour: number; // 所需时长（小时）
  qualityPrices?: {
    qualityId: string; // 品质ID;
    lowPrice: number;
    highPrice: number;
  }[]; // 品质价格列表
  tendencyStoreIds?: string[]; // 用户偏好店铺ID列表
  tendencyQualityIds?: string[]; // 用户偏好品质ID列表
}

export interface IStandardItemKnowledgeRecommendStores {
  partsBrandId: string; // 品牌ID
  storeIds: string[]; // 门店ID列表
}

// 店铺仓库信息
export interface IStoreFacilityInfo {
  productStoreId: string;
  facilityId: string;
  facilityName: string;
  warehouseFromType: string;
  warehouseType: string;
  warehouseTypeExtra: string;
  isDefault: number;
  provinceId: string;
  provinceName: string;
  cityId: string;
  cityName: string;
  countyId: string;
  countyName: string;
  streetId: string;
  streetName: string;
  roadInfo: string;
  houseNumber: string;
  address: string;
  latitude: string;
  longitude: string;
  distance: number;
  createdDate: number;
  receiverName: string;
  contactNumber: string;
  isDefaultRefundAddress: number;
  manuallyModifiable: boolean;
  showInventoryFlag: boolean;
  downloadInventoryFlag: boolean;
}

export interface IStoreInfo {
  storeId: string;
  storeFacilityList: IStoreFacilityInfo[];
}

export interface InquiryBasic {
  /**
   * 是否允许追加
   */
  allowAppend?: string;
  /**
   * 汽车品牌编码
   */
  carBrandCode?: string;
  /**
   * 汽车品牌
   */
  carBrandId?: string;
  /**
   * 车品牌名称
   */
  carBrandName?: string;
  /**
   * 车型ID
   */
  carModelId?: string;
  /**
   * 车型名称
   */
  carModelName?: string;
  /**
   * 底盘号
   */
  chassisId?: string;
  /**
   * 手机号码
   */
  contactNumber?: string;
  /**
   * 主账号ID
   */
  corporateId?: string;
  /**
   * 主账号名称
   */
  corporateName?: string;
  /**
   * 区ID
   */
  countyGeoId?: string;
  /**
   * 区名称
   */
  countyGeoName?: string;
  /**
   * 创建人
   */
  createdBy?: string;
  /**
   * 创建人名称
   */
  createdName?: string;
  /**
   * 创建时间
   */
  createdStamp: number;
  /**
   * 已譯碼需求数量
   */
  decodedNeedCount?: number;
  /**
   * 期望到货时间
   */
  desiredTime?: number;
  /**
   * 启动跨区域首轮分配召回 1:是，0：否
   */
  enableCrossRegionalFirstRecall?: number;
  /**
   * 启动跨区域报价不满足召回 1:是，0：否
   */
  enableCrossRegionalQuoteRecall?: number;
  /**
   * 销售车型名称
   */
  engineType?: string;
  /**
   * epc车型ID
   */
  epcModelCode?: string;
  /**
   * epc车型名称
   */
  epcModelName?: string;
  /**
   * 过期时间
   */
  expiredStamp: number;
  /**
   * 维修厂ID
   */
  garageCompanyId?: string;
  /**
   * 维修厂名称
   */
  garageCompanyName?: string;
  /**
   * 市ID
   */
  geoCityId?: string;
  /**
   * 市名称
   */
  geoCityName?: string;
  /**
   * 省份ID
   */
  geoProvinceId?: string;
  /**
   * 省名称
   */
  geoProvinceName?: string;
  /**
   * 集团用户ID
   */
  groupUserId?: string;
  /**
   * 集团用户名称
   */
  groupUserName?: string;
  /**
   * 询价单号ID-->shoppingListId
   */
  id?: string;
  /**
   * 冷热数据
   */
  inquiryDataType?: string;
  /**
   * 询价方式
   */
  inquiryMethod?: string;
  /**
   * 询价单类型
   */
  inquiryType?: string;
  /**
   * 发票类型
   */
  invoiceType?: string;
  /**
   * 是否中端车事故单
   */
  isAccident?: boolean;
  /**
   * 是否匿名，0:实名,1:匿名
   */
  isAnonymous?: string;
  /**
   * 0为未译码完成，1为译码完成
   */
  isInquiryDecoded?: number;
  /**
   * 0不需要到店价，1需要到店价
   */
  isNeedWholePrice?: number;
  /**
   * 是否为新客询价单
   */
  isNewInquiry?: string;
  /**
   * 0为不需要对项发票，1为需要对项发票
   */
  isRequireItemInvoice?: number;
  /**
   * 0为非简易询价，1为简易询价
   */
  isSimpleInquiry?: number;
  /**
   * 已跳过译码的 SkipDecodeEnum
   */
  isSkipDecode?: number;
  /**
   * 原needCount
   */
  itemsNum?: number;
  /**
   * 最后更新时间
   */
  lastUpdatedStamp?: string;
  /**
   * 收货地址的纬度
   */
  latitude?: number;
  /**
   * 车牌号
   */
  licensePlate?: string;
  /**
   * 车系编码
   */
  locationId?: string;
  /**
   * 车系编码名称
   */
  locationName?: string;
  /**
   * 收货地址的经度
   */
  longitude?: number;
  /**
   * 不需要替换件
   */
  noReplacement?: string;
  /**
   * 开票类型YES:开票；NO:不开
   */
  openInvoiceType?: string;
  /**
   * 生产日期
   */
  productionDate?: string;
  /**
   * 询价需求品质
   */
  qualities?: string[];
  /**
   * 品质
   */
  quality?: string;
  /**
   * 译码开始时间
   */
  resolveBeginDate?: string;
  /**
   * 开始译码时间
   */
  resolveCreatedStamp?: string;
  /**
   * 译码结束时间
   */
  resolveEndDate?: string;
  /**
   * 译码结果ID，原sourceId
   */
  resolveId?: string;
  /**
   * 译码状态
   */
  resolveStatusId?: string;
  /**
   * 译码员id
   */
  resolveUserId?: string;
  /**
   * 译码员名称
   */
  resolveUserName?: string;
  /**
   * 车系英文名称
   */
  seriesEn?: string;
  /**
   * 车系id
   */
  seriesId?: string;
  /**
   * 车系中文名称
   */
  seriesZh?: string;
  /**
   * 简易询价需求类型列表
   */
  simpleItemTypeIds?: string[];
  /**
   * 原customerServiceQq
   */
  source?: string;
  /**
   * 状态ID
   */
  statusId?: string;
  /**
   * 疑似事故单方案
   */
  suspectedCustomizeScheme?: string;
  /**
   * 用户需求（前3个）
   */
  userNeed?: string;
  /**
   * 销售车型ID
   */
  vehicleType?: string;
  /**
   * 预览报价详情页权限
   */
  viewQuoteInfoAuthority?: boolean;
  /**
   * 街道ID
   */
  villageGeoId?: string;
  /**
   * 街道名称
   */
  villageGeoName?: string;
  /**
   * 车辆vin码
   */
  vin?: string;
  /**
   * 是否保险事故车
   */
  insuranceDirect?: boolean;
  /**
   * 保险公司简称名字
   */
  insuranceCompanyShortName?: string;
  /**
   * 保险公司代码
   */
  insuranceCompanyCode?: string;
  /**
   * 询价单属性
   */
  inquiryTags?: IInquiryTags[];
}

interface IInquiryTags {
  inquiryId: string;
  tagType: string;
  tagValue: string;
}

export enum QualityType {
  ORIGINAL_BRAND = "原厂",
  ORIGINAL_CURRENCY = "原厂(非国内4s)",
  ORIGINAL_INLAND_4S = "原厂(国内4s)",
  EXTERNAL_BRAND = "国际品牌",
  INTERNAL_BRAND = "其他品牌",
  BRAND = "品牌",
  ORIGINAL_OTHERS = "原厂再制造",
  SECOND_HAND = "拆车件",
  EQUIVALENT_BRAND = "同质件",
  OTHER_BRAND = "其他",
  UNKNOWN_BRAND = "未知",
}
