import httpClient from "@/common/clients/http.client";
import { IFetchMakePurchasePlanPayload, IStandardItemKnowledgeResponse } from "../interface";

class PurchaseClients {
  public async fetchMakePurchasePlan(json: IFetchMakePurchasePlanPayload) {
    const res = await httpClient.post("/inquiry-programme-service/inquiry-programme/recommend-contents/trigger", {
      json,
    });
    return { statusCode: res.statusCode, result: res.result };
  }

  public async getStandardItemKnowledge(demandId: string): Promise<IStandardItemKnowledgeResponse | undefined | null> {
    const { result } = await httpClient.post('/inquiry-programme-service/inquiry-knowledge/standard-item', { json: demandId });
    return result;
  }
}

export const purchaseClients = new PurchaseClients();
