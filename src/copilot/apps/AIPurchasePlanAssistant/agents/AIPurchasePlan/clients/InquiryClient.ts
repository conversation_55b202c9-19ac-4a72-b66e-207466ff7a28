import httpClient from "@/common/clients/http.client";
import { InquiryBasic } from "../interface";

class InquiryClient {
  // 查询询价单基本信息
  public async getInquiryBasicInfo(inquiryId: string): Promise<InquiryBasic |undefined> {
    try {
      const { result } = await httpClient.get<InquiryBasic>(
        `/inquiry-service/inquirys/${inquiryId}`
      );
      return result;
    } catch {
      return;
    }
  }
 
}

export const inquiryClient = new InquiryClient();
