import httpClient from "@/common/clients/http.client";
import { IStoreInfo } from "../interface";

class MerchantClient {
  // 查询店铺仓库信息
  public async searchStoresInfo(body: {
    storeIds: string[];
    geoId: string; // 四级或三级区域id
  }): Promise<IStoreInfo[]> {
    try {
      const { result } = await httpClient.post<IStoreInfo[]>("/merchant-service/satisfy/sales/area/store/facilities", {
        json: body,
      });
      return result || [];
    } catch {
      return [];
    }
  }
}

export const merchantClient = new MerchantClient();
