import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, I<PERSON>aker, IPlanRecommendDTO } from "./interfaces";
import { QuoteMatchGenerator } from "./concrete/generator/QuoteMatchGenerator";
import { PlanWeightGenerator } from "./concrete/generator/PlanWeightGenerator";
import { EnumPlanMaker } from "./concrete/maker/EnumPlanMaker";
import { LLMQuoteFilter } from "./concrete/LLMQuoteFilter";
import { Evaluator } from "./concrete/Evaluator";
import { GAPlanMaker } from "./concrete/maker/GAPlanMaker";
import { FilterRulesGenerator } from "./concrete/generator/FilterRulesGenerator";
import { IStandardItemKnowledgeResponse } from "../interface";
import { ItemOf } from "@/common/utils";
import _ from "lodash";
import { AbstractEnv } from "./abstract/AbstractEnv";
import { MarkerType } from "./interfaces/plan";
import { PlanReasonGenerator } from "./concrete/generator/PlanReasonGenerator";
import { Message<PERSON>eporter } from "@/messages";
import logger from "@/common/logger";
import { PostProcessor } from "./concrete/PostProcessor";
import { IPlanQuoteItem } from "@/clients/quote/interface";

interface PlanRecommenderProps {
  makerType: MarkerType;
  reporter: MessageReporter;
  inquiryId: string;
  dataProvider: IDataProvider;
}

export class PlanRecommender extends AbstractEnv {
  filter: IFilter;
  maker: IMaker;
  evaluator: IEvaluator;
  quoteMatcher: QuoteMatchGenerator;
  planWeightGenerator: PlanWeightGenerator;
  filterRulesGenerator: FilterRulesGenerator;
  planReasonGenerator: PlanReasonGenerator;
  postProcessor: PostProcessor;

  constructor({ makerType, dataProvider, reporter, inquiryId }: PlanRecommenderProps) {
    super({ reporter, dataProvider, inquiryId });
    this.maker = makerType === MarkerType.GAMAKER ? new GAPlanMaker(this) : new EnumPlanMaker(this);
    // 初始化其他依赖项
    this.filter = new LLMQuoteFilter(this);
    this.evaluator = new Evaluator(this);
    this.quoteMatcher = new QuoteMatchGenerator(this);
    this.filterRulesGenerator = new FilterRulesGenerator(this);
    this.planWeightGenerator = new PlanWeightGenerator(this);
    this.planReasonGenerator = new PlanReasonGenerator(this);
    this.postProcessor = new PostProcessor(this);
  }

  static async create({
    reporter,
    dataProvider,
    inquiryId,
    makerType = MarkerType.ENUMMAKER,
  }: {
    dataProvider: IDataProvider;
    reporter: MessageReporter;
    inquiryId: string;
    makerType?: MarkerType;
  }) {
    // 获取报价
    const quoteDetail = await dataProvider.getItem(DataProviderKey.quoteDetail);

    const quotes = quoteDetail?.quotes || [];
    const decodeResultQuotes = _.groupBy(quotes, (quote) => quote.decodeResultId) as Record<string, IPlanQuoteItem[]>;
    const totalCount = Object.values(decodeResultQuotes).reduce((acc, arr) => acc * arr.length, 1);
    let type = makerType;
    // 枚举方案，如果报价数量超过 10000，使用 GAMaker
    const LIMIT = 10000;
    if (totalCount > LIMIT && makerType === MarkerType.ENUMMAKER) {
      type = MarkerType.GAMAKER;
    }
    // 创建 PlanRecommender 实例
    return new PlanRecommender({
      makerType: type,
      reporter,
      dataProvider,
      inquiryId,
    });
  }

  /**
   * 根据知识库补全报价信息
   * @param quotes
   * @param knowledge
   * @returns
   */
  private fullfilQuotesByKnowledge(quotes: IPlanQuoteItem[], knowledge: IStandardItemKnowledgeResponse) {
    const knowledgeMap = new Map<string, ItemOf<typeof knowledge.standardItemKnowledgeList>>();
    knowledge?.standardItemKnowledgeList?.forEach((item) => {
      knowledgeMap.set(item.standardItemId, item);
    });
    return quotes.map((quote) => {
      return {
        ...quote,
        isOEMBrand: knowledgeMap.get(quote.decodeResultId)?.oemBrandIds?.includes(quote.brandId!) || false,
      };
    });
  }

  async recommend({ input, expertAdvice, orderviewExp, industryExp }: {
    input: string; expertAdvice?: string, orderviewExp: string, industryExp: string;
  }): Promise<IPlanRecommendDTO> {
    const recommendStep = this.reporter.step("recommend", {});
    recommendStep.start("开始生成采购方案", { data: { inquiryId: this.inquiryId, input } });
    // 方案权重不依赖其他数据，提前生成
    const planWeightPromise = this.planWeightGenerator.generate(input, expertAdvice, orderviewExp, industryExp);
    // 获取报价
    const quoteDetail = await this.dataProvider.getItem(DataProviderKey.quoteDetail);
    let quotes = quoteDetail?.quotes || [];
    const industryKnowledge = await this.dataProvider.getItem(DataProviderKey.industryKnowledge);
    recommendStep.progress("查询完报价和知识库信息");
    // TODO： 重新定义后续流程需要的 QuoteItem 数据结构
    quotes = this.fullfilQuotesByKnowledge(quotes, industryKnowledge);
    // 出现异常则不过滤
    let filterQuotes = quotes;
    try {
      const filterRules = await this.filterRulesGenerator.generate({ quotes, input, expertAdvice, orderviewExp, industryExp });
      filterQuotes = await this.filter.filter({ quotes, rules: filterRules });
    } catch (error) {
      logger.error(error);
      filterQuotes = quotes;
    }

    const quoteMatches = await this.quoteMatcher.generate({
      quotes: filterQuotes,
      input,
      expertAdvice,
      orderviewExp,
      industryExp,
    });

    const planWeight = await planWeightPromise;
    // 制作方案
    recommendStep.progress(`找到${filterQuotes.length}个报价，正在制作采购方案`, {
      total: filterQuotes.length,
      current: filterQuotes.length,
      action: "append",
    });
    let recommendPurchasePlans = await this.maker.make({ quotes: filterQuotes, quoteMatches, planWeight });
    // 方案后处理
    recommendPurchasePlans = await this.postProcessor.process({
      plans: recommendPurchasePlans,
      inquiryId: this.inquiryId,
    });
    await this.evaluator.evaluate({ plans: recommendPurchasePlans });
    recommendStep.end("制作方案结束", { data: {} });
    return { recommendPurchasePlans, quoteMatches, planWeight };
  }
}
