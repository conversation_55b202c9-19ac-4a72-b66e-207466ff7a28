import { IIntelligentProgrammeItem } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { IEvaluator } from "../interfaces";
import { AbstractEnv } from "./AbstractEnv";

export abstract class AbstractEvaluator extends AbstractEnv implements IEvaluator {
  async evaluate({ plans }: { plans: IIntelligentProgrammeItem[] }): Promise<boolean> {
    throw new Error("Not implemented yet!");
  }
}
