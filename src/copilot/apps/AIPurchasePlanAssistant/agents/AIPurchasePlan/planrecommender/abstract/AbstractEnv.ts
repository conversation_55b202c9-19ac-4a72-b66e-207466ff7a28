import { IDataProvider } from "../interfaces";
import { MessageReporter } from "@/messages";

/**
 * 保存环境信息
 */
export abstract class AbstractEnv {
  reporter: MessageReporter;
  dataProvider: IDataProvider;
  inquiryId: string;
  constructor({
    reporter,
    dataProvider,
    inquiryId,
  }: {
    reporter: MessageReporter;
    dataProvider: IDataProvider;
    inquiryId: string;
  }) {
    this.inquiryId = inquiryId;
    this.reporter = reporter;
    this.dataProvider = dataProvider;
  }
}
