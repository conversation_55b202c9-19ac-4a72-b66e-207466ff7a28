import { IIntelligentProgrammeItem } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { AbstractEnv } from "./AbstractEnv";
import { IMaker } from "../interfaces";
import { QuoteMatchDegree } from "../concrete/generator/QuoteMatchGenerator";
import { PlanWeight } from "../concrete/generator/PlanWeightGenerator";
import { IPlanQuoteItem } from "@/clients/quote/interface";

export class AbstractMaker extends AbstractEnv implements IMaker {
  protected planWeight: PlanWeight | undefined;
  async make({
    quotes,
    quoteMatches,
  }: {
    quotes: IPlanQuoteItem[];
    quoteMatches: QuoteMatchDegree[];
  }): Promise<IIntelligentProgrammeItem[]> {
    throw new Error("Not implemented yet!");
  }
  getPlanWeight(): PlanWeight | undefined {
    return this.planWeight;
  }
}
