import { cloneDeep } from "lodash";
import { IRecord, IRecorder } from "../interfaces";
import EventEmitter from "node:events";

export class AbstractRecorder implements IRecorder {
  on(event: "record", listener: (record: IRecord) => void): void {
    this.emitter.on(event, listener);
  }

  private emitter = new EventEmitter();

  protected records: IRecord[] = [];

  record<T>(type: string, message: string, data: T) {
    const record = {
      type,
      timestamp: Date.now(),
      message,
      data: cloneDeep(data),
    };
    this.records.push(record);
    this.emitter.emit("record", record);
  }

  async save() {
    throw new Error("Not implemented yet!");
  }
}
