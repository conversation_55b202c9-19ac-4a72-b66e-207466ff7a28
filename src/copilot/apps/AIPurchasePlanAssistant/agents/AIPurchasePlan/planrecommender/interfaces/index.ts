import { IInquiryInfo } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { QuoteMatchDegree } from "../concrete/generator/QuoteMatchGenerator";
import { IPurchasePlanItem } from "./plan";
import { PlanWeight } from "../concrete/generator/PlanWeightGenerator";
import { InquiryBasic, IStandardItemKnowledgeResponse, IStoreInfo } from "../../interface";
import { IQuoteFromInquiryBodyDetail } from "../../interface/inquiry";
import { IGetNeedDecodeListRes } from "@/service/interface";
import { IPlanQuoteItem } from "@/clients/quote/interface";

export interface IMaker {
  make({
    quotes,
    quoteMatches,
    planWeight,
  }: {
    quotes: IPlanQuoteItem[];
    quoteMatches: QuoteMatchDegree[];
    planWeight: PlanWeight;
  }): Promise<IPurchasePlanItem[]>;
  getPlanWeight(): PlanWeight | undefined;
}
export interface FilterRule {
  partName: string;
  quality: {
    operator: string;
    value: string | string[];
  };
  brand: {
    operator: string;
    value: string | string[];
  };
  price: {
    operator: string;
    value: number | [number, number];
  };
  merchant: {
    operator: string;
    value: string | string[];
  };
  location: {
    operator: string;
    value: string | string[];
  };
}
export interface IFilter {
  filter({ quotes, rules }: { quotes: IPlanQuoteItem[]; rules: FilterRule[] }): Promise<IPlanQuoteItem[]>;
}

export interface IEvaluator {
  evaluate({ plans }: { plans: IPurchasePlanItem[] }): Promise<boolean>;
}

export interface IRecord<T = unknown> {
  type: string;
  timestamp: number;
  message: string;
  data: T;
}

export interface IRecorder {
  record<T>(type: string, message: string, data: T): void;
  on(event: "record", listener: (record: IRecord) => void): void;
  save(): Promise<void>;
}

export interface IStoresAddress {
  basicInfo?: InquiryBasic;
  storeFacilitys?: IStoreInfo[];
}

export interface IDataProviderDTO {
  inquiryDetail: IInquiryInfo;
  quoteDetail: IQuoteFromInquiryBodyDetail;
  industryKnowledge: IStandardItemKnowledgeResponse;
  storesAddress: IStoresAddress;
  needDecodeList: IGetNeedDecodeListRes;
}

export enum DataProviderKey {
  inquiryDetail = "inquiryDetail",
  quoteDetail = "quoteDetail",
  industryKnowledge = "industryKnowledge",
  storesAddress = "storesAddress",
  needDecodeList = "needDecodeList",
}

export interface IDataProvider {
  inquiryId: string;
  prefetch(): Promise<boolean>;
  getAll(): Promise<IDataProviderDTO>;
  getItem<T extends keyof IDataProviderDTO>(key: T): Promise<IDataProviderDTO[T]>;
}

export interface IPlanRecommendDTO {
  recommendPurchasePlans: IPurchasePlanItem[];
  quoteMatches: QuoteMatchDegree[];
  planWeight: PlanWeight;
}
