export interface IShowPlanPayload {
  inquiryId: string;
  companyId: string;
  userId: string;
  platform: string;
}

export interface IPurchasePlanItem {
  _id?: string; //方案id
  name: string; //方案名称
  type: string; //方案类型;共享仓方案:YUN_FACILITY,单店集采方案:COLLECT
  source: string; //方案来源;机器学习:MACHINE_LEARNING,人工兜底:BACKUP
  storeId?: string; //店铺id
  storeName?: string; //店铺id
  facilityId?: string; //仓库id,仅单仓方案有值
  facilityName?: string; //仓库id,仅单仓方案有值
  reason: string; //原因
  standardItemResults: {
    standardItemId: string; //标准需求id（译码结果）
    quotationProductIds: string[]; //报价结果id
  }[]; //方案需求结果
  weightedScore?: number; // 方案加权评分
  features?: string[]; // 方案特征
  defaultEta?: string; //时效信息
  deliveryWarehouse?: string; //发货仓库
}

export enum MarkerType {
  GAMAKER = "GAMAKER",
  ENUMMAKER = "ENUMMAKER",
}

// 方案特征
export enum PurchasePlanFeatureEnum {
  // 严选
  STRICT = "STRICT",
  // 共享仓
  YUN_FACILITY = "YUN_FACILITY",
  // 配套品牌
  OEM = "OEM",
  // 国际品牌
  EXTERNAL_BRAND = "EXTERNAL_BRAND",
  // 原厂
  ORIGINAL_BRAND = "ORIGINAL_BRAND",
  // 整单
  WHOLE_DISTRIBUTE = "WHOLE_DISTRIBUTE",
}
export const PurchasePlanFeatureLabel = {
  // 严选
  STRICT: "开思严选",
  // 共享仓
  YUN_FACILITY: "开思共享仓",
  // 配套品牌
  OEM: "配套品牌",
  // 国际品牌
  EXTERNAL_BRAND: "国际品牌",
  // 原厂
  ORIGINAL_BRAND: "原厂",
};
