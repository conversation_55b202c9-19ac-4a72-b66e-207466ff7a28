import { AbstractEvaluator } from "../abstract/AbstractEvaluator";
import {
  IIntelligentProgrammeItem,
  IntelligentPlanType,
} from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { createDataProvider } from "../factory";
import _ from "lodash";

export class PostProcessor extends AbstractEvaluator {
  async process({
    plans,
    inquiryId,
  }: {
    plans: IIntelligentProgrammeItem[];
    inquiryId: string;
  }): Promise<IIntelligentProgrammeItem[]> {
    const dataProvider = createDataProvider(inquiryId);
    const {
      quoteDetail: { quotes },
      storesAddress,
    } = await dataProvider.getAll();

    // 获取云仓列表
    const cloudWarehouses = storesAddress.storeFacilitys
      ?.map((storeFacility) =>
        storeFacility.storeFacilityList
          .filter((item) => item.warehouseType === "YUN")
          .map((item) => ({ facilityId: item.facilityId, facilityName: item.facilityName }))
      )
      .flat();
    const cloudWarehousesUnique = _.uniqBy(cloudWarehouses, "facilityId");

    return plans.map((plan) => {
      const { standardItemResults } = plan;
      let { facilityName, facilityId, storeId, storeName, type } = plan;
      const quotationProductIds = standardItemResults
        .map((item) => item.quotationProductIds)
        .flat()
        .filter(Boolean) as string[];
      const planQuotes = quotes.filter((quote) => quotationProductIds.includes(quote.quotationProductId));

      // 是否整单
      const planStores = planQuotes.map((quote) => ({
        storeId: quote.storeId,
        storeName: quote.storeName,
      }));
      const planStoresUnique = _.uniqBy(planStores, "storeId");
      if (planStoresUnique.length === 1) {
        storeId = planStoresUnique[0].storeId || "";
        storeName = planStoresUnique[0].storeName || "";
        type = IntelligentPlanType.COLLECT;
      }

      // 是否云仓
      const planFacilities = planQuotes.map((quote) => ({
        facilityId: quote.location,
        facilityName: quote.locationName,
      }));
      const planFacilitiesUnique = _.uniqBy(planFacilities, "facilityId");
      if (
        planFacilitiesUnique.length === 1 &&
        cloudWarehousesUnique.some((item) => item.facilityId === planFacilitiesUnique[0].facilityId)
      ) {
        facilityId = planFacilitiesUnique[0].facilityId || "";
        facilityName = planFacilitiesUnique[0].facilityName || "";
        type = IntelligentPlanType.YUN_FACILITY;
      }

      return { ...plan, storeId, storeName, type, facilityId, facilityName };
    });
  }
}
