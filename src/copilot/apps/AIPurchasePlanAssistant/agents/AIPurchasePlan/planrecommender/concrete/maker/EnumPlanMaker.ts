import { AbstractMaker } from "../../abstract/AbstractMaker";
import { PlanWeight } from "../generator/PlanWeightGenerator";
import { QuoteMatchDegree } from "../generator/QuoteMatchGenerator";
import { IPurchasePlanItem } from "../../interfaces/plan";
import { GAPlanMaker } from "./GAPlanMaker";
import enumPlanWorker from "../worker/enumPlan.worker";
import { IPlanQuoteItem } from "@/clients/quote/interface";

export class EnumPlanMaker extends AbstractMaker {
  /**
   * 推荐采购方案
   * @param plans
   * @returns
   */
  recommendCombinations(plans: IPurchasePlanItem[]) {
    return plans.slice(0, 10);
  }

  async make({
    quotes,
    quoteMatches,
    planWeight,
  }: {
    quotes: IPlanQuoteItem[];
    quoteMatches: QuoteMatchDegree[];
    planWeight: PlanWeight;
  }) {
    const enumMakerStep = this.reporter.step("enumPlanMaker", { parentStep: "recommend" });
    enumMakerStep.start(`开始制作枚举方案`);
    this.planWeight = planWeight;
    const { allPlans } = await enumPlanWorker({
      quotes,
      quoteMatches,
      planWeight,
    });

    enumMakerStep.progress(`制作了${allPlans.length}个方案，正在对比方案...`, {
      data: { length: allPlans.length },
      action: "replace",
    });
    const recommendPlans = this.recommendCombinations(allPlans);
    enumMakerStep.end(`制作枚举方案结束`);
    // 如果枚举方案为空，使用ga方案
    if (recommendPlans.length === 0) {
      const gaMaker = new GAPlanMaker(this);
      return gaMaker.make({ quotes, quoteMatches, planWeight });
    }
    return recommendPlans;
  }
}
