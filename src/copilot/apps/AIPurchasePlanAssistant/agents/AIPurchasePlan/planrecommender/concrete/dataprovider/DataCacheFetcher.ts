import { DataCache } from "@/models/DataCache";

/**
 * 两级数据缓存，内存 -> 数据库 -> 接口
 * 缓存数据优先从内存中获取，如果内存中没有则从数据库中获取，如果数据库中没有则从接口中获取。
 * 数据回源时，限制并发
 */
export class DataCacheFetcher {
  private cache = new Map<string, unknown>();

  private async findOrRetrieve<T>(key: string, retrieve: () => Promise<T>, forceRetrieve = false): Promise<T> {
    if (!forceRetrieve) {
      const cacheItem = await DataCache.findOne({ key }).sort({ createdAt: -1 }).exec();
      if (cacheItem) {
        return JSON.parse(cacheItem.value) as T;
      }
    }
    const data = await retrieve();
    await DataCache.findOneAndUpdate(
      { key },
      {
        value: JSON.stringify(data),
      },
      {
        upsert: true,
      }
    );
    return data;
  }

  /**
   * 从缓存中获取数据，如果没有则调用retrieve函数获取数据，并缓存
   * @param key
   * @param retrieve
   * @returns
   */
  getOrFetch<T>(key: string, retrieve: () => Promise<T>, forceRetrieve: boolean = false): Promise<T> {
    if (!this.cache.has(key) || forceRetrieve) {
      this.cache.set(key, this.findOrRetrieve(key, retrieve, forceRetrieve));
    }
    return this.cache.get(key) as Promise<T>;
  }
}
