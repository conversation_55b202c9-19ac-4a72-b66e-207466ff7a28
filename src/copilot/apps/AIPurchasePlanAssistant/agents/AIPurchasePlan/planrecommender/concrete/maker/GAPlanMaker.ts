import { IntelligentPlanType } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { AbstractMaker } from "../../abstract/AbstractMaker";
import { PlanWeight } from "../generator/PlanWeightGenerator";
import { QuoteMatchDegree } from "../generator/QuoteMatchGenerator";
import _ from "lodash";
import { isDev, ItemOf, Population } from "@/common/utils";
import { IPurchasePlanItem } from "../../interfaces/plan";
import gaPlanEvolve from "../worker/gaPlanEvolve.worker";
import { IPlanQuoteItem } from "@/clients/quote/interface";

/**
 * 使用遗传算法推荐方案
 */
export class GAPlanMaker extends AbstractMaker {
  makePlan(
    populationItem: ItemOf<Population>,
    userData: { products: { quotes: IPlanQuoteItem[] }[]; planWeights: PlanWeight }
  ) {
    const entity = populationItem.entity as number[];
    const score = populationItem.fitness;
    const quoteItems = entity.map((productQuoteIndex, index) => {
      return userData.products[index].quotes[productQuoteIndex];
    });
    const standardItemResults = quoteItems.map((item) => {
      return {
        standardItemId: item.standardItemId,
        quotationProductIds: [item.quotationProductId],
      };
    });
    const storeCombination = _.uniqBy(quoteItems, "storeId");
    const storeId = storeCombination.length === 1 ? storeCombination[0].storeId || "" : "";
    const storeName = storeCombination.length === 1 ? storeCombination[0].storeName || "" : "";
    return {
      name: `方案-遗传算法`,
      type: IntelligentPlanType.COMPREHENSIVE,
      source: "BACKUP",
      storeId,
      storeName,
      reason: "",
      standardItemResults,
      weightedScore: score,
    } as IPurchasePlanItem;
  }

  async make({
    quotes,
    quoteMatches,
    planWeight,
  }: {
    quotes: IPlanQuoteItem[];
    quoteMatches: QuoteMatchDegree[];
    planWeight: PlanWeight;
  }) {
    const gaStep = this.reporter.step("GAPlanMaker", { parentStep: "recommend" });
    gaStep.start("开始使用遗传算法制作方案");
    const groups = _.groupBy(quotes, (quote) => quote.standardItemId);
    const entries = Object.entries(groups);
    // 配置算法参数
    const config = {
      size: 200, // 种群大小
      iterations: 800, // 迭代次数
      crossover: 0.9, // 交叉概率
      mutation: 0.2, // 变异概率
      fittestAlwaysSurvives: 20, // 保留最优的20个个体
    };
    const userData = {
      products: entries.map(([partsNum, quotes]) => ({
        id: partsNum,
        name: quotes[0].partsName,
        quotes: quotes,
      })),
      quoteMatches,
      planWeights: planWeight,
    };
    const sendProgress = _.debounce(
      (progress: number) => {
        gaStep.progress(`正在制作方案(${progress}%)...`, {
          save: false,
          action: "replace",
        });
      },
      200,
      { leading: true, trailing: true }
    );
    const { pop } = await gaPlanEvolve({ userData, config }, (result) => {
      const progress = Math.floor((result.progress.current / config.iterations) * 100);
      sendProgress(progress);
    });
    gaStep.progress(`遗传算法制作方案完成，正在对比`, { action: "replace" });
    const uniqPopulation = _.uniqBy(pop, (item) => item.entity.join("-"));
    const TOP_N = isDev() ? 10 : 10;
    const topNPop = _.orderBy(uniqPopulation, ["fitness"], ["desc"]).slice(0, TOP_N);
    const result = topNPop.map((pop) => this.makePlan(pop, userData));
    gaStep.end(`遗传算法制作方案完成`, { data: { length: result.length } });
    return result;
  }
}
