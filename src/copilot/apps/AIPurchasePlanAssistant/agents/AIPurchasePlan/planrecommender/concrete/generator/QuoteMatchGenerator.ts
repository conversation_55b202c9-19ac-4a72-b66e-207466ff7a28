import { AbstractEnv } from "../../abstract/AbstractEnv";
import { doubao_chat, renderPrompt, waitStreamContent } from "@/copilot/helpers/llm";
import logger from "@/common/logger";
import { IStoreFacilityInfo, QualityType } from "../../../interface";
import _ from "lodash";
import { convertMarkdownTableToJSON } from "@/common/utils/markdown";
import pLimit from "p-limit";
import assert from "assert";
import { isError } from "@/common/utils";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
import { IPlanQuoteItem } from "@/clients/quote/interface";

export interface QuoteMatchDegree {
  quotationProductId: string;
  matchedDegree: number;
  reason: string;
}

type FulfilledQuoteItem = IPlanQuoteItem & {
  markdownId: string;
  distance: number;
  durationHour: number;
};

export class QuoteMatchGenerator extends AbstractEnv {
  async getDataMap() {
    const storeNamesMap: Record<string, string> = {};
    const brandNamesMap: Record<string, string> = {};
    const storeFacilityMap: Record<string, Record<string, IStoreFacilityInfo>> = {};
    const { quoteDetail, storesAddress, industryKnowledge, inquiryDetail } = await this.dataProvider.getAll();

    quoteDetail.quotes.forEach((item) => {
      storeNamesMap[item.storeId || ""] = item.storeName || "";
      brandNamesMap[item.brandId || ""] = item.brandName || "";
    });

    // 仓库信息，用于计算距离
    const storeFacilitys = storesAddress.storeFacilitys || [];
    storeFacilitys.map((item) => {
      if (!storeFacilityMap[item.storeId]) {
        storeFacilityMap[item.storeId] = {};
      }
      item.storeFacilityList.forEach((facility) => {
        storeFacilityMap[item.storeId][facility.facilityId] = facility;
      });
    });
    return {
      storeNamesMap,
      brandNamesMap,
      storeFacilityMap,
      basicInfo: storesAddress.basicInfo,
      inquiryDetail,
      quoteKnowledgeList: industryKnowledge?.standardItemKnowledgeList || [],
    };
  }

  async fullfilQuotesForMarkdown(quotations: IPlanQuoteItem[]): Promise<FulfilledQuoteItem[]> {
    const { storeFacilityMap, basicInfo } = await this.getDataMap();

    const cloneQuotations: (IPlanQuoteItem & { markdownId: string; distance: number; durationHour: number })[] = [];
    quotations.forEach((quote, idx) => {
      const index = `${idx + 1}`;
      const storeFacility = storeFacilityMap[quote.storeId || ""]?.[quote.location || ""];
      const distance = storeFacility?.distance || 0;
      const crossCity = basicInfo?.geoCityId !== storeFacility?.cityId;
      cloneQuotations.push({
        ...quote,
        markdownId: index,
        distance,
        durationHour: this.estimateDurationHour({
          distance,
          crossCity,
          arrivalTime: quote.arrivalTime,
        }),
      });
    });

    return cloneQuotations;
  }

  resolveMarkdownId(quotations: FulfilledQuoteItem[], markdownId: string) {
    return quotations.find((quote) => quote.markdownId === markdownId)?.quotationProductId || "";
  }

  async generate({
    input,
    quotes,
    expertAdvice,
    orderviewExp,
    industryExp,
  }: {
    quotes: IPlanQuoteItem[];
    input: string;
    expertAdvice?: string;
    orderviewExp?: string;
    industryExp?: string;
  }): Promise<QuoteMatchDegree[]> {
    const quoteMatchStep = this.reporter.step("genQuoteMatch", { parentStep: "recommend" });
    quoteMatchStep.start("开始生成报价条目匹配度");

    try {
      const groupQuotes = _.groupBy(quotes, (item) => item.standardItemId);

      let dealSkuNum = 1;
      const skuLength = Object.keys(groupQuotes).length;
      quoteMatchStep.progress(`正在分析报价(${dealSkuNum}/${skuLength})...`, {
        action: "append",
        total: skuLength,
        current: dealSkuNum,
      });
      const updateProgress = () => {
        quoteMatchStep.progress(`正在分析报价(${dealSkuNum}/${skuLength})...`, {
          action: "replace",
          total: skuLength,
          current: dealSkuNum,
        });
        dealSkuNum++;
      };
      // 限制并发数为10
      const CONCURRENCY_LIMIT = 10;
      const limit = pLimit(CONCURRENCY_LIMIT);
      const standardItemIds = Object.keys(groupQuotes);
      const promises = standardItemIds.map((standardItemId) => {
        return limit(async () => {
          const list = await this.batchGenerate({
            standardItemId,
            quotationItems: groupQuotes[standardItemId],
            input,
            expertAdvice,
            orderviewExp,
            industryExp,
          });
          updateProgress();
          return list;
        });
      });
      const results = await Promise.all(promises);
      quoteMatchStep.end("生成报价匹配度结束", { data: {} });
      return results.flat();
    } catch (err) {
      logger.warn("生成报价条目匹配度计算输出结果失败", err);
      assert(isError(err), "e is not an Error");
      quoteMatchStep.progress("分析报价异常", { data: { message: err.message } });
    }
    return [];
  }

  private async batchGenerate({
    standardItemId,
    quotationItems,
    input,
    expertAdvice,
    orderviewExp,
    industryExp,
  }: {
    standardItemId: string;
    quotationItems: IPlanQuoteItem[];
    input: string;
    expertAdvice?: string;
    orderviewExp?: string;
    industryExp?: string;
  }) {
    const quotationChunks = _.chunk(quotationItems, 10);
    const chunksDegrees = await Promise.all(
      quotationChunks.map(async (quotationItems) => {
        return this.generateQuotationDegrees({ quotationItems, standardItemId, input, expertAdvice, orderviewExp, industryExp });
      })
    );
    const list = this.normalizeMatchDegree(chunksDegrees.flat());
    return list;
  }

  // 生成报价条目匹配度
  async generateQuotationDegrees({
    standardItemId,
    quotationItems,
    input,
    expertAdvice,
    orderviewExp,
    industryExp,
  }: {
    standardItemId: string;
    quotationItems: IPlanQuoteItem[];
    input: string;
    expertAdvice?: string;
    orderviewExp?: string;
    industryExp?: string;
  }) {
    const step = this.reporter.step("genQuoteDegree", { parentStep: "genQuoteMatch" });
    step.start("llm计算报价条目匹配度开始", { data: { standardItemId } });
    const fulfilledQuotationItems = await this.fullfilQuotesForMarkdown(quotationItems);

    const prompt = await renderPrompt(
      "采购方案/SKU报价条目匹配度提示词",
      await this.createPromptArgs({
        standardItemId,
        quotationItems: fulfilledQuotationItems,
        input,
        expertAdvice,
        orderviewExp,
        industryExp,
      })
    );
    const stream = await doubao_chat.stream(prompt, { callbacks: [llmLogCallback] });
    const content = await waitStreamContent(stream);
    const degreeList = convertMarkdownTableToJSON(content);
    step.end("llm计算报价条目匹配度结束", { data: { standardItemId, prompt, content, degreeList } });
    const quotationDegrees = degreeList.map((item) => {
      return {
        quotationProductId: this.resolveMarkdownId(fulfilledQuotationItems, item.报价id),
        matchedDegree: parseFloat(item.匹配度) || 30,
        reason: item.理由,
      };
    });
    return quotationDegrees;
  }

  private async createPromptArgs({
    standardItemId,
    quotationItems,
    input,
    expertAdvice,
    orderviewExp,
    industryExp,
  }: {
    standardItemId: string;
    quotationItems: (IPlanQuoteItem & { markdownId: string; distance: number; durationHour: number })[];
    input: string;
    expertAdvice?: string;
    orderviewExp?: string;
    industryExp?: string;
  }) {
    const { inquiryDetail, storeNamesMap, brandNamesMap, quoteKnowledgeList } = await this.getDataMap();
    const curKnowledge = quoteKnowledgeList.find((item) => item.standardItemId === standardItemId);
    const recommendQualitys = (curKnowledge?.recommendQualityIds || [])
      .map((id) => QualityType[id as keyof typeof QualityType] || "")
      .filter(Boolean);
    const qualityPrices: string[] = [];
    curKnowledge?.qualityPrices?.forEach((item) => {
      qualityPrices.push(
        `${QualityType[item.qualityId as keyof typeof QualityType]}: ${item.lowPrice}-${item.highPrice}`
      );
    });

    return {
      modelName: inquiryDetail.saleModelName || "-",
      partName: quotationItems[0]?.partsName,
      quotations: quotationItems,
      qualityPrices,
      recommendQualitys,
      requiredDurationHour: curKnowledge?.requiredDurationHour || "-",
      originalRecommendStores: curKnowledge?.originalRecommendStoreIds?.map((id) => storeNamesMap[id] || id) || [],
      oemBrands: curKnowledge?.oemBrandIds?.map((id) => brandNamesMap[id] || id) || [],
      oemBrandRecommendStores:
        curKnowledge?.oemBrandRecommendStores?.map((item) => {
          const brandId = item.partsBrandId;
          const storeNames = item.storeIds.map((id) => storeNamesMap[id] || id).join(",");
          return `${brandNamesMap[brandId] || brandId}: ${storeNames}`;
        }) || [],
      brandRecommendStores: curKnowledge?.brandRecommendStoreIds?.map((id) => storeNamesMap[id] || id) || [],
      tendencyQualitys:
        curKnowledge?.tendencyQualityIds?.map((id) => QualityType[id as keyof typeof QualityType] || id) || [],
      tendencyStores: curKnowledge?.tendencyStoreIds?.map((id) => storeNamesMap[id] || id) || [],
      input,
      expertAdvice,
      orderviewExp,
      industryExp,
    };
  }

  // 计算时效
  estimateDurationHour({ distance, arrivalTime }: { distance: number; crossCity: boolean; arrivalTime?: number }) {
    let hour = 0;
    if (distance <= 15) {
      hour += 1;
    } else if (distance <= 30) {
      hour += 3;
    } else if (distance <= 60) {
      hour += 6;
    } else {
      hour += 8;
    }

    return hour + (arrivalTime || 0) * 8;
  }
  normalizeMatchDegree(quoteDegrees: QuoteMatchDegree[]) {
    if (!quoteDegrees.length) {
      return [];
    }
    // 归一化为0-1
    const maxMatchDegree = _.maxBy(quoteDegrees, "matchedDegree")?.matchedDegree || 100;
    const scaleFactor = 1 / maxMatchDegree;

    return quoteDegrees.map((item) => {
      return {
        ...item,
        matchedDegree: item.matchedDegree * scaleFactor,
      };
    });
  }
}
