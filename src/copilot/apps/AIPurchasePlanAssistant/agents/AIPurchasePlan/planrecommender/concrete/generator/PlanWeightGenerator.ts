import { AbstractEnv } from "../../abstract/AbstractEnv";
import { doubao_chat, renderPrompt, waitStreamContent } from "@/copilot/helpers/llm";
import logger from "@/common/logger";
import { convertMarkdownTableToJSON } from "@/common/utils/markdown";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";

export interface PlanWeight {
  match_weight: number;
  quality_weight: number;
  merchant_weight: number;
  full_weight: number;
}

/**
 * 方案权重生成器
 * @example
 * ```
 * const planWeightGenerator = new PlanWeightGenerator({...});
 * const planWeight = await planWeightGenerator.generate();
 */
export class PlanWeightGenerator extends AbstractEnv {
  async generate(input: string, expertAdvice?: string, orderviewExp?: string): Promise<PlanWeight> {
    const planWeightStep = this.reporter.step("genPlanWeight", { parentStep: "recommend" });
    planWeightStep.start("生成方案权重开始");
    try {
      const prompt = await renderPrompt("采购方案/方案评估权重提示词", { input, expertAdvice, orderviewExp });
      const stream = await doubao_chat.stream(prompt, { callbacks: [llmLogCallback] });
      const content = await waitStreamContent(stream);
      const weightJson = convertMarkdownTableToJSON(content);
      planWeightStep.progress("生成方案权重", { data: { prompt, content, weightJson } });
      if (weightJson?.length) {
        const [match_weight, merchant_weight, quality_weight, full_weight] = weightJson.map((item) => +item["权重"]);

        return {
          match_weight,
          merchant_weight,
          quality_weight,
          full_weight,
        };
      } else {
        return {
          match_weight: 0.3,
          merchant_weight: 0.3,
          quality_weight: 0.2,
          full_weight: 0.2,
        };
      }
    } catch (err) {
      logger.warn("生成权重计算输出结果失败", err);
    }
    planWeightStep.end("生成方案权重结束", { data: {} });
    return {
      match_weight: 0.3,
      merchant_weight: 0.3,
      quality_weight: 0.2,
      full_weight: 0.2,
    };
  }
}
