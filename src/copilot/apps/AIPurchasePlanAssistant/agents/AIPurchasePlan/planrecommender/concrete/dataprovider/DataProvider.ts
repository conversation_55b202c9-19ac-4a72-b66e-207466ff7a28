import { inquiryService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/InquiryService";
import { purchaseService } from "../../../services/PurchaseService";
import { DataProviderKey, IDataProvider, IDataProviderDTO } from "../../interfaces";
import { DataCacheFetcher } from "./DataCacheFetcher";
import { waitForPromises } from "@/common/utils";
import { inquiryService as inquiryCommonService } from "@/service";
import logger from "@/common/logger";

export class DataProvider implements IDataProvider {
  inquiryId: string;

  private dataCacheFetcher = new DataCacheFetcher();

  constructor(inquiryId: string) {
    this.inquiryId = inquiryId;
  }

  private get CACHE_KEYS() {
    return {
      inquiryDetail: `purchase_getInquiryInfoFromService_${this.inquiryId}`,
      quoteDetail: `purchase_getQuoteFromInquiryBodyDetail_${this.inquiryId}`,
      industryKnowledge: `purchase_getIndustryKnowledge_${this.inquiryId}`,
      storesAddress: `purchase_getStoresFacilityAddressInfo_${this.inquiryId}`,
      needDecodeList: `purchase_getNeedDecodeList_${this.inquiryId}`,
    };
  }

  /**
   * 每个字段的数据获取方法
   */
  private get retrieves() {
    return {
      inquiryDetail: () => inquiryService.getInquiryInfoFromService(this.inquiryId),
      quoteDetail: () => purchaseService.getQuoteFromInquiryBodyDetail(this.inquiryId),
      industryKnowledge: () => purchaseService.getIndustryKnowledge(this.inquiryId),
      storesAddress: async () => {
        const { quotes } = await purchaseService.getQuoteFromInquiryBodyDetail(this.inquiryId);
        const storeIds = [...new Set(quotes.map((quote) => quote.storeId))] as string[];
        return purchaseService.getStoresFacilityAddressInfo(this.inquiryId, storeIds);
      },
      needDecodeList: () => inquiryCommonService.getNeedDecodeList(this.inquiryId),
    };
  }

  /**
   * 获取某个字段
   */
  async getItem<T extends keyof IDataProviderDTO>(key: T, forceRetrieve = false): Promise<IDataProviderDTO[T]> {
    const cacheKey = this.CACHE_KEYS[key];
    const retrieve = this.retrieves[key] as () => Promise<IDataProviderDTO[T]>;
    return this.dataCacheFetcher.getOrFetch(cacheKey, retrieve, forceRetrieve);
  }

  async getAll(forceRetrieve: boolean = false): Promise<IDataProviderDTO> {
    // 请求缓存中没有的数据
    return waitForPromises({
      inquiryDetail: this.getItem(DataProviderKey.inquiryDetail, forceRetrieve),
      quoteDetail: this.getItem(DataProviderKey.quoteDetail, forceRetrieve),
      industryKnowledge: this.getItem(DataProviderKey.industryKnowledge, forceRetrieve),
      storesAddress: this.getItem(DataProviderKey.storesAddress, forceRetrieve),
      needDecodeList: this.getItem(DataProviderKey.needDecodeList, forceRetrieve),
    });
  }

  async prefetch() {
    try {
      await this.getAll(true);
    } catch (error) {
      logger.error(`DataProvider prefetch error, inquiryId: ${this.inquiryId} ,error:${error}`);
      return false;
    }
    return true;
  }
}
