import logger from "@/common/logger";
import { IDataProvider, IDataProviderDTO } from "../../interfaces";
import csv from "csv";
import fs from "fs";
import util from "util";

export class MockDataProvider implements IDataProvider {
  inquiryId: string;
  constructor(inquiryId: string) {
    this.inquiryId = inquiryId;
  }
  async prefetch() {
    try {
      await this.getAll();
    } catch (error) {
      logger.error(`MockDataProvider prefetch error, inquiryId: ${this.inquiryId} error: ${error}`);
      return false;
    }
    return true;
  }
  async getItem<T extends keyof IDataProviderDTO>(key: T): Promise<IDataProviderDTO[T]> {
    const all = await this.getAll();
    return all[key];
  }
  // mock数据，先不管类型
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  cache = new Map<string, any>();

  private get CACHE_KEYS() {
    return {
      inquiryDetail: `purchase_getInquiryInfoFromService_${this.inquiryId}`,
      quoteDetail: `purchase_getQuoteFromInquiryBodyDetail_${this.inquiryId}`,
      industryKnowledge: `purchase_getIndustryKnowledge_${this.inquiryId}`,
      storesAddress: `purchase_getStoresFacilityAddressInfo_${this.inquiryId}`,
      needDecodeList: `purchase_getNeedDecodeList_${this.inquiryId}`,
    };
  }
  async fetchAll() {
    const csvFilePath = `./data/mock/datacache.csv`;
    const keys = Object.values(this.CACHE_KEYS);
    const parser = csv.parse(fs.readFileSync(csvFilePath), { columns: true }).on("data", ({ key, value }) => {
      if (keys.includes(key) && !this.cache.has(key)) {
        const data = JSON.parse(value);
        this.cache.set(key, data);
      }
    });
    await Promise.race([
      util.promisify(parser.on.bind(parser))("end"),
      util
        .promisify(parser.on.bind(parser))("error")
        .then((err) => {
          throw err;
        }),
    ]);
  }
  async getAll(): Promise<IDataProviderDTO> {
    if (!this.cache.size) {
      await this.fetchAll();
    }
    return {
      inquiryDetail: this.cache.get(this.CACHE_KEYS.inquiryDetail),
      quoteDetail: this.cache.get(this.CACHE_KEYS.quoteDetail),
      industryKnowledge: this.cache.get(this.CACHE_KEYS.industryKnowledge),
      storesAddress: this.cache.get(this.CACHE_KEYS.storesAddress),
      needDecodeList: this.cache.get(this.CACHE_KEYS.needDecodeList),
    };
  }
  async get<T>(key: string): Promise<T> {
    let val = this.cache.get(key);
    if (!val) {
      await this.fetchAll();
      val = this.cache.get(key);
    }
    return val as Promise<T>;
  }
}
