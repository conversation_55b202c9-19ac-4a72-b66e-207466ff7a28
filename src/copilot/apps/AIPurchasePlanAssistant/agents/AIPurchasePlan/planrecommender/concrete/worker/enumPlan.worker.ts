import { isMainThread } from "worker_threads";
// 在worker中没有注册module-alias，暂时用相对路径引入
import { IntelligentPlanType } from "../../../../../../GarageAssistant/agents/inquiry/interface";
import { PlanWeight } from "../generator/PlanWeightGenerator";
import { QuoteMatchDegree } from "../generator/QuoteMatchGenerator";
import _ from "lodash";
import Piscina from "piscina";
import { IPurchasePlanItem } from "../../interfaces/plan";
// 在worker中没有注册module-alias，暂时用相对路径引入
import logger from "../../../../../../../../common/logger";
import { IPlanQuoteItem } from "@/clients/quote/interface";

interface Inputs {
  quotes: IPlanQuoteItem[];
  planWeight: PlanWeight;
  quoteMatches: QuoteMatchDegree[];
}

let enumPlanWorker: (inputs: Inputs) => Promise<{ allPlans: IPurchasePlanItem[] }>;

/**
 * 枚举所有采购组合
 * @param quotes
 * @returns
 */
function enumAllCombinations(quotes: IPlanQuoteItem[]): IPlanQuoteItem[][] {
  const partsNumQuotes = _.groupBy(quotes, "standardItemId") as Record<string, IPlanQuoteItem[]>;

  const keys = Object.keys(partsNumQuotes);
  // 递归生成所有组合
  const generateCombinations = (index: number, currentCombination: IPlanQuoteItem[]): IPlanQuoteItem[][] => {
    if (index === keys.length) {
      // combination 中同品类必须有同一品牌和品质
      const groupedByNeedsName = _.groupBy(currentCombination, "needsName");
      const isValid = Object.values(groupedByNeedsName).every(
        (group) => _.uniqBy(group, "partsBrandQualityAndBrandName").length === 1
      );
      return isValid ? [currentCombination] : [];
    }

    const key = keys[index];
    const quotes = partsNumQuotes[key];
    const combinations: IPlanQuoteItem[][] = [];

    quotes.forEach((quote) => {
      combinations.push(...generateCombinations(index + 1, [...currentCombination, quote]));
    });

    return combinations;
  };

  const allQuoteCombinations = generateCombinations(0, []);

  return allQuoteCombinations;
}

/**
 * 报价组合转换为采购方案
 * @param plans
 * @returns
 */
function combinationToPlan(
  combinations: IPlanQuoteItem[][],
  weight: PlanWeight,
  quoteMatches: QuoteMatchDegree[]
): IPurchasePlanItem[] {
  // 最大的sku数
  const skuTotal = Math.max(...combinations.map((combination) => combination.length));
  const plans = combinations.map((combination, index) => {
    const quotationProductIds: string[] = [];
    const features: string[] = [];
    const storeCombination = _.uniqBy(combination, "storeId");
    if (storeCombination.every((item) => item.storeName?.includes("开思严选"))) {
      features.push("严选商家");
    }
    const storeId = storeCombination.length === 1 ? storeCombination[0].storeId || "" : "";
    const storeName = storeCombination.length === 1 ? storeCombination[0].storeName || "" : "";
    let sumScore = 0;
    const standardItemResults = combination.map((quote) => {
      const { quotationProductId, standardItemId } = quote;
      const matchScore =
        quoteMatches.find((item) => item.quotationProductId === quotationProductId)?.matchedDegree || 0;
      quotationProductIds.push(quote.quotationProductId);
      sumScore += matchScore;
      return {
        standardItemId,
        quotationProductIds: [quotationProductId],
      };
    });
    const score = sumScore / combination.length;
    const storeCount = _.uniqBy(combination, "storeId").length;
    const brandQualityCount = _.uniqBy(combination, "partsBrandQualityAndBrandName").length;
    const skuCount = combination.length;

    const { match_weight, merchant_weight, quality_weight, full_weight } = weight;
    const weightedScore =
      score * match_weight +
      (1 / storeCount) * merchant_weight +
      Math.sqrt(1 / brandQualityCount) * quality_weight +
      (skuCount / skuTotal) * full_weight;

    return {
      name: `方案${index + 1}`,
      type: IntelligentPlanType.COMPREHENSIVE,
      source: "BACKUP",
      storeId,
      storeName,
      reason: "",
      standardItemResults,
      weightedScore,
      features,
    };
  });

  return _.orderBy(plans, "weightedScore", "desc").slice(0, 100);
}

// 主线程逻辑
if (isMainThread) {
  // 创建线程池
  const piscina = new Piscina({
    filename: __filename,
    maxQueue: 50,
    minThreads: 2,
    maxThreads: 4,
  });

  // 初始化函数
  enumPlanWorker = async function (input: Inputs) {
    try {
      // 运行工作线程并返回结果
      const result = await piscina.run(input);
      return result;
    } catch (error) {
      logger.error("枚举方案出错", error);
      return { allPlans: [] };
    }
  };
} else {
  // 工作线程逻辑
  enumPlanWorker = async ({ quotes, planWeight, quoteMatches }: Inputs) => {
    // 枚举所有组合
    const allCombinations = enumAllCombinations(quotes);
    // 转换为采购方案
    const allPlans = combinationToPlan(allCombinations, planWeight, quoteMatches);
    // 返回结果
    return { allPlans };
  };
}

// 导出函数
export default enumPlanWorker;
