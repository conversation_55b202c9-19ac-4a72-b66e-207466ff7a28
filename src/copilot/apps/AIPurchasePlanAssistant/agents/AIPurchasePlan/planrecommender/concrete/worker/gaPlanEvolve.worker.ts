import { isMainThread, MessageChannel, MessagePort } from "worker_threads";
import Piscin<PERSON> from "piscina";
import _ from "lodash";
import type { GeneticConfiguration, Population, Stats } from "@/common/utils";
// 在worker中没有注册module-alias，暂时用相对路径引入
import { Genetic } from "../../../../../../../../common/utils";

import type { QuoteMatchDegree } from "../generator/QuoteMatchGenerator";
import type { PlanWeight } from "../generator/PlanWeightGenerator";
import { IPlanQuoteItem } from "@/clients/quote/interface";

interface Inputs {
  config: Partial<GeneticConfiguration>;
  userData: {
    products: {
      id: string;
      name: string;
      quotes: IPlanQuoteItem[];
    }[];
    quoteMatches: QuoteMatchDegree[];
    planWeights: PlanWeight;
  };
  port?: MessagePort;
}

type EvolveResult = {
  pop: Population;
  generation: number;
  stats: Stats;
  isFinished: boolean;
};

let gaPlanEvolve: (
  inputs: Inputs,
  onProgress?: (result: EvolveResult & { progress: { current: number; total: number } }) => void
) => Promise<EvolveResult>;

if (isMainThread) {
  const piscina = new Piscina({ filename: __filename, maxQueue: 50, minThreads: 2, maxThreads: 10 });
  gaPlanEvolve = async (input: Inputs, onProgress) => {
    const mc = new MessageChannel();
    mc.port2.on("message", (result) => onProgress?.(result));
    mc.port2.unref();
    const result = await piscina.run({ ...input, port: mc.port1 }, { transferList: [mc.port1] });
    return result;
  };
} else {
  function initGenetic() {
    const genetic = Genetic.create();

    genetic.select1 = Genetic.Select1.Tournament3;
    genetic.select2 = Genetic.Select2.Tournament3;

    // 我们需要随机生成初始采购方案
    genetic.seed = function () {
      // 创建一个数组，每个元素代表一个商品的选择
      // 数组的索引是商品ID，值是选择的报价索引
      const solution = [];
      for (let i = 0; i < this.userData.products.length; i++) {
        const randomQuoteIndex = Math.floor(Math.random() * this.userData.products[i].quotes.length);
        solution.push(randomQuoteIndex);
      }
      return solution;
    };

    // 我们需要计算每个采购方案的综合得分
    genetic.fitness = function (entity: number[]) {
      const products = this.userData.products;
      const planWeights = this.userData.planWeights;
      const quoteMatches: QuoteMatchDegree[] = this.userData.quoteMatches;
      const quoteMatchesGroup = _.groupBy(quoteMatches, (item) => item.quotationProductId);
      const quoteItems: IPlanQuoteItem[] = entity.map((productQuoteIndex, index) => {
        return products[index].quotes[productQuoteIndex];
      });

      //报价匹配度得分
      const sumMatchScore = _.sumBy(quoteItems, (item) => {
        const degree = _.first(quoteMatchesGroup[item.quotationProductId]);
        const percent = degree?.matchedDegree ?? 0.2;
        return percent;
      });
      const avgMatchScore = sumMatchScore / entity.length;
      // 商家聚合度
      const storeCount = _.uniqBy(quoteItems, (item) => item.storeId).length;
      const storeAggregation = 1 / storeCount;

      // 品质聚合度
      const brandQualityCount = _.uniqBy(quoteItems, (item) => item.partsBrandQualityAndBrandName).length;
      const qualityAggregation = Math.sqrt(1 / brandQualityCount);
      const { match_weight, merchant_weight, quality_weight, full_weight } = planWeights;

      // 整单报出率
      const fullScore = quoteItems.length === 1 ? 1 : 0;

      const weightedScore =
        avgMatchScore * match_weight +
        storeAggregation * merchant_weight +
        qualityAggregation * quality_weight +
        fullScore * full_weight;
      return weightedScore;
    };

    genetic.optimize = Genetic.Optimize.Maximize;

    genetic.unique = (population) => {
      const uniquePopulation = _.uniqBy(population, (item) => item.entity.join(","));
      return uniquePopulation;
    };

    // 变异操作随机改变某个商品的选择
    genetic.mutate = function (entity: number[]) {
      for (const [productIndex] of entity.entries()) {
        const availableQuotes = this.userData.products[productIndex].quotes.length;
        if (Math.random() <= 0.5 && availableQuotes > 1) {
          entity[productIndex] = Math.floor(Math.random() * availableQuotes);
        }
      }
      return entity;
    };

    // 交叉操作将两个采购方案的部分选择进行交换：
    genetic.crossover = function (mother: number[], father: number[]) {
      const reproduction = () =>
        [...father].map((item, index) => {
          if (Math.random() < 0.5) {
            return item;
          }
          return mother[index];
        });
      return [reproduction(), reproduction()];
    };

    return genetic;
  }
  gaPlanEvolve = async ({ userData, config, port }: Inputs) => {
    try {
      const genetic = initGenetic();
      let current = 0;
      const result = await genetic.evolve(config, userData, (pop, generation, stats, isFinished) => {
        current++;
        port?.postMessage({
          pop,
          generation,
          stats,
          isFinished,
          progress: { current, total: config.iterations },
        });
      });
      return result;
    } finally {
      port?.close();
    }
  };
}

export default gaPlanEvolve;
