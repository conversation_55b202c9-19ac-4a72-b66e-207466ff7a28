import { AbstractFilter } from "../abstract/AbstractFilter";
import { QuoteFilter, SkuRules } from "../../utils/QuoteFilter";
import { FilterRule } from "../interfaces";
import { IPlanQuoteItem } from "@/clients/quote/interface";

function generateRulesToSkuRules(rules: FilterRule[]) {
  const ret = {} as SkuRules;
  for (const rule of rules) {
    ret[rule.partName] = {
      quality: {
        operator: rule.quality.operator,
        value: rule.quality.value,
      },
      brand: {
        operator: rule.brand.operator,
        value: rule.brand.value,
      },
      price: {
        operator: rule.price.operator,
        value: rule.price.value,
      },
      merchant: {
        operator: rule.merchant.operator,
        value: rule.merchant.value,
      },
    } as SkuRules[keyof SkuRules];
  }
  return ret;
}

export class LLMQuoteFilter extends AbstractFilter {
  async filter({ quotes, rules }: { quotes: IPlanQuoteItem[]; rules: FilterRule[] }): Promise<IPlanQuoteItem[]> {
    const step = this.reporter.step("llmQuoteFilter", { parentStep: "recommend" });
    step.start("开始llmQuoteFilter过滤");
    const skuRules = generateRulesToSkuRules(rules);
    const quoteFilter = new QuoteFilter(skuRules);
    const result = quoteFilter.filter(quotes);
    step.end("llmQuoteFilter过滤结束", { data: result });
    return result;
  }
}
