import logger from "@/common/logger";
import { doubao_chat, renderPrompt, waitStreamContent } from "@/copilot/helpers/llm";
import _ from "lodash";
import { FilterRule } from "../../interfaces";
import { AbstractEnv } from "../../abstract/AbstractEnv";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
import { parseJsonMarkdown } from "@/common/utils";
import { IPlanQuoteItem } from "@/clients/quote/interface";

export class FilterRulesGenerator extends AbstractEnv {
  async generate({
    quotes,
    input,
  }: {
    quotes: IPlanQuoteItem[];
    input: string;
  }): Promise<FilterRule[]> {
    try {
      const step = this.reporter.step("genRuleFilter", { parentStep: "recommend" });
      const partNames = quotes.map((quote) => quote.partsName).filter(Boolean);
      const brandNames = quotes.map((quote) => quote.brandName).filter(Boolean);
      const storeNames = quotes.map((quote) => quote.storeName).filter(Boolean);
      const warehouses = quotes.map((quote) => quote.locationName).filter(Boolean);
      step.start("开始生成过滤规则", { data: { partNames, brandNames, storeNames, warehouses } });
      const prompt = await renderPrompt("采购方案/过滤规则提示词", {
        input,
        brandNames: _.uniq(brandNames),
        storeNames: _.uniq(storeNames),
        partNames: _.uniq(partNames),
        warehouses: _.uniq(warehouses),
      });
      const stream = await doubao_chat.stream(prompt, { callbacks: [llmLogCallback] });
      const content = await waitStreamContent(stream);
      const result = await parseJsonMarkdown(content);
      step.progress("生成过滤规则得到llm输出", { data: { prompt, content } });
      step.end("生成过滤规则完成", { data: { result } });
      return result;
    } catch (error) {
      logger.warn("FilterRulesGenerator", "Error in generate method", error);
      return [];
    }
  }
}
