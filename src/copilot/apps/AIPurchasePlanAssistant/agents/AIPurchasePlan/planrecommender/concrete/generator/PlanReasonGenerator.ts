import { doubaoDeepSeek_chat, extractJsonFromStream, renderPrompt } from "@/copilot/helpers/llm";
import { AbstractEnv } from "../../abstract/AbstractEnv";
import { IPurchasePlanItem } from "../../interfaces/plan";
import { PlanWeight } from "./PlanWeightGenerator";
import { QuoteMatchDegree } from "./QuoteMatchGenerator";
import _ from "lodash";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
import { IPlanQuoteItem } from "@/clients/quote/interface";

export class PlanReasonGenerator extends AbstractEnv {
  async generate({
    input,
    recommendPurchasePlans,
    quoteMatches,
    planWeight,
  }: {
    input: string;
    recommendPurchasePlans: IPurchasePlanItem[];
    quoteMatches: QuoteMatchDegree[];
    planWeight: PlanWeight;
  }) {
    const { inquiryDetail, quoteDetail } = await this.dataProvider.getAll();
    const reasonStep = this.reporter.step("planReason", {});
    reasonStep.start("开始生成方案理由");
    const plansForPrompt: (IPurchasePlanItem & {
      quotations: (IPlanQuoteItem & { matchDegree?: QuoteMatchDegree })[];
    })[] = [];
    recommendPurchasePlans.map((plan) => {
      const quotesById = _.groupBy(quoteDetail.quotes, (quote) => quote.quotationProductId);
      const quotations = plan.standardItemResults.map((item) => {
        const quotationProductId = item.quotationProductIds[0];
        const quote = quotesById[quotationProductId];
        return {
          ...quote[0],
          matchDegree: quoteMatches.find((quote) => quote.quotationProductId === quotationProductId),
        };
      });
      plansForPrompt.push({
        ...plan,
        quotations,
      });
    });
    const prompt = await renderPrompt("采购方案/方案理由提示词", {
      modelName: inquiryDetail.saleModelName || "-",
      input,
      planWeight,
      plansForPrompt,
    });
    const stream = await doubaoDeepSeek_chat.stream(prompt, { callbacks: [llmLogCallback] });
    const content = ((await extractJsonFromStream(stream)) as string[]) || [];
    recommendPurchasePlans.map((plan, idx) => {
      plan.reason = content[idx] || plan.reason;
    });
    reasonStep.end("生成方案理由结束", { data: { prompt, content } });
    return recommendPurchasePlans;
  }
}
