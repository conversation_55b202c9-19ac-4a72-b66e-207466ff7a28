import { isDev } from "@/common/utils";
import { DataProvider } from "./concrete/dataprovider/DataProvider";
import { IDataProvider } from "./interfaces";

export function createDataProvider(inquiryId: string): IDataProvider {
  // if (isDev()) {
  //   // 如果你想使用 MockDataProvider，请取消下面两行注释
  //   const { MockDataProvider } = require("./concrete/dataprovider/MockDataProvider");
  //   return new MockDataProvider(inquiryId);
  // }
  return new DataProvider(inquiryId);
}
