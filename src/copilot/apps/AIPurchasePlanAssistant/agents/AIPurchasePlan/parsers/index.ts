import { Context, Entity } from "@casstime/copilot-core";

import _ from "lodash";
import { FormNames, purchaseForm } from "../forms";
import { purchaseEntityEditService } from "../services/PurchaseEntityEditService";
import { Dialogue } from "@/models";
import { Types } from "mongoose";
import { purchaseService } from "../services/PurchaseService";

export async function parseEntities(context: Context): Promise<Entity[]> {
  const lastMessage = context.lastMessage;
  if (lastMessage.type === "text" || lastMessage.type === "voice") {
    const { content = "", dialogueId = "" } = lastMessage;
    const form = context.getForm<typeof purchaseForm>(FormNames.purchaseForm);
    const formData = form.getValues();
    // const dialogue = await Dialogue.findById(Types.ObjectId(dialogueId)).exec();
    // const inquiryId = dialogue?.businessId || "";
    // const entityData = await purchaseEntityEditService.getFullEntityData(inquiryId);
    const actions = await purchaseEntityEditService.parseEditEntityActions(content);
    const qualities = formData.qualities || [];
    const brands = formData.brands || [];
    const stores = formData.stores || [];
    const editData = { qualities, brands, stores };
    const { qualityEntities, brandEntities, storeEntities } = purchaseEntityEditService.applyEditActions(
      editData,
      actions
    );
    const entities: Entity[] = _.concat(qualityEntities, brandEntities, storeEntities);
    return _.uniqBy(entities, (entity) => entity.value);
  }
  return [];
}
