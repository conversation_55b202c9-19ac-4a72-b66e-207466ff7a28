import { AgentName, commandIntents } from "@/copilot/constants";
import { Agent, Entity } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { AIPurchasePlanIntents } from "./intent";
import {
  change<PERSON><PERSON><PERSON><PERSON><PERSON>,
  init<PERSON>hat<PERSON><PERSON><PERSON>,
  interaction<PERSON><PERSON><PERSON>mend<PERSON><PERSON><PERSON>,
  reset<PERSON><PERSON><PERSON><PERSON><PERSON>,
  modify<PERSON><PERSON><PERSON>and<PERSON>,
} from "./handlers";
import { FormNames } from "./forms/FormNames";
import { purchaseForm } from "./forms";
import { SlotNames } from "./slots/SlotNames";
import { brandSlot, qualitySlot, storeSlot } from "./slots";
import { Dialogue } from "@/models";
import { Types } from "mongoose";
import { NluActionIntents } from "@/copilot/constants";
import { savePlanHandler } from "./handlers/savePlanHandler";
import { initRecommendHandler } from "./handlers/initRecommendHandler";
import { intentClassifier } from "./classifiers/intentClassifier";
import { FallbackHandler } from "./handlers/FallbackHandler";

/**
 * Agent
 */
const agent = new Agent(AgentName.AIPurchasePlanAgent);

agent.registerSlot(SlotNames.qualities, qualitySlot);
agent.registerSlot(SlotNames.brands, brandSlot);
agent.registerSlot(SlotNames.stores, storeSlot);
agent.registerSlot(SlotNames.isGuard, {}); //
agent.registerSlot(SlotNames.contentSource, { lifetime: "forever" });

agent.registerForm(FormNames.purchaseForm, purchaseForm);

/**
 * 设置实体解析器
 */
agent.setEntitiesParser(async (context) => {
  const entities: Entity[] = [];
  const dialogueId = context.lastMessage.dialogueId;
  if (dialogueId) {
    const dialogue = await Dialogue.findById(Types.ObjectId(dialogueId)).exec();
    if (dialogue) {
      entities.push({ name: "inquiryId", value: dialogue.businessId });
    }
  }
  context.setEntities(entities);
});

agent.registerSlot("inquiryId", {
  filling: (entities) => {
    return entities.find((entity) => entity.name === "inquiryId")?.value;
  },
  lifetime: "forever",
});

/**
 * 第一次进入Agent
 */
agent.onEnter(() => {
  logger.info("进入 AIPurchasePlanAgent");
});

/**
 * 离开当前Agent
 */
agent.onLeave(() => {
  logger.info("离开 AIPurchasePlanAgent");
});

/**
 * 设置意图解析器
 */
agent.registerIntentClassifier(intentClassifier, true);

agent.handleCommand(commandIntents.inquiryStart, initChatHandler);

agent.handleCommand(commandIntents.AI_PURCHASE_RECOMMEND, initRecommendHandler);

agent.handleCommand(commandIntents.AI_PURCHASE_INTERACTIVE_RECOMMEND, interactionRecommendHandler);

agent.handle(AIPurchasePlanIntents.重新开始, resetChatHandler);

agent.handle(AIPurchasePlanIntents.交互式推荐, interactionRecommendHandler);

agent.handle(AIPurchasePlanIntents.切换方案, changeModeHandler);

agent.handle(NluActionIntents.MODIFY_PLAN, modifyPlanHandler);

agent.handle(NluActionIntents.INQUIRY_PLAN_SAVE, savePlanHandler);

agent.handleFallback(FallbackHandler);

export default agent;
