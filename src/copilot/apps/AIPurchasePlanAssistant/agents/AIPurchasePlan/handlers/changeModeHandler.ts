import { IHandlerFunction } from "@casstime/copilot-core";
import { commandIntents } from "@/copilot/constants";
import { MessageFactory } from "@casstime/copilot-core";
import { Types } from "mongoose";
import { inquiryService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/InquiryService";
import { IMessageExtra, TaskType } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { taskService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/TaskService";
import { purchaseService } from "../services/PurchaseService";
import { IContentSource, IFetchMakePurchasePlanPayload, IScenarioType } from "../interface";
import { config } from "@casstime/apollo-config";
import { createDataProvider } from "../planrecommender/factory";
import { DataProviderKey } from "../planrecommender/interfaces";

export const changeModeHandler: IHandlerFunction = async (context) => {
  const { payload, slots } = context;
  const contentSource = slots["contentSource"] || IContentSource.MACHINE_LEARNING;
  const inquiryId = slots["inquiryId"] || "";
  const { data: msg, platform = "ANDROID", companyId } = payload;
  const { owner, app, fromUser = "", dialogueId = "" } = msg;
  const source = (platform as string).toLocaleUpperCase();
  const fromPage = "AIChatScreen";

  const dataProvider = createDataProvider(inquiryId);
  const inquiryDetail = await dataProvider.getItem(DataProviderKey.inquiryDetail);

  // 发送询价卡片
  const embed = inquiryService.createEmptyQuoteRichText(inquiryDetail, {}, false, false);
  const extra: IMessageExtra = {};
  const messageId = new Types.ObjectId().toString();
  const expiration = (inquiryDetail.createdStamp || new Date().getTime()) + 24 * 60 * 60 * 1000;
  extra.inquiryId = inquiryId;
  extra.vinCode = inquiryDetail.vin || "";

  // 采购方案任务配置
  const purchaseTask = {
    taskId: inquiryId,
    taskType: TaskType.PURCHASE_PLAN,
    owner,
    app,
    companyId,
    userId: fromUser,
    done: false,
    params: {
      inquiryId,
      source,
      fromPage,
      isOpenInvoice: true,
      messageId,
      hasSentTurn: 0,
      turns: 0,
      expiration,
      inquiryDetail,
    },
  };
  const task = await taskService.findOneAndUpdateTaskPolling(
    { owner, taskId: inquiryId, taskType: TaskType.PURCHASE_PLAN, dialogueId, done: false },
    purchaseTask
  );
  // 请求制作方案
  const json: IFetchMakePurchasePlanPayload = {
    demandId: inquiryId,
    scenario: IScenarioType.GARAGE_ASSISTANT_SENTINEL,
    operatorId: fromUser,
    identifyId: task?.id,
    callbackUrl: `${config.get("API_INTRA_BASE_URL")}copilot-server/copilot/purchase_plan`,
    contentSource,
  };
  await purchaseService.fetchMakePurchasePlan(json);

  context.reply(MessageFactory.markdown(`正在为你制作AI采购方案，请稍等...`), { embed, id: messageId, extra });
  // 触发轮询
  context.reply(
    MessageFactory.command(
      commandIntents.inquiryPolling,
      {},
      {
        background: true,
        reply: {
          type: "command",
          command: commandIntents.inquiryPolling,
          background: true,
          replyDelay: 10 * 1000,
          params: {},
        },
      }
    )
  );
};
