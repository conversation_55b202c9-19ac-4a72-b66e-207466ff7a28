import { IHandlerFunction, ICommandMessage } from "@casstime/copilot-core";
import { createDataProvider } from "../planrecommender/factory";

export const initChatHandler: IHandlerFunction = async (context) => {
  const inquiryId = context.slots["inquiryId"];
  const dataProvider = createDataProvider(inquiryId);
  // 异步获取数据
  dataProvider.prefetch();
  const { lastMessage } = context;
  const { params = {} } = (lastMessage as ICommandMessage) || {};
  context.mergeSlots({ isGuard: <PERSON><PERSON><PERSON>(params.isGuard) });
};
