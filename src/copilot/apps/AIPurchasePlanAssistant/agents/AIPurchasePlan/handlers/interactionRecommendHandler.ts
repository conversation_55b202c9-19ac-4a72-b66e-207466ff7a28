import { <PERSON><PERSON><PERSON><PERSON>Function } from "@casstime/copilot-core";
import { MessageFactory } from "@casstime/copilot-core";
import { Types } from "mongoose";
import { stringifyMessage, isDev } from "@/common/utils";
import { createCollapseXml, createInquiryCollapseXml, createTableXml } from "../richtext";
import { PlanRecommender } from "../planrecommender";
import { IGetIntelligentPlanRes, IInquiryInfo } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { inquiryService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/InquiryService";
import { IPurchasePlanItem, MarkerType } from "../planrecommender/interfaces/plan";
import { createDataProvider } from "../planrecommender/factory";
import { IPlanRecommendDTO } from "../planrecommender/interfaces";
import _ from "lodash";
import { purchasePlanService } from "@/service";
import { MessageReporter } from "@/messages";
import { getMerchantClue } from "@/copilot/apps/GarageAssistant/agents/inquiry/copilot-xml/utils";
import { createIntelligentPlanEmbed } from "@/copilot/apps/GarageAssistant/agents/inquiry/copilot-xml";
import { DivisionRecord } from "@/models";
import logger from "@/common/logger";
import { config } from "@casstime/apollo-config";
import { IPlanQuoteItem } from "@/clients/quote/interface";
import { INeedDecodeItem } from "@/service/interface";
import { featureTogglesClient } from "@/copilot/apps/GarageAssistant/agents/inquiry/clients/FeatureTogglesClient";
import { FeatureTogglesEnum } from "@/common/enums";
import { renderRichtext } from "@/copilot/richtext";
import { AIRecommendTips } from "../richtext/components/AIRecommendTips";
import { IntelligentPlan } from "@/copilot/apps/GarageAssistant/agents/richtext/components/IntelligentPlan";

export const interactionRecommendHandler: IHandlerFunction = async (context) => {
  const inquiryId = context.slots["inquiryId"];
  const isGuard = context.slots["isGuard"];
  const dataProvider = createDataProvider(inquiryId);
  const {
    quoteDetail: { quotes },
    inquiryDetail,
    needDecodeList: { needDecodeList, needsNames },
  } = await dataProvider.getAll();
  inquiryDetail.needsNames = needsNames;
  // 询价卡片
  const inquiryCollapseXmlId = new Types.ObjectId().toString();
  const indicator = createInquiryCollapseXml(inquiryCollapseXmlId, inquiryDetail);

  if (quotes.length === 0) {
    context.reply(MessageFactory.text("当前询价单暂无报价，稍后再请求制作采购方案", { indicator }));
    return context.break();
  }

  let makerType = context.slots["makerType"];
  let input = stringifyMessage(context.lastMessage, false);

  const id = new Types.ObjectId().toString();
  const factory = MessageFactory.with({ id });
  let isFirstRecommend = false;
  if (input === "AI交互" || input === "GA交互") {
    if (isDev() && input === "GA交互") {
      makerType = MarkerType.GAMAKER;
    }
    isFirstRecommend = true;
    input = "综合考虑品质、品牌、价格、商家、时效，给我推荐最优方案";
  }
  const messageReporter = new MessageReporter(id);
  context.setReplyMode("stream");
  const loadingMsg: string[] = [];
  loadingMsg.push("正在拉取报价......");
  let header = "正在核对方案……";
  const collapseId = new Types.ObjectId().toString();
  context.reply(factory.richtext(createCollapseXml(collapseId, header, loadingMsg), { indicator }));

  const planRecommender = await PlanRecommender.create({
    inquiryId,
    reporter: messageReporter,
    dataProvider,
    makerType,
  });

  // 回复消息
  messageReporter.on("progress", (message: string, options?: unknown) => {
    const { action } = (options || {}) as { action: "append" | "replace" | "done" };
    if (!action) {
      return;
    }
    switch (action) {
      case "replace":
        loadingMsg.pop();
        loadingMsg.push(message);
        context.reply(factory.richtext(createCollapseXml(collapseId, header, loadingMsg), { indicator }));
        break;
      case "done":
        context.reply(factory.richtext(createCollapseXml(collapseId, header, loadingMsg, true), { indicator }));
        break;
      case "append":
      default:
        loadingMsg.push(message);
        context.reply(factory.richtext(createCollapseXml(collapseId, header, loadingMsg), { indicator }));
        break;
    }
  });

  // 获取用户输入的看单总结和行业经验
  const params = context.lastMessage.type === 'command' ? (context.lastMessage as any).params : {};
  const orderviewExp = params?.orderviewExp || "";
  const industryExp = params?.industryExp || "";

  // 推荐方案
  const recommendPromises = [
    planRecommender.recommend({
      input,
      orderviewExp,
      industryExp,
    }),
  ];
  if (isFirstRecommend) {
    const messageReporterNotListen = new MessageReporter(id);
    const planRecommenderSection = await PlanRecommender.create({
      inquiryId,
      reporter: messageReporterNotListen,
      dataProvider,
      makerType,
    });
    recommendPromises.push(
      ...[
        planRecommenderSection.recommend({
          input: "要原厂的，品质最优的",
        }),
        planRecommenderSection.recommend({
          input: "要便宜的，价格最优的",
        }),
      ]
    );
  }
  const recmmendResults = await Promise.all(recommendPromises);
  const { recommendPurchasePlans } = handleRecommendResult(recmmendResults);
  // const recommendPurchaseReasonPlansPromise = planRecommender.planReasonGenerator.generate({
  //   input,
  //   recommendPurchasePlans,
  //   quoteMatches,
  //   planWeight,
  // });
  // await sleep(3000);
  // const recommendPurchaseReasonPlans = await recommendPurchaseReasonPlansPromise;
  // 呈现方案
  const planGroupInfo = await purchasePlanService.fillAndSaveRecommendPlan({
    makerType,
    userLoginId: context.lastMessage.fromUser || "",
    companyId: context.payload.companyId,
    plans: recommendPurchasePlans,
    inquiryId,
  });

  const { embed, planInfo, needFetch } = await createPurchasePlanEmbed({
    inquiryId,
    recommendPurchasePlans: planGroupInfo.plans,
    inquiryDetail,
    quotes,
    planGroupId: planGroupInfo.id,
    needs: needDecodeList,
    isGuard,
  });
  header = `综合行业评价 + 商家优势 + 采购倾向，满足高品质和比价诉求，推荐你以下${recommendPurchasePlans.length}个采购方案`;
  console.log(planGroupInfo);
  // 将当前方案id存入槽，以便其他场景可用
  context.mergeSlots({ planGroupId: planGroupInfo._id });
  context.reply(
    factory.richtext(createCollapseXml(collapseId, header, loadingMsg, true), {
      indicator,
      embed,
      extra: { inquiryId, keepTop: true, planInfo, needFetch, planGroupId: planGroupInfo.id },
      tips: isGuard ? undefined : {
        type: "richtext",
        content: renderRichtext(AIRecommendTips, { clue: getMerchantClue(inquiryDetail) }),
      }
    })
  );

  // 异步更新数据
  dataProvider.prefetch();
};

// 生成方案展示embed内容
export async function createPurchasePlanEmbed({
  inquiryId,
  recommendPurchasePlans,
  inquiryDetail,
  quotes,
  planGroupId,
  needs,
  isGuard,
  displayType: propsDisplayType,
}: {
  inquiryId: string;
  recommendPurchasePlans: IPurchasePlanItem[];
  inquiryDetail: IInquiryInfo;
  quotes: IPlanQuoteItem[];
  planGroupId: string;
  needs: INeedDecodeItem[];
  isGuard: boolean;
  displayType?: 'LIST' | 'TABLE';
}) {
  const purchasePlansDes: IGetIntelligentPlanRes = {
    demandId: inquiryId,
    scenario: "",
    createdDate: new Date().getTime(),
    recommendProgrammes: recommendPurchasePlans,
    recommendStores: [],
    turns: 1,
  };
  const intelligentPlans = await inquiryService.getInquiryQuoteByPurchase(purchasePlansDes, inquiryDetail, quotes);
  // 方案信息
  const planInfo = intelligentPlans.map((item) => {
    return {
      planId: item._id || "", // 方案id
      name: item.name, // 方案名称
      type: item.type, // 方案类型;共享仓方案:YUN_FACILITY,单店集采方案:COLLECT
      reason: item.reason,
      quoteIds: item.quotes?.filter((item) => item.quotationProductId).map((item) => item.quotationProductId) || [],
    };
  });
  let displayType = propsDisplayType;
  if (displayType === undefined) {
    const useTable = await useTableDisplay(inquiryDetail.companyId);
    displayType = useTable ? 'TABLE' : 'LIST';
  }
  if (displayType === 'LIST') {
    // const embed = createIntelligentPlanEmbed({
    //   intelligentPlans,
    //   inquiryDetail,
    //   needDecodeList: needs,
    //   isA9tAirforceEnter: false,
    //   noInquiryHeader: true,
    //   disableAction: isGuard,
    //   planGroupId,
    // });
    const embed = {
      type: 'richtext',
      content: renderRichtext(IntelligentPlan, {
        intelligentPlans,
        inquiryDetail,
        needDecodeList: needs,
        isA9tAirforceEnter: false,
        noInquiryHeader: true,
        disableAction: isGuard,
        planGroupId,
      })
    }
    return { embed, planInfo, needFetch: false };
  }
  const tableParams = {
    tableData: intelligentPlans,
    isOpenInvoice: inquiryDetail.openInvoiceType === "YES",
    planGroupId,
    needs,
  };
  const tableTsx = createTableXml(tableParams, isGuard);

  return { embed: tableTsx, planInfo, needFetch: true };
}

export function handleRecommendResult(recmmendResults: IPlanRecommendDTO[]): IPlanRecommendDTO {
  if (recmmendResults.length === 1) {
    const recommendPurchasePlans = recmmendResults[0].recommendPurchasePlans
      .slice(0, 3)
      .map((item, index) => ({ ...item, name: `方案${index + 1}` }));
    return { ...recmmendResults[0], recommendPurchasePlans };
  }
  const [
    { recommendPurchasePlans: recommendPurchasePlansBoth },
    { recommendPurchasePlans: recommendPurchasePlansQuality },
    { recommendPurchasePlans: recommendPurchasePlansPrice },
  ] = recmmendResults;
  // 是否有严选方案
  let hasStrict = false;
  let recommendPurchasePlans: IPurchasePlanItem[] = [];
  // 1.原厂优先
  recommendPurchasePlans.push(recommendPurchasePlansQuality[0]);
  hasStrict = recommendPurchasePlans.some((item) => item?.features?.includes("严选商家"));

  // 2.综合考虑
  const recommendPurchasePlansBothFilter = recommendPurchasePlansBoth
    .filter((item) => !hasStrict || !item.features?.includes("严选商家")) // 过滤严选
    .filter(
      (item) =>
        !recommendPurchasePlans.some(
          (planItem) =>
            stringifyQuotationProductIds(planItem.standardItemResults) ===
            stringifyQuotationProductIds(item.standardItemResults)
        ) // 过滤已选方案
    );
  recommendPurchasePlans.push(recommendPurchasePlansBothFilter?.[0] || recommendPurchasePlansBoth[0]);

  // 3.价格最优
  const recommendPurchasePlansPriceFilter = recommendPurchasePlansPrice
    .filter((item) => !hasStrict || !item.features?.includes("严选商家")) // 过滤严选
    .filter(
      (item) =>
        !recommendPurchasePlans.some(
          (planItem) =>
            stringifyQuotationProductIds(planItem.standardItemResults) ===
            stringifyQuotationProductIds(item.standardItemResults)
        )
    ); // 过滤已选方案
  recommendPurchasePlans.push(recommendPurchasePlansPriceFilter?.[0] || recommendPurchasePlansPrice[0]);
  hasStrict = recommendPurchasePlans.some((item) => !item?.features?.includes("严选商家"));

  // lodash去重
  recommendPurchasePlans = _.uniqBy(recommendPurchasePlans, (item) =>
    stringifyQuotationProductIds(item.standardItemResults)
  );
  recommendPurchasePlans = recommendPurchasePlans.map((item, index) => ({ ...item, name: `方案${index + 1}` }));

  return {
    ...recmmendResults[0],
    recommendPurchasePlans,
  };
}

function stringifyQuotationProductIds(
  standardItemResults: { standardItemId: string; quotationProductIds: string[] }[]
) {
  const quotationProductIds = standardItemResults
    .map((item) => item.quotationProductIds)
    .flat()
    .sort();
  return JSON.stringify(quotationProductIds);
}

async function useTableDisplay(companyId: string = "") {
  const [tableFeature, listFeature] = await Promise.all([
    featureTogglesClient.getIsPilotArea(companyId, FeatureTogglesEnum.PURCHASE_PLAN_TABLE),
    featureTogglesClient.getIsPilotArea(companyId, FeatureTogglesEnum.PURCHASE_PLAN_LIST),
  ]);
  // 强制使用表格展示
  if (tableFeature.enabled) {
    return true;
  }
  // 强制使用列表展示
  if (listFeature.enabled) {
    return false;
  }

  // 随机展示
  let ratio = 0;
  try {
    ratio = config.get("PURCHASE_PLAN_GROUP_RATIO") as number;
  } catch (e) {
    logger.info("查询阿波罗配置 PURCHASE_PLAN_GROUP_RATIO 失败", e);
  }
  const divisionName = Math.random() < ratio ? "TABLE" : "LIST";
  const divisionInit = {
    type: "PURCHASE_PLAN_GROUP",
    entityId: companyId,
    divisionName,
  };
  try {
    const existingDivision = await DivisionRecord.findOne({
      type: "PURCHASE_PLAN_GROUP",
      entityId: companyId,
    }).exec();
    if (!existingDivision) {
      await DivisionRecord.create(divisionInit);
      return divisionInit.divisionName === "TABLE";
    }
    return existingDivision.divisionName === "TABLE";
  } catch (error) {
    logger.info("DivisionRecord.findOne 或者 DivisionRecord.create 失败", error);
  }
  return false;
}
