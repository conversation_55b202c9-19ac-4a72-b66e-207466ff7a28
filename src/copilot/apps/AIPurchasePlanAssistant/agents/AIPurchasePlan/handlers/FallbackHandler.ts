import { createToolsExecutor, renderPrompt } from "@/copilot/helpers";
import { Hand<PERSON>, MessageFactory } from "@casstime/copilot-core";
import { createDataProvider } from "../planrecommender/factory";
import { DynamicStructuredTool } from "@langchain/core/tools";
import { z } from "zod";
import {
  AIMessageChunk,
  BaseMessageChunk,
  HumanMessage,
  SystemMessage,
  ToolMessageChunk,
} from "@langchain/core/messages";
import { createObjectId, stringifyHistory, stringifyMessage } from "@/common/utils";
import { renderRichtext, ThinkingSteps } from "@/copilot/richtext";
import { AI_DISCLAIMER } from "@/copilot/constants";
import { RecommendPlanGroup } from "@/models";
import logger from "@/common/logger";

const quote_type = `
 interface IPlanQuoteItem {
   /** 商品报价id */
  userNeedsItemId: string;
  remark: string;
  inquiryId: string;
  /** 译码结果id */
  resolveResultId: string;
  /** 商家id */
  storeId: string;
  /** 商家名称 */
  storeName: string;
  storeServiceArea: string;
  /** 商品id */
  productId: string;
  productType: string;
  /** 译码结果零件号 */
  partsNum: string;
  /** 译码结果名称 */
  partsName: string;
  partType: string;
  /** 品牌id */
  brandId: string;
  /** 品牌名称 */
  brandName: string;
  /** 销售价格 */
  sellerPrice: number;
  /** 税前销售价格 */
  sellerBtPrice: number;
  taxRate: number;
  /** 价格 */
  price: number;
  /** 税前价格 */
  btPrice: number;
  btBoxFee: number;
  atBoxFee: number;
  afterSaleSnapshot: string;
  /** 品质id */
  partsBrandQuality: string;
  /** 数量 */
  quantity: number;
  sellStatus: string;
  source: string;
  arrivalTime: number;
  /** 仓库id */
  location: string;
  /** 仓库名称 */
  locationName: string;
  whetherProductSet: string;
  createdStamp: number;
  lastUpdatedStamp: number;
  createdBy: string;
  originalAssort: number;
  quotedTime: number;
  oeOriginalAssort: boolean;
  /** 是否有配套资质 */
  categoryOriginalAssort: boolean;
  /** 品质名称 */
  partsBrandQualityName: string;
  /** 品质展示名称 */
  qualityName: string;
  /** 品质和品牌名称 */
  partsBrandQualityAndBrandName: string;
  /** 商家简称 */
  positioningName: string;
  /** SPD特征详情 */
  spdFeatureDetail: string;
  /** 需求id */
  needId: string;
  /** 需求 */
  needsName: string;
  /** 商品报价id */
  quotationProductId: string;
  /** 译码结果id */
  decodeResultId: string;
  /** 译码结果id */
  standardItemId: string;
}
`;

export class FallbackHandler extends Handler {
  private dataProvider = createDataProvider(this.context.slots.inquiryId);

  private getQuotationDetailTool = new DynamicStructuredTool({
    name: "get_quotation_detail",
    description: "获取报价条目详情，详情包含库存、价格、调货、零件号及其他信息",
    schema: z.object({
      needName: z.string().describe("需求名称，单个配件"),
      planName: z.string().describe("方案名称"),
    }),
    func: async ({ needName, planName }) => {
      const planGroupId = this.context.slots.planGroupId;
      logger.info(
        `调用工具 get_quotation_detail，参数：${JSON.stringify({ planGroupId, needName, planName })}`,
        this.context.slots
      );
      const group = await RecommendPlanGroup.findById(planGroupId).lean().exec();
      if (!group) {
        throw new Error("还没有方案哦，重新生成一个吧");
      }
      const plan = group.plans.find((plan) => plan.name === planName || plan.storeName === planName);
      if (!plan) {
        throw new Error(`没有找到方案【${planName}】`);
      }
      const quoteIds = plan.standardItemResults.map((item) => item.quotationProductIds).flat();
      const { quoteDetail } = await this.dataProvider.getAll();
      const planQuotes = quoteDetail.quotes.filter((quote) => quoteIds.includes(quote.quotationProductId));
      const quote = planQuotes.filter((quote) => [quote.needsName, quote.partsName].includes(needName));
      return JSON.stringify(quote.length === 1 ? quote[0] : planQuotes, null, 2) + "\n字段定义如下:\n" + quote_type;
    },
  });

  private replyMsgId = createObjectId();

  private isToolMessage(chunk: BaseMessageChunk): chunk is AIMessageChunk {
    return !!(chunk as AIMessageChunk).tool_call_chunks?.length || !!(chunk as ToolMessageChunk).tool_call_id;
  }

  async reply(chunks: BaseMessageChunk[]) {
    const lastChunk = chunks[chunks.length - 1];
    let indicator: string | undefined;
    if (this.isToolMessage(lastChunk)) {
      indicator = renderRichtext(ThinkingSteps, { steps: [{ label: "正在查询信息...", done: false }] });
    }
    const markdown = chunks
      .filter((chunk) => !this.isToolMessage(chunk))
      .map((chunk) => chunk.content)
      .join("");
    this.context.reply(MessageFactory.markdown(markdown), {
      id: this.replyMsgId,
      indicator: { type: "richtext", content: indicator },
      disclaimer: AI_DISCLAIMER,
    });
  }

  async handle() {
    this.dataProvider.prefetch(); // 预取数据
    const toolsExecutor = createToolsExecutor([this.getQuotationDetailTool]);
    const prompt = await renderPrompt("采购方案/兜底回复提示词", {
      history: stringifyHistory(this.context.historyMessages, 10, { system: "你", user: "用户" }),
    });
    const stream = toolsExecutor.execute([
      new SystemMessage(prompt),
      new HumanMessage(stringifyMessage(this.context.lastMessage)),
    ]);
    const chunks: BaseMessageChunk[] = [];
    for await (const chunk of stream) {
      chunks.push(chunk);
      this.reply(chunks);
    }
  }
}
