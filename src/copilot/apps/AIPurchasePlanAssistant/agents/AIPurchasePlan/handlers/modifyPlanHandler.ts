import { EditPartNameAction } from "@/copilot/constants";
import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { createChangePlanReplyXml } from "../richtext";
import { IModifyPlanReplyParam } from "../richtext/interface";
export const modifyPlanHandler: IHandlerFunction = async (context) => {
  context.reply(
    MessageFactory.markdown("", {
      indicator: createChangePlanReplyXml(context.slots as IModifyPlanReplyParam),
    })
  );
};
