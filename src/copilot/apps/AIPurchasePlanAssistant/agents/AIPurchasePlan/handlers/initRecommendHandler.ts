import { IHandlerFunction, ICommandMessage, MessageFactory } from "@casstime/copilot-core";
import { createDataProvider } from "../planrecommender/factory";
import { purchasePlanService } from "@/service";
import { createPurchasePlanEmbed } from "./interactionRecommendHandler";
import { getMerchantClue } from "@/copilot/apps/GarageAssistant/agents/inquiry/copilot-xml/utils";
import { createInquiryCollapseXml } from "../richtext";
import { Types } from "mongoose";
import { renderRichtext } from "@/copilot/richtext";
import { AIRecommendTips } from "../richtext/components/AIRecommendTips";

export const initRecommendHandler: IHandlerFunction = async (context) => {
  const inquiryId = context.slots["inquiryId"];
  const { lastMessage, payload } = context;
  const { params = {} } = (lastMessage as ICommandMessage) || {};
  const isGuard = context.slots["isGuard"];
  if (params.recommendPlanGroupId) {
    // 查询用户未读的方案
    const planGroupItem = await purchasePlanService.getUnreadPurchasePlanById(
      params.recommendPlanGroupId,
      payload.data.dialogueId || ""
    );
    if (planGroupItem) {
      const dataProvider = createDataProvider(planGroupItem.inquiryId);
      const {
        inquiryDetail,
        quoteDetail: { quotes },
        needDecodeList: { needDecodeList, needsNames },
      } = await dataProvider.getAll();
      inquiryDetail.needsNames = needsNames;
      // 询价卡片
      const collapseId = new Types.ObjectId().toString();
      const indicator = createInquiryCollapseXml(collapseId, inquiryDetail);
      const { embed, planInfo, needFetch } = await createPurchasePlanEmbed({
        inquiryId,
        recommendPurchasePlans: planGroupItem.plans,
        inquiryDetail,
        quotes,
        planGroupId: params.recommendPlanGroupId,
        needs: needDecodeList,
        isGuard,
      });
      context.reply(
        MessageFactory.markdown(
          `综合行业评价 + 商家优势 + 采购倾向，满足高品质和比价诉求，推荐你以下${planGroupItem.plans.length}个采购方案`,
          {
            embed,
            indicator,
            extra: { inquiryId, keepTop: true, planInfo, needFetch, planGroupId: planGroupItem.id },
            tips: isGuard ? undefined : {
              type: "richtext",
              content: renderRichtext(AIRecommendTips, { clue: getMerchantClue(inquiryDetail) }),
            }
          }
        )
      );
    }
  }
};
