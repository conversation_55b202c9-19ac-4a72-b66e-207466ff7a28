import { Context, Entity, SlotConfig } from "@casstime/copilot-core";
import _ from "lodash";

export const storeSlot: SlotConfig = {
  filling(entities: Entity[], context: Context) {
    const stores = entities.filter((entity) => entity.name === "store").map((entity) => entity.value);

    const beforeStores = (_.get(context.slots, "store", []) as string[]) || [];

    return [...new Set([...beforeStores, ...stores])].filter(Boolean);
  },
};
