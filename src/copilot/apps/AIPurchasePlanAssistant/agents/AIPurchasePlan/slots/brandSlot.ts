import { Context, Entity, SlotConfig } from "@casstime/copilot-core";
import _ from "lodash";

export const brandSlot: SlotConfig = {
  filling(entities: Entity[], context: Context) {
    const brands = entities.filter((entity) => entity.name === "brand").map((entity) => entity.value);

    const beforeBrands = (_.get(context.slots, "brand", []) as string[]) || [];
    return [...new Set([...beforeBrands, ...brands])].filter(Boolean);
  },
};
