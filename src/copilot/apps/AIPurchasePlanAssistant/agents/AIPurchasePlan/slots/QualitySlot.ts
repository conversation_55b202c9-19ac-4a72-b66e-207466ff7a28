import { Context, Entity, SlotConfig } from "@casstime/copilot-core";
import _ from "lodash";

export const qualitySlot: SlotConfig = {
  filling(entities: Entity[], context: Context) {
    const qualities = entities.filter((entity) => entity.name === "quality").map((entity) => entity.value);

    const beforeQualities = (_.get(context.slots, "quality", []) as string[]) || [];

    return [...new Set([...beforeQualities, ...qualities])].filter(Boolean);
  },
};
