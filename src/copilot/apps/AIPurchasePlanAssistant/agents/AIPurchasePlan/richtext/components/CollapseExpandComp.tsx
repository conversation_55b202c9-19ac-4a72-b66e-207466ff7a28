import { React, View, Text, Image, Collapse, Trigger, StyleSheet } from "@casstime/copilot-xml";
import { CHECK_DETAIL_ICON, FOLD_ICON } from "../../constant";
import { IInquiryInfo } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { commandIntents } from "@/copilot/constants";
import { InquiryQuoteTabs } from "@/copilot/apps/GarageAssistant/agents/inquiry/enum";
import dayjs from "dayjs";
import { IAction } from "@casstime/copilot-core";

export function CollapseExpandComp({
  id,
  inquiryInfo,
  disableAction,
}: {
  id: string;
  inquiryInfo: IInquiryInfo;
  disableAction: boolean;
}) {
  const {
    brandLogo = "",
    carModelName = "",
    saleModelName = "",
    vin = "",
    inquiryId = "",
    needsNames = [],
    createdName = "",
    createdStamp = Date.now(),
  } = inquiryInfo;
  const carModelShowName = saleModelName || carModelName;
  const defaultStatus = "FOLD";

  const header =
    !brandLogo || !carModelShowName || !vin ? null : (
      <View style={styles.headView}>
        <Image uri={brandLogo} style={styles.headImage}></Image>
        <View style={styles.headContent}>
          <Text style={{ fontWeight: "bold" }}>{carModelShowName}</Text>
          <Text>{vin}</Text>
        </View>
      </View>
    );

  const query: {
    inquiryId: string;
    fromScreen: string;
    pageIndex: InquiryQuoteTabs;
    isForbidden?: string;
  } = {
    inquiryId,
    fromScreen: "AIChatScreen",
    pageIndex: InquiryQuoteTabs.PARTNAME,
  };
  if (disableAction) {
    query.isForbidden = "true";
  }
  const navigate = `cassapp://route/native/inquiry/quotationResult?query=${JSON.stringify(query)}`;
  const action: IAction = {
    type: "command",
    text: inquiryId,
    command: commandIntents.commonNavigate,
    params: { navigate },
  };

  const content = !inquiryId ? null : (
    <View action={action}>
      <Text style={styles.headContentText}>{needsNames.join("、")}</Text>
      <Text>询价单号: {inquiryId}</Text>
      <Text>询价时间: {dayjs(createdStamp || new Date().getTime()).format("YYYY-MM-DD HH:mm")}</Text>
      <Text>询价人: {createdName}</Text>
    </View>
  );

  return (
    <Collapse
      id={id}
      defaultState={defaultStatus}
      placeholderRender={
        <View style={styles.marginTopView}>
          {header}
          <Trigger for={id} displayWhen="FOLD" hidden="false" style={styles.trigger}>
            <View
              action={{
                text: "展开",
                type: "command",
                command: "CHANGE_RICHTEXT_STATE",
                params: { state: "UNFOLD", id },
              }}
              style={styles.triggerView}
            >
              <Text style={styles.triggerText}>展开</Text>
              <Image style={styles.triggerImage} uri={CHECK_DETAIL_ICON}></Image>
            </View>
          </Trigger>
        </View>
      }
    >
      <View style={styles.fold}>
        <View style={styles.marginTopView}>
          {header}
          <Trigger for={id} displayWhen="UNFOLD" hidden="false" style={styles.trigger}>
            <View
              action={{
                text: "收起",
                type: "command",
                command: "CHANGE_RICHTEXT_STATE",
                params: { state: "FOLD", id },
              }}
              style={styles.triggerView}
            >
              <Text style={styles.triggerText}>收起</Text>
              <Image style={styles.triggerImage} uri={FOLD_ICON}></Image>
            </View>
          </Trigger>
        </View>
        <View style={styles.containerView}>{content}</View>
      </View>
    </Collapse>
  );
}
const styles = StyleSheet.create({
  headView: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 16,
  },
  headImage: {
    width: 72,
    height: 72,
    marginRight: 20,
  },
  headContent: {
    flex: 1,
    marginRight: 10,
  },
  headContentText: {
    fontWeight: "bold",
    fontSize: 30,
    numberOfLines: 2,
  },
  fold: {
    borderRadius: 16,
    backgroundColor: "#F7F8FA",
  },
  marginTopView: {
    backgroundColor: "#F7F8FA",
    borderRadius: 16,
    padding: 16,
  },
  headerText: {
    fontSize: 32,
    lineHeight: 48,
  },
  trigger: {
    position: "absolute",
    right: 0,
    bottom: 0,
  },
  triggerView: {
    flexDirection: "row",
    alignItems: "center",
  },
  triggerText: {
    color: "#646566",
    fontSize: 22,
    lineHeight: 40,
  },
  triggerImage: {
    width: 15,
    height: 10,
  },
  containerView: {
    backgroundColor: "#F7F8FA",
    padding: 10,
    borderRadius: 16,
  },
  contentView: {
    flexDirection: "row",
    alignItems: "center",
  },
  contentImage: {
    width: 30,
    height: 30,
    marginRight: 12,
  },
  contentText: {
    color: "#979899",
    flex: 1,
    flexWrap: "wrap",
  },
});
