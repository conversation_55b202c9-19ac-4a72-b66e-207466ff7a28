import { React, View, Text, Image } from "@casstime/copilot-xml";
import { COMPLETED_ICON, THINKING_ICON } from "../../constant";

export function Loading({ contents, done }: { contents: string[]; done: boolean }) {
  return (
    <View style={{ backgroundColor: "#F7F8FA", borderRadius: 8 }}>
      {contents.map((content, index) => (
        <View style={{ flexDirection: "row", alignItems: "center" }}>
          <Image
            style={{ width: 30, height: 30, marginRight: 12 }}
            uri={done || index + 1 !== contents.length ? COMPLETED_ICON : THINKING_ICON}
          ></Image>
          <Text style={{ color: "#979899" }}>{content}</Text>
        </View>
      ))}
    </View>
  );
}
