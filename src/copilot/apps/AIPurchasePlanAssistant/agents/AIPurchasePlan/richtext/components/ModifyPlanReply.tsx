import { React, View, Text } from "@casstime/copilot-xml";
import { createViewDetailAction } from "../action";
import { IModifyPlanReplyParam } from "../interface";

export function ModifyPlanReply({ data }: { data: IModifyPlanReplyParam }) {
  const { planId, planGroupId, inquiryId, isChecked } = data;
  return (
    <View>
      <Text style={{ fontSize: 32, color: "#2A2B2C ", marginBottom: 16, marginTop: 20 }}>请告诉我你想怎么修改呢?</Text>
      <View style={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        borderTopWidth: 1,
        borderColor: '#DCDEE0 ',
        borderStyle: 'solid',
        paddingTop: 20
      }}>
        <Text style={{ fontSize: 28, color: "#2A2B2C", marginRight: 16 }}>你也可以手动修改</Text>
        <View
          action={createViewDetailAction({ planId, planGroupId, isChecked, inquiryId, text: '手动修改' })}
          style={{
            borderRadius: 44,
            borderWidth: 1,
            borderColor: '#E51E1E',
            borderStyle: 'solid',
          }}
        >
          <Text style={{ color: "#E51E1E", fontSize: 26, paddingTop: 4, paddingBottom: 4, paddingLeft: 24, paddingRight: 24 }}>手动修改</Text>
        </View>
      </View>
    </View>
  );
}
