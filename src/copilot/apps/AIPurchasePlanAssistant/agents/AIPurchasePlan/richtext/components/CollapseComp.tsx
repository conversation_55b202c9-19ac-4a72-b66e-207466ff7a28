import { React, View, Text, Image, Collapse, Trigger, StyleSheet } from "@casstime/copilot-xml";
import { COMPLETED_ICON, THINKING_ICON, CHECK_DETAIL_ICON, FOLD_ICON } from "../../constant";

export function CollapseComp({
  id,
  header,
  contents,
  done,
}: {
  id: string;
  header: string;
  contents: string[];
  done: boolean;
}) {
  const defaultStatus = done ? "FOLD" : "UNFOLD";
  return (
    <Collapse
      id={id}
      defaultState={defaultStatus}
      placeholderRender={
        <View style={styles.marginTopView}>
          <Text style={styles.headerText}>
            {header}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          </Text>
          <Trigger for={id} displayWhen="FOLD" hidden="false" style={styles.trigger}>
            <View
              action={{
                text: "查看详情",
                type: "command",
                command: "CHANGE_RICHTEXT_STATE",
                params: { state: "UNFOLD", id },
              }}
              style={styles.triggerView}
            >
              <Text style={styles.triggerText}>查看详情</Text>
              <Image style={styles.triggerImage} uri={CHECK_DETAIL_ICON}></Image>
            </View>
          </Trigger>
        </View>
      }
    >
      <View style={styles.marginTopView}>
        <Text style={styles.headerText}>{header}</Text>
        <Trigger for={id} displayWhen="UNFOLD" hidden="false" style={styles.trigger}>
          <View
            action={{ text: "收起", type: "command", command: "CHANGE_RICHTEXT_STATE", params: { state: "FOLD", id } }}
            style={styles.triggerView}
          >
            <Text style={styles.triggerText}>收起</Text>
            <Image style={styles.triggerImage} uri={FOLD_ICON}></Image>
          </View>
        </Trigger>
      </View>
      <View style={styles.containerView}>
        {contents.map((content, index) => (
          <View style={styles.contentView}>
            <Image
              style={styles.contentImage}
              uri={done || index + 1 !== contents.length ? COMPLETED_ICON : THINKING_ICON}
            ></Image>
            <Text style={styles.contentText}>{content}</Text>
          </View>
        ))}
      </View>
    </Collapse>
  );
}
const styles = StyleSheet.create({
  marginTopView: {
    marginTop: 24,
  },
  headerText: {
    fontSize: 32,
    lineHeight: 48,
    whiteSpace: "pre-wrap", // 保留空格和换行
  },
  trigger: {
    position: "absolute",
    right: 0,
    bottom: 0,
  },
  triggerView: {
    flexDirection: "row",
    alignItems: "center",
  },
  triggerText: {
    color: "#646566",
    fontSize: 22,
    lineHeight: 40,
  },
  triggerImage: {
    width: 15,
    height: 10,
  },
  containerView: {
    backgroundColor: "#F7F8FA",
    borderRadius: 20,
    padding: 10,
  },
  contentView: {
    flexDirection: "row",
    alignItems: "center",
  },
  contentImage: {
    width: 30,
    height: 30,
    marginRight: 12,
  },
  contentText: {
    color: "#979899",
    flex: 1,
    flexWrap: "wrap",
  },
});
