import { IMerchantClue } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { AI_CHAT_SCREEN, commandIntents, IMTeamId } from "@/copilot/constants";
import { React, View, Text, Button } from "@casstime/copilot-xml";

export const AIRecommendTips = ({ clue }: { clue: IMerchantClue }) => {
  return (
    <View>
      <Text style={{ marginTop: 24 }}>您可以：</Text>
      <View style={{ flexDirection: "row", alignItems: "center", marginLeft: 16 }}>
        <Text style={{ color: "#646566" }}>• 找专业导购协助</Text>
        <Button
          type="command"
          command={commandIntents.commonNavigate}
          textStyle={{ color: "#E51E1E" }}
          style={{ marginLeft: 12, borderColor: "#E51E1E" }}
          params={{
            navigate: "im/conversation",
            params: {
              teamId: IMTeamId.CASS_SERVICE,
              fromScreen: AI_CHAT_SCREEN,
              referer: AI_CHAT_SCREEN,
              clue,
            },
            platform: "native",
          }}
        >
          导购协助
        </Button>
      </View>
      <View style={{ flexDirection: "row", alignItems: "center", marginLeft: 16 }}>
        <Text style={{ color: "#646566" }}>• 告诉我你的诉求（如：只要本地的、要最便宜的），我帮您精准选购</Text>
      </View>
    </View>
  );
};
