import { React, Text } from "@casstime/copilot-xml";
import { IIntelligentPlan } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";

export function PlanReasonComp({ data }: { data: IIntelligentPlan[] }) {
  return <>
    {data.map((item) => {
      return item.reason ? <>
        <Text style={styles.title}>{`${item.name}: ${item.facilityName || item.storeName || '多商家方案'}`}{` (${item.showFeature})`}</Text>
        <Text style={styles.content}>{item.reason}</Text>
      </> : <></>
    })}
  </>
}

const styles = {
  title: {
    color: "#2A2B2C",
    fontSize: 30,
    fontWeight: 'bold',
    marginBottom: 12,
    marginTop: 40
  },
  content: {
    color: "#2A2B2C",
    fontSize: 24,
    lineHeight: 40,
  }
}