import { React, Text, Table, Row, Col, View, Line, StyleSheet } from "@casstime/copilot-xml";
import { createViewDetailAction } from "../action";
import { ButtonText } from "../action/interface";
import { ITableParam } from "../interface";
import { IPlanQuoteItem } from "@/clients/quote/interface";
import { indexMap } from "@/copilot/apps/GarageAssistant/agents/inquiry/enum";

export function TableComp({ params }: { params: ITableParam }) {
  const { tableData, planGroupId, needs } = params;

  // 补充缺货需求展示
  const tables = tableData.map((plan) => {
    const { quotes } = plan;
    const newQuotes: Partial<IPlanQuoteItem>[] = needs.map((need) => {
      const { decodeResultId, needsName, partsName = "" } = need;
      const quote = quotes?.find((q) => q.standardItemId === decodeResultId);
      if (!quote) {
        return { needsName, partsName };
      }
      return quote;
    });
    return { ...plan, quotes: newQuotes };
  });
  const quotes = tables[0]?.quotes || [];
  const tableLen = tables.length;

  const tableColHeights: number[] = new Array(quotes.length).fill(0);
  let tableHeaderHeight = 0;
  tables.forEach((plan) => {
    const { quotes, deliveryWarehouse, storeId, storeName, defaultEta, packagePrice } = plan;
    const headerHeight = [storeName, deliveryWarehouse, defaultEta, packagePrice].filter(Boolean).length * 30 + 60;
    tableHeaderHeight = Math.max(headerHeight, tableHeaderHeight);
    quotes?.forEach((quote, index) => {
      let count = 1;
      if (quote?.price) {
        // 有报价的条目
        count++;
        if (deliveryWarehouse === "多仓发货") count++;
        if (!storeId) count++;
        if (quote?.categoryOriginalAssort || quote?.spdFeatureDetail) count++;
      }
      tableColHeights[index] = Math.max(tableColHeights[index], count * 30 + 30);
    });
  });

  return (
    <Table style={tableLen === 1 ? styles.oneContainer : styles.container}>
      <Row>
        <Col style={styles.spaceCol}>
          <Text>&nbsp;</Text>
        </Col>
        {tables.map((item, index) => (
          <Col key={index} style={tableLen === 1 ? styles.oneTitleCol : styles.titleCol}>
            <Text style={styles.titleText} numberOfLines={2}>
              {`(${indexMap[index + 1]})${item.name}`}
            </Text>
          </Col>
        ))}
      </Row>
      <Row>
        <Col style={{ ...styles.rectangleCol, height: tableHeaderHeight }}>
          <View style={{ ...styles.rectangle, height: tableHeaderHeight - 2 }}>
            <Line xStart={0} yStart={0} xEnd={1} yEnd={1} stroke="#DCDEE0" strokeWidth={1}></Line>
            <Text style={styles.titleTextBottomLeft}>需求</Text>
            <Text style={styles.titleTextTopRight}>方案</Text>
          </View>
        </Col>
        {tables.map((plan, index) => (
          <Col key={index} style={{ width: 240, height: tableHeaderHeight }}>
            {plan.storeName && (
              <Text numberOfLines={1} style={styles.storeName}>
                {plan.positioningName || plan.storeName}
              </Text>
            )}
            {plan.deliveryWarehouse && <Text style={styles.warehouse}>{plan.deliveryWarehouse}</Text>}
            {plan.defaultEta && <Text style={styles.defaultEtaText}>{plan.defaultEta}</Text>}
            {!!plan.packagePrice && (
              <View style={styles.flexContainer}>
                <Text style={styles.totalPriceLabel}>总价：</Text>
                <Text style={styles.totalPriceValue}>¥{plan.packagePrice.toFixed(2)}</Text>
              </View>
            )}
          </Col>
        ))}
      </Row>

      {quotes.map((quote, quoteIndex) => (
        <Row key={quoteIndex}>
          <Col style={{ ...styles.firstCol, height: tableColHeights[quoteIndex] }}>
            <Text style={styles.partsName}>
              {`${quote.partsName || quote.needsName}${quote.quantity && quote.quantity > 1 ? `(x${quote.quantity})` : ""}`}
            </Text>
          </Col>
          {tables.map((plan, planIndex) => {
            const planQuote = plan.quotes?.[quoteIndex];
            return (
              <Col
                key={planIndex}
                style={
                  tableLen === 1
                    ? { width: 400, height: tableColHeights[quoteIndex] }
                    : { height: tableColHeights[quoteIndex] }
                }
              >
                {planQuote?.price ? (
                  <>
                    <Text numberOfLines={1} style={styles.qualityName}>
                      {planQuote.qualityName || ""}
                    </Text>
                    <Text style={styles.price}>¥{(planQuote.showPrice || planQuote.price)?.toFixed(2)}</Text>
                    {planQuote.categoryOriginalAssort || planQuote.spdFeatureDetail ? (
                      <Text numberOfLines={1} style={styles.orangeText}>
                        {planQuote.categoryOriginalAssort && "配套 "}
                        {planQuote.spdFeatureDetail}
                      </Text>
                    ) : (
                      <></>
                    )}
                    {plan.deliveryWarehouse === "多仓发货" && (
                      <Text style={styles.departureText} numberOfLines={1}>
                        {planQuote.locationName}
                      </Text>
                    )}
                    {!plan.storeId && (
                      <Text style={styles.storeText} numberOfLines={1}>
                        {planQuote.positioningName || planQuote.storeName}
                      </Text>
                    )}
                  </>
                ) : (
                  <Text style={styles.noQuote}>暂无报价</Text>
                )}
              </Col>
            );
          })}
        </Row>
      ))}

      <Row>
        <Col style={{ ...styles.firstCol, height: 150 }}>
          <Text>&nbsp;</Text>
        </Col>
        {tables.map((_, index) => (
          <Col key={index} style={{ height: 150 }}>
            <View
              action={createViewDetailAction({
                planId: _._id || "",
                planGroupId,
                text: ButtonText.VIEW_DETAIL,
                isChecked: false,
                inquiryId: tables[0].inquiryId || "",
              })}
            >
              <Text style={styles.button}>{ButtonText.VIEW_DETAIL}</Text>
            </View>
            <View
              action={createViewDetailAction({
                planId: _._id || "",
                planGroupId,
                text: ButtonText.GO_TO_PURCHASE,
                isChecked: true,
                inquiryId: tables[0].inquiryId || "",
              })}
            >
              <Text style={styles.button}>{ButtonText.GO_TO_PURCHASE}</Text>
            </View>
          </Col>
        ))}
      </Row>
    </Table>
  );
}

const styles = StyleSheet.create({
  oneContainer: {
    flex: 1,
    borderRadius: 20,
    borderColor: "#DCDEE0",
    backgroundColor: "#fff",
    width: 540,
    marginTop: 20,
  },
  container: {
    flex: 1,
    // borderWidth: 1,
    borderRadius: 20,
    borderColor: "#DCDEE0",
    backgroundColor: "#fff",
    width: "100%",
    marginTop: 20,
  },
  spaceCol: {
    backgroundColor: "#F9FAFC",
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    width: 150,
    height: 100,
  },
  firstCol: {
    backgroundColor: "#F9FAFC",
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    width: 150,
  },
  rectangleCol: {
    backgroundColor: "#F9FAFC",
    width: 150,
    height: 180,
    padding: 0,
  },
  rectangle: {
    width: 148,
    height: 180,
    backgroundColor: "#F9FAFC",
    overflow: "hidden", // 确保对角线不超出边界
  },
  orangeText: {
    color: "#FC6405",
    fontSize: 24,
    lineHeight: 32,
  },
  titleTextBottomLeft: {
    fontSize: 24,
    color: "#333333",
    position: "absolute" as const,
    left: 15,
    bottom: 15,
  },
  titleTextTopRight: {
    fontSize: 24,
    color: "#333333",
    position: "absolute" as const,
    right: 15,
    top: 15,
  },
  oneTitleCol: {
    backgroundColor: "#E2EFFB",
    width: 400,
    height: 100,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    padding: 5,
  },
  titleCol: {
    backgroundColor: "#E2EFFB",
    width: 240,
    height: 100,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    padding: 5,
  },
  titleText: {
    fontSize: 28,
    lineHeight: 36,
    color: "#333333",
    textAlign: "center",
  },
  planTitle: {
    color: "#20234F",
    fontSize: 24,
  },
  storeName: {
    color: "#2A2B2C",
    fontSize: 24,
    fontWeight: "bold",
    lineHeight: 32,
  },
  warehouse: {
    color: "#20234F",
    fontSize: 20,
    lineHeight: 28,
  },
  defaultEtaText: {
    fontSize: 24,
    color: "#2A2B2C",
    lineHeight: 30,
  },
  totalPriceLabel: {
    color: "#E51E1E",
    fontSize: 20,
  },
  totalPriceValue: {
    color: "#E51E1E",
    fontSize: 26,
    fontWeight: "bold",
  },
  flexContainer: {
    display: "flex",
    flexDirection: "row" as const,
    alignItems: "center",
  },
  partsName: {
    color: "#20234F",
    fontSize: 24,
    lineHeight: 32,
  },
  qualityName: {
    color: "#2A2B2C",
    fontSize: 22,
    lineHeight: 32,
  },
  price: {
    color: "#2A2B2C",
    fontSize: 26,
    fontWeight: "bold",
    lineHeight: 32,
  },
  departureText: {
    color: "#2A2A2B",
    fontSize: 24,
    lineHeight: 32,
  },
  storeText: {
    color: "#646566",
    fontSize: 24,
    lineHeight: 32,
  },
  noQuote: {
    color: "#2A2B2C",
    fontSize: 24,
    lineHeight: 26,
  },
  button: {
    color: "#008CF5",
    fontSize: 26,
    fontWeight: "bold",
    marginTop: 5,
    marginBottom: 5,
  },
});
