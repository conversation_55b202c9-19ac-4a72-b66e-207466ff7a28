import { React } from "@casstime/copilot-xml";
import { renderToXML } from "@casstime/copilot-xml";
import { Loading } from "./components/Loading";
import { CollapseComp } from "./components/CollapseComp";
import { TableComp } from "./components/Table";
import { ModifyPlanReply } from "./components/ModifyPlanReply";
import { IModifyPlanReplyParam, ITableParam } from "./interface";
import { AdditionalFormat } from "@casstime/copilot-core";
import { CollapseExpandComp } from "./components/CollapseExpandComp";
import { IInquiryInfo } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";

export function createLoadingXml(contents: string[], done = false) {
  return {
    type: "richtext",
    content: renderToXML(<Loading contents={contents} done={done}></Loading>),
  };
}

export function createCollapseXml(id: string, header: string, contents: string[], done = false) {
  const xml = renderToXML(<CollapseComp id={id} header={header} contents={contents} done={done}></CollapseComp>);
  return xml;
}

export function createInquiryCollapseXml(id: string, inquiryInfo: IInquiryInfo, disableAction = false) {
  const content = renderToXML(
    <CollapseExpandComp id={id} inquiryInfo={inquiryInfo} disableAction={disableAction}></CollapseExpandComp>
  );
  return {
    type: "richtext",
    content,
  };
}
export function createTableXml(params: ITableParam, disabledAction = false): AdditionalFormat {
  let content = renderToXML(
    <>
      <TableComp params={params}></TableComp>
    </>
  );
  if (disabledAction) {
    content = content.replace(/action="[^"]*"\s*/g, 'action="{}" ');
  }
  return {
    type: "richtext",
    content,
  };
}

export function createChangePlanReplyXml(data: IModifyPlanReplyParam) {
  return {
    type: "richtext",
    content: renderToXML(<ModifyPlanReply data={data}></ModifyPlanReply>),
  };
}
