import { ActionFactory } from "@/common/factory/ActionFactory";
import { AgentName, NluActionIntents, commandIntents } from "@/copilot/constants";
import { ICreateChangePlanActionParams, ICreateViewDetailActionParams } from "./interface";

/** 修改方案nlu */
export function createChangePlanAction(params: ICreateChangePlanActionParams) {
  const { planId, isChecked, inquiryId, planGroupId, name } = params;
  return ActionFactory.nlu(`修改${name}`, {
    intent: NluActionIntents.MODIFY_PLAN,
    agentName: AgentName.AIPurchasePlanAgent,
    slots: {
      planId,
      isChecked,
      inquiryId,
      planGroupId
    },
  });
}

/** 查看详情command */
export function createViewDetailAction(params: ICreateViewDetailActionParams) {
  const { planId, isChecked, text, inquiryId, planGroupId } = params;
  return ActionFactory.command(text, commandIntents.VIEW_PLAN_DETAIL, {
    params: {
      planId,
      isChecked,
      inquiryId,
      planGroupId
    },
  });
}