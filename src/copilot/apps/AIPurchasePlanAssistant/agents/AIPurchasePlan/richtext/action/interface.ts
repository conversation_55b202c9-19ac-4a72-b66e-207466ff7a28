export interface ICreateChangePlanActionParams {
  planId: string; // 方案id，一个方案组有多个方案
  isChecked: boolean;
  inquiryId: string;
  planGroupId: string; // 方案组id
  name: string; // 方案名称
}

export interface ICreateViewDetailActionParams {
  planId: string;
  isChecked: boolean;
  text: string;
  inquiryId: string;
  planGroupId: string;
}

export enum ButtonText {
  VIEW_DETAIL = "查看详情",
  GO_TO_PURCHASE = "前往采购",
}