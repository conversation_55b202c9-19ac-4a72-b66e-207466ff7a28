import { IIntelligentPlan } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface/IIntelligentPlan";
import { INeedDecodeItem } from "@/service/interface";
export interface IModifyPlanReplyParam {
  brands: string[];
  contentSource: string;
  inquiryId: string;
  isChecked: boolean;
  planId: string;
  planGroupId: string;
  makerType: string;
}
export interface ITableParam {
  tableData: IIntelligentPlan[];
  needs: INeedDecodeItem[];
  isOpenInvoice: boolean;
  planGroupId: string;
}
