import { isDev, stringifyHistory, stringifyMessage } from "@/common/utils";
import { executePrompt, waitStreamContent } from "@/copilot/helpers";
import { IClassifier } from "@casstime/copilot-core";
import { AIPurchasePlanIntents } from "../intent";
import logger from "@/common/logger";
import { FeatureTogglesEnum } from "@/common/enums";
import { featureTogglesClient } from "@/copilot/apps/GarageAssistant/agents/inquiry/clients/FeatureTogglesClient";

export const intentClassifier: IClassifier = {
  id: "INTENT_CLASSIFIER",
  async classify(context) {
    try {
      const companyId = context.payload.companyId;
      const { enabled } = await featureTogglesClient.getIsPilotArea(
        companyId,
        FeatureTogglesEnum.AI_PURCHASE_INTERACTION
      );
      if (!enabled && !isDev()) {
        return AIPurchasePlanIntents.交互式推荐;
      }
      const stream = await executePrompt("采购方案/意图分类提示词", {
        history: stringifyHistory(context.historyMessages, 5, { system: "助手", user: "用户" }),
        input: stringifyMessage(context.lastMessage),
      });
      const content = await waitStreamContent(stream);
      // console.log(content);
      if (content.startsWith("是")) {
        return AIPurchasePlanIntents.交互式推荐;
      }
      return "其他";
    } catch (err) {
      logger.warn("意图分类失败", err);
      return AIPurchasePlanIntents.交互式推荐;
    }
  },
};
