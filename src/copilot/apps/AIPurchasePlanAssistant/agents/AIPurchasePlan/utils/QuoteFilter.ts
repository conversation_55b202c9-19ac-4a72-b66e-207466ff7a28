import { IDecodeResultsItem, IQuotationProductsItem } from "@/clients/aggregationInquiry/interfaces/IPostnquiryV3DTO";
import { IPlanQuoteItem } from "@/clients/quote/interface";
import _ from "lodash";
export type QuoteItem = IQuotationProductsItem & IDecodeResultsItem & { standardItemId: string };

/**
 * - 等于 (`=`)
   - 不等于 (`!=`)
   - 大于 (`>`)
   - 大于等于 (`>=`)
   - 小于等于 (`<=`)
   - 包含 (`contains`)
   - 不包含 (`not contains`)
   - 在范围内 (`in range`)
   - 不在范围内 (`not in range`)
   - 在列表中 (`in list`)
   - 不在列表中 (`not in list`)
 */
type IOperator =
  | "="
  | "!="
  | ">"
  | ">="
  | "<="
  | "contains"
  | "not contains"
  | "in range"
  | "not in range"
  | "in list"
  | "not in list";

interface IRule<T = unknown> {
  operator: IOperator;
  value: T;
}

type Rules = {
  quality?: IRule<string>;
  brand?: IRule<string>;
  price?: IRule<number>;
  merchant?: IRule<string>;
  location?: IRule<string>;
};

export type SkuRules = Record<string, Rules>;

/**
 * quoteFilter
 * @param skuRules
 * @returns
 * @example
 * ```ts
 * const filter = new QuoteFilter({...})
 * const quotes = filter.filter(items)
 * ```
 */
export class QuoteFilter {
  private readonly partsRules: SkuRules;
  constructor(skuRules: SkuRules) {
    this.partsRules = skuRules;
  }

  private isMatch(value: any, rule?: IRule<any>) {
    if (!rule) {
      return true;
    }
    if (rule.value === "不限" || /待确认/.test(rule.value)) {
      return true;
    }
    switch (rule.operator) {
      case "=":
        return value === rule.value;
      case "!=":
        return value !== rule.value;
      case ">":
        return value > rule.value;
      case ">=":
        return value >= rule.value;
      case "<=":
        return value <= rule.value;
      case "contains":
        return value.toString().includes(rule.value.toString());
      case "not contains":
        return !value.toString().includes(rule.value.toString());
      case "in range":
        return value >= rule.value[0] && value <= rule.value[1];
      case "not in range":
        return value < rule.value[0] || value > rule.value[1];
      case "in list":
        return rule.value.includes(value);
      case "not in list":
        return !rule.value.includes(value);
      default:
        return true;
    }
  }

  normalizeQuality(quote: IPlanQuoteItem & { isOEMBrand?: boolean }) {
    const quality = quote.partsBrandQualityName || "";
    if (quality.includes("原厂")) {
      return "原厂";
    }
    if (quality.includes("拆车")) {
      return "拆车";
    }
    if (quote.isOEMBrand) {
      return "配套";
    }
    if (quality.includes("品牌")) {
      return "品牌";
    }
    return "其他";
  }

  filter(items: IPlanQuoteItem[]) {
    const results: IPlanQuoteItem[] = [];
    const group = _.groupBy(items, (item) => item.partsName);
    for (const [partsName, quotes] of Object.entries(group)) {
      const rules = this.partsRules[partsName] || this.partsRules["未指定"] || {};
      let filtered = quotes.filter((item) => {
        const fact = {
          quality: this.normalizeQuality(item),
          brand: item.brandName,
          price: item.price,
          merchant: item.storeName,
          warehouse: item.locationName,
        };
        return Object.keys(fact).every((key) => {
          const value = fact[key as keyof typeof fact];
          const rule = rules[key as keyof typeof rules];
          if (!rules) {
            return true;
          }
          return this.isMatch(value, rule);
        });
      });
      // 如果过滤后，没有符合规则的报价，放宽条件
      if (!filtered.length) {
        filtered = quotes;
      }
      results.push(...filtered);
    }
    return results;
  }
}
