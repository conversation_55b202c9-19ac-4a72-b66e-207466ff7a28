import { purchaseClients } from "../clients/PurchaseClients";
import { merchantClient } from "../clients/MerchantClient";
import { inquiryClient as aiInquiryClient } from "../clients/InquiryClient";
import { PurchaseFormData } from "../forms";
import { IFetchMakePurchasePlanPayload, QualityType } from "../interface";
import { IStoresAddress } from "../planrecommender/interfaces";
import { IQuoteFromInquiryBodyDetail } from "../interface/inquiry";
import { quoteClient } from "@/clients/quote";
import productClient from "@/clients/product";
import { IPlanQuoteItem } from "@/clients/quote/interface";
import { inquiryService as inquiryCommonService } from "@/service";
import { inquiryService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/InquiryService";

class PurchaseService {
  public async fetchMakePurchasePlan(json: IFetchMakePurchasePlanPayload) {
    return await purchaseClients.fetchMakePurchasePlan(json);
  }
  public async getEntityFilterMsg(formData: PurchaseFormData) {
    const { qualities = [], brands = [], stores = [] } = formData;
    let filterMsg = "";
    if (qualities.length > 0) {
      filterMsg += `【品质】${qualities.join("、")}\n`;
    }
    if (brands.length > 0) {
      filterMsg += `【品牌】${brands.join("、")}\n`;
    }
    if (stores.length > 0) {
      filterMsg += `【商家】${stores.join("、")}\n`;
    }
    return filterMsg;
  }
  public async getIndustryKnowledge(inquiryId: string) {
    return await purchaseClients.getStandardItemKnowledge(inquiryId);
  }

  public async getStoresFacilityAddressInfo(inquiryId: string, storeIds: string[]): Promise<IStoresAddress> {
    const basicInfo = await aiInquiryClient.getInquiryBasicInfo(inquiryId);
    if (!basicInfo) {
      return {};
    }
    const geoId = basicInfo.villageGeoId || basicInfo.geoCityId || "";
    const storeFacilitys = await merchantClient.searchStoresInfo({ geoId, storeIds });
    return {
      basicInfo,
      storeFacilitys,
    };
  }

  public async getQuoteFromInquiryBodyDetail(inquiryId: string): Promise<IQuoteFromInquiryBodyDetail> {
    const [quotesRes, quoteStores, { needDecodeList }, inquiryDetail] = await Promise.all([
      quoteClient.getQuoteResult(inquiryId),
      productClient.getInquirySortStores(inquiryId),
      inquiryCommonService.getNeedDecodeList(inquiryId),
      inquiryService.getInquiryInfoFromService(inquiryId),
    ]);
    if (!quotesRes?.length) return { quotes: [] };
    const quotationProductIds = quotesRes.map((quote) => quote.userNeedsItemId);
    const spdOriginal = await quoteClient.getSPDOriginal({ inquiryId, quotationProductIds });

    const quotes: IPlanQuoteItem[] = quotesRes.map((quote) => {
      const { partsBrandQuality, brandName, storeId, userNeedsItemId, partType, resolveResultId, btPrice, price } =
        quote;
      const partsBrandQualityName = QualityType[partsBrandQuality as keyof typeof QualityType];
      const partsBrandQualityAndBrandName = `${brandName}:${partsBrandQuality}`;
      const qualityName = partType === "BRAND_PARTS" ? brandName : partsBrandQualityName;
      const positioningName = quoteStores.find((item) => item.storeId === storeId)?.positioningName || "";
      const spdFeatureDetail =
        spdOriginal?.find((item) => item.quotationProductId === userNeedsItemId)?.spdFeatureDetail || "";
      const need = needDecodeList.find((item) => item.decodeResultId === resolveResultId);
      const { needId = "", needsName = "", partsName = "" } = need || {};
      const showPrice = inquiryDetail.openInvoiceType === "NO" ? btPrice : price;

      return {
        ...quote,
        partsBrandQualityName,
        partsBrandQualityAndBrandName,
        positioningName,
        spdFeatureDetail,
        qualityName,
        needId,
        needsName,
        quotationProductId: userNeedsItemId,
        decodeResultId: resolveResultId,
        standardItemId: resolveResultId,
        partsName,
        showPrice,
      };
    });

    return { quotes };
  }
}

export const purchaseService = new PurchaseService();
