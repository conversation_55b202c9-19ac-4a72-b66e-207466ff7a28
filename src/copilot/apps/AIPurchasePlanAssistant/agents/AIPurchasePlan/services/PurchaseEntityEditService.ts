import { Entity } from "@casstime/copilot-core";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
import { LLMMessageTemplate, LLMRunnableBuilder } from "@/copilot/helpers/llm/tools";
import logger from "@/common/logger";
import { EntityData } from "../interface";
import _ from "lodash";
import { createDataProvider } from "../planrecommender/factory";
import { DataProviderKey } from "../planrecommender/interfaces";
import { IPlanQuoteItem } from "@/clients/quote/interface";

export type EditData = { qualities: string[]; brands: string[]; stores: string[] };
export type EditAction = {
  type: "quality" | "brand" | "store";
  action: "delete" | "add";
  entities: string[];
};

const prompt = `
## 任务
请根据用户输入，分析用户想对表单进行哪些操作，具体地，根据输入，提取实体及操作意图，并结果输出成JSON。

## 要求
可提取的实体类型（type）有：**品质（quality）**、**品牌（brand）**和**商家（store）**。
可提取的操作意图（action）有：**增加（add）**、**删除（delete）**。

## 规则：

### 品质
1. 品质(quality)可能的值只能是 "原厂"、"配套"、"品牌"、"拆车件"、"原厂再制造件"，
2. 用户如果说“原厂”、 "正品"、“正厂”，则将品质设置称“原厂”。如果说 "品牌"、“副厂”，则将品质设置成“品牌”。如果说 "拆车"、“二手”，则将品质设置称“拆车件”。

### 品牌
1. 品牌(brand)是指汽车配件品牌，例如：博世、宝马、贝偲特、威巴克、拓普、采埃孚伦福德等等

### 商家
1. 商家(store)是指汽车配件销售商家，例如：东莞悦宝行汽配、东莞宝乾汽配、开思严选专营店-广州博辉、东莞悦宝行汽配、东莞国钜汽配、宝马直供认证店（广东）、贝偲特奔宝汽配全国店等等

### 其他规则
1. 实体类型(type)包含 "quality"（品质）、"brand"（品牌）、"store"（商家）。
2. action 可能的值只能是 "add" 及 "delete"，不能有其他action。
3. 如果用户消息没有明显的 "delete" 意图，则 action 应为 "add"
4. 如果用户表达了删除、不要，则 action 应为 "delete"
5. 如果用户直接输入品质、品牌或者商家时，action 置为 "add"
6. 直接输出JSON数组，一定要确保格式正确，禁止输出其他代码

## 输出格式：
  [
    {{"action": "<action>", "type": "<type>", "entities": ["<entity>"]}}
  ]

请结合以上规则及示例，直接输出正确的JSON，并确保格式正确。
用户消息：{message}
输出JSON：
`.trim();

/**
 * 编辑表单
 */
export class PurchaseEntityEditService {
  generator = LLMRunnableBuilder.create()
    .addPrompt(LLMMessageTemplate.create("user").addText(prompt.trim()))
    .build<EditAction[]>({ type: "json" });

  /**
   * 根据用户输入解析出要进行的编辑操作
   * @param FormData 当前的表单数据
   * @param input 用户输入
   * @param entities 从用户输入中解析出的实体
   */
  async parseEditEntityActions(input: string): Promise<EditAction[]> {
    const content = input?.replace(/\//g, " ");
    const actions = await this.generator
      .invoke(
        {
          message: content,
        },
        { callbacks: [llmLogCallback] }
      )
      .catch((err) => {
        logger.error(err);
        return [];
      });

    return actions;
  }

  /**
   * 根据编辑操作返回更新后的表单数据
   * @param formData
   * @param actions
   */
  applyEditActions(formData: EditData, actions: EditAction[]) {
    const result = { ...formData };
    const qualities = formData.qualities || [];
    const brands = formData.brands || [];
    const stores = formData.stores || [];
    for (const { action, type, entities } of actions) {
      if (type === "quality") {
        if (action === "delete") {
          result.qualities = qualities.filter((e) => !entities.includes(e));
        }
        if (action === "add") {
          result.qualities = [...new Set([...qualities, ...entities])];
        }
      }
      if (type === "brand") {
        if (action === "delete") {
          result.brands = brands.filter((e) => !entities.includes(e));
        }
        if (action === "add") {
          result.brands = [...new Set([...brands, ...entities])];
        }
      }
      if (type === "store") {
        if (action === "delete") {
          result.stores = stores.filter((e) => !entities.includes(e));
        }
        if (action === "add") {
          result.stores = [...new Set([...stores, ...entities])];
        }
      }
    }
    const qualityEntities: Entity[] = result.qualities.map((quality) => ({
      name: "quality",
      value: quality,
    }));
    const brandEntities: Entity[] = result.brands.map((brand) => ({
      name: "brand",
      value: brand,
    }));
    const storeEntities: Entity[] = result.stores.map((store) => ({
      name: "store",
      value: store,
    }));
    return { qualityEntities, brandEntities, storeEntities };
  }

  async getFullEntityData(inquiryId: string): Promise<EntityData> {
    const dataProvider = createDataProvider(inquiryId);
    // 获取报价
    const quoteDetail = await dataProvider.getItem(DataProviderKey.quoteDetail);
    const quotes = quoteDetail?.quotes || [];

    const extractUniqueEntities = (keyName: keyof IPlanQuoteItem, keyCode: keyof IPlanQuoteItem) =>
      _.unionBy(
        quotes.map((quote) => ({
          displayName: quote[keyName] as string,
          code: quote[keyCode] as string,
        })),
        "code"
      );

    const qualities = extractUniqueEntities("partsBrandQualityName", "partsBrandQuality");
    const brands = extractUniqueEntities("brandName", "brandId");
    const stores = extractUniqueEntities("storeName", "storeId");

    return { qualities, brands, stores };
  }
}

export const purchaseEntityEditService = new PurchaseEntityEditService();
