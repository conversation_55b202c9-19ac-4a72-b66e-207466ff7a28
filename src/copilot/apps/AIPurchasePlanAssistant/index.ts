import { Application } from "@casstime/copilot-core";
import AIPurchasePlanAgent from "./agents/AIPurchasePlan";
import { AppName } from "@/common/enums";
import backgroundAgent from "@/copilot/apps/GarageAssistant/agents/background";

const app = new Application(AppName.AIPurchasePlanAssistant);

const DEFAULT_SCENE = "AI_PURCHASE_PLAN";

app.setSceneClassifier({
  id: "AIPurchasePlanClassifier",
  classify: async () => {
    return DEFAULT_SCENE;
  },
});

// 设置后台Agent
app.setBackgroundAgent(backgroundAgent);

app.route(DEFAULT_SCENE, AIPurchasePlanAgent);

export default app;
