import { AgentName, commandIntents } from "@/copilot/constants";
import { Agent, MessageFactory } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { queryHandler } from "./handlers/query";
import _ from "lodash";
import { stringifyMessage } from "@/common/utils/message";
import { drawHandler } from "./handlers/draw";
import { restartHandler } from "./handlers/restart";

/**
 * 发布询价Agent
 */
const agent = new Agent(AgentName.fallbackAgent);

/**
 * 第一次进入Agent
 */
agent.onEnter(() => {
  logger.info("进入Fallback Agent");
});

/**
 * 离开当前Agent
 */
agent.onLeave(() => {
  logger.info("离开Fallback Agent");
});

/**
 * 设置意图解析器
 */
agent.setIntentParser(async (context) => {
  const text = stringifyMessage(context.lastMessage, false);
  if (text === "重新开始") {
    context.setIntent("重新开始");
    return;
  }
  if (/(饼图|图表|柱状图|折线图)|可视化/.test(text)) {
    context.setIntent("可视化");
  } else {
    context.setIntent("问答");
  }
});

agent.handleCommand(commandIntents.inquiryStart, async (context) => {
  // 查询最后一条展示的消息，避免重复发送欢迎语
  const lastShowMessage = _.last(context.historyMessages);
  if (lastShowMessage?.extra?.isGreet) return;
  await context.next("重新开始");
});

agent.handle("可视化", drawHandler);

agent.handle("重新开始", restartHandler);

agent.handle(["问答"], queryHandler);

export default agent;
