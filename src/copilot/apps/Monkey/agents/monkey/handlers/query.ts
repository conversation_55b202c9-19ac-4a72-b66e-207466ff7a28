import { doubao_chat } from "@/clients/llm";
import { LLMMessageTemplate, LLMRunnableBuilder } from "@/copilot/helpers/llm/tools";
import { stringifyHistory, stringifyMessage } from "@/common/utils/message";
import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import duckdb from "duckdb";
import { Types } from "mongoose";

const db = new duckdb.Database(":memory:");

function executeQuery(query: string) {
  return new Promise<any[]>((resolve, reject) => {
    db.connect().all(query, (err, rows) => {
      if (err) {
        console.error(err);
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

const prompt = `
你是一个专业的数据库工程师，擅长将自然语言问题转换为 SQL 查询。请根据以下历史对话为用户问题生成相应的 SQL 查询语句。

<chat_history>
{chat_history}
</chat_history>

用户问题：{question}

请确保生成的 SQL 查询语句符合以下要求：
1. 使用标准的 SQL 语法。
2. 假设数据库表结构如下：
    message_analysis 表包含以下字段：
    - buyer (VARCHAR): 买家(维修厂)名称
    - seller (VARCHAR): 卖家(供应商、商家)名称
    - time_range (INT): 时间范围（以秒为单位）
    - is_resolved (VARCHAR): 是否已解决（如 "已解决" 或 "不确定"）
    - user_emotion (VARCHAR): 用户情感（如 "中性"）
    - seller_emotion (VARCHAR): 卖家情感（如 "中性" 或 "正面"）
    - stage (VARCHAR): 交易阶段（如 "售前" 或 "不确定"）
    - guidedToOffline (VARCHAR): 是否引导至线下（如 "有" 或 "无"）
    - part_names (VARCHAR): 零件(配件)名称（多个零件用逗号分隔）
    - has_image (BOOLEAN): 是否有图片（1 表示有，空表示无）
    - has_voice (BOOLEAN): 是否有语音（1 表示有，空表示无）
    - has_video (BOOLEAN): 是否有视频（1 表示有，空表示无）
3. 如果问题需要聚合函数（如 COUNT、SUM、AVG 等），请在查询中使用。
4. 如果问题需要排序或分组，请在查询中使用 ORDER BY 或 GROUP BY。
5. 生成的sql包裹在sql代码块中。
`.trim();
// 有多少对话在咨询机油

function replaceBigInt(json: any) {
  return JSON.parse(
    JSON.stringify(
      json,
      (key, value) => (typeof value === "bigint" ? value.toString() : value) // return everything else unchanged
    )
  );
}

const template = LLMMessageTemplate.create("user", prompt);
const sqlRunnable = LLMRunnableBuilder.create(doubao_chat).addPrompt(template).build({ type: "string" });
const replyRunnable = LLMRunnableBuilder.create(doubao_chat)
  .addPrompt(template)
  .addPrompt(LLMMessageTemplate.create("assistant", "{assistant_reply}"))
  .addPrompt(
    LLMMessageTemplate.create("user", "这条SQL执行结果是：{data}\n请组织语言回复用户问题,有需要时，可以用markdown展示")
  )
  .build({ type: "string" });

export const queryHandler: IHandlerFunction = async (context) => {
  context.setReplyMode("stream");
  let factory = MessageFactory.with({ id: new Types.ObjectId().toString(), extra: { system_tips: true } });
  let msg = factory.markdown("正在生成SQL...");
  context.reply(msg);
  const question = stringifyMessage(context.lastMessage, false);
  const chat_history = stringifyHistory(
    context.historyMessages.filter((msg) => msg.extra?.system_tips !== true),
    5,
    { user: "用户", system: "助手" }
  );
  const sqlStream = await sqlRunnable.stream({
    question,
    chat_history,
  });

  const sqlTokens: string[] = [];
  for await (const event of sqlStream) {
    sqlTokens.push(event);
    if (/```sql\n(.*?)\n```/s.test(sqlTokens.join(""))) {
      break;
    }
  }
  const assistant_reply = sqlTokens.join("");
  console.log(assistant_reply);
  // 提取结果中的 SQL 查询语句
  let sql = assistant_reply.match(/```s?q?l?\n(.*?)\n```/s)?.[1];
  if (sql) {
    sql = sql.replace(/from\s+message_analysis/gi, "from read_csv('./data/data.csv')");
    context.reply(factory.markdown("**正在执行SQL**\n```\n" + sql + "\n```\n"));

    factory = MessageFactory.with({ id: new Types.ObjectId().toString() });
    msg = factory.markdown("正在执行SQL...", {});
    context.reply(msg);
    const data = replaceBigInt(await executeQuery(sql));
    const msgStream = await replyRunnable.stream({
      question,
      assistant_reply,
      chat_history,
      data: JSON.stringify(data.slice(0, 10), null, 2),
    });
    const tokens: string[] = [];
    for await (const event of msgStream) {
      tokens.push(event);
      context.reply(factory.markdown(tokens.join("")));
      console.log(event);
    }
    const txt = tokens.join("");
    context.reply(factory.markdown(txt));
  }
};
