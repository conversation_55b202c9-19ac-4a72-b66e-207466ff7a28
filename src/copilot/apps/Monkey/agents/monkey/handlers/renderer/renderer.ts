// import path from "path";
// import * as echarts from "echarts";
// import { createCanvas, registerFont } from "canvas";

// const fontDir = path.resolve(process.cwd(), "./fonts");

// registerFont(`${fontDir}/SourceHanSansCN-Regular.otf`, {
//   family: "SourceHanSansCN-Regular",
// });

// echarts.setPlatformAPI({
//   createCanvas: createCanvas as any,
// });

export function renderCharts(output = { width: 800, height: 600, fontSize: 12 }, options: any) {
  return Buffer.from("");
  // const canvas = createCanvas(+output.width, +output.height);
  // const ctx = canvas.getContext("2d");
  // ctx.font = output.fontSize + "px";
  // const chart = echarts.init(canvas as any, null, { devicePixelRatio: 2 });
  // options.animation = false;
  // options.textStyle = {
  //   fontSize: output.fontSize,
  //   fontFamily: "SourceHanSansCN-Regular",
  // };
  // options.backgroundColor = options.darkMode ? "#1f1f1f" : "#fff";
  // chart.setOption(options); // 就是echarts的options
  // const buffer = (chart.getDom() as any).toBuffer() as Buffer; // 返回buffer
  // return buffer;
}
