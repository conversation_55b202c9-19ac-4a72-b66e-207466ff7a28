import { doubao_chat } from "@/clients/llm";
import { LLMMessageTemplate, LLMRunnableBuilder } from "@/copilot/helpers/llm/tools";
import { stringifyHistory, stringifyMessage } from "@/common/utils/message";
import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { Types } from "mongoose";
import { renderCharts } from "./renderer/renderer";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
const prompt = `
你是一个专业的可视化工程师，擅长写ECharts,非常熟悉ECharts Options。请从历史对话中提取数据，为用户问题选择合适的图表类型，生成ECharts Options。

<chat_history>
{chat_history}
</chat_history>

用户问题：{question}

请确保生成的 options 配置可以直接用于 ECharts 实例化，输出到json代码块内。
`.trim();
// 有多少对话在咨询机油

const template = LLMMessageTemplate.create("user", prompt);
const drawRunnable = LLMRunnableBuilder.create(doubao_chat).addPrompt(template).build({ type: "string" });

export const drawHandler: IHandlerFunction = async (context) => {
  context.setReplyMode("stream");
  const factory = MessageFactory.with({ id: new Types.ObjectId().toString() });
  try {
    const msg = factory.markdown("正在生成图表...");
    context.reply(msg);
    const question = stringifyMessage(context.lastMessage, false);
    const chat_history = stringifyHistory(context.historyMessages, 5, { user: "用户", system: "助手" });
    const output = await drawRunnable.invoke({ question, chat_history }, { callbacks: [llmLogCallback] });
    const options = output.match(/```j?s?o?n?\n(.*?)\n```/s)?.[1] || output;
    const buffer = renderCharts({ width: 640, height: 640, fontSize: 14 }, JSON.parse(options));
    const base64url = buffer.toString("base64");
    console.log(base64url);
    context.reply(factory.image("data:image/png;base64," + base64url, {}));
  } catch (err) {
    console.error(err);
    context.reply(factory.markdown("生成图表失败:\n" + err));
  }
};
