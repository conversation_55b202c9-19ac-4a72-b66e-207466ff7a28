import { chatglm_chat } from "@/clients/llm";
import { LLMMessageTemplate, LLMRunnableBuilder } from "@/copilot/helpers/llm/tools";
import { stringifyHistory, stringifyMessage } from "@/common/utils/message";
import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { Types } from "mongoose";

const runnable = LLMRunnableBuilder.create(chatglm_chat)
  .addPrompt(
    LLMMessageTemplate.create(
      "system",
      `你是个助手应用，名字叫“莎士比亚的猴子”，能够帮助用户分析表的数据。
请根据历史聊天记录响应用户。
<chat_history>
{chat_history}
</chat_history>
要求：
1. 响应用户时，请使用莎士比亚的风格。
2. 响应用户时，请使用中文。
3. 响应用户时，保持回复简短，3秒内完成响应。
      `
    )
  )
  .addPrompt(LLMMessageTemplate.create("user", "<user_input>"))
  .build({ type: "string" });

export const restartHandler: IHandlerFunction = async (context) => {
  const chatHistory = stringifyHistory(
    context.historyMessages.filter((msg) => msg.type !== "command"),
    4,
    { system: "莎士比亚的猴子", user: "用户" }
  );
  const userInput = stringifyMessage(context.lastMessage, false);
  const stream = await runnable.stream({ chat_history: "", user_input: userInput });
  const factoryWithId = MessageFactory.with({ id: new Types.ObjectId().toString() });
  const tokens = [];
  for await (const token of stream) {
    tokens.push(token);
    context.reply(factoryWithId.markdown(tokens.join("")));
  }
  context.reply(factoryWithId.markdown(tokens.join("")));
  // 重置session
  await context.recreateSession(true);
  // 补一条命令消，将新的sessionId带给前端
  context.reply(MessageFactory.command("__restart__", {}));
};
