import { AdditionalFormat } from "@casstime/copilot-core";

export function createTips(type: AdditionalFormat["type"], text: string = "") {
  if (!text) return null;
  const content = `
    <view>
      ${text}
    </view>
  `;
  return { type, content };
}

export function createPartExpertGreetTips() {
  return `
    <text style="font-weight: bold;">您可以这样问我：</text>
    <text style="color: #646566;margin-left: 16;">1. 宝马刹车片的配套品牌是什么</text>
    <text style="color: #646566;margin-left: 16;">2. 奥迪机油格</text>
    </view>
  `;
}
