import { Agent<PERSON><PERSON>, AI_DISCLAIMER, commandIntents } from "@/copilot/constants";
import { AdditionalFormat, Agent, IMessage, MessageFactory } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { Types } from "mongoose";
import FastGptApi, { ResponseEvent } from "@/common/clients/FastGptApi";
import config from "@casstime/config";
import { PartExpertIntents } from "./intent";
import { createPartExpertGreetTips, createTips } from "./xml";
import { messageService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/MessageService";
import { extractVinCodeEntities } from "@/copilot/helpers";
import { inquiryClient } from "@/copilot/apps/GarageAssistant/agents/inquiry/clients/InquiryClient";

/**
 * Agent
 */
const agent = new Agent(AgentName.partExpert);

// 配置 fastgpt
const fastGptApi = new FastGptApi({
  baseUrl: config.get("FASTGPT_BASE_URL"),
  apiKey: config.get("FASTGPT_PARTEXPERTFAQ_KEY"),
});

/**
 * 第一次进入Agent
 */
agent.onEnter(() => {
  logger.info("进入 partExpertAgent");
});

/**
 * 离开当前Agent
 */
agent.onLeave(() => {
  logger.info("离开 partExpertAgent");
});

/**
 * 设置意图解析器
 */
agent.setIntentParser(async (context) => {
  context.setIntent(PartExpertIntents.问答);
});

agent.handleCommand(commandIntents.inquiryStart, async (context) => {
  // 查询最后一条展示的消息，避免重复发送欢迎语
  const lastShowMessage = (await messageService.getLastShowMessage(context.payload)) as IMessage;
  if (lastShowMessage?.extra?.isGreet) return;
  const tipsContent = createPartExpertGreetTips();
  const tips = createTips("richtext", tipsContent) as AdditionalFormat;
  context.reply(
    MessageFactory.text("嗨，欢迎来找我。我是配件专家，可以告诉您配套品牌信息", {
      extra: { isGreet: true },
      tips,
    })
  );
});

agent.handle(PartExpertIntents.问答, async (context) => {
  const { lastMessage, sessionId } = context;
  if (lastMessage.type === "text" || lastMessage.type === "voice") {
    const messageId = new Types.ObjectId().toString();
    const factory = MessageFactory.with({ id: messageId });
    const tokens: string[] = [];
    context.setReplyMode("stream");
    const doSend = async (ev: ResponseEvent) => {
      console.log("ev_result->", JSON.stringify(ev));
      if (ev.event === "message") {
        tokens.push(ev.answer);
      }
      if (tokens.length > 2) {
        context.reply(factory.markdown(tokens.join("")));
      }
      if (ev.event === "message_end") {
        context.reply(
          factory.markdown(tokens.join(""), {
            disclaimer: AI_DISCLAIMER,
          })
        );
      }
    };

    let input = lastMessage.content || "";
    try {
      const entities = extractVinCodeEntities(input);
      const vinCode = entities.find((entity) => entity.name === "vinCode")?.value;
      if (vinCode) {
        try {
          const { list: carModelNameRes } = await inquiryClient.getVinCarModel(vinCode || "");
          if (carModelNameRes?.length) {
            const carModel = carModelNameRes[0].model;
            if (carModel) {
              input = `${input}\nPS: 车架号 ${vinCode} 对应的车型是 ${carModel}`;
            }
          }
        } catch (err) {
          logger.error("配套专家获取车型信息失败", err);
        }
      }
      await fastGptApi.sendChatMessage(
        {
          inputs: {},
          query: input,
          platform: "fastgptChat",
          conversationId: sessionId,
          user: lastMessage.fromUser!,
        },
        (event) => {
          doSend(event);
        }
      );
    } catch (error) {
      logger.warn(`fastGptApi-partExpertAgent 出错：${error}`);
    }
    if (tokens.join("") === "") {
      context.reply(factory.text("服务器繁忙，请稍后再试"));
    }
  }
});

export default agent;
