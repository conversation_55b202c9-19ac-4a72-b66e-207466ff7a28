import { AgentName, EntityNames, Intents, NluActionIntents, commandIntents } from "@/copilot/constants";
import { AdditionalFormat } from "@casstime/copilot-core";
import _ from "lodash";
import type { InquiryFormData } from "../forms";
import { InquiryIntents } from "../parsers/inquiryIntentClassifier";

export function createTips(type: AdditionalFormat["type"], text: string = "") {
  if (!text) return undefined;
  const content = `
    <view>
      ${text}
    </view>
  `;
  return { type, content };
}

export function createInquiryStartTips() {
  return `
    <hr style="margin-bottom: 24;margin-horizontal: 0"></hr>
    <text style="font-weight: bold;">您可以：</text>
    <view style="flex-direction: row; align-items: center;margin-left: 16;">
      <text style="color: #646566;">• 扫描车架号</text>
      <button
      type="command"
      command="${commandIntents.inquiryPicture}" 
      textStyle="color: #E51E1E;"
      style="margin-left: 12;border-color: #E51E1E"
      params="${_.escape(JSON.stringify({ field: EntityNames.vinCode }))}"
      >
        扫VIN码
      </button>
    </view>
    <view style="flex-direction: row; align-items: center;margin-left: 16;">
      <text style="color: #646566;">• 拍工单</text>
      <button
      type="command"
      command="${commandIntents.inquiryPicture}" 
      textStyle="color: #E51E1E;"
      style="margin-left: 12;border-color: #E51E1E"
      params="${_.escape(JSON.stringify({ field: EntityNames.partName }))}"
      >
        工单识别
      </button>
    </view>
    <view style="flex-direction: row; align-items: center;margin-left: 16;">
      <text style="color: #646566;">• 告诉我配件名称</text>
      <button
      type="command"
      command="${commandIntents.inquiryUpdateField}" 
      textStyle="color: #E51E1E;"
      style="margin-left: 12;border-color: #E51E1E"
      params="${_.escape(JSON.stringify({ field: EntityNames.partName }))}"
      >
        添加配件
      </button>
    </view>
  `;
}

export function createNeedVinTips() {
  return `
    <text style="font-weight: bold;line-height: 48">1. 如果配件不准确，您可以：</text>
    <text style="color: #646566;margin-left: 16;">• 告诉我如何修改</text>
    <view style="flex-direction: row; align-items: center;">
      <text style="color: #646566;margin-left: 16;">• 手动修改</text>
      <button
      type="command"
      command="${commandIntents.inquiryUpdateField}" 
      textStyle="color: #E51E1E;"
      style="margin-left: 12;border-color: #E51E1E"
      params="${_.escape(JSON.stringify({ field: EntityNames.partName }))}"
      >
        修改配件
      </button>
    </view>
    <text style="font-weight: bold;">2. 如果没有配件要补充，您可以：</text>
    <view style="flex-direction: row; align-items: center;margin-left: 16;">
      <text style="color: #646566;">• 告诉我车架号</text>
      <button
      type="command"
      command="${commandIntents.inquiryPicture}" 
      textStyle="color: #E51E1E;"
      style="margin-left: 12;border-color: #E51E1E"
      params="${_.escape(JSON.stringify({ field: EntityNames.vinCode }))}"
      >
        扫VIN码
      </button>
    </view>
  `;
}

export function createNeedPartNameTips() {
  return `
    <view style="flex-direction: row; align-items: center;margin-left: 16;">
      <text style="color: #646566;">您可以告诉我你需要的配件，或发给我配件工单，马上帮你问价</text>
    </view>
  `;
}

export function createInquiryTips(formData: InquiryFormData) {
  return `
    <text style="font-weight: bold;line-height: 48">1. 如果配件不准确，您可以：</text>
    <text style="color: #646566;margin-left: 16;">• 告诉我如何修改</text>
    <view style="flex-direction: row; align-items: center;">
      <text style="color: #646566;margin-left: 16;">• 手动修改</text>
      <button
      type="command"
      command="${commandIntents.inquiryUpdateField}" 
      textStyle="color: #E51E1E;"
      style="margin-left: 12;border-color: #E51E1E"
      params="${_.escape(JSON.stringify({ field: EntityNames.partName }))}"
      >
        修改配件
      </button>
    </view>
    <text style="font-weight: bold;">2. 如果没有配件要补充，您可以：</text>
    <text style="color: #646566;margin-left: 16;">• 语音告诉我“没有了”、“发布询价”</text>
    <view style="flex-direction: row; align-items: center;">
      <text style="color: #646566;margin-left: 16;">• 或点击询价按钮发布询价</text>
      <button 
      type="nlu" 
      textStyle="color: #E51E1E;"
      style="margin-left: 12;border-color: #E51E1E"
      nlu="${_.escape(
        JSON.stringify({
          intent: commandIntents.IMMEDIATE_INQUIRY,
          agentName: AgentName.inquiryAgent,
          slots: { ...formData },
        })
      )}" 
      >
        发布询价
      </button>
    </view>
  `;
}

export function createAppendInquiryTips() {
  return `
    <view>
      <button
      type="nlu"
      nlu="${_.escape(
        JSON.stringify({
          intent: Intents.newInquiry,
          agentName: AgentName.inquiryAgent,
        })
      )}" 
      >
        发布新询价
      </button>
    </view>
      <view>
      <button
      type="command"
      command="${commandIntents.inquiryPressSubmitForm}" 
      >
        追加配件
      </button>
    </view>
  `;
}

export function createReidentificationTyreTips() {
  return `
    <text style="font-weight: bold;">您可以：</text>
    <text style="color: #646566;margin-left: 16;">• 手动输入轮胎规格</text>
    <view style="flex-direction: row; align-items: center;margin-left: 16;">
      <text style="color: #646566;">• 重新发送轮胎图片</text>
      <button
      type="command"
      command="${commandIntents.inquiryPicture}" 
      textStyle="color: #E51E1E;"
      style="margin-left: 12;border-color: #E51E1E"
      params="${_.escape(JSON.stringify({ field: "tyreImage" }))}"
      >
        发送图片
      </button>
    </view>
  `;
}

export function createTyreSizeTips() {
  return `
    <text style="font-weight: bold;">您可以：</text>
    <text style="color: #646566;margin-left: 16;">• 手动输入轮胎规格（例如：255 50 20）</text>
    <view style="flex-direction: row; align-items: center;margin-left: 16;">
      <text style="color: #646566;">• 拍原车轮胎图片</text>
      <button
      type="command"
      command="${commandIntents.inquiryPicture}" 
      textStyle="color: #E51E1E;"
      style="margin-left: 12;border-color: #E51E1E"
      params="${_.escape(JSON.stringify({ field: "tyreImage" }))}"
      >
        拍轮胎
      </button>
    </view>
  `;
}

export function createOcrPartsRecommandTips(parts: { code: string; name: string }[]) {
  return `<view style="flex-direction: row;align-items: center;flex-wrap: wrap;">${parts
    .map((part) => {
      const value = part.code ? `${part.name}(${part.code})` : part.name;
      return `<button
        type="nlu"
        textStyle="color: #E51E1E;font-size:30;margin-top: 10;margin-bottom: 10;"
        style="margin-left: 12;margin-top:8;border-color:#E51E1E;"
        nlu="${_.escape(
          JSON.stringify({
            intent: InquiryIntents.询报价,
            agentName: AgentName.inquiryAgent,
            entities: [
              {
                name: "partName",
                value,
              },
            ],
          })
        )}" 
        >${value}</button>`;
    })
    .join("\n")}
    </view>`;
}

export function createOcrSimilarPartsRecommandTips(params: {
  partNames: string[];
  imageUrl: string;
  asyncCommandParams: object[];
}) {
  const { partNames, imageUrl, asyncCommandParams } = params;
  return `<view style="flex-direction: row;align-items: center;flex-wrap: wrap;">${partNames
    .map((name, idx) => {
      return `<button
      type="command"
      command="${commandIntents.asyncCommand}"
      textStyle="color: #E51E1E;font-size: 30;margin-top: 10;margin-bottom: 10;"
      style="margin-left: 12;margin-top: 8;border-color: #E51E1E;"
      params="${_.escape(JSON.stringify(asyncCommandParams[idx]))}"
    >${name}</button>`;
    })
    .join("\n")}
    </view>
    <button
      textStyle="color: #fff;font-size:30;margin-top: 10;margin-bottom: 10;"
      style="margin-left: 12;margin-top: 8;background-color: #E51E1E;"
      type="nlu"
      nlu="${_.escape(
        JSON.stringify({
          intent: NluActionIntents.IMAGE_REC_PARTS_SIMILAR_NO,
          slots: {
            imageUrl,
            options: partNames,
          },
          agentName: AgentName.inquiryAgent,
        })
      )}"
    >都不是</button>`;
}
