import { commandIntents, IMTeamId, YUAN } from "@/copilot/constants";
import { ITyreDemandResponse } from "@/interfaces/tyreDemand";
import { AdditionalFormat } from "@casstime/copilot-core";
import _ from "lodash";
import dayjs from "dayjs";
import {
  IAsyncCommandItem,
  IGroupedByProgrammeIdAndStoreId,
  IInquiryCardInfo,
  IInquiryInfo,
  IQuoteDisplayItem,
  IQuoteResItem,
  IQuoteResponseItem,
  IQuoteStoreItem,
  IStockInfoItem,
} from "../interface";
import { asyncCommandService } from "../services/AsyncCommandService";
import { InquiryQuoteTabs } from "../enum/inquiry";

export const MerchantIcon = "https://cassnotify.oss-cn-shenzhen.aliyuncs.com/im-site/merchant.png";

export function createEmbed(
  type: AdditionalFormat["type"],
  texts: string[],
  backgroundColor = "#F7F8FA"
): AdditionalFormat {
  const text = texts.filter(Boolean).join(`<hr style="margin-vertical: 10;"></hr>`);
  if (!text) return { type, content: "" };
  const content = `
    <view style="background-color: ${backgroundColor}; border-radius: 16;padding: 20;">
      ${text}
    </view>
  `;
  return { type, content };
}

export function createEmbedBlock(embeds: AdditionalFormat[]) {
  const content = embeds
    .map((embed) => embed.content)
    .filter(Boolean)
    .join(createViewHeight(24));
  return { type: embeds[0].type, content };
}

export function createCarModelEmbed(brandLogo: string, carModelShowName: string, vinCode: string, inquiryId?: string) {
  if (!brandLogo || !carModelShowName || !vinCode) return "";
  const inquiryIdText = inquiryId ? `<text>询价单号: ${inquiryId}</text>` : "";
  return `        
    <view style="flex-direction: row; align-items: center;">
      <image uri="${brandLogo}" style="width: 72; height: 72; margin-right: 20;"></image>
      <view style="flex: 1; margin-right: 10;">
        <text style="font-weight: bold;">${carModelShowName}</text>
        <text >${vinCode}</text>
        ${inquiryIdText}
      </view>
    </view>`;
}

export function createPartNamesEmbed(partNames: string[] = []) {
  if (!partNames.length) return "";
  let text = "";
  if (partNames.length <= 10) {
    text = partNames
      .map((partName, index) => `<text style="font-weight: bold;">${index + 1}.${partName}</text>`)
      .join("\n");
  } else {
    const partNamesMap = partNames.map((partName, index) => {
      return { partName, index };
    });
    const prefix = partNamesMap
      .slice(0, 5)
      .map((item) => `<text style="font-weight: bold;">${item.index + 1}.${item.partName}</text>`)
      .join("\n");
    const suffix = partNamesMap
      .slice(-4)
      .map((item) => `<text style="font-weight: bold;">${item.index + 1}.${item.partName}</text>`)
      .join("\n");
    const omit = '<text style="font-weight: bold;">......</text>';
    const params = {
      type: "richtext",
      content: partNames
        .map((partName, index) => `<text style="font-weight: bold;">${index + 1}.${partName}</text>`)
        .join("\n"),
    };

    const checkAllButton = `
      <view style="align-items: flex-end">
        <button
        type="command"
        command="${commandIntents.showFullContent}" 
        params="${_.escape(JSON.stringify(params))}"
        textStyle="color: #E51E1E;"
        style="border-color: #E51E1E; background-color: #FFFFFF"
        >
          查看全部
        </button>
      </view>
    `;
    text = [prefix, omit, suffix, checkAllButton].join("\n");
  }
  return `<view>${text}</view>`;
}

export function createInquiryInfoEmbed(
  partNames: string[],
  inquiryId: string,
  createdName: string,
  createdStamp: number = new Date().getTime()
) {
  if (!inquiryId) return "";
  const query = {
    inquiryId,
    fromScreen: "AIChatScreen",
    pageIndex: InquiryQuoteTabs.PARTNAME,
  };
  const navigate = `cassapp://route/native/inquiry/quotationResult?query=${JSON.stringify(query)}`;
  const action = {
    type: "command",
    text: inquiryId,
    command: commandIntents.commonNavigate,
    params: { navigate },
  };
  return `
    <view action="${_.escape(JSON.stringify(action))}">
      <text style="font-weight: bold;font-size: 30;" numberOfLines="2">${partNames.join("、")}</text>
      <text>询价单号: ${inquiryId}</text>
      <text>询价时间: ${dayjs(createdStamp).format("YYYY-MM-DD HH:mm")}</text>
      <text>询价人: ${createdName}</text>
    </view>
  `;
}
export function createRecommendInfoEmbed(inquiryId: string) {
  if (!inquiryId) return "";
  const query = {
    inquiryId,
    fromScreen: "AIChatScreen",
    pageIndex: InquiryQuoteTabs.PARTNAME,
  };
  const navigate = `cassapp://route/native/inquiry/quotationResult?query=${JSON.stringify(query)}`;
  const action = {
    type: "command",
    text: inquiryId,
    command: commandIntents.commonNavigate,
    params: { navigate },
  };
  return `
    <view action="${_.escape(JSON.stringify(action))}">
      <text>询价单号: ${inquiryId}</text>
    </view>
  `;
}
export function createTyreInquiryEmbed(tyreInquiryDemand: ITyreDemandResponse) {
  const { demandId, originalItems = [], createdDate = new Date().getTime(), userName = "" } = tyreInquiryDemand;
  if (!demandId) return "";
  const query = {
    inquiryId: demandId,
    fromScreen: "AIChatScreen",
  };
  const navigate = `cassapp://route/rn/TireQuoteDetail?query=${JSON.stringify(query)}`;
  const action = {
    type: "command",
    text: demandId,
    command: commandIntents.commonNavigate,
    params: { navigate },
  };
  return `
    <view action="${_.escape(JSON.stringify(action))}">
      <text  style="font-weight: bold;font-size: 30;">${originalItems.map((item) => item.description).join("、")}</text>
      <text>轮胎询价单号: ${demandId}</text>
      <text>询价时间: ${dayjs(createdDate).format("YYYY-MM-DD HH:mm")}</text>
      <text>询价人: ${userName}</text>
    </view>
  `;
}

export function createInquiryMoreQuoteEmbed(inquiryId?: string) {
  if (!inquiryId) return "";
  const query = {
    inquiryId,
    fromScreen: "AIChatScreen",
    pageIndex: InquiryQuoteTabs.STORE,
  };
  const navigate = `cassapp://route/native/inquiry/quotationResult?query=${JSON.stringify(query)}`;
  return `
    <view style="flex-direction: row; align-items: center;margin-left: 24;flex-wrap: wrap;">
      <text style="color: #646566;">如果想看更多报价，可点击</text>
      <link style="margin-left: 10;" to="${_.escape(navigate)}">查看更多</link>
    </view>
  `;
}

export function createTyreMoreQuoteEmbed(inquiryId?: string) {
  if (!inquiryId) return "";
  const query = {
    inquiryId,
    fromScreen: "AIChatScreen",
  };
  const navigate = `cassapp://route/rn/TireQuoteDetail?query=${JSON.stringify(query)}`;
  return `
    <view style="flex-direction: row; align-items: center;margin-left: 24;flex-wrap: wrap;">
      <text style="color: #646566;">如果想看更多报价，可点击</text>
      <link style="margin-left: 10;" to="${_.escape(navigate)}">查看更多</link>
    </view>
  `;
}

export function createEmptyQuoteEmbed() {
  return `
  <view style="justify-content: center;align-items: center;">
    <text style="text-align: center">报价中...</text>
  </view>
  `;
}

export function createViewHeight(height: number) {
  return `
  <view style="height: ${height};"></view>
  `;
}

export async function createInquiryQuoteEmbed(
  quotes: IQuoteStoreItem[] = [],
  inquiryDetail: IInquiryInfo = {},
  source: string = ""
) {
  if (!quotes?.length) return "";
  const {
    inquiryId,
    statusId,
    createdName: userName,
    needsNames,
    carModelName,
    carBrandId,
    vin,
    createdStamp,
  } = inquiryDetail;
  const inquiryInfo = {
    carModelName,
    carBrandId,
    inquiryId,
    vin,
    status: statusId, // 询价单状态（中文）
    userName, // 发布人
    createdStamp,
    needsName: needsNames?.join("、") || "", // 需求拼接
    id: inquiryId, // 询价单 id
  };
  const quotesView: string[] = [];
  for (const quote of quotes) {
    const { storeName, stocks, decodesArray, isOpenInvoice, storeId } = quote;
    // 商家信息-仓库名称
    let stockName = "无仓库";
    const availableStocks = stocks?.filter((item) => item?.availableToPromiseTotal);
    if (availableStocks?.length > 0) {
      stockName = availableStocks.length > 1 ? "多仓发货" : availableStocks[0].facilityName;
    }
    // 最多只展示三个配件报价
    const showNeeds: IQuoteDisplayItem[] = decodesArray.length > 3 ? decodesArray.slice(0, 3) : decodesArray;
    const query = {
      storeId,
      clue: {
        type: "INQUIRY",
        id: inquiryDetail.inquiryId,
        data: inquiryInfo,
      },
    };
    const navigate = `cassapp://route/native/im/conversation?query=${JSON.stringify(query)}`;

    // 生成command按钮
    // const sendMessageCommand: IAsyncCommandItem = {
    //   command: commandIntents.sendMessage,
    //   params: {
    //     content: {
    //       content: showNeeds.map((item) => item.partsName).join("、"),
    //     },
    //     messageType: "TextMessage",
    //     displayName: storeName,
    //     targetId: `${storeId}-default`,
    //     platform: source,
    //   },
    // };
    const navigateCommand: IAsyncCommandItem = {
      command: commandIntents.commonNavigate,
      params: {
        navigate,
      },
    };
    const commands: IAsyncCommandItem[] = [navigateCommand];
    const asyncCommand = await asyncCommandService.createAsyncCommand(commands);
    const id = asyncCommand._id?.toString();

    // 前往采购
    const toQuoteQuery = {
      inquiryId,
      fromScreen: "AIChatScreen",
      pageIndex: InquiryQuoteTabs.PARTNAME,
      storeId,
    };
    const toQuoteNavigate = `cassapp://route/native/inquiry/quotationResult?query=${JSON.stringify(toQuoteQuery)}`;

    const view = `
      <view>
        <view style="flex-direction: row;align-items: center;flex-wrap: wrap">
          <button
          style="border-width: 0; background-color: transparent;margin-right: 20; padding-horizontal: 0;"
          textStyle="color: #008CF5;font-weight: bold;font-size: 28;"
          type="command"
          command="${commandIntents.asyncCommand}" 
          iconUrlRight="${MerchantIcon}"
          iconStyle="tint-color: #008CF5;margin-left: 8;"
          params="${_.escape(JSON.stringify({ id }))}"
          >
            ${storeName}
          </button>
          <text>${stockName}</text>
        </view>
        <view>
          ${showNeeds
            .map((item, index) => {
              const { partsName, needsName, decodeArray } = item;
              const { qualityName, price, btPrice, departure } = decodeArray[0];
              const priceShow = isOpenInvoice ? price : btPrice;
              // 多仓发货需要展示每个配件的仓库
              let departureName = "";
              if (availableStocks.length > 1) {
                departureName = `<text>${departure}</text>`;
              }
              const name = needsName === partsName ? partsName : `${needsName}/${partsName}`;
              return `
              <view style="flex-direction: row;align-items: center;flex-wrap: wrap;">
                <text style="margin-right: 12;font-weight: bold;">${index + 1}. ${name}</text>
                <text style="margin-right: 12;">${qualityName}</text>
                <text style="margin-right: 12;">${YUAN} ${priceShow}</text>
                ${departureName}
              </view>
            `;
            })
            .join(" ")}
        </view>
        <view style="align-items: flex-end;margin-top: 20;">
          <button
          style="border-color: #E51E1E"
          textStyle="color: #E51E1E;"
          type="command"
          command="${commandIntents.commonNavigate}" 
          params="${_.escape(JSON.stringify({ navigate: toQuoteNavigate }))}">
            前往采购
          </button>
        </view>
      </view>
    `;
    quotesView.push(view);
  }

  return `
    <view>
      ${quotesView.join(`<hr style="margin-vertical: 20;"></hr>`)}
    </view>
  `;
}

export async function createRecommendQuoteEmbed(
  quoteStoreList: IQuoteStoreItem[] = [],
  inquiryDetail: IInquiryInfo = {},
  source: string = "",
  storeQuoteByRecommend: IQuoteResItem[] = [],
  groupedByProgrammeIdAndStoreId: IGroupedByProgrammeIdAndStoreId[] = [],
  isOpenInvoice: boolean
) {
  if (!quoteStoreList?.length) return "";
  if (!groupedByProgrammeIdAndStoreId?.length) return "";
  const {
    inquiryId,
    statusId,
    createdName: userName,
    needsNames,
    carModelName,
    carBrandId,
    vin,
    createdStamp,
  } = inquiryDetail;
  const inquiryInfo = {
    carModelName,
    carBrandId,
    inquiryId,
    vin,
    status: statusId, // 询价单状态（中文）
    userName, // 发布人
    createdStamp,
    needsName: needsNames?.join("、") || "", // 需求拼接
    id: inquiryId, // 询价单 id
  };
  const quotesView: string[] = [];
  groupedByProgrammeIdAndStoreId.forEach(async (programmeItem, programmeIndex) => {
    const { programmeDescription, items, programmeId } = programmeItem;
    // 整单价格
    let wholePrice: number = 0;

    const renderQuoteLayer = (quoteItems: IQuoteResItem[], availableStocks: IStockInfoItem[]) => {
      const quoteLayer = quoteItems.map((quote, index) => {
        const { price, btPrice, departure, partTypeDesc, partName } = quote;
        const priceShow = isOpenInvoice ? price : btPrice;
        if (priceShow) {
          wholePrice += Number(priceShow);
        }
        let departureName = "";
        if (availableStocks.length > 1) {
          departureName = `<text>${departure}</text>`;
        }
        return `
        <view style="flex-direction: row;align-items: center;flex-wrap: wrap;">
          <text style="margin-right: 12;font-weight: bold;">${index + 1}. ${partName}</text>
          <text style="margin-right: 12;">${partTypeDesc}</text>
          <text style="margin-right: 12;">${YUAN} ${priceShow}</text>
          ${departureName}
        </view>
      `;
      });
      return quoteLayer;
    };

    const renderStoreLayer = () => {
      const renderStoreItems = items.map((storeItem) => {
        const { storeName, quoteItems, storeId } = storeItem;
        let storeStockName = "无仓库";
        // 商家信息-仓库名称
        let availableStocks: IStockInfoItem[] = [];
        const query = {
          storeId,
          clue: {
            type: "INQUIRY",
            id: inquiryDetail.inquiryId,
            data: inquiryInfo,
          },
        };
        const navigate = `cassapp://route/native/im/conversation?query=${JSON.stringify(query)}`;

        const quoteStore = quoteStoreList.find((quoteStoreItem) => quoteStoreItem.storeId === storeId);

        if (quoteStore) {
          availableStocks = quoteStore.stocks?.filter((item) => item?.availableToPromiseTotal);
          if (availableStocks?.length > 0) {
            storeStockName = availableStocks.length > 1 ? "多仓发货" : availableStocks[0].facilityName;
          }
        }
        // 报价层
        const quoteLayer = renderQuoteLayer(quoteItems, availableStocks);

        return `
       <view style="flex-direction: row;align-items: center;flex-wrap: wrap">
        <button
        style="border-width: 0; background-color: transparent;margin-right: 20; padding-horizontal: 0;"
        textStyle="color: #008CF5;font-weight: bold;font-size: 28;"
        type="command"
        command="${commandIntents.commonNavigate}" 
        iconUrlRight="${MerchantIcon}"
        iconStyle="tint-color: #008CF5;margin-left: 8;"
        params="${_.escape(JSON.stringify({ navigate }))}"
        >
          ${storeName}
        </button>
        <text>${storeStockName}</text>
      </view>
      <view>
       ${quoteLayer.join("\n")}
      </view>
      `;
      });
      return renderStoreItems;
    };
    // 商家层
    const storeLayer = renderStoreLayer();

    const quotationProductIds = storeQuoteByRecommend
      .filter((item) => item.programmeId === programmeId)
      .map((item) => item.quotationProductId);

    const view = `
      <view>
      <text style="font-size: 30; font-weight: bold">方案${programmeIndex + 1}：${programmeDescription}
      </text>
      <view>
       <text style="font-size: 30; font-weight: bold">
      ${!!wholePrice && `整单价格：${YUAN}${wholePrice.toFixed(2)}`}
        </text>
      </view>
       ${storeLayer.join("\n")}
      </view>
      <view style="align-items: flex-end;margin-top: 20;">
        <button
        style="border-color: #E51E1E"
        textStyle="color: #E51E1E;"
        type="command"
        command="${commandIntents.IMMEDIATE_PURCHASE}" 
        params="${_.escape(JSON.stringify({ inquiryId, quotationProductIds }))}">
          一键采购
        </button>
      </view>
    `;
    quotesView.push(view);
  });

  return `
    <view>
      ${quotesView.join(`<hr style="margin-vertical: 20;"></hr>`)}
    </view>
  `;
}
export function createTyreHeaderEmbed(demandId: string, createdDate: number) {
  if (!demandId) return "";
  return `
    <text style="font-weight: bold;font-size: 30;">轮胎询价单：${demandId}</text>
    <text style="font-size: 28;">询价时间：${dayjs(createdDate).format("YYYY-MM-DD HH:mm")}</text>
  `;
}

export function createTyreQuoteEmbed(list: IQuoteResponseItem[], demandId: string) {
  if (!list.length) return "";
  const query = {
    inquiryId: demandId,
    fromScreen: "AIChatScreen",
  };
  const navigate = `cassapp://route/rn/TireQuoteDetail?query=${JSON.stringify(query)}`;
  const listView = list.slice(0, 1).map((item) => {
    const { partsNumber, quotationProductList = [] } = item;
    return `
      <view>
        <text style="font-weight: bold;font-size: 30;">${partsNumber}</text>
        ${quotationProductList
          .slice(0, 3)
          .map((quote, index) => {
            const { partsName, btPrice, organization, quoteStatus } = quote;
            const title = quoteStatus === "outOfStock" ? "缺货" : partsName;
            const price = quoteStatus === "outOfStock" ? "--" : `${YUAN} ${btPrice}`;
            return `
            <view style="flex-direction: row;">
              <text style="font-size: 24;font-weight: bold;">${index + 1}.</text>
              <view>
                <text style="font-size: 24;font-weight: bold;">${title}</text>
                <text style="font-size: 24;">店铺：${organization.organizationName}</text>
                <view style="flex-direction: row;">
                  <text style="font-size: 24;">价格：</text>
                  <text style="font-size: 24;font-weight: bold; color: #E51E1E">${price}</text>
                </view>
              </view>
            </view>
          `;
          })
          .join(" ")}
        <view style="align-items: flex-end;margin-top: 20;">
          <button
          style="border-color: #E51E1E"
          textStyle="color: #E51E1E;"
          type="command"
          command="${commandIntents.commonNavigate}" 
          params="${_.escape(JSON.stringify({ navigate }))}">
            前往采购
          </button>
        </view>
      </view>`;
  });

  return `
    <view>
      ${listView.join("\n")}
    </view>
  `;
}

export function createCouponsEmbed(amount: number) {
  const navigate = "cassapp://route/rn/DiscountCouponList";
  const action = {
    type: "command",
    text: `${amount}元无门槛`,
    command: commandIntents.commonNavigate,
    params: { navigate },
  };
  return `
   <view action="${_.escape(JSON.stringify(action))}">
      <image uri="https://cass-upload.oss-cn-shenzhen.aliyuncs.com/copilot/production/coupon_bg.png" style="position: relative;width: 474; height: 107"></image>
      <text style="position: absolute; top: 30; left: 200;font-size: 32;color: #DE424D; font-weight: bold">${amount}元无门槛</text>
    </view>
  `;
}

export function createA9tAirforceEnterEmbed(inquiryDetail: IInquiryCardInfo = {}) {
  const {
    inquiryId,
    statusId,
    createdName: userName,
    needsNames,
    carModelName,
    saleModelName,
    carBrandId,
    vin,
    createdStamp,
  } = inquiryDetail;
  const data = {
    carModelName: carModelName || saleModelName || "",
    carBrandId,
    inquiryId,
    vin,
    status: statusId, // 询价单状态（中文）
    userName, // 发布人
    createdStamp,
    needsName: needsNames?.join("、") || "", // 需求拼接
    id: inquiryId, // 询价单 id
  };
  const query = {
    teamId: IMTeamId.CASS_SERVICE, // 开思客服
    clue: {
      type: "INQUIRY",
      id: inquiryId,
      data,
    },
    referer: "AIChatScreen",
  };
  const navigate = `cassapp://route/native/im/conversation?query=${JSON.stringify(query)}`;
  const action = {
    type: "command",
    text: "专属客服",
    command: commandIntents.commonNavigate,
    params: { navigate },
  };
  return `
    <view action="${_.escape(JSON.stringify(action))}">
      <image uri="https://cass-upload.oss-cn-shenzhen.aliyuncs.com/copilot/production/a9tAirforceEnter.png" style="position: relative;width: 580; height: 189"></image>
    </view>
  `;
}

export function getEmbedFormat(type: AdditionalFormat["type"], content: string): AdditionalFormat {
  return {
    type,
    content,
  };
}
