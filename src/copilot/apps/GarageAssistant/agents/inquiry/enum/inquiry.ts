export enum InquiryResultStatus {
  RE_DECODE = "RE_DECODE", // 重新译码
  IN_THE_DECODING = "IN_THE_DECODING", // 正在译码
  DECODING_ERROR = "DECODING_ERROR", // 无法译码
}

// 供应商报价状态 UNQUOTE 报价中，QUOTE 报价完成，EXPIRED 已过期'
export enum InquiryQuoteStatus {
  UNQUOTE = "UNQUOTE",
  QUOTE = "QUOTE",
  EXPIRED = "EXPIRED",
}
// 按配件选 1 按商家选 2 小时达 3
export enum InquiryQuoteTabs {
  PARTNAME = 1,
  STORE = 2,
  WAREHOUSE = 3,
}

// 询价单 报价完成、已取消
export const InquiryQuoteOverStatusList: string[] = [InquiryQuoteStatus.QUOTE, InquiryQuoteStatus.EXPIRED];

export const DecodingStatusList: string[] = [InquiryResultStatus.RE_DECODE, InquiryResultStatus.IN_THE_DECODING];

export const DecodeErrorStatusList: string[] = [InquiryResultStatus.DECODING_ERROR];

// 通道类型
export enum IDialogueBusinessEnum {
  DEFAULT = "DEFAULT",
  // 事故
  ACCIDENT = "ACCIDENT",
  // 空调
  AIR_CONDITIONER = "AIR_CONDITIONER",
}

export const indexMap: Record<number, string> = {
  1: "一",
  2: "二",
  3: "三",
  4: "四",
  5: "五",
  6: "六",
  7: "七",
  8: "八",
  9: "九",
  10: "十",
};
