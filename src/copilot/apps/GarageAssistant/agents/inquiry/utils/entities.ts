import { TyreSpec } from "@/common/utils";
import { IOriginalItemsItem } from "@/interfaces/inquiry";
import { Entity } from "@casstime/copilot-core";
import { IInquiryTyreSpecItem } from "../interface";
import { EntityNames } from "@/copilot/constants";

export function isOnlyTyreEntities(entities: Entity[]): boolean {
  const entitiesNoQuatity = entities.filter((entity) => entity.name !== EntityNames.qualities);
  return (
    Boolean(entitiesNoQuatity.length) &&
    entitiesNoQuatity.every((entity) => {
      return entity.name === EntityNames.partName && TyreSpec.includesTyreSpec(entity.value);
    })
  );
}

export function isOriginalItemsHasTyreEntities(
  entities: Entity[],
  originalItems: (IOriginalItemsItem & IInquiryTyreSpecItem)[]
): boolean {
  const tyreEntities = entities.filter(
    (entity) => entity.name === "partName" && TyreSpec.includesTyreSpec(entity.value)
  );
  const originalItemsName = originalItems?.map((item) => item.specification) || [];
  return (
    Boolean(tyreEntities?.length) &&
    tyreEntities?.every((item) => {
      return originalItemsName.includes(item.value);
    })
  );
}
