import { Entity } from "@casstime/copilot-core";
import { isOriginalItemsHasTyreEntities } from "../entities";
import { IOriginalItemsItem } from "@/interfaces/inquiry";
import { IInquiryTyreSpecItem } from "../../interface";

it("isOriginalItemsHasTyreEntities", () => {
  const entities: Entity[] = [
    {
      name: "partName",
      value: "（轮胎）LT175/70R14",
    },
  ];
  const originalItems: (IOriginalItemsItem & IInquiryTyreSpecItem)[] = [
    {
      brandCode: "LINGLONG",
      brandName: "玲珑",
      speedLevel: "S",
      treadWidth: "175",
      ratio: "70",
      meridian: "LT",
      size: "14",
      originalTyre: undefined,
      explosionProofMark: undefined,
      sidewallEnhance: "8PR",
      tyreFeature: undefined,
      pattern: "LMA16",
      whetherImport: undefined,
      specification: "（轮胎）LT175/70R14",
    },
  ];
  const isFlag = isOriginalItemsHasTyreEntities(entities, originalItems);

  expect(isFlag).toEqual(true);
});
