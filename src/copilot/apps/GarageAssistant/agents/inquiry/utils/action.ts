import { AgentName } from "@/common/enums";
import { ActionFactory } from "@/common/factory/ActionFactory";
import { commandIntents, NluActionIntents } from "@/copilot/constants";
import { IAction } from "@casstime/copilot-core";

export function createRotateImageAction(text: string, imageUrl: string, extra: { [key: string]: unknown }) {
  return ActionFactory.nlu(text, {
    intent: NluActionIntents.ROTATE_IMAGE,
    slots: {
      imageUrl,
      extra,
    },
    agentName: AgentName.inquiryAgent,
  });
}

function createReRecognitionAction(text: string, imageType: string, imageUrl: string) {
  return ActionFactory.nlu(text, {
    intent: commandIntents.reRecognizeImage,
    slots: {
      imageUrl,
      imageType,
    },
    agentName: AgentName.inquiryAgent,
  });
}

// 候选按钮
export function createCandidateAction(label: string, imageUrl: string) {
  switch (label) {
    case "铭牌":
    case "VIN码":
    case "证件":
      return createReRecognitionAction("这是车架号图片", label, imageUrl);
    case "工单":
    case "系统截图":
    case "聊天截图":
    case "爆炸图":
      return createReRecognitionAction("这是工单图片", label, imageUrl);
    case "轮胎":
      return createReRecognitionAction("这是轮胎图片", label, imageUrl);
    case "配件":
      return createReRecognitionAction("这是配件图", label, imageUrl);
    default:
      throw new Error(`不支持 label 【${label}】`);
  }
}

export function createAddressAction(addressId?: string): IAction {
  return {
    theme: "primary",
    command: "COMMON-NAVIGATE",
    type: "command",
    params: {
      navigate: "AddressList",
      params: {
        addressId: addressId,
        fromOrderConfirm: true,
      },
    },
    text: "收货地址",
  };
}
