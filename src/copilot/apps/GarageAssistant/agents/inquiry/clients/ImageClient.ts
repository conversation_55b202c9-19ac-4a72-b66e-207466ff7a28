import httpClient from "@/common/clients/http.client";
import {
  IImageParams,
  IImageRecognizeVinRes,
  IImageRotateSizeRes,
  ImageClassifyRes,
  IMatchNameParams,
  IMatchNeedsResult,
  IProOcrRes,
  IRecognizePartsNamesResult,
  IRecognizeVinResult,
  ISimilarPartsRes,
} from "@/interfaces/client";
import config from "@casstime/config";
import logger from "@/common/logger";
import { ImageTyreSizePrediction } from "../interface";

class ImageClient {
  public async getImageClassify(img: string) {
    try {
      const { result } = await httpClient.post<ImageClassifyRes>(
        `${config.get<string>("IMAGE_CLASSIFY")}/classify/image`,
        {
          body: {
            img,
          },
        }
      );
      return result || {};
    } catch (error) {
      logger.info("图片分类接口调用失败", error);
      return {};
    }
  }

  public async ocrRecognizeVin(imageBase64Str: string) {
    try {
      const { result } = await httpClient.post<IRecognizeVinResult>("/ocr-service/ocr/recognize/vin", {
        body: { imageBase64Str },
      });
      return result?.result || "";
    } catch (error) {
      logger.info("vin识别接口调用失败", error);
      return "";
    }
  }

  public async ocrRecognizePartsNames(imageParams: IImageParams) {
    try {
      let response = await httpClient.post<IRecognizePartsNamesResult>("/ocr-node/ocr-api/ocr/handwriting", {
        body: imageParams,
      });
      // 超时重试
      if (response?.statusCode === 504) {
        response = await httpClient.post<IRecognizePartsNamesResult>("/ocr-node/ocr-api/ocr/handwriting", {
          body: imageParams,
        });
      }
      return response?.result?.data || [];
    } catch (error) {
      logger.info("手写工单接口调用失败", error);
      return [];
    }
  }

  public async getMatchNeeds(needs: IMatchNameParams) {
    try {
      const { result } = await httpClient.post<IMatchNeedsResult>("/ocr-node/ocr-api/match/needName", {
        body: needs,
      });
      return result?.data || [];
    } catch (error) {
      logger.info("工单纠错接口调用失败", error);
      return [];
    }
  }

  public async getTyreSize(img: string) {
    try {
      const { result } = await httpClient.post<ImageTyreSizePrediction>(
        `${config.get<string>("IMAGE_CLASSIFY")}/tyre/e2e`,
        {
          body: {
            img,
          },
        }
      );
      return result || {};
    } catch (error) {
      logger.info("轮胎识别接口调用失败", error);
      return {};
    }
  }

  // 图片方向分类
  public async getImageRotate(img: string) {
    try {
      const { result } = await httpClient.post<IImageRotateSizeRes>(
        `${config.get<string>("IMAGE_CLASSIFY")}/classify/doc_direction`,
        {
          body: {
            img,
          },
        }
      );
      return result || {};
    } catch (error) {
      logger.info("图片方向分类接口调用失败", error);
      return {};
    }
  }

  // VIN码识别
  public async getImageRecognizeVin(img: string) {
    try {
      const { result } = await httpClient.post<IImageRecognizeVinRes>(
        `${config.get<string>("IMAGE_CLASSIFY")}/vin/e2e`,
        {
          body: {
            img,
          },
        }
      );
      return result?.vins || [];
    } catch (error) {
      logger.info("VIN码识别接口调用失败", error);
      return [];
    }
  }

  // 相似配件名称识别
  public async getSimilarParts(img: string) {
    try {
      const { result } = await httpClient.post<ISimilarPartsRes[]>(
        `${config.get<string>("IMAGE_CLASSIFY")}/classify/similar_parts`,
        {
          body: {
            img,
          },
        }
      );
      return result || [];
    } catch (error) {
      logger.info("相似配件名称识别失败", error);
      return [];
    }
  }
  /** pro-ocr识别 */
  public async recognizeImageByProOcr(img: string, labels: string[] = ["qr_code"]) {
    try {
      const { result } = await httpClient.post<IProOcrRes>(`${config.get<string>("IMAGE_CLASSIFY")}/pro-ocr/e2e`, {
        body: { img, labels },
        timeout: 30000,
      });
      return result?.data || [];
    } catch (error) {
      logger.info("proocr识别接口调用失败", error);
      return [];
    }
  }
}

export const imageClient = new ImageClient();
