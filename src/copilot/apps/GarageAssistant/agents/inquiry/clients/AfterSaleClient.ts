import httpClient from "@/common/clients/http.client";
import { ITagInfoResult } from "../interface/ITagInfo";
import { TerminalRes } from "@/interfaces/client";
import { utilsClient } from "./UtilsClient";

class AfterSaleClient {
  /** 查溯源码信息 */
  async getWarrantyLabel(tagNo: string) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.get<TerminalRes<ITagInfoResult>>(
      `/terminal-api-v2/aftersale/tag_info/by_code?labelCode=${tagNo}`,
      {
        headers,
      }
    );
    return {
      statusCode: result?.errorCode,
      result: result?.data,
    };
  }
}

export const afterSaleClient = new AfterSaleClient();
