import httpClient from "@/common/clients/http.client";
import { TerminalRes } from "@/interfaces/client";
import {
  IGetOptionalTyreSpecificationsResponse,
  ITireSpecificationsItem,
  ITyreInquiryReParameter,
} from "@/interfaces/inquiry";
import { utilsClient } from "./UtilsClient";
import { ITyreDemandResponse } from "@/interfaces/tyreDemand";
import { ITyreQuoteDetailRes, ITyreQuoteResponse } from "../interface";

class TyreClient {
  // 根据vin码获取轮胎规格信息
  public async getTyreSpecifications(vin: string) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.get<TerminalRes<ITireSpecificationsItem[]>>(
      `/terminal-api-v2/tyre_inquiry/tyre/specifications/${vin}`,
      {
        headers,
      }
    );

    return {
      errorCode: result?.errorCode,
      message: result?.message,
      data: result?.data || [],
    };
  }

  // 查询胎面宽、扁平比、尺寸等信息
  public async getOptionalTyreSpecifications() {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<TerminalRes<IGetOptionalTyreSpecificationsResponse>>(
      `/terminal-api-v2/tyre_inquiry/tyre/specifications`,
      {
        headers,
        body: {}, // 必须传空对象获取所有信息
      }
    );

    return {
      errorCode: result?.errorCode,
      message: result?.message,
      data: result?.data,
    };
  }

  // 轮胎询价单详情
  public async getTyreInquiryDemand(demandId: string) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.get<TerminalRes<ITyreDemandResponse>>(
      `/terminal-api-v2/tyre_inquiry/demand?demandId=${demandId}`,
      {
        headers,
      }
    );

    return {
      errorCode: result?.errorCode,
      message: result?.message,
      data: result?.data,
    };
  }

  // 创建轮胎询价
  public async createTyreInquiry(body: ITyreInquiryReParameter) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<
      TerminalRes<{
        demandId: string;
      }>
    >(`/terminal-api-v2/tyre_inquiry/create`, {
      headers,
      body,
    });

    return {
      errorCode: result?.errorCode,
      demandId: result?.data?.demandId,
      message: result?.message,
    };
  }

  // 创建代客轮胎询价
  public async createProxyTyreInquiry(body: ITyreInquiryReParameter) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<
      TerminalRes<{
        demandId: string;
      }>
    >(`/terminal-api-v2/tyre_inquiry/customer/create`, {
      headers,
      body,
    });

    return {
      errorCode: result?.errorCode,
      demandId: result?.data?.demandId,
      message: result?.message,
    };

  }

  /**
   * 获取轮胎报价数据
   */
  public async getTyreQuoteDetail(body: ITyreQuoteDetailRes) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<TerminalRes<ITyreQuoteResponse>>("/terminal-api-v2/tyre_inquiry/detail", {
      headers,
      body,
    });
    return {
      errorCode: result?.errorCode,
      data: result?.data || {},
      message: result?.message,
    };
  }

  /**
   * 获取代理询价轮胎报价数据
   */
  public async getProxyTyreQuoteDetail(body: ITyreQuoteDetailRes) {
    const headers = utilsClient.getHeaders();
    const { result } = await httpClient.post<TerminalRes<ITyreQuoteResponse>>("/terminal-api-v2/tyre_inquiry/customer/detail", {
      headers,
      body,
    });
    return {
      errorCode: result?.errorCode,
      data: result?.data || {},
      message: result?.message,
    };
  }
}

export const tyreClient = new TyreClient();
