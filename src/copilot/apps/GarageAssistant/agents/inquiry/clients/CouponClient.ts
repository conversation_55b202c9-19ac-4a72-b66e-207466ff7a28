import httpClient from "@/common/clients/http.client"
import { ICouponActivitiesResponse } from "../interface";

class CouponClient {
  public async acquireCoupons(companyId: string, couponActivityId: string) {
    const res = await httpClient.post(
      `/coupon-service/coupons/acquire?companyId=${companyId}&couponActivityId=${couponActivityId}`,
      { body: {} }
    );
    return {
      statusCode: res.statusCode,
      result: res.result,
    }
  }

  public async getCouponActivity(couponActivityId: string) {
    const res = await httpClient.get<ICouponActivitiesResponse>(
      `/coupon-service/coupon_activities/${couponActivityId}`
    );
    return {
      statusCode: res.statusCode,
      result: res.result,
    }
  }
}

export const couponClient = new CouponClient()
