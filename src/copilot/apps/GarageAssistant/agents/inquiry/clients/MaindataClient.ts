import httpClient from "@/common/clients/http.client";
import { MaindataRes } from "@/interfaces/client";
import { IBombPicData, IBombPicPayload, ICodeResult, IExactStdname, IStandardCodeItem } from "../interface/ICodeList";

class MaindataClient {
  /** 查询零件号信息 */
  async searchCodeList(codeList: string[]) {
    try {
      const { result } = await httpClient.post<MaindataRes<ICodeResult[]>>(
        `/maindata-service/maindata/restapi/codemessagebatch`,
        { body: { codeList } }
      );
      return result?.data || [];
    } catch {
      return [];
    }
  }
  /** 配件查标名 */
  async getStandardCodeList(names: string[]): Promise<IStandardCodeItem[]> {
    try {
      const { result } = await httpClient.post<MaindataRes<IExactStdname[]>>(
        `/maindata-service/maindata/restapi/exactmatchs`,
        {
          body: {
            req: names.map((word) => {
              return {
                word,
                seqNum: word,
              };
            }),
          },
        }
      );
      const resultItem: IStandardCodeItem[] = [];
      result?.data?.forEach((item) => {
        if (item.status !== 0) {
          return;
        }
        const idx = names.findIndex((name) => name === item.input.word);
        if (idx === -1) {
          return;
        }
        resultItem[idx] = {
          stdName: item.bestStdName.stdName,
          code: item.bestStdName.code,
        };
      });
      return resultItem;
    } catch {
      return [];
    }
  }

  /** 查询 epc 爆炸图 */
  async searchOeImages(body: IBombPicPayload) {
    try {
      const { result } = await httpClient.post<MaindataRes<IBombPicData[]>>(
        `/maindata-service/maindata/restapi/oeImages/oe/code/v1`,
        { body }
      );
      return result?.data || [];
    } catch {
      return [];
    }
  }
}

export const maindataClient = new MaindataClient();
