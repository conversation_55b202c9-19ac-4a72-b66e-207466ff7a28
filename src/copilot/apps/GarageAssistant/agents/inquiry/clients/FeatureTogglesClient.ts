import httpClient from "@/common/clients/http.client";
import logger from "@/common/logger";
import { Feature } from "@/interfaces/featureToggles";

class FeatureTogglesClient {
  public async getIsPilotArea(companyId: string, featureId: string): Promise<Feature> {
    try {
      const { result } = await httpClient.get(
        `/feature-toggles-service/feature_toggles/${companyId}?featureId=${featureId}`
      );
      if (result) {
        return result;
      }
    } catch (error) {
      logger.info(`获取特性开关 ${featureId} 失败: ${error}`);
    }
    return {
      enabled: false,
      featureId,
    };
  }
}

export const featureTogglesClient = new FeatureTogglesClient();
