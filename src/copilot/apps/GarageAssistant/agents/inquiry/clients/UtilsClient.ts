import { useRequestMeta } from "@/common/asyncStore";
import { CustomError } from "@/common/hooks/error";

class UtilsClient {
  /** 鉴权失败时，直接返回errorCode */
  public checkLogin(result: any) {
    if (result?.errorCode === 652 || result?.errorCode === 655) {
      throw new CustomError({
        errorCode: 652,
        message: result?.message,
      });
    }
  }

  public getHeaders() {
    const { headers } = useRequestMeta();
    return {
      cookie: headers.cookie || "",
      authorization: headers?.authorization || "",
      "user-agent": headers["user-agent"] || "",
      "ec-accessinfo": headers["ec-accessinfo"] || "",
      "ec-userinfo": headers["ec-userinfo"] || "",
    };
  }
}

export const utilsClient = new UtilsClient();
