import { IntentClassifier, Category } from "@/copilot/helpers/llm/tools";

export const InquiryIntents = {
  询报价: "询报价",
  修改配件信息: "修改配件信息",
  输入车架号: "输入车架号",
  买轮胎: "买轮胎",
  买配件: "买配件",
  修改品质: "修改品质",
  修改开票信息: "修改开票信息",
  修改地址: "修改地址",
  重新开始: "重新开始",
  其他: "其他",
  领福利: "领福利",
};

export const inquiryIntentClassifier = IntentClassifier.create()
  .task("此时用户正在采购配件，请根据用户的历史对话和当前消息，分析用户的意图，并以JSON格式返回。")
  .addCategory(
    Category.create(InquiryIntents.询报价)
      .describe(`用户询问价格或想要发起询报价。如果用户同时输入配件信息和车架号,归类到${InquiryIntents.询报价}`)
      .example(`车架号  LFV3A28W3K3725052，火花塞 -> ${InquiryIntents.询报价}`)
  )
  .addCategory(
    Category.create(InquiryIntents.买配件)
      .describe(`用户输入买配件,归类到${InquiryIntents.买配件}`)
      .example(`买配件 -> ${InquiryIntents.买配件}`)
  )
  .addCategory(
    Category.create(InquiryIntents.修改配件信息)
      .describe(
        `用户想要输入或修改配件信息。如果用户的输入中只包含配件信息及零件号，归类到${InquiryIntents.修改配件信息}。`
      )
      .alias("输入配件信息", "输入轮胎规格")
      .example(`刹车片不要了 -> ${InquiryIntents.修改配件信息}`)
      .example(`废气管 -> ${InquiryIntents.修改配件信息}`)
  )
  .addCategory(Category.create(InquiryIntents.输入车架号).describe("用户输入或修改车架号(VIN码)。"))
  .addCategory(Category.create(InquiryIntents.修改品质).describe("包含的品质有：原厂、品牌、拆车件。"))
  .addCategory(
    Category.create(InquiryIntents.修改开票信息)
      .describe(`用户想要修改开票信息，确认是否需要发票。`)
      .example(`需要开票 -> ${InquiryIntents.修改开票信息}`)
      .example(`在哪修改发票信息 -> ${InquiryIntents.其他}`)
  )
  .addCategory(Category.create(InquiryIntents.修改地址).describe(`用户想要修改地址或者收货地址`))
  .addCategory(
    Category.create(InquiryIntents.重新开始)
      .describe("用户想清空表单，重新开始对话。注意用户发出的是取消指令时，不能分类到此类别。")
      .alias("重置", "告别", "退出")
  )
  .addCategory(
    Category.create(InquiryIntents.领福利)
      .describe("用户想要领取福利，发放平台优惠券")
      .example(`给我点好处 -> ${InquiryIntents.领福利}`)
      .example(`给我点优惠券 -> ${InquiryIntents.领福利}`)
      .example(`给我点福利 -> ${InquiryIntents.领福利}`)
      .alias("领优惠券")
  )
  .addCategory(
    Category.create(InquiryIntents.其他)
      .describe("不在上面列表中的其他意图")
      .alias("咨询汽车故障", "查询配套品牌", "咨询代工信息", "咨询适配型号", "咨询功能入口", "咨询其他问题")
  );
