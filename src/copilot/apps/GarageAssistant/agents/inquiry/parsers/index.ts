import { Context, Entity } from "@casstime/copilot-core";

import _ from "lodash";
import logger from "@/common/logger";
import { inquiryIntentClassifier, InquiryIntents } from "./inquiryIntentClassifier";
import { extractPartNameEntities, extractQualityEntities, extractVinCodeEntities } from "@/copilot/helpers/entities";

// vin码正则
export const vinCodeRegex = /^[A-Z0-9]{17}$/i;

export async function parseEntities(context: Context): Promise<Entity[]> {
  const lastMessage = context.lastMessage;
  if (lastMessage.type === "text" || lastMessage.type === "voice") {
    const content = lastMessage.content || "";
    const partNameEntities = await extractPartNameEntities(content);
    const qualityEntities = extractQualityEntities(content);
    const vinCodeEntities = extractVinCodeEntities(content);

    const vinCodeSet = new Set(vinCodeEntities.map((entity) => entity.value));
    const entities = _.concat(
      qualityEntities,
      vinCodeEntities,
      partNameEntities.filter((entity) => !vinCodeSet.has(entity.value.replace(/\s/g, ""))) // 排除车架号
    );
    return _.uniqBy(entities, (entity) => entity.value);
  }
  return [];
}

export async function parseIntent(context: Context, candidates: string[]): Promise<string> {
  const lastMessage = context.lastMessage;
  if (lastMessage.nlu?.intent) {
    return lastMessage.nlu.intent;
  }
  if (lastMessage.type === "command") {
    return `@command/${lastMessage.command}`;
  }
  const intent = "其他";
  if (lastMessage.type === "text" || lastMessage.type === "voice") {
    const content = lastMessage.content || "";
    if (vinCodeRegex.test(content.trim())) {
      return InquiryIntents.输入车架号;
    }
    const registerIntents = candidates.filter((intent) => !intent.startsWith("@"));
    try {
      const intents = new Set(registerIntents);
      intents.add("其他");
      const intent = await inquiryIntentClassifier.classify(context, Array.from(intents));
      if (intents.has(intent)) {
        return intent;
      }
      return "其他";
    } catch (err) {
      logger.warn(`意图分类有误：${err}`);
    }
  }
  return intent;
}
