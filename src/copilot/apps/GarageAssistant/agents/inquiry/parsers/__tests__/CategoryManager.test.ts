import { Category, IntentClassifier } from "@/copilot/helpers/llm/tools";

it("CategoryManager", () => {
  const manger = new IntentClassifier();

  manger.addCategory(
    Category.create("询报价")
      .describe("用户想发起询报价。")
      .alias("配件输入", "车架号输入")
      .example("我想询价 -> 询报价")
      .example("机油格、空气滤芯 -> 询报价")
  );

  manger.addCategory(
    Category.create("修改发票信息").describe("用户想修改发票信息。").example("不需要发票 -> 修改发票信息")
  );

  expect(manger.categories.map((c) => c.name)).toEqual(["询报价", "修改发票信息"]);
});
