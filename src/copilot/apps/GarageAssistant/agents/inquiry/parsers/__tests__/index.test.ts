import { parseEntities } from "../index";
import { Context } from "@casstime/copilot-core";

// 测试数据数组
const testCases = [
  {
    name: "正常文本消息-提取所有实体类型",
    context: {
      lastMessage: {
        type: "text",
        content: "我需要原厂的发动机和轮胎，VIN码是1HGCM82633A123456",
      },
    },
    expected: [{ value: "原厂件" }, { value: "1HGCM82633A123456" }, { value: "发动机" }, { value: "轮胎" }],
  },
  {
    name: "包含轮胎规格-过滤特定零件实体",
    context: {
      lastMessage: {
        type: "text",
        content: "205/55R16防爆胎",
      },
    },
    expected: [{ value: "（轮胎）205/55R16" }],
  },
  {
    name: "非文本类型消息-返回空数组",
    context: {
      lastMessage: {
        type: "command",
        command: "search",
      },
    },
    expected: [],
  },
  {
    name: "空内容消息-返回空数组",
    context: {
      lastMessage: {
        type: "text",
        content: "",
      },
    },
    expected: [],
  },
  {
    name: "重复实体-自动去重",
    context: {
      lastMessage: {
        type: "text",
        content: "拆车 发动机发动机",
      },
    },
    expected: [{ value: "拆车件" }, { value: "发动机" }],
  },
];

describe("parseEntities", () => {
  // 使用测试数据数组执行测试
  test.each(testCases)("$name", async (testCase) => {
    // 执行被测函数
    const result = await parseEntities(testCase.context as Context);
    result.forEach((entity, index) => {
      expect(entity.value).toBe(testCase.expected[index]?.value);
    });
  });
});
