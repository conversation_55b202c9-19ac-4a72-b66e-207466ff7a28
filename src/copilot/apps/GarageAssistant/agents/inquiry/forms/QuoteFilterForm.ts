import { IFormConfig } from "@casstime/copilot-core";
import { SlotRecords } from "@casstime/copilot-core";
import inquiryClient from "@/clients/aggregationInquiry";
import { IDecodeResultsItem, IQuotationProductsItem } from "@/clients/aggregationInquiry/interfaces/IPostnquiryV3DTO";
import _ from "lodash";
import { IGetRecommendPlanRes } from "../interface/IRecommendPlan";
import { createObjectId } from "@/common/utils";

export interface QuoteFilterFormData {
  qualities?: string[];
  price?: "desc" | "asc";
  inquiryId?: string;
}

export const quoteFilterForm: IFormConfig<QuoteFilterFormData, IGetRecommendPlanRes[]> = {
  doMapping: (slots: SlotRecords) => {
    return {
      price: slots["price"],
      qualities: slots["qualities"],
      inquiryId: slots["inquiryId"],
    };
  },

  doClear: (context) => {
    // inquiryId 暂时不清除
    context.clearSlots(["price", "qualities"]);
  },
  /**
   * 表单校验
   */
  // doValidate: async (formData, context) => {},

  doSubmit: async function (formData) {
    const res = await inquiryClient.getInquiryDetailV3(formData.inquiryId!);
    type Quote = IQuotationProductsItem & IDecodeResultsItem;
    let quotes: Quote[] = [];
    res.userNeeds?.map((need) => {
      need.decodeResults?.forEach((decode) => {
        decode.quotationProducts?.forEach((product) => {
          quotes.push({
            ...need,
            ...decode,
            ...product,
            userNeeds: undefined,
            decodeResults: undefined,
            quotationProducts: undefined,
          } as Quote);
        });
      });
    });

    if (formData.qualities?.length) {
      const map: Record<string, string[]> = {
        原厂件: ["ORIGINAL_BRAND", "ORIGINAL_CURRENCY", "ORIGINAL_INLAND_4S"],
        品牌件: ["BRAND", "EXTERNAL_BRAND", "INTERNAL_BRAND"],
        拆车件: ["SECOND_HAND"],
      };
      console.log(JSON.stringify(quotes, null, 2));
      quotes = quotes.filter((item) => {
        return formData.qualities?.some((quality) => {
          return map[quality].includes(item.partsBrandQuality!);
        });
      });
    }
    if (formData.price) {
      quotes = _.orderBy(quotes, "sellerBtPrice", formData.price);
    }

    if (!quotes.length) {
      return [];
    }
    const data: IGetRecommendPlanRes = {
      programmeId: createObjectId(), // 询价单推荐方案id
      inquiryId: formData.inquiryId!, // 询价单号
      scenario: "场景", // 场景a
      programmeName: "", // 推荐方案名称
      programmeDescription: "", // 推荐方案描述
      createdDate: Date.now(), // 方案创建时间
      programmeItems: [
        {
          quotationProductId: quotes[0].quotationProductId!,
          standardItemId: quotes[0].resolveResultId!,
        },
      ],
    };
    return [data];
  },
};
