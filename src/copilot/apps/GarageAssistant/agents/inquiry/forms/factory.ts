import { ITextMessage, MessageFactory } from "@casstime/copilot-core";
import { createAddressAction } from "../utils";
import { ToQualificationAction } from "@/copilot/constants";

export function createErrorMessage(message: string): ITextMessage {
  const actionRules = [
    {
      matcher: /收货地址/,
      actions: [createAddressAction()],
    },
    {
      matcher: /未认证|本日询价次数已用完/,
      actions: [ToQualificationAction],
    },
  ];
  const actions = actionRules.find((rule) => rule.matcher.test(message))?.actions ?? [];
  return MessageFactory.text(message, { actions: [actions] });
}
