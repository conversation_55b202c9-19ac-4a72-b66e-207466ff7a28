import { IClassifier, UNKNOWN_SYMBOL } from "@casstime/copilot-core";
import { IntentCodes } from "../enum";
import { stringifyHistory, stringifyMessage } from "@/common/utils/message";
import { extractChars, hasChinese<PERSON>har } from "@/common/utils";
import { classifyTyreFilterIntent } from "@/copilot/apps/GarageAssistant/generator";
import { extractTyreEntities } from "@/copilot/helpers/entities";
import { AgentName } from "@/common/enums";

/**
 *
 */
export const quoteFilterClassifier: IClassifier = {
  id: "INQUIRY_QUOTE_FILTER_CLASSIFIER",
  async classify(context) {
    const vinCode = context.entities?.find((e) => e.name === "vinCode")?.value || "";
    if (vinCode) {
      return UNKNOWN_SYMBOL;
    }

    const partNames = context.entities?.filter((e) => e.name === "partName").map((e) => e.value) || [];
    if (partNames.length > 2) {
      return UNKNOWN_SYMBOL;
    }

    const entities = (context.entities || []).filter((e) => ["partName", "vinCode"].includes(e.name));
    const text = stringifyMessage(context.lastMessage, false).trim();
    const tyreEntities = extractTyreEntities(text);
    // 输入规格重新发布询价
    if (tyreEntities.some((e) => e.name === "tyreSpec")) {
      return UNKNOWN_SYMBOL;
    }
    // 输入中包含轮胎相关实体时，继续筛选
    if (tyreEntities.length > 0) {
      return IntentCodes.QUOTE_FILTER;
    }
    // 防止用户输入 雪地胎、At胎时，触发退出筛选
    if (entities.length === 1 && /胎$/.test(entities[0].value)) {
      return IntentCodes.QUOTE_FILTER;
    }

    const entitiesLength = entities.map((e) => e.value).join("").length || 0;
    // 统计有效字符长度（中文、英文和数字）
    const validTextLength = extractChars(text, "CHAR_CN_EN_NUM").length;

    const keywordsExit = ["退出", "结束", "询价", "看不懂", "重新开始"].some((item) => text.includes(item));
    // 输入多个实体，或者输入的全是实体，认为需要退出筛选
    if (keywordsExit || entities.length > 10 || entitiesLength >= validTextLength) {
      return UNKNOWN_SYMBOL;
    }

    const intent = await classifyTyreFilterIntent(text, stringifyHistory(context.historyMessages, 5));
    if (intent !== "A") {
      // 在轮胎筛选还未重构成独立Agent前，在这里拦截，重定向到fallbackAgent
      // fallback回答相关问题后，重新进入筛选
      await context.routeTo(AgentName.fallbackAgent);
      context.activateAgent(AgentName.inquiryAgent);
      context.activatePreClassifierOnce(quoteFilterClassifier);
      context.break();
      return UNKNOWN_SYMBOL;
    }

    /**
     * 无中文时，可能输入了花纹、标识等，保持筛选状态
     */
    if (!hasChineseChar(text)) {
      return IntentCodes.QUOTE_FILTER;
    }

    return IntentCodes.QUOTE_FILTER;
  },
};
