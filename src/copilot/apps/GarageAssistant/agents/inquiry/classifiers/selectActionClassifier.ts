import { stringifyHistory } from "@/common/utils/message";
import { IClassifier, UNKNOWN_SYMBOL } from "@casstime/copilot-core";
import _ from "lodash";
import { PromptService } from "@/copilot/services/PromptService";

/**
 * 根据上一条系统回复的消息分析用户当前发送消息的意图
 */
export const selectActionClassifier: IClassifier = {
  id: "SELECT_MESSAGE_ACTION_CLASSIFIER",
  async classify(context) {
    const lastMessage = context.lastMessage;
    if (lastMessage.type !== "text" || !lastMessage.nlu) {
      return UNKNOWN_SYMBOL;
    }
    const questionMessage = _.last(context.historyMessages);
    if (questionMessage?.fromUser !== "system") {
      return UNKNOWN_SYMBOL;
    }

    const actions = questionMessage.actions?.flat()?.filter((action) => action.type === "nlu");
    if (!actions?.length) {
      return UNKNOWN_SYMBOL;
    }
    const promptService = context.getService(PromptService);
    const lastRoundMessage = [questionMessage, lastMessage];
    const dialogue = stringifyHistory(lastRoundMessage);
    const allIntents = [...actions.map((action) => action.text), "不确定"];
    const intent = await promptService.parseUserIntentQuickly(dialogue, allIntents);
    if (!intent) {
      return UNKNOWN_SYMBOL;
    }
    const index = allIntents.indexOf(intent);
    const action = actions[index];
    if (!action) {
      return UNKNOWN_SYMBOL;
    }
    Object.assign(context.lastMessage, { nlu: action.nlu });
    return action.nlu?.intent || UNKNOWN_SYMBOL;
  },
};
