import { Context, IClassifier, ITextMessage } from "@casstime/copilot-core";
import { parseIntent } from "../parsers";
import { InquiryIntents } from "../parsers/inquiryIntentClassifier";
import { commandIntents, imageIntents } from "@/copilot/constants";
import _ from "lodash";
import { extractChars } from "@/common/utils";
import { SlotNames } from "../slots/SlotNames";

/**
 * 根据消息确认用户是否是“其他”意图
 * @param text
 * @returns
 */
function isOtherIntent(text: string) {
  const isQuestion = /(什么(品牌)?|[呢吗？?])$/.test(text);
  // 查配套
  const hasKeywords = /配套/.test(text);
  return isQuestion || hasKeywords;
}

/**
 * 询价Agent的意图分类器
 */
export const intentClassifier: IClassifier = {
  id: "INQUIRY_INTENT_CLASSIFIER",
  async classify(context, categories) {
    // 默认图片消息，走图片识别
    if (context.lastMessage.type === "image") {
      return imageIntents.classifyImage;
    }
    const text = (context.lastMessage as ITextMessage)?.content;
    // 订单号开头
    const orderIds = text?.match(/^S\d{13}/) || [];
    if (orderIds.length) {
      context.mergeSlots({
        orderIds: [orderIds[0]],
      });
      return `@command/${commandIntents.VIEW_ORDER}`;
    }

    // 询价单号开头
    const inquiryIds = text?.match(/^B\d{11}/) || [];
    if (inquiryIds.length) {
      context.mergeSlots({
        inquiryIds: [inquiryIds[0]],
      });
      return `@command/${commandIntents.VIEW_INQUIRY}`;
    }
    const entitiesLength = context.entities.map((e) => e.value).join("").length;
    const partNameEntities = context.entities.filter((e) => e.name === "partName");
    // 统计有效字符长度（中文、英文和数字）
    const validTextLength = extractChars(text, "CHAR_CN_EN_NUM").length;
    const entityValues = partNameEntities.map((e) => e.value);
    // 如果有效字符长度等于实体字符长度，则认为意图为询报价，不需要LLM分类，减少调用次数，加快响应速度
    if (validTextLength === entityValues.join("").length) {
      // 用户在【报价结果】后面发送配件时，提示追加。 TODO: 前置分类器放开后，这里逻辑应移到前置分类器中
      const notCommandMessages = context.historyMessages?.filter((msg) => msg.type !== "command");
      const isQuoteMessage = _.last(notCommandMessages)?.extra?.inquiryId;
      return isQuoteMessage ? `@command/${commandIntents.DIRECT_APPEND_PARTS}` : InquiryIntents.询报价;
    }

    let intent = await parseIntent(
      context,
      categories.filter((item) => typeof item === "string")
    );

    // 用于判断用户是否想填充表单
    const isFillingForm = entitiesLength && (validTextLength * 0.8 < entitiesLength || partNameEntities.length >= 5);
    if (intent === "其他" && !isOtherIntent(text) && isFillingForm) {
      intent = InquiryIntents.询报价;
    }
    const isInquiry = [InquiryIntents.询报价, InquiryIntents.修改配件信息, InquiryIntents.修改品质].includes(intent);

    // 询报价且是以“追加”开头，则重置意图为直接追加配件
    if (isInquiry && text.startsWith("追加")) {
      intent = `@command/${commandIntents.DIRECT_APPEND_PARTS}`;
    }

    return intent;
  },
};

function isOil(context: Context) {
  // 只判断文本消息
  if (context.lastMessage.type !== "text") {
    return false;
  }

  // 多个实体
  const entities = context.entities.filter((e) => e.name === "partName");
  if (entities.length > 1 || context.slots[SlotNames.partNames]?.length > 0) {
    return false;
  }

  // 带VIN码
  if (context.slots[SlotNames.vinCode]) {
    return false;
  }

  const content = context.lastMessage.content;
  const entityValue: string = entities[0]?.value || "";
  return entityValue.endsWith("机油") || /机油|\dW-\d+/.test(content);
}
