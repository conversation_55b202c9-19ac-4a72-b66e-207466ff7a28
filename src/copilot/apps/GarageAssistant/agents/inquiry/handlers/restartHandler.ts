import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { SLOTS_CLEARED_FOR_BYE } from "@/copilot/constants/ClearSlots";
import { AgentName } from "@/copilot/constants";

export const restartHandler: IHandlerFunction = async (context) => {
  context.clearSlots(SLOTS_CLEARED_FOR_BYE);
  await context.routeTo(AgentName.fallbackAgent);
  // 重置session
  await context.recreateSession(true);
  // 重置时，激活inquiryAgent
  context.activateAgent(AgentName.inquiryAgent);
  // 补一条命令消，将新的sessionId带给前端
  context.reply(MessageFactory.command("__restart__", {}));
};
