import { Context, IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { FormNames, inquiryForm, InquiryFormErrorField } from "../forms";
import { inquiryService, IReplyMessagePayload } from "../services/InquiryService";
import { commandIntents } from "@/copilot/constants";
import { Version } from "@/common/utils";
import { inquirySubmitClassifier } from "../classifiers";
import { generateGuidanceText } from "@/copilot/apps/GarageAssistant/generator";
import logger from "@/common/logger";
import ValidateError, { ValidateErrorCode } from "@/common/error/ValidateError";
import { isOnlyTyreEntities } from "../utils";
import { executePrompt, getOrCreateReplyMsgId, waitStreamContent } from "@/copilot/helpers";
import { selectActionClassifier } from "../classifiers/selectActionClassifier";
import { ChannelService } from "../services/ChannelService";
import { MsgContent } from "@/copilot/richtext/components/Content";
import { renderRichtext } from "@/copilot/richtext";

export const inquiryHandler: IHandlerFunction = async (context: Context) => {
  // 1、处理表单
  // 图片消息且携带 nlu，是则保存图片
  const { lastMessage, slotsChanges, payload, entities } = context;
  const form = context.getForm<typeof inquiryForm>(FormNames.inquiryForm);
  let formData = form.getValues();
  if (lastMessage.type === "image" && lastMessage.nlu) {
    const imageSlots = inquiryService.saveNluImage(formData, lastMessage, slotsChanges);
    context.mergeSlots(imageSlots);
  }
  // 2、校验表单
  const { error } = await form.validate();
  // 3、生成回复消息
  formData = form.getValues();
  // 只输入轮胎规格实体，跳过校验，直接发布询价
  const onlyTyreEntities = isOnlyTyreEntities(entities);
  if (onlyTyreEntities) {
    return context.next(commandIntents.IMMEDIATE_INQUIRY);
  }
  let replyMsg: IReplyMessagePayload = {};
  if (error === undefined) {
    // 表单通过校验，当前输入车架号，且没有修改配件，直接发布询价
    const { vinCode, partNames } = slotsChanges || {};
    if (vinCode?.changed && !partNames?.changed) {
      return context.next(commandIntents.IMMEDIATE_INQUIRY);
    }

    replyMsg = inquiryService.getSubmitInquiryFormMessage(formData, vinCode?.changed, {
      slotsChanges,
      repairScenarios: context.slots.repairScenarios,
      id: getOrCreateReplyMsgId(context),
      appVersion: payload.appVersion as Version,
    });
    context.clearSlots(["__ADDITIONALPARTS__"]);
  } else {
    replyMsg = inquiryService.getFillInquiryFormMessage(formData, error, {
      repairScenarios: context.slots.repairScenarios,
      appVersion: payload.appVersion as Version,
    });
    // 清空候选车架号
    context.clearSlots(["vinCodes"]);
  }

  // 有command消息则直接回复
  if (replyMsg.replyCommandMsg) {
    context.reply(MessageFactory.markdown("识别到多车型，请选择"), { id: getOrCreateReplyMsgId(context) });
    return context.reply(replyMsg.replyCommandMsg);
  }
  // 获取通道类型
  const channelService = context.getService(ChannelService);
  const dialogue = await channelService.getDialogue();
  const businessId = dialogue?.businessId || "";
  const msg = inquiryService.createInquiryFormReplyMessage(formData, replyMsg, payload.appVersion as Version, {
    businessId,
  });
  // 仅需要输入车架号或配件名，直接回复
  let needAIReply = true;
  if (ValidateError.isValidateError<{ key: string; errorVinCode?: string }>(error)) {
    const needVinCodeOrPartName =
      error.code === ValidateErrorCode.MISSING_REQUIRED_FIELD &&
      ([InquiryFormErrorField.VINCODE, InquiryFormErrorField.PARTNAME] as string[]).includes(error.data.key);
    const suspectedVin =
      error.code === ValidateErrorCode.LENGTH_ERROR && error.data.key === InquiryFormErrorField.VINCODE;
    const duplicateInquiryId =
      error.code === ValidateErrorCode.DUPLICATE_VALUE && error.data.key === InquiryFormErrorField.INQUIRYID;
    needAIReply = !(needVinCodeOrPartName || suspectedVin || duplicateInquiryId);
  }
  if (context.entities.length === 0 && lastMessage.type !== "image") {
    needAIReply = true;
  }
  logger.debug("need ai reply", context.entities, needAIReply);
  const id = getOrCreateReplyMsgId(context);
  // ai回复，流式输出
  if (needAIReply) {
    try {
      context.setReplyMode("stream");
      const content = "content" in lastMessage ? lastMessage.content : "";
      const msgStream = await executePrompt("采购助手/采购助手询价表单引导提示词", {
        input: content,
        answer: msg.content || msg.extra?.content || "",
        formData: JSON.stringify(
          {
            ...formData,
            carModel: formData.carModel?.saleModelName || formData.carModel?.model,
            // 地址信息不传给大模型，防止泄露用户隐私
            address: formData.address?.address ? "已录入" : undefined,
          },
          null,
          2
        ),
        hasVinCode: Boolean(formData.carModel),
      });
      msg.extra!.content = await waitStreamContent(msgStream);
      msg.id = id;
    } catch (error) {
      logger.info("generateGuidanceText error", error);
    }
  }
  const embedContent = renderRichtext(MsgContent, { text: msg.extra?.content || "" });
  if (msg.embed?.type === "richtext") {
    msg.embed.content += embedContent;
  } else if (!msg.embed) {
    msg.embed = { type: "richtext", content: embedContent };
  } else {
    msg.content = msg.extra?.content || msg.content;
  }
  context.reply(msg, { id });
  context.activatePreClassifierOnce(inquirySubmitClassifier);
  // TODO: 是否需要发票和立即询价更优雅的判断
  if (msg.actions?.flat()?.some((action) => action.text.includes("发票"))) {
    context.activatePreClassifierOnce(selectActionClassifier);
  } else {
    context.activatePreClassifierOnce(inquirySubmitClassifier);
  }
};
