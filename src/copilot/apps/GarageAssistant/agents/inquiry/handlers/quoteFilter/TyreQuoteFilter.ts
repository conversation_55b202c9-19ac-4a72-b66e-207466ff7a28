import { doubao_chat } from "@/clients/llm";
import { LLMMessageTemplate, LLMRunnableBuilder } from "@/copilot/helpers/llm/tools";
import logger from "@/common/logger";
import _ from "lodash";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
import { IGetRecommendPlanRes } from "../../interface/IRecommendPlan";
import { TyreQuoteTable } from "./TyreQuoteTable";
import { executePrompt, waitStreamContent } from "@/copilot/helpers/llm";
import { ITyreInquiryInfo } from "../../interface/ITyreInquiry";

const text2sqlPrompt = `
你是一名专业的数据库工程师，擅长将自然语言问题转换为 SQL 查询。请根据用户问题生成相应的 SQL 查询语句。
用户问题：{question}

数据库表结构如下：
  tyre_item(轮胎报价条目表)
    - quotationProductId TEXT, -- 报价条目ID
    - atPrice REAL, -- 含税价格
    - btPrice REAL, -- 税前价格
    - locationName TEXT, -- 发货仓，使用LIKE匹配，如 LIKE %北京五方仓%
    - organizationName TEXT, -- 发货店铺名称（卖家、供应商），使用LIKE匹配
    - partsName TEXT, -- 轮胎名称，用LIKE匹配
    - modelName TEXT, -- 适用车型，用LIKE匹配
    - remark TEXT, -- 备注，包含轮胎品牌、花纹、特性及标识，使用LIKE匹配，可能含多个条件，如：LIKE %米其林% AND LIKE %PNCS%
    - outOfStock TEXT，-- 缺货标识，是：缺货，否：不缺货

remark可能包含以下内容：
    - 轮胎品牌，一般为中文，如：米其林、邓禄普等
    - 轮胎花纹，英文大写，如： CINTURATO P7、P ZERO等
    - 轮胎特性，如：防爆、非防爆、防滑、轮辋保护、静音、节油、雪地胎等
    - 轮胎标识，PNCS、MO、XL、ACOUSTIC、FRV等

请按照以下步骤完成任务：
    - 仔细理解用户问题，提取关键信息。
    - 仔细理解数据库表结构，注意各张表包含的字段及类型。
    - 将用户问题转换为 SQL 语句。
    
生成 SQL 查询时，请遵循以下要求：
    - 涉及价格时，如果没有明确说明，使用 btPrice 字段。
    - 涉及仓库或地区时，使用 locationName 字段，使用 LIKE 语句匹配。
    - 用户问题包含价格时，请先对价格排序。
    - TEXT类型尽可能使用 LIKE 语句匹配。
    - 尽可能返回 id 字段，便于后续操作。
    - 返回 SQL 语句，格式为 JSON 对象，包含 "sql" 字段。
    - 如果无法回答问题或问题不明确，返回 {{"msg": "无法回答"}}。
`.trim();

const replyPrompt = `
请根据以下执行结果，组织语言回复用户问题。
这条SQL执行结果是：{result}
有需要时，可以用markdown展示，要求：
1. 文案简洁明了，不要出现重复累赘信息
2. 文案中禁止出现“数据库”字样或SQL语句
3. 可以按需要使用表格，禁止使用列表
4. 使用表格时，表头用中文，不显示id字段，不能超过4列，不超过5行，第5行用“...”填充
`.trim();

const rewritePrompt = `
你是一名自然语言及数据库专家，帮助用户重写问题，以便获得更准确的SQL查询结果。
1. 帮助用户明确需要查询的的条件。
2. 不能改变用户语义，不能丢失信息。
3. 用户问题包含地区时，需要考虑是店铺名称的组成部分。
4. 用户输入中，如果是英文品牌，需要转换为中文品牌。

示例：
1. 广州凌云汽配最便宜 -> 我想查【广州凌云汽配】商家中最便宜的报价条目
2. 广州最便宜的 -> 我想查【广州】地区中最便宜的报价条目
3. 深圳米其林最便宜的轮胎 -> 我想查【深圳】地区中，【米其林】品牌，最便宜的报价条目
4. 广州宝源的 -> 我想查【广州宝源】商家的所有报价条目
5. 北京五方仓 -> 我想查【北京五方仓】仓库中的所有报价条目
6. 带棉的 -> 我想查带【静音棉】报价条目

问题：
{input}
输出：
`;
const template = LLMMessageTemplate.create("user", text2sqlPrompt);
const replyRunnable = LLMRunnableBuilder.create(doubao_chat)
  .addPrompt(template)
  .addPrompt(LLMMessageTemplate.create("assistant", "{assistant_reply}"))
  .addPrompt(LLMMessageTemplate.create("user", replyPrompt))
  .build({ type: "string" });

const rewriteRunnable = LLMRunnableBuilder.create(doubao_chat)
  .addPrompt(LLMMessageTemplate.create("user", rewritePrompt))
  .build({ type: "string" });

export class TyreQuoteFilter {
  private table: TyreQuoteTable;
  constructor(
    private inquiryId: string,
    private input: string,
    private inquiryInfo: ITyreInquiryInfo
  ) {
    this.table = new TyreQuoteTable(this.inquiryId, this.inquiryInfo);
  }
  private executedSql?: { sql: string };

  /**
   * 重新改写用户输入，提升输入质量
   * @returns
   */
  private async rewriteInput() {
    const input = await rewriteRunnable.invoke({ input: this.input }, { callbacks: [llmLogCallback] });
    return input;
  }

  /**
   * 生成SQL语句
   * @returns
   */
  async generateSql() {
    const input = await this.rewriteInput();
    const stream = await executePrompt("采购助手/采购助手轮胎筛选SQL生成提示词", {
      question: input,
    });
    const content = await waitStreamContent(stream);
    const sql = /```sql\n([\s\S]+?)```/g.exec(content);
    return sql ? sql[1] : "";
  }

  sample(n: number) {
    return this.table.sample(n);
  }

  /**
   * 重写SQL, 使其查询结果更为准确
   * @param sql
   * @returns
   */
  async rewriteSql(sql: string) {
    let newSql = sql;
    if (sql?.includes("防爆") && !sql?.includes("非防爆")) {
      newSql = sql.replace(/\sWHERE\s/i, ' WHERE remark NOT LIKE "%非防爆%" AND ');
    }
    return newSql;
  }

  initialize() {
    return this.table.initialize();
  }

  /**
   * 提取报价方案
   */
  async exactSchemes(rows: unknown[]): Promise<IGetRecommendPlanRes[]> {
    if (!rows?.length) {
      return [];
    }

    return [];
  }

  /**
   * 执行SQL语句，返回执行结果
   * @param sql
   * @returns
   */

  async executeSql(sql: string) {
    this.executedSql = { sql };
    try {
      let result = await this.table.exec(sql!);
      console.log("--->", result);
      // let schemeIds = result.map((item) => _.get(item, "schemeId")).filter(Boolean);
      const productIds = result.map((item) => _.get(item, "quotationProductId")).filter(Boolean);
      if (productIds?.length) {
        result = await this.table.exec(`SELECT * FROM tyre_item WHERE quotationProductId IN (${productIds.join(",")})`);
      }
      return result;
    } catch (err) {
      logger.error("execute sql error", err);
    }
    return [];
  }

  exactResponses(rows: unknown[]) {
    const hasProductId = rows.some((row) => _.get(row, "quotationProductId"));
    if (hasProductId) {
      return this.table.filterResponseList(rows.map((row) => _.get(row, "quotationProductId")!));
    }
    return [];
  }

  /**
   * 根据SQL执行结果生成回复
   * @param answers
   * @returns
   */
  async generateReply(answers: unknown[]) {
    // 最大行数
    const MAX_ROWS = 6;
    return replyRunnable.stream(
      {
        assistant_reply: JSON.stringify(this.executedSql),
        result: JSON.stringify(
          answers.slice(0, MAX_ROWS).map((item) => ({ ...(item as object), description: "" })),
          null,
          2
        ),
        question: this.input,
      },
      { callbacks: [llmLogCallback] }
    );
  }

  dispose() {
    return this.table.dispose();
  }
}
