import inquiryClient from "@/clients/aggregationInquiry";
import { IDecodeResultsItem, IQuotationProductsItem } from "@/clients/aggregationInquiry/interfaces/IPostnquiryV3DTO";
import { callbackify, withResolvers } from "@/common/utils/async";
import logger from "@/common/logger";
import _ from "lodash";
import { Database } from "sqlite3";

export type QuoteItem = IQuotationProductsItem & IDecodeResultsItem & { itemId: number };

const fields = `
inquiryId TEXT,
arrivalTime INTEGER,
atBoxFee REAL,
atBuyerAndBoxFee REAL,
btBoxFee REAL,
btBuyerAndBoxFee REAL,
btPrice REAL,
brandId TEXT,
brandName TEXT,
distance REAL,
hotSale TEXT,
isOrdered BOOLEAN,
isQuotationNoInventory BOOLEAN,
isReplacement BOOLEAN,
isShowPartsNum BOOLEAN,
locationName TEXT,
originalAssort INTEGER,
partType TEXT,
partsBrandQuality TEXT,
partsName TEXT,
needsName TEXT,
partsNum TEXT,
price REAL,
productId TEXT,
productSetCode TEXT,
productSetId TEXT,
productType TEXT,
quotationId TEXT,
quotationProductId TEXT,
quotedTime DATETIME,
resolveResultId TEXT,
sellStatus TEXT,
sellType TEXT,
sellerBtPrice REAL,
sellerBtPriceAndBoxFee REAL,
sellerPrice REAL,
sellerPriceAndBoxFee REAL,
source TEXT,
storeCategoryScore REAL,
storeId TEXT,
storeName TEXT,
storeServiceArea TEXT,
supplierCompanyId TEXT,
taxRate REAL,
whetherProductSet TEXT
`.trim();

const keys = fields
  .split(",")
  .map((field) => field.trim().split(" ")[0])
  .filter(Boolean);

const QUALITY_NAMES: Record<string, string> = {
  ORIGINAL_CURRENCY: "原厂件",
  ORIGINAL_BRAND: "原厂件", // 原厂件
  EXTERNAL_BRAND: "品牌件", // 国外品牌件
  INTERNAL_BRAND: "品牌件", // 其他品牌件
  SECOND_HAND: "拆车件", // 拆车件
  OTHER_BRAND: "品牌件", // 其他
  BRAND: "品牌件", // 品牌
  ORIGINAL_INLAND_4S: "原厂件",
  EQUIVALENT_BRAND: "品牌件",
};

export type Scheme = {
  schemeId: number;
  schemeName: string;
  items: number[];
  btTotalPrice: number;
  totalPrice: number;
  distance: number;
  description: string;
};

export class QuoteTable {
  private db!: Database;
  constructor(private inquiryId: string) {}

  /**
   * 拉取当前询价单对应的报价条目
   * @returns
   */
  private async fetchQuoteItems(): Promise<QuoteItem[]> {
    const res = await inquiryClient.getInquiryDetailV3(this.inquiryId);
    const quotes: QuoteItem[] = [];
    let index = 1;

    res.userNeeds?.map((need) => {
      need.decodeResults?.forEach((decode) => {
        decode.quotationProducts?.forEach((product) => {
          quotes.push({
            itemId: index,
            ...product,
            ...need,
            ...decode,
            userNeeds: undefined,
            decodeResults: undefined,
            quotationProducts: undefined,
          } as QuoteItem);
          index++;
        });
      });
    });

    return quotes;
  }

  private getSchemeDesc(quoteItems: QuoteItem[]) {
    const SEP = " | ";
    const storeName = _.uniq(quoteItems.map((item) => item.storeName)).join(SEP);
    const quality = _.uniq(quoteItems.map((item) => QUALITY_NAMES[item.partsBrandQuality!])).join(SEP);
    const locationName = _.uniq(quoteItems.map((item) => item.locationName)).join(SEP);
    const storeServiceArea = _.uniq(quoteItems.map((item) => item.storeServiceArea)).join(SEP);
    return [storeName, quality, locationName, storeServiceArea].join(SEP);
  }

  private getSchemeItem(quoteItem: QuoteItem[], schemeId: number, schemeName: string) {
    return {
      schemeId,
      schemeName,
      btTotalPrice: _.sumBy(quoteItem, "btPrice"),
      totalPrice: _.sumBy(quoteItem, "price"),
      distance: _.sumBy(quoteItem, "distance") / quoteItem.length,
      description: this.getSchemeDesc(quoteItem),
      items: quoteItem.map((item) => item.itemId),
    };
  }

  private getCheapestItemsByQuality = (items: QuoteItem[], quality: string): QuoteItem[] => {
    return items
      .filter((item) => QUALITY_NAMES[item.partsBrandQuality!] === quality)
      .reduce((cheapestItems, item) => {
        if (!cheapestItems.length || item.price! < cheapestItems[0].price!) {
          cheapestItems = [item];
        }
        return cheapestItems;
      }, [] as QuoteItem[]);
  };

  private getCheapestItemsByName = (items: QuoteItem[], key: keyof QuoteItem, value: string): QuoteItem[] => {
    return items
      .filter((item) => item[key] === value)
      .reduce((cheapestItems, item) => {
        if (!cheapestItems.length || item.price! < cheapestItems[0].price!) {
          cheapestItems = [item];
        }
        return cheapestItems;
      }, [] as QuoteItem[]);
  };

  /**
   * 生成报价方案
   * @param quoteItems
   * @returns
   */

  private generateSchemes(quoteItems: QuoteItem[]): Scheme[] {
    let index = 1;
    const schemes: Scheme[] = [];
    // 按店铺、配件名称分组
    const storeGroup = _.groupBy(quoteItems, "storeName");
    const partsNameGroup = _.groupBy(quoteItems, "partsName") as Record<string, QuoteItem[]>;

    // 指定店铺最便宜
    Object.keys(storeGroup).forEach((storeName) => {
      const cheapests: QuoteItem[] = [];
      for (const key of Object.keys(partsNameGroup)) {
        const cheapestItems = this.getCheapestItemsByName(partsNameGroup[key], "storeName", storeName);
        cheapests.push(...cheapestItems);
      }
      if (cheapests.length > 0) {
        schemes.push(this.getSchemeItem(cheapests, index++, `${storeName}最便宜`));
      }
    });

    // 指定配件最便宜
    for (const key of Object.keys(partsNameGroup)) {
      const cheapestItems = this.getCheapestItemsByName(partsNameGroup[key], "partsName", key);
      if (cheapestItems.length > 0) {
        schemes.push(this.getSchemeItem(cheapestItems, index++, `${key}最便宜`));
      }
    }

    // 指定品质最便宜
    ["原厂件", "品牌件", "拆车件"].forEach((quality) => {
      const cheapests: QuoteItem[] = [];
      for (const key of Object.keys(partsNameGroup)) {
        const cheapestItems = this.getCheapestItemsByQuality(partsNameGroup[key], quality);
        cheapests.push(...cheapestItems);
      }
      if (cheapests.length > 0) {
        schemes.push(this.getSchemeItem(cheapests, index++, `${quality}最便宜`));
      }
    });

    // 所有配件最便宜的
    const cheapest = Object.values(partsNameGroup).map((items) => _.orderBy(items, "price", "asc")[0]);
    const cheapestItemIds = cheapest.map((item) => item.itemId);
    if (cheapest.length > 0) {
      schemes.push({
        schemeId: index++,
        schemeName: "最便宜",
        btTotalPrice: _.sumBy(cheapest, "btPrice"),
        totalPrice: _.sumBy(cheapest, "price"),
        distance: _.sumBy(cheapest, "distance") / cheapest.length,
        description: this.getSchemeDesc(cheapest),
        items: cheapestItemIds,
      });
    }

    // 品质最好的
    const best = Object.values(partsNameGroup).map(
      (items) =>
        _.orderBy(
          items,
          [
            (item) => {
              const values = {
                原厂件: 1,
                品牌件: 2,
                拆车件: 3,
              } as Record<string, number>;
              const quality = QUALITY_NAMES[item.partsBrandQuality as keyof typeof QUALITY_NAMES];
              console.log(values[quality], "--->", item.partsBrandQuality);
              return values[quality];
            },
            (item) => item.storeName,
          ],
          ["asc", "asc"]
        )[0]
    );
    const bestItemIds = best.map((item) => item.itemId);
    if (!schemes.some((item) => item.items.join(",") === bestItemIds.join(","))) {
      schemes.push({
        schemeId: index++,
        schemeName: "品质最好",
        btTotalPrice: _.sumBy(best, "btPrice"),
        totalPrice: _.sumBy(best, "price"),
        distance: _.sumBy(best, "distance") / best.length,
        description: this.getSchemeDesc(best),
        items: best.map((item) => item.itemId),
      });
    }

    return schemes;
  }

  /**
   * 实例化内存数据库
   * @returns
   */
  private createDb() {
    const { promise, resolve, reject } = withResolvers();
    this.db = new Database(":memory:", callbackify(resolve, reject));
    return promise;
  }

  /**
   * 创建报价方案表
   * @returns
   */
  private createSchemeTable() {
    const { promise, resolve, reject } = withResolvers();
    const sql = `CREATE TABLE quotation_scheme (
      schemeId INTEGER PRIMARY KEY AUTOINCREMENT,
      schemeName TEXT,
      distance REAL,
      btTotalPrice REAL, 
      totalPrice REAL,
      description TEXT,
      itemCount INTEGER
    )`;
    this.db.run(sql, callbackify(resolve, reject));
    return promise;
  }

  /**
   * 创建关联表
   * @returns
   */
  private createMappingTable() {
    const { promise, resolve, reject } = withResolvers();
    const sql = `CREATE TABLE scheme_item_mapping (
      schemeId INTEGER,
      itemId INTEGER,
      PRIMARY KEY (schemeId, itemId),
      FOREIGN KEY (schemeId) REFERENCES quotation_scheme(schemeId),
      FOREIGN KEY (itemId) REFERENCES quotation_item(itemId)
    )`;
    this.db.run(sql, callbackify(resolve, reject));
    return promise;
  }

  /**
   * 创建报价条目表
   * @returns
   */
  private createItemsTable() {
    const { promise, resolve, reject } = withResolvers();
    this.db.run(
      `CREATE TABLE IF NOT EXISTS quotation_item (
        itemId INTEGER PRIMARY KEY AUTOINCREMENT,
        ${fields}
      )`,
      callbackify(resolve, reject)
    );
    return promise;
  }
  /**
   * 插入报价条目
   * @param quoteItems
   */

  private async insertQuoteItems(quoteItems: QuoteItem[]) {
    const sql = `INSERT INTO quotation_item VALUES (?, ${keys.map(() => " ? ").join(",")})`;
    const stmt = this.db.prepare(sql);
    logger.info("sql-->", sql);
    for (const quoteItem of quoteItems) {
      const { promise, resolve, reject } = withResolvers();
      const values = keys.map((key) => {
        const value = _.get(quoteItem, key);
        if (key === "quotedTime") {
          return value / 1000;
        }
        if (key === "partsBrandQuality") {
          return QUALITY_NAMES[value as keyof typeof QUALITY_NAMES];
        }
        return value;
      });
      stmt.run([quoteItem.itemId, ...values], callbackify(resolve, reject));
      await promise;
    }
    const { promise, resolve, reject } = withResolvers();
    stmt.finalize(callbackify(resolve, reject));
    await promise;
  }

  /**
   * 插入报价方案
   * @param schemes
   */
  private async insertSchemes(schemes: Scheme[]) {
    const sql = `INSERT INTO quotation_scheme VALUES (?, ?, ?, ?, ?, ?, ?)`;
    const stmt = this.db.prepare(sql);
    for (const scheme of schemes) {
      const { promise, resolve, reject } = withResolvers();
      stmt.run(
        [
          scheme.schemeId,
          scheme.schemeName,
          scheme.distance,
          scheme.btTotalPrice,
          scheme.totalPrice,
          scheme.description,
          scheme.items.length,
        ],
        callbackify(resolve, reject)
      );
      await promise;
    }
    const { promise, resolve, reject } = withResolvers();
    stmt.finalize(callbackify(resolve, reject));
    await promise;
  }

  /**
   * 插入关联报价条目
   * @param schemes
   */

  private async insertMapping(schemes: Scheme[]) {
    const sql = `INSERT INTO scheme_item_mapping VALUES (?, ?)`;
    const stmt = this.db.prepare(sql);
    for (const scheme of schemes) {
      for (const itemId of scheme.items) {
        const { promise, resolve, reject } = withResolvers();
        stmt.run([scheme.schemeId, itemId], callbackify(resolve, reject));
        await promise;
      }
    }
    const { promise, resolve, reject } = withResolvers();
    stmt.finalize(callbackify(resolve, reject));
    await promise;
  }

  async initialize() {
    const quoteItems = await this.fetchQuoteItems();
    const schemes = this.generateSchemes(quoteItems);
    await this.createDb();
    await this.createItemsTable();
    await this.createSchemeTable();
    await this.createMappingTable();
    await this.insertQuoteItems(quoteItems);
    await this.insertSchemes(schemes);
    await this.insertMapping(schemes);
  }

  async exec(sql: string) {
    const { promise, resolve, reject } = withResolvers<unknown[]>();
    this.db.all(sql, callbackify(resolve, reject));
    return promise;
  }

  async dispose() {
    await this.db?.close();
  }
}
