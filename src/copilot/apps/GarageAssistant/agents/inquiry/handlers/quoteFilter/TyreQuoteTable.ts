import { callbackify, withResolvers } from "@/common/utils/async";
import logger from "@/common/logger";
import _ from "lodash";
import { Database } from "sqlite3";
import { tyreService } from "../../services/TyreService";
import { IQuoteProductData, ITyreQuoteAndDemand } from "../../interface/ITyreInquiry";
import { sleep } from "@/common/utils";
import { ITyreInquiryInfo } from "../../interface/ITyreInquiry";

const fields = `
quotationProductId TEXT,
atPrice REAL,
btPrice REAL,
locationName TEXT,
organizationName TEXT,
partsName TEXT,
modelName TEXT,
remark TEXT,
outOfStock TEXT
`.trim();

type TyreQuoteItem = {
  quotationProductId: string;
  atPrice: number;
  btPrice: number;
  locationName: string;
  organizationName: string;
  partsName: string;
  modelName: string;
  remark: string;
  outOfStock: string;
};

const keys = fields
  .split(",")
  .map((field) => field.trim().split(" ")[0])
  .filter(Boolean);

export class TyreQuoteTable {
  private db!: Database;
  constructor(
    private inquiryId: string,
    private inquiryInfo: ITyreInquiryInfo
  ) { }

  responses?: ITyreQuoteAndDemand;

  private getImportFlag(quoteItem: IQuoteProductData) {
    let whetherImport = false;
    const tyreAttr = quoteItem.quotationProductAttributeList?.find(
      (quoteItem) => quoteItem.attributeType === "QUOTATION_PRODUCT_TYRE_ATTR"
    );
    if (tyreAttr?.attributeValue) {
      const attr = JSON.parse(tyreAttr.attributeValue);
      if (_.get(attr, "whetherImport") === "进口") {
        whetherImport = true;
      }
    }
    return whetherImport ? "进口" : "国产";
  }

  private getOrganizationFlag(quoteItem: IQuoteProductData) {
    const organizationFlag = quoteItem.organizationFlag;
    if (organizationFlag === "TIRE_BRAND_MANUFACTURER") {
      return "厂家直销";
    } else if (organizationFlag === "TIRE_STRATEGIC_SUPPLIER") {
      return "代理直销";
    } else if (organizationFlag === "TIRE_FACILITATOR") {
      return "轮胎服务商";
    }
    return "";
  }
  /**
   * 拉取当前询价单对应的报价条目
   * @returns
   */
  private async fetchQuoteItems(): Promise<TyreQuoteItem[]> {
    const res = await tyreService.getTyreInquiryDemandAndQuote(this.inquiryId, this.inquiryInfo);
    this.responses = res;
    const quotes: TyreQuoteItem[] = [];
    res.list?.forEach((quoteItem) => {
      quoteItem.quotationProductList?.forEach((item) => {
        const modelName = item.modelNameList?.join(" , ") || "";
        const importFlag = this.getImportFlag(item);
        const organizationFlag = this.getOrganizationFlag(item);
        const remark = [
          item.locationName,
          item.partsName,
          modelName,
          item.facility?.map((item) => item.facilityName),
          importFlag,
          organizationFlag,
        ]
          .filter(Boolean)
          .join(" | ")
          .replace(/防爆/g, "防爆胎");

        quotes.push({
          quotationProductId: item.quotationProductId!,
          atPrice: +item.atPrice!,
          btPrice: +item.btPrice!,
          locationName: item.facility?.map((item) => item.facilityName).join(" , ") || "",
          organizationName: item.organization?.organizationName || "",
          partsName: item.partsName!,
          modelName,
          remark,
          outOfStock: item.quoteStatus === "outOfStock" ? "是" : "否",
        });
      });
    });
    // writeFileSync("a.json", JSON.stringify(res.list, null, 2));
    return quotes;
  }

  filterResponseList(quotationProductIds: string[]) {
    const list =
      this.responses?.list?.map((item) => {
        return {
          ...item,
          quotationProductList: item.quotationProductList?.filter((item) =>
            quotationProductIds.includes(item.quotationProductId!)
          ),
        };
      }) || [];
    return _.orderBy(list, ["quotationProductList.length"], ["desc"]);
  }

  /**
   * 实例化内存数据库
   * @returns
   */
  private createDb() {
    const { promise, resolve, reject } = withResolvers();
    this.db = new Database(":memory:", callbackify(resolve, reject));
    return promise;
  }

  /**
   * 创建报价条目表
   * @returns
   */
  private createItemsTable() {
    const { promise, resolve, reject } = withResolvers();
    this.db.run(
      `CREATE TABLE IF NOT EXISTS tyre_item (
        ${fields}
      )`,
      callbackify(resolve, reject)
    );
    return promise;
  }
  /**
   * 插入报价条目
   * @param quoteItems
   */

  private async insertQuoteItems(quoteItems: TyreQuoteItem[]) {
    const sql = `INSERT INTO tyre_item VALUES (${keys.map(() => " ? ").join(",")})`;
    const stmt = this.db.prepare(sql);
    logger.info("sql-->", sql);
    for (const quoteItem of quoteItems) {
      if (!quoteItem?.quotationProductId) {
        continue;
      }
      const { promise, resolve, reject } = withResolvers();
      const values = keys.map((key) => {
        const value = _.get(quoteItem, key);
        return value;
      });
      stmt.run(values, callbackify(resolve, reject));
      await promise;
    }
    const { promise, resolve, reject } = withResolvers();
    stmt.finalize(callbackify(resolve, reject));
    await promise;
  }

  sample(n: number) {
    return this.exec(`SELECT * FROM tyre_item ORDER BY RANDOM() LIMIT ${n}`) as Promise<TyreQuoteItem[]>;
  }

  async initialize() {
    let quoteItems = await this.fetchQuoteItems();
    // 系统报价是异步的，有可能会获取不到数据，需要等待3秒再获取一次
    if (quoteItems.length === 0) {
      await sleep(3000);
      quoteItems = await this.fetchQuoteItems();
    }
    await this.createDb();
    await this.createItemsTable();
    await this.insertQuoteItems(quoteItems);
  }

  async exec(sql: string) {
    const { promise, resolve, reject } = withResolvers<unknown[]>();
    this.db.all(sql, callbackify(resolve, reject));
    return promise;
  }

  async dispose() {
    await this.db?.close();
  }
}
