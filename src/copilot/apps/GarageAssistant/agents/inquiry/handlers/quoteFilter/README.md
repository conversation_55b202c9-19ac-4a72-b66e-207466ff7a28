# 报价结果AI筛选

用户可以通过自然语言对报价结果进行筛选。

具体实现步骤如下：
1. 获取询价单报价条目
2. 将报价条目存储到数据库
3. 通过大模型为用户输入生成SQL语句
4. 执行SQL语句，获取筛选结果
5. 大模型根据筛选结果生成文案或报价方案

```mermaid
graph TD
    A[获取询价单报价条目] --> B[将报价条目存储到数据库]
    B --> C[通过大模型为用户输入生成SQL语句]
    C --> D[执行SQL语句，获取筛选结果]
    D --> E[大模型根据筛选结果生成文案或报价方案]
    E --> F[返回生成的文案或报价方案给用户]

    subgraph "数据库操作"
        B1[初始化数据库连接]
        B2[保存报价条目]
    end

    subgraph "大模型生成SQL"
        C1[调用大模型API]
        C2[获取生成的SQL语句]
    end

    subgraph "执行SQL"
        D1[执行SQL查询]
        D2[获取查询结果]
    end

    subgraph "大模型生成文案"
        E1[调用大模型API]
        E2[获取生成的文案或报价方案]
    end

    B --> B1
    B1 --> B2
    B2 --> C
    C --> C1
    C1 --> C2
    C2 --> D
    D --> D1
    D1 --> D2
    D2 --> E
    E --> E1
    E1 --> E2
    E2 --> F
```