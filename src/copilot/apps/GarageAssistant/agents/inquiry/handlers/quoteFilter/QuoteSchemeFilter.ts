import { doubao_chat } from "@/clients/llm";
import { LLMMessageTemplate, LLMRunnableBuilder } from "@/copilot/helpers/llm/tools";
import { QuoteItem, QuoteTable, Scheme } from "./QuoteTable";
import logger from "@/common/logger";
import _ from "lodash";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
import { IGetRecommendPlanRes } from "../../interface/IRecommendPlan";
import { createObjectId } from "@/common/utils";

const rewritePrompt = `
你是一名自然语言及数据库专家，帮助用户重写问题，以便获得更准确的SQL查询结果。
1. 帮助用户明确需要查询的表，默认查询报价方案，带配件信息时查询报价条目。
2. 用户所说好坏指的是品质，原厂件 > 品牌件 > 拆车件。
3. 不能改变用户语义，不能丢失信息。
4. 用户问题包含地区时，需要考虑是店铺名称的组成部分。

示例：
1. 最便宜的 -> 我想要所有配件都是【最便宜】的报价方案
2. 最便宜的机油格 -> 我想要【机油格】配件【最便宜】的报价方案
3. 广州凌云汽配原厂 -> 我想要【广州凌云汽配】商家的【原厂件】品质，帮我查询报价方案
4. 最好的火花塞 -> 我想查询名为【火花塞】配件，品质是【原厂件】的报价条目
5. 品牌件 -> 我想查询品质是【品牌件】的报价方案
6. 前杠有没有深圳的 -> 我想报价条目，配件名【前杠】，地点【深圳】
7. 最好的 -> 我想查询品质是【原厂件】的报价方案
8. 拆车 -> 我想查品质是【拆车件】的报价方案
9. 有哪些商家报价 -> 我想查询报价条目中报价的商家

问题：
{input}
输出：
`;

const text2sqlPrompt = `
你是一名专业的数据库工程师，擅长将自然语言问题转换为 SQL 查询。请根据用户问题生成相应的 SQL 查询语句。
用户问题：{question}

数据库相关表表由以下SQL语句创建：
\`\`\`sql
-- 创建报价方案表
CREATE TABLE quotation_scheme (
    schemeId INTEGER PRIMARY KEY AUTOINCREMENT, -- 主键，自增
    schemeName TEXT, -- 方案名称，内容可能为指定品质、指定商家、指定配件
    distance REAL, -- 距离
    btTotalPrice REAL, -- 税前总价格（不开发票，默认）
    totalPrice REAL -- 税后总价格（开票价格）
    description TEXT -- 方案备注，内容可能为品质（原厂件、品牌件、拆车件等）、商家、城市、发货地、发货仓等信息，可以直接使用LIkE匹配，如：LIKE %品牌件%
    itemCount INTEGER -- 方案中的报价条目数量
);

-- 创建报价条目表
CREATE TABLE quotation_item (
    itemId INTEGER PRIMARY KEY AUTOINCREMENT, -- 主键，自增
    arrivalTime INTEGER, -- 最快到货时间（单位：天）
    btPrice REAL, -- 税前价格（不开发票）
    price REAL, -- 税后价格（开票价格）
    createdStamp TEXT, -- 创建时间
    distance REAL, -- 距离
    isOrdered BOOLEAN, -- 是否已采购
    locationName TEXT, -- 发货地名称
    originalAssort INTEGER, -- 配套标识（0：非配套，1：配套）
    partsBrandQuality TEXT, -- 零件品质（原厂件、品牌件、拆车件等）
    needsName TEXT, -- 需求名称
    partsName TEXT, -- 零件名
    partsNum TEXT, -- 零件号
    productSetCode TEXT, -- 套件编码
    productType TEXT, -- 商品类型（FINISHED_GOODS 或 DISPATCH_GOODS）
    quotedTime DATETIME, -- 报出时间
    storeName TEXT, -- 卖家店铺名称（如华砺汽配、凌云汽配等）
    storeServiceArea TEXT, -- 店铺服务范围
    whetherProductSet TEXT -- 是否套件
);

-- 创建报价方案与报价条目关联表
CREATE TABLE scheme_item_mapping (
    schemeId INTEGER, -- 报价方案ID
    itemId INTEGER, -- 报价条目ID
    PRIMARY KEY (schemeId, itemId), -- 联合主键
    FOREIGN KEY (schemeId) REFERENCES quotation_scheme(schemeId), -- 外键关联报价方案表
    FOREIGN KEY (itemId) REFERENCES quotation_item(itemId) -- 外键关联报价条目表
);
\`\`\`

请按照以下步骤完成任务：
    - 仔细理解用户问题，提取关键信息。
    - 仔细理解数据库表结构，注意各张表包含的字段及类型。
    - 将用户问题转换为 SQL 语句。
    
生成 SQL 查询时，请遵循以下要求：
    - 用户提及【最便宜】或【价格最低】，只查询报价方案表 quotation_scheme，通过schemeName而不是description字段匹配，且限制 SQL 查询输出为1条。
    - 查询方案表quotation_scheme时，第一排序条件应为itemCount字段降序。
    - 用户指定配件，查询quotation_item表相关字段匹配。
    - 查询报价条目，使用quotation_item表相关字段匹配。
    - TEXT类型尽可能使用 LIKE 语句匹配。
    - 尽可能返回 id 字段，便于后续操作。
    - 返回 SQL 语句，格式为 JSON 对象，包含 "sql" 字段。
    - 如果无法回答问题或问题不明确，返回 {{"msg": "无法回答"}}。
`.trim();

const replyPrompt = `
请根据以下执行结果，组织语言回复用户问题。
这条SQL执行结果是：{result}
有需要时，可以用markdown展示，要求：
1. 文案简洁明了，不要出现重复累赘信息
2. 文案中禁止出现“数据库”字样或SQL语句
3. 可以按需要使用表格，禁止使用列表
4. 使用表格时，表头用中文，不显示id字段，不能超过4列，不超过5行，第5行用“...”填充
`.trim();
const template = LLMMessageTemplate.create("user", text2sqlPrompt);
const sqlRunnable = LLMRunnableBuilder.create(doubao_chat)
  .addPrompt(template)
  .build<{ msg?: string; sql?: string }>({ type: "json" });
const replyRunnable = LLMRunnableBuilder.create(doubao_chat)
  .addPrompt(template)
  .addPrompt(LLMMessageTemplate.create("assistant", "{assistant_reply}"))
  .addPrompt(LLMMessageTemplate.create("user", replyPrompt))
  .build({ type: "string" });

const rewriteRunnable = LLMRunnableBuilder.create(doubao_chat)
  .addPrompt(LLMMessageTemplate.create("user", rewritePrompt))
  .build({ type: "string" });

export class QuoteSchemeFilter {
  private table: QuoteTable;
  constructor(
    private inquiryId: string,
    private input: string
  ) {
    this.table = new QuoteTable(this.inquiryId);
  }
  private executedSql?: { sql: string };
  /**
   * 生成SQL语句
   * @returns
   */
  async generateSql() {
    const input = await this.rewriteInput();
    const result = await sqlRunnable.invoke({ question: input }, { callbacks: [llmLogCallback] });
    return result;
  }

  initialize() {
    return this.table.initialize();
  }

  /**
   * 重新用户输入，提升输入质量
   * @returns
   */
  private async rewriteInput() {
    const input = await rewriteRunnable.invoke({ input: this.input }, { callbacks: [llmLogCallback] });
    return input;
  }

  /**
   * 构建推荐条目，每个配件只出现一次
   * @param items
   */
  private buildProgrammeItems(items: QuoteItem[]) {
    return Object.values(_.groupBy(items, (item) => item.partsName))
      .map((items) => _.orderBy(items, "price", "asc"))
      .map((items) => _.first(items))
      .flat()
      .map((item) => ({
        quotationProductId: _.get(item, "quotationProductId")!,
        standardItemId: _.get(item, "resolveResultId")!,
      }));
  }

  /**
   * 提取报价方案
   */
  async exactSchemes(rows: unknown[]): Promise<IGetRecommendPlanRes[]> {
    if (!rows?.length) {
      return [];
    }

    const hasSchemeId = rows.some((item) => _.get(item, "schemeId"));
    if (hasSchemeId) {
      const schemes = rows as Scheme[];
      const promises = schemes.map((scheme) =>
        this.table.exec(
          `SELECT * FROM scheme_item_mapping a LEFT JOIN quotation_item b ON a.itemId = b.itemId WHERE schemeId = ${scheme.schemeId}`
        )
      );
      const results = await Promise.all(promises);

      return results.map((result) => {
        return {
          programmeId: createObjectId(), // 询价单推荐方案id
          inquiryId: this.inquiryId!, // 询价单号
          scenario: "场景", // 场景a
          programmeName: "", // 推荐方案名称
          programmeDescription: "", // 推荐方案描述
          createdDate: Date.now(), // 方案创建时间
          programmeItems: this.buildProgrammeItems(result as QuoteItem[]),
        };
      });
    }

    const itemIds = rows.map((item) => _.get(item, "itemId")).filter(Boolean);
    if (itemIds?.length) {
      const plans: IGetRecommendPlanRes[] = [];
      const group = _.groupBy(rows, "storeName");
      for (const [, values] of Object.entries(group)) {
        const partsNameGroup = _.groupBy(values, "partsName");
        const quoteItems = Object.values(partsNameGroup)
          .map((items) => _.first(items))
          .filter(Boolean);
        const plan: IGetRecommendPlanRes = {
          programmeId: createObjectId(), // 询价单推荐方案id
          inquiryId: this.inquiryId!, // 询价单号
          scenario: "场景", // 场景a
          programmeName: "", // 推荐方案名称
          programmeDescription: "", // 推荐方案描述
          createdDate: Date.now(), // 方案创建时间
          programmeItems: this.buildProgrammeItems(quoteItems as QuoteItem[]),
        };
        plans.push(plan);
      }
      return _.orderBy(plans, (item) => item.programmeItems.length, "desc");
    }

    return [];
  }

  /**
   * 执行SQL语句，返回执行结果
   * @param sql
   * @returns
   */

  async executeSql(sql: string) {
    this.executedSql = { sql };
    try {
      let result = await this.table.exec(sql!);
      // let schemeIds = result.map((item) => _.get(item, "schemeId")).filter(Boolean);
      const ids = result.map((item) => _.get(item, "itemId")).filter(Boolean);
      if (ids?.length) {
        result = await this.table.exec(`SELECT * FROM quotation_item WHERE itemId IN (${ids.join(",")})`);
      }
      return result.map((item) => {
        const quotedTime = _.get(item, "quotedTime");
        if (quotedTime) {
          return {
            ...(item as object),
            quotedTime: new Date(quotedTime * 1000).toLocaleString(),
          };
        }
        return item;
      });
    } catch (err) {
      logger.error("execute sql error", err);
    }
    return [];
  }

  /**
   * 根据SQL执行结果生成回复
   * @param answers
   * @returns
   */
  async generateReply(answers: unknown[]) {
    // 最大行数
    const MAX_ROWS = 6;
    return replyRunnable.stream(
      {
        assistant_reply: JSON.stringify(this.executedSql),
        result: JSON.stringify(
          answers.slice(0, MAX_ROWS).map((item) => ({ ...(item as object), description: "" })),
          null,
          2
        ),
        question: this.input,
      },
      { callbacks: [llmLogCallback] }
    );
  }

  dispose() {
    return this.table.dispose();
  }
}
