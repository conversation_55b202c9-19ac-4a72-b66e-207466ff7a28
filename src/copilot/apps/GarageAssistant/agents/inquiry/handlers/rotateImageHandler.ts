import { commandIntents } from "@/copilot/constants";
import { Context, IHandlerFunction, MessageFactory } from "@casstime/copilot-core";

// 旋转图片
export const rotateImageHandler: IHandlerFunction = async (context: Context) => {
  const { imageUrl, extra } = context?.slots || {};
  // 旋转后的图片
  context.reply(MessageFactory.image(imageUrl, { extra }));
  // 进行工单识别
  context.reply(
    MessageFactory.text("图像识别中，请稍候", {
      reply: {
        type: "command",
        command: commandIntents.recognizeChecklistImage,
        params: {
          imageUrl,
          extra,
        },
      },
    })
  );
};
