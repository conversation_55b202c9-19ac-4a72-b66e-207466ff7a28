import { inquiryClient } from "@/clients/inquiry";
import { InquiryStatus } from "@/clients/inquiry/interface";
import { commandIntents } from "@/copilot/constants";
import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { IOePartNameItem } from "../forms";

export const EpcAppendPartsHandler: IHandlerFunction = async (context) => {
  const { epcData = {} } = context.slots;
  const { selectParts, vinCode, inquiryId, carModel } = epcData;
  const oePartNames = selectParts.map((item: Partial<IOePartNameItem>) => ({
    ...item,
    oeCode: item.partsNum?.replace(/\s+/g, ""),
    oeName: item.partsName,
    stdName: item.partsName,
    inquirySource: "ELECTRONIC_CATALOG",
    stdInquirySource: "ELECTRONIC_CATALOG",
    hasResolved: true,
  }));
  // 1-判断询价单是否过期
  const listInquiryStatus = await inquiryClient.getListInquiryStatus([{ inquiryId }]);
  const inquiryStatus = (listInquiryStatus?.find((item) => item.inquiryId === inquiryId)?.inquiryStatus ||
    "") as InquiryStatus;
  const inquiryValid = [InquiryStatus.QUOTE, InquiryStatus.UNQUOTE].includes(inquiryStatus);

  // 2-发布新询价或者追加
  if (inquiryValid) {
    context.mergeSlots({ inquiryId });
  } else {
    context.reply(MessageFactory.text(`询价单【${inquiryId}】已过期，为你发布新询价`));
  }
  context.mergeSlots({ vinCode, oePartNames, carModel, __NOASKAPPEND__: true });

  return context.next(commandIntents.IMMEDIATE_INQUIRY);
};
