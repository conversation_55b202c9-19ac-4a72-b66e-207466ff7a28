import { IHandlerFunction } from "@casstime/copilot-core";
import { FormNames, inquiryForm } from "../forms";
import { inquiryService } from "../services/InquiryService";
import { Version } from "@/common/utils";
import { quoteFilterClassifier } from "../classifiers/quoteFilterClassifier";
import { featureTogglesClient } from "../clients/FeatureTogglesClient";
import { FeatureTogglesEnum } from "@/common/enums";
import { renderRichtext } from "@/copilot/richtext";
import { MsgContent } from "@/copilot/richtext/components/Content";

// 用户点击发布询价
export const immediateInquiryHandler: IHandlerFunction = async (context) => {
  // 校验表单
  const form = context.getForm<typeof inquiryForm>(FormNames.inquiryForm);
  const { error } = await form.validate();
  if (error !== undefined) {
    const formData = form.getValues();
    const replyMsg = inquiryService.getFillInquiryFormMessage(formData, error, {
      repairScenarios: context.slots.repairScenarios,
      appVersion: context.payload.appVersion as Version,
    });
    // 清空候选车架号
    context.clearSlots(["vinCodes"]);
    // 有command消息则直接回复
    if (replyMsg.replyCommandMsg) {
      context.reply(replyMsg.replyCommandMsg);
      return;
    }
    const msg = inquiryService.createInquiryFormReplyMessage(formData, replyMsg, context.payload.appVersion as Version);
    const embedContent = renderRichtext(MsgContent, { text: msg.extra?.content || "" });
    if (msg.embed?.type === "richtext") {
      msg.embed.content += embedContent;
    } else if (!msg.embed) {
      msg.embed = { type: "richtext", content: embedContent };
    } else {
      msg.content = msg.extra?.content || msg.content;
    }
    context.reply(msg);
    return;
  }
  // 提交表单
  const { demandId, inquiryId } = await form.submit();
  // 获取表单数据，用于获取代客询价信息
  const formData = form.getValues();
  // 交互式推荐方案
  const { companyId = "" } = context.payload;
  if (demandId) {
    const { enabled } = await featureTogglesClient.getIsPilotArea(companyId, FeatureTogglesEnum.AI_INTERACTION_TYRE);
    if (enabled) {
      const filter = {
        type: "tyre", // 轮胎询价
        inquiryId: demandId,
        // 添加代客询价相关信息
        isProxyInquiry: formData.isProxyInquiry || false,
        userLoginId: formData.user?.userId || '',
        garageCompanyId: formData.user?.companyId || '',
      };
      context.mergeSlots({ filter });
      context.activatePreClassifierOnce(quoteFilterClassifier);
    }
  }
  if (inquiryId) {
    const { enabled } = await featureTogglesClient.getIsPilotArea(companyId, FeatureTogglesEnum.AI_INTERACTION_FULL);
    if (enabled) {
      const filter = {
        type: "whole", // 全车件询价
        inquiryId,
      };
      context.mergeSlots({ filter });
      context.activatePreClassifierOnce(quoteFilterClassifier);
    } else {
      context.clearSlots(["filter"]);
    }
  }
};
