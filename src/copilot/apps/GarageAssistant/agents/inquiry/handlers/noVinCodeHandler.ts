import { Context, IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { FormNames, inquiryForm } from "../forms";
import { GoToInquiryAction } from "@/copilot/constants";

// 没有车架号
export const noVinCodeHandler: IHandlerFunction = async (context: Context) => {
  const formData = context.getForm<typeof inquiryForm>(FormNames.inquiryForm).getValues();
  // 去掉"车架"配件
  const partNames = (formData?.partNames || []).filter((item) => item !== "车架");
  context.mergeSlots({
    partNames,
  });
  context.reply(
    MessageFactory.text("无VIN码询价正在努力构建中，当前仅支持VIN码询价。", {
      actions: [[{ ...GoToInquiryAction, text: "继续询价" }]],
    })
  );
};
