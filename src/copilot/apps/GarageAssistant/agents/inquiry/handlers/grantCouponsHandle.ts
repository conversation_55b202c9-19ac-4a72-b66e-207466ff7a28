import { Context, IHandlerFunction, ITextMessage, MessageFactory } from "@casstime/copilot-core";
import { couponService } from "../services/CouponService";
import { ActivityStatusEnum } from "../enum";
import { AgentName, BuyPartAction, BuyTyreAction } from "@/copilot/constants";
import { config } from "@casstime/apollo-config";
import _ from "lodash";
import { createCouponsEmbed, createEmbed, createInquiryStartTips, createTips } from "../xml";
import { featureTogglesClient } from "../clients/FeatureTogglesClient";
import { FeatureTogglesEnum, VersionEnum } from "@/common/enums";

export const grantCouponHandler: IHandlerFunction = async (context: Context) => {
  const { companyId, appVersion } = context.payload;
  const { enabled } = await featureTogglesClient.getIsPilotArea(companyId, FeatureTogglesEnum.AICOUPON);
  if (!enabled) {
    return context.routeTo(AgentName.fallbackAgent);
  }
  const noSupportRichText = appVersion?.isLessThan(VersionEnum.FIVE_18_0);
  if (noSupportRichText) {
    context.reply(MessageFactory.text("请将App升级到最新版本~ "));
    return;
  }
  const couponActivityId = _.get(config.get("GRANT_COUPONS"), "benefitsCouponActivityId", "");
  const [acquireRes, couponActivityRes] = await Promise.all([
    couponService.acquireCoupons(companyId, couponActivityId),
    couponService.getCouponActivitiy(couponActivityId),
  ]);
  if (couponActivityRes && couponActivityRes.statusCode === 200) {
    // 活动进行中
    if (couponActivityRes.result?.activityStatus === ActivityStatusEnum.ONGOING) {
      if (acquireRes && acquireRes.statusCode === 200) {
        // 领取优惠券成功
        const amount = couponActivityRes.result.couponAmount || 0;
        const others: Partial<ITextMessage> = {};
        const couponEmbed = createCouponsEmbed(amount);
        others.embed = createEmbed("richtext", [couponEmbed], "#fff");
        others.tips = createTips("richtext", createInquiryStartTips());
        others.actions = [[BuyPartAction, BuyTyreAction]];
        context.reply(
          MessageFactory.text(`您的￥${amount}元优惠券福利已到账，快去询价吧~~（询价成功后还有优惠券领取哦）`, others)
        );
        return;
      }
      // 已领取优惠券
      context.reply(MessageFactory.text("您已经领取过了哦~", { actions: [[BuyPartAction, BuyTyreAction]] }));
      return;
    } else if (couponActivityRes.result?.activityStatus === ActivityStatusEnum.ENDED) {
      // 活动已结束
      context.reply(MessageFactory.text("您来晚了，活动已结束~ ", { actions: [[BuyPartAction, BuyTyreAction]] }));
      return;
    } else {
      // 活动未开始
      context.reply(MessageFactory.text("您来早了，活动未开始~ "));
      return;
    }
  }
  return context.routeTo(AgentName.fallbackAgent);
};
