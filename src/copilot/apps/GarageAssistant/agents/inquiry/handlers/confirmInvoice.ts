import { Intents } from "@/copilot/constants";
import { Category, IntentClassifier } from "@/copilot/helpers/llm/tools";
import { IInquiryInvoiceRes } from "@/interfaces/inquiry";
import { Context, IHandlerFunction, MessageFactory } from "@casstime/copilot-core";

const invoiceIntentClassifier = IntentClassifier.create()
  .addCategory(Category.create("需要发票"))
  .addCategory(Category.create("不需要发票"));

// 确认发票信息
export const confirmInvoice: IHandlerFunction = async (context: Context) => {
  if (context.lastMessage.nlu?.slots?.invoice) {
    // 首次确认，直接校验发布
    return context.next(`@command/${Intents.inquiryPressSubmitForm}`);
  }
  const invoice: IInquiryInvoiceRes = {
    isRequireItemInvoice: false,
  };

  const intent = await invoiceIntentClassifier.classify(context);
  if (intent === Intents["需要发票"]) {
    invoice.openInvoiceType = "YES";
  } else if (intent === Intents["不需要发票"]) {
    invoice.openInvoiceType = "NO";
  }
  context.mergeSlots({
    invoice,
  });
  context.reply(
    MessageFactory.markdown(`已将您的发票类型修改为**${invoice.openInvoiceType === "YES" ? "" : "不"}需要开票**`)
  );
  context.mergeSlots({
    __CHANGEINVOICE__: true,
  });
  return context.next(Intents.inquiry);
};
