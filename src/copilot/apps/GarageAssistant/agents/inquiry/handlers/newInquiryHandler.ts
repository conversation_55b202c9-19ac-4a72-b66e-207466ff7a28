import { Context, IHandlerFunction } from "@casstime/copilot-core";
import { FormNames, inquiryForm } from "../forms";
import { Intents } from "@/copilot/constants";
import { SLOTS_CLEARED_FOR_NEWVIN } from "@/copilot/constants/ClearSlots";

export const newInquiryHandler: IHandlerFunction = async (context: Context) => {
  const form = context.getForm<typeof inquiryForm>(FormNames.inquiryForm);
  const formData = form.getValues();
  if (formData.inquiryId) {
    // 有inquiryid，已存在询价单，清除询价单信息
    context.clearSlots(["inquiryId", "inquiryCarModel", "__NOASKAPPEND__"]);
    // 直接去校验表单并发布询价
    return context.next(Intents.inquiry);
  } else {
    // 修改vin，发起新单
    context.clearSlots(SLOTS_CLEARED_FOR_NEWVIN);
    return context.next(Intents.inquiry);
  }
};
