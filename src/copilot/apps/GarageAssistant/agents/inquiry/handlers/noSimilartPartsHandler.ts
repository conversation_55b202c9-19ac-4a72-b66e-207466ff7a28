import { Context, IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { AddPartNameAction } from "@/copilot/constants";
import { userSelectRecordService } from "@/service";
import { IUserSelectRecords, UserSelectRecordType } from "@/models";
import { FormNames, inquiryForm } from "../forms";

// 没有相似配件
export const noSimilartPartsHandler: IHandlerFunction = async (context: Context) => {
  const formData = context.getForm<typeof inquiryForm>(FormNames.inquiryForm).getValues();
  const { partNames } = formData;
  const { options, imageUrl } = context.lastMessage.nlu?.slots || {};
  const owner = context.lastMessage.owner || "";
  const record: IUserSelectRecords = {
    owner,
    type: UserSelectRecordType.SIMILAR_PARTS,
    options,
    imageUrl,
    selected: "都不是",
  };
  // 异步记录用户选择
  userSelectRecordService.createUserSelectRecord(record);
  const extra: Record<string, unknown> = {};
  if (partNames?.length) {
    extra.partNames = partNames;
  }
  context.reply(
    MessageFactory.text("很抱歉没有准确识别出配件，你可以告诉我正确的配件名称吗，谢谢！", {
      actions: [[AddPartNameAction]],
      extra,
    })
  );
};
