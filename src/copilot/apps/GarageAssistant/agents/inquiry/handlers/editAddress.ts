import { Context, IHandlerFunction, IAction, MarkdownStreamReplier, MessageFactory } from "@casstime/copilot-core";
import { FormNames, inquiryForm } from "../forms";
import { AI_DISCLAIMER, ToCassServiceAction } from "@/copilot/constants";
import { stringifyMessage } from "@/common/utils/message";
import { createAddressAction } from "../utils";
import { executePrompt } from "@/copilot/helpers/llm";
import logger from "@/common/logger";
import { createObjectId } from "@/common/utils";

// 修改地址信息
export const editAddress: IHandlerFunction = async (context: Context) => {
  const formData = context.getForm<typeof inquiryForm>(FormNames.inquiryForm).getValues();

  const buttons: IAction[] = [];
  if (formData.address?.id) {
    buttons.push(createAddressAction(formData.address.id));
  }
  buttons.push({ ...ToCassServiceAction, theme: "secondary" });

  let message = MessageFactory.markdown("您可以点击下方按钮，添加或编辑收货地址");
  try {
    const msgId = createObjectId();
    const stream = await executePrompt("采购助手/修改地址回复提示词", {
      message: stringifyMessage(context.lastMessage, false),
    });
    const replier = MarkdownStreamReplier.for(context);
    message = await replier.reply(stream, { id: msgId });
  } catch (err) {
    logger.warn("生成【修改地址】回复提示词失败", err);
  }

  context.reply(message, {
    actions: [buttons],
    extra: {
      addressTip: {
        visible: true,
      },
    },
    disclaimer: AI_DISCLAIMER,
  });
};
