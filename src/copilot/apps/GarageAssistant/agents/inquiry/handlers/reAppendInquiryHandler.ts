import { Context, IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { FormNames, inquiryForm } from "../forms";
import { inquiryClient } from "../clients/InquiryClient";
import { inquiryService } from "../services/InquiryService";
import { createCarModelEmbed, createEmbed, createInquiryInfoEmbed, createPartNamesEmbed } from "../xml";

// 短时间内重新追加需求
export const reAppendInquiryHandler: IHandlerFunction = async (context: Context) => {
  const formData = context.getForm<typeof inquiryForm>(FormNames.inquiryForm).getValues();
  const { vinCode = "", source = "", fromPage = "", partNames = [] } = formData;
  // 车架号查询价单号
  const { data: inquiryItem } = await inquiryClient.getInquiryIsAllowAppend(vinCode);
  if (!inquiryItem?.isAllowAppend) {
    // 无法追加，直接发起新询价
    // return context.next(commandIntents.reNewInquiry);
  }

  const inquiryId = inquiryItem?.inquiryId || "";
  context.mergeSlots({
    inquiryId,
    inquiryCarModel: inquiryItem,
  });
  // 查询已有询价单详情
  const inquiryDetail = await inquiryService.getInquiryDetail({
    inquiryId,
    source,
    fromPage,
  });
  const {
    brandLogo = "",
    carModelName = "",
    saleModelName = "",
    vin = "",
    needsNames = [],
    createdName = "",
    createdStamp,
  } = inquiryDetail;
  // 生成嵌入内容
  const carModelRichText = createCarModelEmbed(brandLogo, saleModelName || carModelName, vin, inquiryId);
  const inquiryInfoRichText = createInquiryInfoEmbed(needsNames, inquiryId, createdName, createdStamp);
  const partNameRichText = createPartNamesEmbed(partNames);
  const embed = createEmbed("richText", [carModelRichText, inquiryInfoRichText, partNameRichText]);
  context.reply(MessageFactory.text("您还需要其他配件吗？", { embed }));
};
