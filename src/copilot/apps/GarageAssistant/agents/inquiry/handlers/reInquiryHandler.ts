import { inquiryClient } from "@/clients/inquiry";
import { commandIntents } from "@/copilot/constants";
import { <PERSON><PERSON> } from "@casstime/copilot-core";


export class ReInquiryH<PERSON>ler extends Handler {
  async handle() {
    const { slots } = this.context;
    const reInquiryId = slots['reInquiryId'];
    const userNeeds = await inquiryClient.getUserNeedsList(reInquiryId);
    const inquiryInfo = await inquiryClient.getInquiryInfo(reInquiryId);
    const partNames = userNeeds?.map(need => need.needsName) || [];
    this.context.mergeSlots({
      inquiryId: undefined,
      vinCode: inquiryInfo?.inquiryCarInfos?.vin,
      partNames,
      carModel: inquiryInfo?.inquiryCarInfos,
    })
    return this.context.next(commandIntents.IMMEDIATE_INQUIRY);
  }
}