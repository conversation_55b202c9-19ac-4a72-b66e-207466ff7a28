import { IHandlerFunction } from "@casstime/copilot-core";
import { InquiryIntents } from "../parsers/inquiryIntentClassifier";
import { inquiryClient } from "../clients/InquiryClient";
import { commandIntents } from "@/copilot/constants";

export const scanVINHandler: IHandlerFunction = async (context) => {
  const { slots } = context;
  // 1-解析车型
  const carModelResult = await inquiryClient.getVinCarModel(slots.vinCode || "");
  if (!carModelResult?.list?.length) {
    // 2-api识别
    return context.next(`@command/${commandIntents.recognizeVinImage}`);
  }
  return context.next(InquiryIntents.询报价);
};
