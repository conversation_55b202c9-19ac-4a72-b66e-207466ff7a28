import { ImageType, PartsNamesImageType, TyreImageType, VINImageType } from "@/copilot/constants";
import { Context, IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { vinImageHandler } from "./vinImageHandler";
import { checklistImageHandler } from "./checklistImageHandler";
import { tyreImageHandler } from "./tyreImageHandler";
import { recognizeImageHandler } from "./recognizeImageHandler";
import { createLoadingXml } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/richtext";
import { ImageService } from "../../../services/ImageService";
import { similarPartsImageHandler } from "./similarPartsImageHandler";
// import { imageAgent } from "@/copilot/agents/image/imageAgent";

// 重新识别图片
export const reRecognizeImageHandler: IHandlerFunction = async (context: Context) => {
  const imageService = context.getService(ImageService);
  const factory = MessageFactory.with({ id: imageService.getReplyMsgId() });
  context.setReplyMode("stream");
  context.reply(
    factory.markdown("请稍等...", {
      indicator: {
        type: "richtext",
        content: createLoadingXml(["图片识别中..."]),
      },
    })
  );
  const reporter = imageService.getReporter();
  const step = reporter.step("image-reRecognizeImageHandler", {});
  step.start("重新开始图片识别", { data: {} });

  const { imageType } = context?.slots || {};
  // let imageCommandIntent = "";
  if (VINImageType.includes(imageType)) {
    // imageCommandIntent = Intents.recognizeVinImage;
    await vinImageHandler(context);
  } else if (PartsNamesImageType.includes(imageType)) {
    // imageCommandIntent = Intents.recognizeChecklistImage;
    await checklistImageHandler(context);
  } else if (TyreImageType.includes(imageType)) {
    // imageCommandIntent = Intents.recognizeTyreImage;
    await tyreImageHandler(context);
  } else if (imageType === ImageType.配件) {
    await similarPartsImageHandler(context);
  } else {
    await recognizeImageHandler(context);
  }
};
