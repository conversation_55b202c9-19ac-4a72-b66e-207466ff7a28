import { Context, IHandlerFunction, ICommandMessage } from "@casstime/copilot-core";
import { userSelectRecordService } from "@/service";
import { IUserSelectRecords, UserSelectRecordType } from "@/models";

// 选择某个相似配件
export const selectedSimilartPartsHandler: IHandlerFunction = async (context: Context) => {
  const owner = context.lastMessage.owner || "";
  const { params } = context.lastMessage as ICommandMessage;
  const { options, imageUrl, selected } = params;
  const record: IUserSelectRecords = {
    owner,
    type: UserSelectRecordType.SIMILAR_PARTS,
    options,
    imageUrl,
    selected,
  };
  // 异步记录用户选择
  userSelectRecordService.createUserSelectRecord(record);
  return;
};
