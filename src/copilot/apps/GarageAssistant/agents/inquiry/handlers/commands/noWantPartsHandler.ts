import { Context, IAction, MessageFactory } from "@casstime/copilot-core";
import { createNoWantPartsReply } from "../../../richtext";
import { AgentName } from "@/common/enums";
import { commandIntents } from "@/copilot/constants";

const action: IAction = {
  type: "nlu",
  text: "发布询价",
  nlu: {
    agentName: AgentName.inquiryAgent,
    intent: commandIntents.IMMEDIATE_INQUIRY,
  },
};

export const noWantPartsHandler = async (context: Context) => {
  const hasPartNames = context.slots["partNames"];
  context.mergeSlots({
    repairScenarios: {
      partNames: [],
    },
  });
  context.reply(
    MessageFactory.markdown("", {
      indicator: createNoWantPartsReply(hasPartNames),
      actions: hasPartNames.length ? [[action]] : [],
    })
  );
};
