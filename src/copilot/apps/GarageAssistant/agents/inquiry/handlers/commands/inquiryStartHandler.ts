import { ICommandMessage, IMessage, MessageFactory, Handler } from "@casstime/copilot-core";
import { inquiryService } from "../../services/InquiryService";
import { commandIntents, InquiryStartLimitTime } from "@/copilot/constants";
import { AgentName, AppName, FeatureTogglesEnum, VersionEnum } from "@/common/enums";
import { taskService } from "../../services/TaskService";
import { messageService } from "../../services/MessageService";
import { TaskType } from "../../interface";
import { pollingService } from "../../../background/services/PollingService";
import { ChannelService } from "../../services/ChannelService";
import { IDialogueBusinessEnum } from "../../enum";
import { Version } from "@/common/utils";
import { inquiryForm, FormNames } from "../../forms";
import { InquiryIntents } from "../../parsers/inquiryIntentClassifier";
import { inquiryClient } from "../../clients/InquiryClient";
import { BackgroundIntents } from "../../../background/constans/enum";

// 用户开启会话，存储地址信息，判断是否发送欢迎语
export class InquiryStartHandler extends Handler {
  handle = async () => {
    const { lastMessage } = this.context;
    const { params = {} } = (lastMessage as ICommandMessage) || {};
    const { source, ...slots } = params;
    this.context.mergeSlots(slots);

    await this.replyGreetMsgs();
    this.replyDiffStamp();
    await this.replyPollingCommand();
    await this.replyRepairScenarios(source);
    return this.context.done();
  };

  replyGreetMsgs = async () => {
    const { payload } = this.context;
    // 查询最后一条展示的消息，避免重复发送欢迎语
    const lastShowMessage = (await messageService.getLastShowMessage(payload)) as IMessage | null;
    // 和最后一条消息的时间差
    const diffTime = new Date().getTime() - new Date(lastShowMessage?.createdAt || "").getTime();
    const noNeedInquiryStartMsg = lastShowMessage?.extra?.isGreet && diffTime < InquiryStartLimitTime;
    const sessionChanged = lastShowMessage?.sessionId !== this.context.sessionId;

    if ((!sessionChanged || noNeedInquiryStartMsg) && lastShowMessage) {
      // session没过期，或者事故场景进入不发送欢迎语
      return;
    }
    const noSupportGuideMsg = payload?.appVersion?.isLessThan(VersionEnum.FIVE_14_6);

    const businessId = await this.getBusinessId();
    switch (businessId) {
      case IDialogueBusinessEnum.ACCIDENT: {
        // 事故通道
        const greetMsg = await inquiryService.getAccidentGreetMessage();
        this.context.reply(greetMsg);
        break;
      }
      case IDialogueBusinessEnum.AIR_CONDITIONER: {
        // 空调通道
        const greetMsg = await inquiryService.getAirforceGreetMessage();
        this.context.reply(greetMsg);
        break;
      }
      default: {
        if (payload.app === AppName.ProxyInquiry) {
          const greetMsg = await inquiryService.getProxyInquiryGreetMessage();
          this.context.reply(greetMsg);
        } else {
          const greetMsgs = await inquiryService.getConfigureGreetMessage(payload.companyId || "", noSupportGuideMsg);
          greetMsgs.forEach((greetMsg) => {
            this.context.reply(greetMsg);
          });
        }
      }
    }
  };
  // 维修场景
  replyRepairScenarios = async (source: string) => {
    const { lastMessage, payload } = this.context;
    const { params } = lastMessage as ICommandMessage;
    const { accidentCode, accidentText, carModel, vinCode } = params || {};
    if (source !== "repair_scenarios" || !vinCode) {
      return;
    }
    if (!accidentCode) {
      return this.context.next(InquiryIntents.询报价);
    }
    const form = this.context.getForm<typeof inquiryForm>(FormNames.inquiryForm);
    const formData = form.getValues();
    const msg = inquiryService.createInquiryFormReplyMessage(formData, {}, payload.appVersion as Version, {
      businessId: "",
    });
    const { data: inquiryItem } = await inquiryClient.getInquiryIsAllowAppend(vinCode || "");
    if (inquiryItem?.isAllowAppend) {
      // 可追加，后续询问是否追加
      this.context.mergeSlots({
        inquiryId: inquiryItem.inquiryId,
        __NOASKAPPEND__: false,
        inquiryCarModel: inquiryItem,
        carModel,
      });
    }
    msg.reply = {
      type: "text",
      content: accidentText,
      nlu: {
        agentName: AgentName.partInfoAgent,
        entities: [
          { name: "accidentCode", value: accidentCode },
          { name: "brandCode", value: carModel?.carBrandCode || carModel.carBrandId },
          { name: "vehicleTypeCode", value: carModel?.vehicleTypeClass },
        ],
      },
    };
    this.context.reply(msg);
    return;
  };
  replyDiffStamp = () => {
    const { createdAt } = this.context.lastMessage;
    if (createdAt) {
      // 返回客户端和服务器时间差值
      this.context.reply(inquiryService.getDiffStampCommandMessage(createdAt));
    }
  };
  replyPollingCommand = async () => {
    const { payload } = this.context;
    // 查询是否需要开启询价轮询任务
    const taskQuery = {
      owner: payload?.data.owner || "",
      done: false,
      dialogueId: payload?.data.dialogueId || "",
    };
    const tasks = await taskService.getTaskPolling(taskQuery);
    const inquiryTasks = tasks.find((task) => task.taskType === TaskType.INQUIRY);
    if (inquiryTasks) {
      // 约定后台轮询消息
      const reply = {
        type: "command",
        command: commandIntents.inquiryPolling,
        background: true,
        replyDelay: 10 * 1000,
        params: {},
      } as ICommandMessage;
      this.context.reply(MessageFactory.command(commandIntents.inquiryPolling, {}, { reply, background: true }));
    }
    //  查询智能方案
    const intelligentPlanTasks = tasks.filter((task) => task.taskType === TaskType.INTELLIGENT_PLAN);
    // 是否专属客服试点
    const isA9tAirforceEnter = await inquiryService.getIsPilotArea(
      payload.companyId,
      FeatureTogglesEnum.A9T_AIRFORCE_ENTER
    );
    const isNeedInquiryPolling = payload.app === AppName.ProxyInquiry;
    for (const intelligentPlanTask of intelligentPlanTasks) {
      const { messageList, updateTask } = await pollingService.processPollingIntelligentPlanTask(
        intelligentPlanTask,
        isA9tAirforceEnter,
        isNeedInquiryPolling
      );
      const commandMessages: ICommandMessage[] = [];
      messageList.forEach((message) => {
        if (message.type === "command" && message.command === BackgroundIntents.INQUIRY_POLLIN) {
          commandMessages.push(message);
        } else {
          this.context.reply(message);
        }
      });
      if (commandMessages.length > 0) {
        this.context.reply(commandMessages[0]);
      }
      if (updateTask) {
        await taskService.updateTaskPolling({ taskId: updateTask.taskId }, { params: updateTask.params });
      }
    }
  };

  getBusinessId = async () => {
    const channelService = this.context.getService(ChannelService);
    const dialogue = await channelService.getDialogue();
    return dialogue?.businessId;
  };
}
