import { IHandlerFunction } from "@casstime/copilot-core";
import { commandIntents } from "@/copilot/constants";
import { FormNames, inquiryForm } from "../../forms";
import { inquiryService } from "../../services/InquiryService";
import { Version } from "@/common/utils";
import { renderRichtext } from "@/copilot/richtext";
import { MsgContent } from "@/copilot/richtext/components/Content";

// 用户点击发布询价
export const validateFormHandler: IHandlerFunction = async (context) => {
  // 校验表单
  const form = context.getForm<typeof inquiryForm>(FormNames.inquiryForm);
  const { error } = await form.validate();
  if (error === undefined) {
    return context.next(commandIntents.IMMEDIATE_INQUIRY);
  } else {
    const formData = form.getValues();
    const replyMsg = inquiryService.getFillInquiryFormMessage(formData, error, {
      repairScenarios: context.slots.repairScenarios,
      appVersion: context.payload.appVersion as Version,
    });
    // 清空候选车架号
    context.clearSlots(["vinCodes"]);
    // 有command消息则直接回复
    if (replyMsg.replyCommandMsg) {
      context.reply(replyMsg.replyCommandMsg);
      return;
    }

    const msg = inquiryService.createInquiryFormReplyMessage(formData, replyMsg, context.payload.appVersion as Version);
    const embedContent = renderRichtext(MsgContent, { text: msg.extra?.content || "" });
    if (msg.embed?.type === "richtext") {
      msg.embed.content += embedContent;
    } else if (!msg.embed) {
      msg.embed = { type: "richtext", content: embedContent };
    } else {
      msg.content = msg.extra?.content || msg.content;
    }
    context.reply(msg);
  }
};
