import { CommandActionIntents, commandIntents } from "@/copilot/constants";
import { IHandlerFunction, Context, MessageFactory, IAction, MarkdownStreamReplier } from "@casstime/copilot-core";
import { imageClient } from "../../../clients/ImageClient";
import { ActionFactory } from "@/common/factory/ActionFactory";
import _ from "lodash";
import { IAsyncCommandItem } from "../../../interface";
import { asyncCommandService } from "../../../services/AsyncCommandService";
import { afterSaleClient } from "../../../clients/AfterSaleClient";
import { ITagInfoResult } from "../../../interface/ITagInfo";
import { getChatOpenAI, renderPrompt } from "@/copilot/helpers/llm";
import { maindataClient } from "../../../clients/MaindataClient";
import { createOcrPartsRecommandTips, createTips } from "../../../xml";
import { nlpClient } from "@/clients/nlp";
import { InquiryIntents } from "../../../parsers/inquiryIntentClassifier";
import { ImageService } from "../../../services/ImageService";
import { renderRichtext } from "@/copilot/richtext";
import { ChannelService } from "../../../services/ChannelService";
import { IDialogueBusinessEnum } from "../../../enum";
import { getApolloConfig, hasVinCode, parseJsonMarkdown } from "@/common/utils";
import { PartListWithEditBtnEmbed } from "@/copilot/richtext/components/accident/AccidentParts/AvailbleAccidentParts";
import { SelectSimilarParts } from "@/copilot/richtext/components/accident/AccidentParts/SimilarSelectParts";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
import { extractVinCodeEntities } from "@/copilot/helpers";
import { FormNames, inquiryForm } from "../../../forms";

export const createSelectSimilarPartsCommand = async ({
  text,
  imageUrl,
  options,
  fillValue,
}: {
  text: string;
  imageUrl: string;
  options: string[];
  fillValue?: string;
}) => {
  // 回传command
  const echoCommand: IAsyncCommandItem = {
    command: CommandActionIntents.ECHO_COMMAND,
    params: {
      command: commandIntents.IMAGE_REC_PARTS_SIMILAR_SELECTED,
      imageUrl,
      selected: text,
      options,
    },
  };
  // 回填text
  const fillInputTextCommand: IAsyncCommandItem = {
    command: CommandActionIntents.FILL_INPUT_TEXT,
    params: {
      value: fillValue || text,
    },
  };
  const commands: IAsyncCommandItem[] = [echoCommand, fillInputTextCommand];
  const asyncCommand = await asyncCommandService.createAsyncCommand(commands);
  const id = asyncCommand._id?.toString();
  return { id };
};

const createSelectSimilarPartsAction = async (params: {
  text: string;
  imageUrl: string;
  options: string[];
  fillValue?: string;
}) => {
  const { text } = params;
  const { id } = await createSelectSimilarPartsCommand(params);
  return ActionFactory.command(text, commandIntents.asyncCommand, { params: { id } });
};

const createGoWarrantyDetailAction = (tagNo: string) => {
  return ActionFactory.command("查看溯源码", commandIntents.commonNavigate, {
    params: {
      navigate: `cassapp://route/rn/WarrantyLabelDetail?query=${JSON.stringify({
        tagNo,
        fromScreen: "AIChatScreen",
      })}`,
    },
  });
};

// ocr得到文字，然后提取配件名称和配件编码
const getPartsNameAndCodesFromImage = async (imageUrl: string) => {
  try {
    const ocrResults = await imageClient.recognizeImageByProOcr(imageUrl, ["text"]);
    const proOcrTexts = ocrResults.filter((result) => result.type === "text").map(({ text }) => text);
    if (!proOcrTexts.length) {
      return {
        names: [],
        codes: [],
        partList: [],
        vinCodes: [],
      };
    }
    const nlpResults = await nlpClient.extractPartNamesEntitiesBatch(proOcrTexts);
    const nlpParts = nlpResults.data.flat();
    const codeLists = await maindataClient.searchCodeList(proOcrTexts.filter((text) => text.length > 5));
    const partList: { code: string; name: string }[] = [];
    codeLists.forEach((result) => {
      const name = result.stdnameobj?.stdName || result.name;
      if (!name) return;
      if (partList.find((p) => p.code === result.codeTrim || p.name === name)) {
        return;
      }
      partList.push({
        name,
        code: result.codeTrim,
      });
    });
    const vinCodes = proOcrTexts
      .filter((item) => hasVinCode(item))
      .map((vin) => extractVinCodeEntities(vin).map((entity) => entity.value))
      .flat()
      .filter(Boolean);

    return {
      // nlp解析的配件名称
      names: _.uniq(nlpParts.map((part) => part.name)) || [],
      // 不能查出配件信息的零件号
      codes: [],
      // 能查出配件信息的零件号
      partList,
      vinCodes,
    };
  } catch {
    return {
      names: [],
      partList: [],
      codes: [],
      vinCodes: [],
    };
  }
};
// 配件图片信息识别
export const similarPartsImageHandler: IHandlerFunction = async (context: Context) => {
  const imageService = context.getService(ImageService);
  const imageUrl = imageService.getImageUrl();
  const factory = MessageFactory.with({ id: imageService.getReplyMsgId() });
  context.setReplyMode("stream");
  context.reply(
    factory.markdown("识别中...", {
      indicator: imageService.createIndicator("分析图片是否包含二维码..."),
    })
  );
  const reporter = imageService.getReporter();
  const step = reporter.step("image-similarPartsImageHandler", {});
  step.start("开始相似配件识别", { data: { imageUrl } });

  const [similarParts, proOcrResults] = await Promise.all([
    imageClient.getSimilarParts(imageUrl),
    imageClient.recognizeImageByProOcr(imageUrl, ["qr_code"]),
  ]);

  // 查询溯源码
  let warrantyTagCode = "";
  proOcrResults.forEach((result) => {
    if (result.type === "qr_code" && result.text.startsWith("APL/")) {
      warrantyTagCode = result.text.replace("APL/", "");
    }
  });
  let tagCodeResult: ITagInfoResult | undefined;
  if (warrantyTagCode) {
    const { result } = await afterSaleClient.getWarrantyLabel(warrantyTagCode);
    tagCodeResult = result;
    step.progress("识别出溯源码", { data: { warrantyTagCode } });
  }
  if (tagCodeResult) {
    // 不是收货方维修厂账号，看不到商品信息
    const productName = tagCodeResult.target?.goodInfo.productName || "";
    let replyText = `从图片中识别到开思溯源码，你可以点击下方按钮查看溯源码详情`;
    const actions: IAction[][] = [[createGoWarrantyDetailAction("APL/" + warrantyTagCode)]];
    if (productName) {
      actions[0].push(await createSelectSimilarPartsAction({ text: productName, imageUrl, options: [] }));
      replyText += `，或点击配件名称发起询价`;
    }
    context.reply(
      factory.markdown(replyText, {
        actions,
        indicator: undefined,
      })
    );
    return;
  }
  context.reply(
    factory.markdown("识别中...", {
      indicator: imageService.createIndicator("识别车架号及配件信息..."),
    })
  );
  const partNameAndCodes = await getPartsNameAndCodesFromImage(imageUrl);
  step.progress("识别图片中的文字、配件", { data: { partNameAndCodes } });
  if (partNameAndCodes.vinCodes?.length) {
    context.mergeSlots({ vinCode: partNameAndCodes.vinCodes[0], partNames: partNameAndCodes.names });
    await context.next(InquiryIntents.询报价);
    return;
  }

  const extra: Record<string, unknown> = { imageUrl };
  const formData = context.getForm<typeof inquiryForm>(FormNames.inquiryForm).getValues();
  const { partNames } = formData;
  if (partNames?.length) {
    extra.partNames = partNames;
  }
  if (partNameAndCodes.partList.length || partNameAndCodes.names.length) {
    const partList = partNameAndCodes.partList.concat(partNameAndCodes.names.map((name) => ({ name, code: "" })));
    // 查出主数据配件信息，不展示其他可能配件信息
    const tipsContent = createOcrPartsRecommandTips(partList);
    const tips = createTips("richtext", tipsContent);
    context.reply(
      factory.markdown("图片中可能包含以下配件，你可以选择正确的选项", {
        extra,
        indicator: undefined,
        tips,
      })
    );
    return;
  }
  const recommandParts: string[] = [
    ...partNameAndCodes.names,
    ...partNameAndCodes.codes,
    ...similarParts.map((part) => part.name),
  ];
  if (!recommandParts.length) {
    context.reply(
      factory.markdown("很抱歉没有准确识别出配件，你可以告诉我正确的配件名称吗，谢谢！", {
        extra,
        indicator: undefined,
      })
    );
    return;
  }

  const asyncCommandParams = [];
  const showRecommendParts = recommandParts.slice(0, 4);
  for (const part of showRecommendParts) {
    const params = await createSelectSimilarPartsCommand({
      text: part,
      imageUrl,
      options: showRecommendParts,
      fillValue: part,
    });
    asyncCommandParams.push(params);
  }

  // 已录入的配件
  const hasPartNames = context.slots["partNames"];
  const embedContent = renderRichtext(PartListWithEditBtnEmbed, { partNames: hasPartNames });

  const messageId = imageService.getReplyMsgId();

  const channelService = context.getService(ChannelService);
  const dialogue = await channelService.getDialogue();

  let showSimilarText = "";
  let showSimilarParts: string[] = showRecommendParts;
  if (dialogue?.businessId === IDialogueBusinessEnum.ACCIDENT || getApolloConfig("ENABLE_GPT_SIMILAR_PARTS")) {
    // 事故场景，使用gpt4o识别图片配件
    const messages = [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: await renderPrompt("采购助手/事故车图片识别配件", {}),
          },
          {
            type: "image_url",
            image_url: { url: imageUrl },
          },
        ],
      },
    ];
    const gpt4o_chat = getChatOpenAI({ fields: { temperature: 0.1 } });
    const stream = await gpt4o_chat.stream(messages, { callbacks: [llmLogCallback] });
    const replier = MarkdownStreamReplier.for(context);
    const msg = await replier.reply(stream, { id: messageId });
    const [text, partNameStr] = msg.content.split(/\n+/).filter((line) => !!line.trim());
    showSimilarParts = (parseJsonMarkdown(partNameStr) || []).filter(Boolean);
    showSimilarText = text;
    step.progress("gpt-4o识别图片配件，定损", {
      data: { partNameAndCodes, prompt: messages[0]?.content?.[0]?.text, content: msg.content },
    });
    if (!partNameStr || !showSimilarParts.length) {
      showSimilarText = "暂未识别到配件，请告诉我配件名称，或发给我配件清单。";
    }
  }
  step.progress("相似配件回复用户", { data: { showSimilarParts, showSimilarText } });

  const similarTipContent = renderRichtext(SelectSimilarParts, {
    partNames: showSimilarParts,
    messageId,
    selectedPartNames: [],
    content: "",
    source: "similar" as const,
  });

  const tips = createTips("richtext", similarTipContent);
  context.reply(
    factory.markdown(showSimilarText, {
      extra,
      indicator: undefined,
      tips,
      embed: { type: "richtext", content: embedContent },
    })
  );
  context.done();
  return;
};
