import { Context, IHandlerFunction } from "@casstime/copilot-core";
import { FormNames, inquiryForm } from "../../forms";
import { InquiryIntents } from "../../parsers/inquiryIntentClassifier";

// 误识别短车架号场景
export const notShortVinCodeHandler: IHandlerFunction = async (context: Context) => {
  const form = context.getForm<typeof inquiryForm>(FormNames.inquiryForm);
  const formData = form.getValues();
  const { vinCode, partNames = [] } = formData;
  const isShortVinCode = vinCode && vinCode.length < 17;
  if (isShortVinCode) {
    context.mergeSlots({ partNames: [...partNames, vinCode] });
    context.clearSlots(["vinCode"]);
  }
  return context.next(InquiryIntents.买配件);
};
