import { ICommandMessage, MessageFactory, Handler, IMarkdownMessage } from "@casstime/copilot-core";
import { renderRichtext } from "@/copilot/richtext";
import { AccidentParts } from "@/copilot/richtext/components/accident/AccidentParts";
import { SelectRelatedParts } from "@/copilot/richtext/components/accident/AccidentParts/InquirySelectRelatedParts";
import _ from "lodash";
import { SelectSimilarParts } from "@/copilot/richtext/components/accident/AccidentParts/SimilarSelectParts";

export class AccidentTogglePartsHandler extends Handler {
  handle = async () => {
    const { lastMessage, slots, historyMessages } = this.context;
    const { params = {} } = (lastMessage as ICommandMessage) || {};
    let { selectedPartNames = [] } = params;
    let repairScenariosSelectedPartNames: string[] = slots.repairScenarios?.selectedPartNames || [];

    if (params.isAllSelected !== undefined) {
      // 处理全选
      if (params.isAllSelected) {
        // 已经选中
        selectedPartNames = [];
        repairScenariosSelectedPartNames = repairScenariosSelectedPartNames.filter(
          (name) => !params.partNames?.includes(name)
        );
      } else {
        // 未选中
        selectedPartNames = params.partNames || [];
        repairScenariosSelectedPartNames = repairScenariosSelectedPartNames.concat(params.partNames || []);
      }
    } else {
      // 处理单个
      if (selectedPartNames.includes(params.partName)) {
        // 已经选中
        selectedPartNames = selectedPartNames.filter((part: string) => part !== params.partName);
        repairScenariosSelectedPartNames = repairScenariosSelectedPartNames.filter((name) => name !== params.partName);
      } else {
        // 未选中
        selectedPartNames = selectedPartNames.concat(params.partName);
        repairScenariosSelectedPartNames = repairScenariosSelectedPartNames.concat(params.partName);
      }
    }
    this.context.mergeSlots({
      repairScenarios: {
        ...slots.repairScenarios,
        selectedPartNames: _.uniq(repairScenariosSelectedPartNames),
      },
    });

    const factory = MessageFactory.with({ id: params.messageId });
    const prevMessage = historyMessages.find(
      (message) => message.id?.toString() === params.messageId
    ) as IMarkdownMessage;

    let TipsComponent = AccidentParts;
    switch (params.source) {
      case "similar":
        TipsComponent = SelectSimilarParts;
        break;
      case "accident":
        TipsComponent = AccidentParts;
        break;
      case "relatedParts":
        TipsComponent = SelectRelatedParts;
        break;
    }

    this.context.reply(
      factory.markdown(prevMessage?.content, {
        ...prevMessage,
        extra: {
          partSelectStatus: this.getPartStatus(params.partNames, selectedPartNames),
          // 是否需要把选中的配件加入输入框
          isNeedAddInput: true,
        },
        tips: {
          type: "richtext",
          content: renderRichtext(TipsComponent, {
            messageId: params.messageId,
            partNames: params.partNames || [],
            selectedPartNames,
            content: params?.content || "",
            source: params.source || "accident",
          }),
        },
      })
    );
  };
  /** 组织选中状态的结构 */
  getPartStatus(allPart: string[] = [], selectedPart: string[]) {
    return allPart.map((partName: string) => {
      return {
        partName,
        isSelected: selectedPart.includes(partName),
      };
    });
  }
}
