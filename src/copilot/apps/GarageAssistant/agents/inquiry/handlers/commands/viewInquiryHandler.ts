import { Context, IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { AgentName, commandIntents, NluActionIntents } from "@/copilot/constants";
import { FormNames, inquiryForm } from "../../forms";
import { ActionFactory } from "@/common/factory/ActionFactory";
import { InquiryIntents } from "../../parsers/inquiryIntentClassifier";
import { inquiryClient } from "@/clients/inquiry";

// 查看询价单按钮
function getCheckInquiryDetailAction(inquiryId: string) {
  return ActionFactory.command("查看询价单", commandIntents.VIEW_INQUIRY, {
    params: {
      inquiryId,
    },
  });
}

function getReInquiryAction(inquiryId: string) {
  return ActionFactory.nlu("重新询价", {
    intent: NluActionIntents.RE_INQUIRY,
    agentName: AgentName.inquiryAgent,
    slots: {
      reInquiryId: inquiryId,
    }
  })
}
// 查询询价单
export const viewInquiryHandler: IHandlerFunction = async (context: Context) => {
  const formData = context.getForm<typeof inquiryForm>(FormNames.inquiryForm).getValues();
  const { inquiryIds } = formData;
  const actions = [];
  if (inquiryIds?.length) {
    const inquiryId = inquiryIds[0];
    const inquiryInfo = await inquiryClient.getInquiryInfo(inquiryId);

    const abated = inquiryInfo?.inquiryBaseInfos?.statusId === 'ABATE';

    actions[0] = [getCheckInquiryDetailAction(inquiryId)];
    if (abated) {
      actions[0].push(getReInquiryAction(inquiryId));
    }
    context.reply(
      MessageFactory.text("你输入了询价单号，现在可以点击下方的【查看询价单】按钮，查阅询价单详情", { actions })
    );
    return;
  }
  return context.next(InquiryIntents.询报价);
};
