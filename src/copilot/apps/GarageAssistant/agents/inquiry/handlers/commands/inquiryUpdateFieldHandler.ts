import { IHandlerFunction, ICommandMessage, MessageFactory } from "@casstime/copilot-core";
import { inquiryUpdateFields } from "@/copilot/constants";
import { InquiryIntents } from "../../parsers/inquiryIntentClassifier";
import { extractVinCodeEntities } from "@/copilot/helpers/entities";
import { tyreService } from "../../services/TyreService";

// 用户修改信息：配件信息或询价基本信息
export const inquiryUpdateFieldHandler: IHandlerFunction = async (context) => {
  const params = (context.lastMessage as ICommandMessage).params || {};
  const { field, ...fieldSlots } = params;
  if (field === inquiryUpdateFields.inquiryInfo) {
    context.mergeSlots(fieldSlots);
    return;
  }
  if (field === inquiryUpdateFields.partNames) {
    // 修改配件，对轮胎规格进行格式化
    const vins: string[] = [];
    // 提取修改配件列表中的 vinCode
    const partNamesAfter: string[] = (fieldSlots?.partNames || []).filter((text: string) => {
      const parseVins = extractVinCodeEntities(text).map((entity) => entity.value);
      if (parseVins?.length > 0) {
        vins.push(...parseVins);
      }
      // 存在vinCode 则不再作为配件名称
      return !parseVins?.length;
    });
    if (vins.length === 1) {
      context.mergeSlots({ vinCode: vins[0] });
    }
    if (vins.length > 1) {
      context.mergeSlots({ vinCodes: vins });
    }
    const { partNames } = tyreService.matchTyreSize(partNamesAfter);
    // 确保不超过最大配件数
    context.mergeSlots({ partNames });
    context.reply(MessageFactory.text("已为您修改配件"));
    return context.next(InquiryIntents.询报价);
  }
};
