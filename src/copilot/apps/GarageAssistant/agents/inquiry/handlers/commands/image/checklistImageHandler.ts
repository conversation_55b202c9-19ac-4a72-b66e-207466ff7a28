import { IHandlerFunction, Context, MessageFactory, ICommandMessage, IAction } from "@casstime/copilot-core";
import { ImageService } from "../../../services/ImageService";
import { imageClient } from "../../../clients/ImageClient";
import { parseUserAgent } from "@/common/utils";
import { FormNames, inquiryForm } from "../../../forms";
import { InquiryIntents } from "../../../parsers/inquiryIntentClassifier";
import { candidateActionImageType, ImageType, ToCassServiceAction } from "@/copilot/constants";
import { createCandidateAction, createRotateImageAction } from "@/copilot/apps/GarageAssistant/agents/inquiry/utils";

export const checklistImageHandler: IHandlerFunction = async (context: Context) => {
  const { payload } = context;
  const imageService = context.getService(ImageService);
  const factory = MessageFactory.with({ id: imageService.getReplyMsgId() });
  context.setReplyMode("stream");
  const lastMessage = context.lastMessage as ICommandMessage;
  // 工单未识别到配件，引导用户旋转图片
  let extra: Record<string, unknown> = {};
  const rotateExtra: Record<string, unknown> = {};
  let label = imageService.getOrSetImageLabel() || "";
  if (lastMessage.type === "command") {
    extra = lastMessage.params?.extra || {};
    label = lastMessage.params?.label || "";
  }
  const imageUrl = imageService.getImageUrl();
  const candidates = imageService.getClassCandidates();
  const { platform } = parseUserAgent(payload.headers["user-agent"] || "");
  const imageParams = {
    imageUrl: imageUrl,
    application: "MALL_MAINTENANCE_SHOP_APP",
    channel: "CASSMALL",
    scene: "INTELLIGENT_PURCHASE",
    os: platform,
  };
  const reporter = imageService.getReporter();
  const step = reporter.step("image-checklist", {});
  step.start("开始识别手写工单", { data: { imageParams, label } });
  const ocrRecognizePromises = [imageClient.ocrRecognizePartsNames(imageParams)];
  const imageRotate = await imageClient.getImageRotate(imageUrl);
  // 非正向工单处理
  if (imageRotate.score && imageRotate.score >= 0.5 && imageRotate.label !== "0") {
    const rotateImageUrl = imageService.rotateImage(imageUrl, 360 - Number(imageRotate.label));
    ocrRecognizePromises.push(imageClient.ocrRecognizePartsNames({ ...imageParams, imageUrl: rotateImageUrl }));
  }
  // 并发查询手写工单识别
  const ocrRecognizeResList = await Promise.all(ocrRecognizePromises);
  // 取出识别字符最多的结果
  const ocrRecognizeRes =
    ocrRecognizeResList.reduce((pre, cur) => {
      const preStringLength = pre?.map((item) => item.words).join("").length || 0;
      const curStringLength = cur?.map((item) => item.words).join("").length || 0;
      return curStringLength > preStringLength ? cur : pre;
    }, []) || [];

  const vinCodes = imageService.getVIN(ocrRecognizeRes);
  let qualities = imageService.getQuality(ocrRecognizeRes);
  let partNames = await imageService.handleOcrRecognizeRes(ocrRecognizeRes);
  step.progress("手写工单识别结果", { data: { vinCodes, qualities, partNames } });
  if (label === ImageType.系统截图 || label === ImageType.工单) {
    const page = imageService.parseScreenShootCategory(ocrRecognizeRes);
    const replyText = imageService.getReplyByPage(page);
    step.progress("手写工单识别系统截图页面", { data: { page } });
    if (replyText) {
      context.reply(factory.text(replyText, { actions: [[ToCassServiceAction]] }));
      return;
    }
    const recognizeVinOnlyPages: Array<typeof page> = [
      "VIN汽修宝",
      "VINF6智数车辆详情",
      "VIN分类选择",
      "VIN跑街令车型配置详情",
      "港澳台车辆登记文件",
      "智能目录",
      "车辆详情",
      "查油液页面",
    ];
    if (vinCodes.length === 1 && partNames.length && page && recognizeVinOnlyPages.includes(page)) {
      partNames = [];
      qualities = [];
    }
  }
  // 处理vin码、工单
  if (vinCodes.length || partNames.length || qualities.length) {
    const formData = context.getForm<typeof inquiryForm>(FormNames.inquiryForm).getValues();
    const mergeSlot: {
      vinCode?: string;
      vinCodes?: string[];
      partNames?: string[];
      qualities?: string[];
      partNamesImages?: string[];
    } = {};
    if (vinCodes.length === 1) {
      mergeSlot.vinCode = vinCodes[0];
    } else {
      // 多个候选vin码
      mergeSlot.vinCodes = vinCodes;
    }
    if (partNames.length) {
      const entities = partNames.map((partName) => ({ name: "partName", value: partName }));
      context.setEntities(entities);
      const partNamesNew = [...new Set([...(formData.partNames || []), ...partNames])].filter(Boolean);
      mergeSlot.partNames = partNamesNew;
      mergeSlot.partNamesImages = (formData?.partNamesImages || []).concat(imageUrl);
    }
    if (qualities.length) {
      const qualitiesNew = [...new Set([...(formData.qualities || []), ...qualities])].filter(Boolean);
      mergeSlot.qualities = qualitiesNew;
    }
    context.mergeSlots(mergeSlot);
    return context.next(InquiryIntents.询报价);
  }

  // 爆炸图不提示旋转工单
  if (label === ImageType.爆炸图) {
    context.reply(
      factory.text("很抱歉，系统未能从您提供的图片中识别出配件名称。为确保询价信息准确，烦请你手动输入配件名称。")
    );
    return;
  }
  if (ocrRecognizeRes.length >= 3) {
    // 识别出多行文字，没有配件信息
    context.reply(factory.text("未能识别出配件信息，麻烦您手动输入配件名称或者车架号。"), {
      actions: [[createCandidateAction(ImageType.配件, imageUrl)]],
    });
    return;
  }
  if (extra.height && extra.width) {
    rotateExtra.height = extra.width;
    rotateExtra.width = extra.height;
  }
  const actions: IAction[][] = [
    [
      createRotateImageAction("左转90度", imageService.rotateImage(imageUrl, 270), rotateExtra),
      createRotateImageAction("右转90度", imageService.rotateImage(imageUrl, 90), rotateExtra),
    ],
    [createRotateImageAction("旋转180度", imageService.rotateImage(imageUrl, 180), extra)],
  ];
  const candidateLabel = candidates?.[0]?.label || "";
  if (candidateActionImageType.includes(candidateLabel)) {
    actions[1].push(createCandidateAction(candidateLabel, imageUrl)!);
  }
  context.reply(
    factory.text("未能识别出有用信息。请发送更清晰的工单图片，或者将图片旋转至正向", {
      extra: { imageUrl },
      actions,
    })
  );
  context.done();
  return;
};
