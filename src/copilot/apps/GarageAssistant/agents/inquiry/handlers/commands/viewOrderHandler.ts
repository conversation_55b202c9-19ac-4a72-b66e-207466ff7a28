import { Context, IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { commandIntents, ToCassServiceAction } from "@/copilot/constants";
import { FormNames, inquiryForm } from "../../forms";
import { ActionFactory } from "@/common/factory/ActionFactory";
import { InquiryIntents } from "../../parsers/inquiryIntentClassifier";

function createOrderDetailAction(orderId: string) {
  return ActionFactory.command("查看订单", commandIntents.commonNavigate, {
    params: {
      navigate: `cassapp://route/native/order/orderDetail?query=${JSON.stringify({ orderId })}`,
    },
  });
}
// 查询订单
export const viewOrderHandler: IHandlerFunction = async (context: Context) => {
  const formData = context.getForm<typeof inquiryForm>(FormNames.inquiryForm).getValues();
  const { orderIds } = formData;
  const actions = [];
  if (orderIds?.length) {
    actions[0] = [createOrderDetailAction(orderIds[0]), ToCassServiceAction];
    context.reply(
      MessageFactory.text(
        "你输入了订单号，现在可以点击下方的【查看订单】按钮，查阅订单详情。如遇售后问题，请直接点击【人工客服】按钮，我们的客服团队将为您提供专业解答。",
        { actions }
      )
    );
    return;
  }
  return context.next(InquiryIntents.询报价);
};
