import { IHandlerFunction } from "@casstime/copilot-core";
import { Types } from "mongoose";
import { MessageFactory } from "@casstime/copilot-core";
import { RecommendPlanGroup } from "@/models/RecommendPlanGroup";
import { MessageReporter } from "@/messages";
import { createDataProvider } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/factory";
import { PostProcessor } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/concrete/PostProcessor";
import { createPurchasePlanEmbed } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/handlers";

export const savePlanHandler: IHandlerFunction = async (context) => {
    const { inquiryId, plans } = context.slots;
    const dataProvider = createDataProvider(inquiryId);

    const reporterId = new Types.ObjectId().toString();
    const reporter = new MessageReporter(reporterId);
    const postProcessor = new PostProcessor({ reporter, dataProvider, inquiryId });
    // 存储到数据表
    const recommendPlanItem = (
        await RecommendPlanGroup.create({
            inquiryId,
            plans,
        })
    ).toJSON();

    // 呈现方案
    const recommendPurchasePlans = await postProcessor.process({
        plans,
        inquiryId,
    });
    const {
        inquiryDetail,
        quoteDetail: { quotes },
        needDecodeList: { needDecodeList, needsNames },
    } = await dataProvider.getAll();
    inquiryDetail.needsNames = needsNames;
    const { embed, planInfo, needFetch } = await createPurchasePlanEmbed({
        inquiryId,
        recommendPurchasePlans,
        inquiryDetail,
        quotes,
        planGroupId: recommendPlanItem.id,
        needs: needDecodeList,
        isGuard: context.slots["isGuard"],
        displayType: 'LIST',
    });
    // 询价卡片
    const id = new Types.ObjectId().toString();
    const factory = MessageFactory.with({ id });
    context.reply(
        factory.richtext("", {
            embed,
            extra: { inquiryId, keepTop: true, planInfo, needFetch, planGroupId: recommendPlanItem.id },
        })
    );
};
