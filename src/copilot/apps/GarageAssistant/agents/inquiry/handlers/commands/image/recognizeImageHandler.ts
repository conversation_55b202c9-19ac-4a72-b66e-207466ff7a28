import { imageIntents } from "@/copilot/constants";
import { Context, IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { tyreImageHandler } from "./tyreImageHandler";
import { checklistImageHandler } from "./checklistImageHandler";
import { imageClassifyHandler } from "./imageClassifyHandler";
import { ImageService } from "../../../services/ImageService";

// 图片分类、识别轮胎规格、识别工单
export const recognizeImageHandler: IHandlerFunction = async (context: Context) => {
  const imageService = context.getService(ImageService);
  context.setReplyMode("stream");
  const { intent } = context;
  context.reply(
    MessageFactory.markdown("请稍等...", {
      indicator: imageService.createIndicator("正在分析图片..."),
    }),
    { id: imageService.getReplyMsgId() }
  );
  const reporter = imageService.getReporter();
  const step = reporter.step("image-recognizeImageHandler", {});
  step.start("开始图片识别", { data: { intent } });
  // const imageCommandIntent = "";
  switch (intent) {
    case imageIntents.classifyImage:
      await imageClassifyHandler(context);
      return;
    case imageIntents.tyreImage:
      await tyreImageHandler(context);
      // imageCommandIntent = Intents.recognizeTyreImage;
      return;
    case imageIntents.checklistImage:
      await checklistImageHandler(context);
      // imageCommandIntent = Intents.recognizeChecklistImage;
      return;
  }
  await imageClassifyHandler(context);
};
