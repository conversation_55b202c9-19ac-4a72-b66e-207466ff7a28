import { commandIntents } from "@/copilot/constants";
import { IHandlerFunction, Context, MessageFactory, AdditionalFormat, Entity } from "@casstime/copilot-core";
import { imageClient } from "../../../clients/ImageClient";
import { createReidentificationTyreTips, createTips } from "../../../xml";
import { tyreService } from "../../../services/TyreService";
import { FormNames, inquiryForm } from "../../../forms";
import { IOriginalItemsItem } from "@/interfaces/inquiry";
import { IInquiryTyreSpecItem } from "../../../interface";
import { ImageService } from "../../../services/ImageService";

export const tyreImageHandler: IHandlerFunction = async (context: Context) => {
  const imageService = context.getService(ImageService);
  const factory = MessageFactory.with({ id: imageService.getReplyMsgId() });
  context.setReplyMode("stream");
  const form = context.getForm<typeof inquiryForm>(FormNames.inquiryForm);
  const formData = form.getValues();
  const imageUrl = imageService.getImageUrl();
  const reporter = imageService.getReporter();
  const step = reporter.step("image-tyreImageHandler", {});
  step.start("开始轮胎图片识别", { data: { imageUrl } });

  // 轮胎图片识别
  const tyreSize = await imageClient.getTyreSize(imageUrl);
  const originalItemsFromImage = tyreService.parseTyreSize(tyreSize, imageUrl) as (IOriginalItemsItem &
    IInquiryTyreSpecItem)[];
  step.progress("识别轮胎信息", { data: { originalItemsFromImage } });
  if (originalItemsFromImage) {
    // 轮胎规格识别成功埋点
    context.reply(
      MessageFactory.command(commandIntents.TRACK_COMMAND, {
        eventName: "tyre_ocr_result",
        isRecordUser: true,
        extra: {
          ...tyreSize,
          imgUrl: imageUrl,
          isSuccess: true,
          fromScreen: "AIChatScreen",
        },
      })
    );
    const entities: Entity[] = originalItemsFromImage.map((item) => ({ name: "partName", value: item.specification }));
    const originalItems = originalItemsFromImage.concat(formData.originalItems || []);
    context.mergeSlots({ originalItems });
    context.setEntities(entities);
    return context.next(commandIntents.IMMEDIATE_INQUIRY);
  }
  // 轮胎规格识别失败埋点
  context.reply(
    MessageFactory.command(commandIntents.TRACK_COMMAND, {
      eventName: "tyre_ocr_result",
      isRecordUser: true,
      extra: {
        messaage: "轮胎识别不准确",
        imgUrl: imageUrl,
        isSuccess: false,
        fromScreen: "AIChatScreen",
      },
    })
  );
  return context.next(`@command/${commandIntents.IMAGE_REC_PARTS_SIMILAR}`);
};
