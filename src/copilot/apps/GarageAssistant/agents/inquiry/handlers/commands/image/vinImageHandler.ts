import { fetch } from "undici";
import { IHandlerFunction, Context, MessageFactory } from "@casstime/copilot-core";
import { imageClient } from "../../../clients/ImageClient";
import { commandIntents, ImageType, ScanVinCodeAction } from "@/copilot/constants";
import { InquiryIntents } from "../../../parsers/inquiryIntentClassifier";
import { validateVINCheckBit } from "@/common/utils";
import { ImageService } from "../../../services/ImageService";
import { createCandidateAction } from "../../../utils";

export const vinImageHandler: IHandlerFunction = async (context: Context) => {
  const imageService = context.getService(ImageService);
  const factory = MessageFactory.with({ id: imageService.getReplyMsgId() });
  context.setReplyMode("stream");
  const imageUrl = imageService.getImageUrl();
  const reporter = imageService.getReporter();
  const step = reporter.step("image-vinImageHandler", {});
  step.start("开始识别vin码图片", { data: { imageUrl } });
  const response = await fetch(imageUrl);
  const imageBuffer = await response.arrayBuffer();
  const imageBase64Str = Buffer.from(imageBuffer).toString("base64");
  const [yituVin, vins] = await Promise.all([
    imageClient.ocrRecognizeVin(imageBase64Str),
    imageClient.getImageRecognizeVin(imageUrl),
  ]);
  const zettVin = vins[0] || "";
  const vinCode = findCorrectVIN([yituVin, zettVin]);
  step.start("识别出vin码", { data: { yituVin, zettVin, vinCode } });
  // vin码识别埋点
  context.reply(
    MessageFactory.command(commandIntents.TRACK_COMMAND, {
      eventName: "OCR_RECOGNIZE_VIN_RESULT",
      isRecordUser: true,
      extra: {
        imageUrl,
        yituVin,
        zettVin,
        vinCode,
        validRecognize: Boolean(zettVin && vinCode === zettVin),
        yituValidRecognize: Boolean(yituVin && vinCode === yituVin),
        fromScreen: "AIChatScreen",
      },
    })
  );
  if (vinCode) {
    context.mergeSlots({ vinCode });
    return context.next(InquiryIntents.询报价);
  }
  context.reply(factory.markdown("识别中", { indicator: imageService.createIndicator("继续识别车架号...") }));
  const proVins = await imageService.recognizeVinByProOcr();
  step.start("proocr识别vin", { data: { proVins } });
  if (proVins.length) {
    const vinCode = proVins[0];
    context.mergeSlots({ vinCode });
    return context.next(InquiryIntents.询报价);
  }
  context.reply(
    factory.text("抱歉，没有识别出有用信息，你可以输入车架号或者重新上传图片", {
      extra: { imageUrl },
      actions: [[ScanVinCodeAction, createCandidateAction(ImageType.配件, imageUrl)]],
    })
  );
  context.done();
};

const findCorrectVIN = (VINs: string[]) => {
  const validVINs = VINs.filter((VIN) => validateVINCheckBit(VIN));
  return validVINs[0] || VINs.filter(Boolean)[0];
};
