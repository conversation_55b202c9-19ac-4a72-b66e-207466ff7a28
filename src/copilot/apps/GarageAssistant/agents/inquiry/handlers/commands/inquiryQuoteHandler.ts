import { Context, IHandlerFunction, ICommandMessage } from "@casstime/copilot-core";
import { inquiryService } from "../../services/InquiryService";

// 用户更新报价信息，展示
export const inquiryQuoteHandler: IHandlerFunction = async (context: Context) => {
  const params = (context.lastMessage as ICommandMessage).params || {};
  context.reply(inquiryService.getInquiryQuoteMessage(params));
  context.done();
  return;
};
