import { Context, IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { imageClient } from "../../../clients/ImageClient";
import { ICandidateDTO, ILabel, ImageClassifyRes } from "@/interfaces/client";
import {
  candidateActionImageType,
  ImageTypeTips,
  PartsNamesImageType,
  SimilarPartsImageType,
  TyreImageType,
  VINImageType,
} from "@/copilot/constants";
import _ from "lodash";
import { vinImageHandler } from "./vinImageHandler";
import { tyreImageHandler } from "./tyreImageHandler";
import { checklistImageHandler } from "./checklistImageHandler";
import { similarPartsImageHandler } from "./similarPartsImageHandler";
import { ImageService } from "../../../services/ImageService";

export const imageClassifyHandler: IHandlerFunction = async (context: Context) => {
  const imageService = context.getService(ImageService);
  const factory = MessageFactory.with({ id: imageService.getReplyMsgId() });
  context.setReplyMode("stream");
  const imageUrl = imageService.getImageUrl();
  const labelResult: ImageClassifyRes = await imageClient.getImageClassify(imageUrl);
  const label: ILabel = labelResult?.label || ILabel["工单"];
  // 取出top5有效的候选label
  const labelResultList: ICandidateDTO[] =
    labelResult.labels?.map((label, index) => ({ label, prob: labelResult.probs?.[index] || 0 })) || [];
  const candidates = labelResultList
    .filter((item) => item.label !== label)
    .filter((item) => item.prob > 0.05)
    .filter((item) => candidateActionImageType.includes(item.label));

  const top5Candidate = _.orderBy(candidates, ["prob"], ["desc"]).slice(0, 5);
  imageService.setClassCandidates(top5Candidate);

  let indicator = imageService.createIndicator(ImageTypeTips[label]?.tip || "");
  imageService.getOrSetImageLabel(label);
  const reporter = imageService.getReporter();
  const step = reporter.step("image-classify", {});
  step.start("开始图片分类", { data: { imageUrl, label, top5Candidate } });
  if (VINImageType.includes(label)) {
    // VIN码图片 ["铭牌", "VIN码", "证件"]
    context.reply(factory.markdown("识别中...", { indicator }));
    await vinImageHandler(context);
    return;
  }

  if (PartsNamesImageType.includes(label)) {
    // 工单图片 ["工单","系统截图","聊天截图","爆炸图"]
    context.reply(factory.markdown("识别中...", { indicator }));
    await checklistImageHandler(context);
    return;
  }

  if (TyreImageType.includes(label)) {
    context.reply(factory.markdown("识别中...", { indicator }));
    await tyreImageHandler(context);
    return;
  }
  indicator = imageService.createIndicator(ImageTypeTips[SimilarPartsImageType[0]]?.tip || "");
  context.reply(factory.markdown("识别中...", { indicator }));
  await similarPartsImageHandler(context);
  return;
};
