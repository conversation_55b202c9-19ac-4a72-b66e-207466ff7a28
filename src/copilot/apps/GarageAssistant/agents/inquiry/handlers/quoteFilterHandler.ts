import { Context, IHandlerFunction, IAction, MarkdownStreamReplier, MessageFactory } from "@casstime/copilot-core";
import { quoteFilterClassifier } from "../classifiers/quoteFilterClassifier";
import { stringifyMessage } from "@/common/utils/message";
import { QuoteSchemeFilter } from "./quoteFilter/QuoteSchemeFilter";
import { createObjectId } from "@/common/utils";
import { inquiryService } from "../services/InquiryService";
import { InquiryIntents } from "../parsers/inquiryIntentClassifier";
import { TyreQuoteFilter } from "./quoteFilter/TyreQuoteFilter";
import logger from "@/common/logger";
import { createEmbed, createTyreMoreQuoteEmbed } from "../xml";
import _ from "lodash";
import { GoToInquiryAction } from "@/copilot/constants";
import { createTyreQuoteCard } from "../copilot-xml";
import { IntentCodes } from "../enum";

function generateSuggestions(samples: Awaited<ReturnType<typeof TyreQuoteFilter.prototype.sample>>) {
  const keywords = [
    "米其林",
    "倍耐力",
    "邓禄普",
    "韩泰",
    "锦湖",
    "优科豪马",
    "固特异",
    "普利司通",
    "马牌",
    "玲珑",
    "防爆",
    "静音棉",
  ];
  const keywordsInRemark = keywords.filter((keyword) =>
    samples.some((item) => item.remark.includes(keyword) && !item.remark.includes("非" + keyword))
  );
  const locations = samples.map((item) => item.locationName);
  return _.uniq(keywordsInRemark.concat(locations).concat("最便宜的"));
}

/**
 * 筛选轮胎
 */
async function filterTyreQuote(context: Context) {
  const input = stringifyMessage(context.lastMessage, false);
  const filter = context.slots.filter;
  const { inquiryId, isProxyInquiry, userLoginId, garageCompanyId } = filter;
  const id = createObjectId();
  // 构造代客询价信息
  const inquiryInfo = {
    isProxyInquiry: isProxyInquiry || false,
    userLoginId: userLoginId || "",
    garageCompanyId: garageCompanyId || "",
  };
  const tyreFilter = new TyreQuoteFilter(inquiryId, input, inquiryInfo);

  try {
    context.reply(MessageFactory.markdown("请稍等，正在准备数据...", { id }));

    let [sql] = await Promise.all([tyreFilter.generateSql(), tyreFilter.initialize()]);
    logger.info("tyre quote filter sql:", sql);
    let rows: unknown[] = [];
    if (sql) {
      sql = await tyreFilter.rewriteSql(sql);
      rows = await tyreFilter.executeSql(sql);
      const list = tyreFilter.exactResponses(rows);
      if (list.length) {
        const tyreQuoteContent = createTyreQuoteCard(list, inquiryId);
        const tyreMoreQuoteContent = createTyreMoreQuoteEmbed(inquiryId);
        const tyreQuoteEmbed = createEmbed("richtext", [tyreQuoteContent, tyreMoreQuoteContent]);
        context.reply({
          ...MessageFactory.text(`已为你筛选出以下轮胎：`, { id }),
          embed: tyreQuoteEmbed,
          // actions,
        });
        return;
      }
    }
    const SAMPLES_COUNT = 5;
    const samples = await tyreFilter.sample(SAMPLES_COUNT);
    if (!samples.length) {
      context.reply(
        MessageFactory.markdown("暂时还没有报价结果，请稍后再试", {
          id,
          actions: [[{ type: "nlu", text: input, nlu: { intent: IntentCodes.QUOTE_FILTER } }]],
        })
      );
      return;
    }
    const suggestions = _.shuffle(generateSuggestions(samples)).slice(0, 2);
    const suggestionActions: IAction[] = suggestions.map((keyword) => ({
      type: "nlu",
      text: keyword,
      nlu: { intent: IntentCodes.QUOTE_FILTER },
    }));
    const replier = MarkdownStreamReplier.for(context);
    const stream = await tyreFilter.generateReply(rows);
    await replier.reply(stream, { id, actions: [suggestionActions] });
  } catch (err) {
    logger.warn("轮胎交互式筛选出错: ", err);
    context.reply(MessageFactory.markdown("查询轮胎数据出错了，请稍后再试", { id }));
  } finally {
    await tyreFilter.dispose();
  }
}

/**
 * 筛选全车件
 */
async function filterWholeQuote(context: Context) {
  const input = stringifyMessage(context.lastMessage, false);
  const filter = context.slots.filter;
  const { inquiryId } = filter;
  const quoteSchemeFilter = new QuoteSchemeFilter(inquiryId, input);
  const id = createObjectId();
  const actions = [[{ ...GoToInquiryAction, text: "发布新询价" }]];
  try {
    // 并行生产SQL和初始化表格
    context.reply(MessageFactory.markdown("请稍等，正在准备数据...", { id }));
    const [{ sql, msg }] = await Promise.all([quoteSchemeFilter.generateSql(), quoteSchemeFilter.initialize()]);

    logger.info("quote filter sql:", sql, msg);
    let rows: unknown[] = [];
    if (sql) {
      context.reply(MessageFactory.markdown("请稍等，正在查询...", { id }));
      rows = await quoteSchemeFilter.executeSql(sql);
      const schemes = await quoteSchemeFilter.exactSchemes(rows);
      if (schemes.length) {
        // 条目数量倒序，取前两个
        const sortedSchemes = _.orderBy(schemes, (s) => s.programmeItems.length, "desc");
        const inquiryQuoteRes = await inquiryService.getInquiryQuoteByRecommend(
          inquiryId!,
          context.payload.headers,
          "",
          "",
          false,
          sortedSchemes.slice(0, 2)
        );
        const inquiryQuoteEmbed = await inquiryService.createRecommendRichText(inquiryQuoteRes, "", false);
        context.reply({
          ...MessageFactory.text(`已为你筛选出以下方案：`, { id, actions }),
          embed: inquiryQuoteEmbed,
        });
        return;
      }
    }
    const replier = MarkdownStreamReplier.for(context);
    const stream = await quoteSchemeFilter.generateReply(rows);
    await replier.reply(stream, { id, actions });
  } catch (err) {
    logger.warn("全车件交互式筛选出错:", err);
    context.reply(MessageFactory.markdown("查询失败，请稍后再试", { id, actions }));
  } finally {
    await quoteSchemeFilter.dispose();
  }
}

export const quoteFilterHandler: IHandlerFunction = async (context) => {
  context.setReplyMode("stream");
  const filter = context.slots.filter;
  if (!filter) {
    return context.next(InquiryIntents.买配件);
  }
  const { type } = filter;
  if (type === "tyre") {
    await filterTyreQuote(context);
  } else {
    await filterWholeQuote(context);
  }
  // 防止用户输入配件名导致槽变更
  context.rollbackSlots();
  context.activatePreClassifierOnce(quoteFilterClassifier);
};
