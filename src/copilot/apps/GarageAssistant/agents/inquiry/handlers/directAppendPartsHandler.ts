import { Context, IHandlerFunction } from "@casstime/copilot-core";
import { Intents } from "@/copilot/constants";
import { messageService } from "../services/MessageService";
import { inquiryService } from "../services/InquiryService";

// 重新开始新询价
export const directAppendPartsHandler: IHandlerFunction = async (context: Context) => {
  const { payload } = context;
  // 校验是否使用历史车架号
  const messages = await messageService.getLastMessageList(payload);
  // 查询车型信息
  const inquiryHistory = await inquiryService.getVinFromMessages(messages);
  if (inquiryHistory.inquiryId) {
    context.mergeSlots({
      ...inquiryHistory,
      inquiryCarModel: inquiryHistory.carModel,
      __NOASKAPPEND__: true,
      __ADDITIONALPARTS__: true,
    });
  }
  return context.next(Intents.inquiry);
};
