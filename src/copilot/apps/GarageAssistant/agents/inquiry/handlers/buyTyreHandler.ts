import { Context, IHandlerFunction, IAction, ITextMessage, MessageFactory } from "@casstime/copilot-core";
import { FormNames, inquiryForm } from "../forms";
import { SendTyreImageAction } from "@/copilot/constants";
import { createTips, createTyreSizeTips } from "../xml";
import { Version } from "@/common/utils";
import { VersionEnum } from "@/common/enums";

// 买轮胎
export const buyTyreHandler: IHandlerFunction = async (context: Context) => {
  const formData = context.getForm<typeof inquiryForm>(FormNames.inquiryForm).getValues();
  // 去掉"轮胎"配件
  const partNames = (formData?.partNames || []).filter((item) => item !== "轮胎");
  context.mergeSlots({
    partNames,
  });
  const others: Partial<ITextMessage> = {};
  // 旧版本处理
  const appVersion = context.payload?.appVersion as Version;
  const noSupportRichText = appVersion.isLessThan(VersionEnum.FIVE_18_0);
  if (noSupportRichText) {
    const actions: IAction[][] = [];
    actions[0] = [SendTyreImageAction];
    others.actions = actions;
  } else {
    const tipsContent = createTyreSizeTips();
    const tips = createTips("richtext", tipsContent);
    if (tips) {
      others.tips = tips;
    }
  }
  context.reply(MessageFactory.text("告诉我轮胎规格，或拍轮胎图帮您问价", others));
};
