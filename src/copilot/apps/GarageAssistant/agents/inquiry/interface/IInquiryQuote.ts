import { IInquiryInfo } from "./IInquiryInfo";
import { IQuoteStoreItem } from "./IInquiryStoreInfo";

// 询报价状态
export interface IInquiryStateRes {
  inquiryCount: number; // Joi.number().required().description('有效的报出商品数')
}

/// 询报价详情 - 增减字段请参考 http://ctsp.casstime.com/interface/manage/detail?id=47471
export interface IStockInfoItem {
  facilityId: string; // 仓库ID
  facilityName: string; // 仓库名称
  inventoryValue: string; // 有货
  isDefault: boolean;
  availableToPromiseTotal: number; // 库存
}

export interface IQuoteResItem {
  /** 自定义 配件名称 */
  partsName: string;
  /** 报价ID */
  quotationProductId: string;
  /** 报价综合排序字段 */
  quotationSortIndex?: number;
  /** 是否显示广告标识 */
  isShowAdvFlag?: boolean;
  /** 报价评分 */
  totalScore?: number;
  /** 是否已采 */
  isOrdered?: boolean;
  /** 是否秒报 */
  isQuickQuote?: boolean;
  /** 发货地ID */
  departureId?: string;
  /** 发货地 二次报价才有 */
  departure?: string;
  /** 卖家含税价格加上钉箱费 */
  sellerPrice?: string;
  /** 卖家不含税价格加上钉箱费 */
  sellerBtPrice?: string;
  /** 价格调价ID，有则显示卖家价格 */
  priceAdjustId?: string;
  /** 报价类别分区：ORDERED_PARTITION：已采，TOP_PARTITION：置顶报价，LOW_QUALITY_PARTITION：低质报价，UNUSUAL_PARTITION：异常报价，NORMAL_PARTITION：正常报价 */
  partition?: string;
  /** 是否是原厂配套 */
  isOriginalAssort?: boolean;
  /** 是否是套件 */
  isProductSet?: boolean;
  /** 套件编码 */
  productSetCode?: string;
  /** 税后钉箱费 */
  atBoxFee?: string;
  /** 税前钉箱费 */
  btBoxFee?: string;
  /** 品牌品质 */
  partsBrandQuality?: string;
  /** 品牌品质ID */
  partsBrandQualityId?: string;
  /** 是否替换件 */
  isReplacement?: boolean;
  /** 品牌介绍 */
  brandIntroductionUrl?: string;
  /** 报价员ID */
  quotedUserId?: string;
  /** 零件号 */
  partsNum?: string;
  /** 译码结果零件号 */
  originOeNumber?: string;
  /** 商品ID */
  productId?: string;
  /** 店铺ID */
  storeId?: string;
  /** 店铺名称 */
  storeName?: string;
  /** 品牌 */
  brandName?: string;
  /** 品牌ID */
  brandId?: string;
  /** 品质: 原厂件(ORIGINAL_PARTS品牌件(BRAND_PARTS) */
  partType?: string;
  /** 品质: 原厂件(ORIGINAL_PARTS品牌件(BRAND_PARTS) */
  partTypeDesc?: string;
  /** 零件品牌品质+品牌 */
  partsBrandQualityAndBrandName?: string;
  /** 税率 */
  taxRate?: number;
  /** 备注 */
  remark?: string;
  /** 调货天数 */
  arrivalTime?: number;
  /** FINISHED_GOODS,现货, DISPATCH_GOODS,调货 */
  productType?: string;
  /** 数量 */
  quantity?: number;
  /** 买家含税价格 */
  price?: string;
  /** 买家不含税价格 */
  btPrice?: string;
  /** MANUALLY 人工报价 系统报价AUTO */
  source?: string;
  /** 询价条目禁售、组合、警示说明 (禁售 =》组合 =》警示) */
  instruction?: string;
  location?: string;
  locationName?: string;
  /** 仓库信息 */
  stockInfo?: IStockInfoItem[];
  /** 禁售原因 可能数据为空字符串,就不展示. 暂未用此字段 */
  unsaleableRemark?: string;
  /** 问题配件问题描述  可能数据为空字符串,就不展示 */
  exceptionWarningDescription?: string;
  /** 组合配件描述  可能数据为空字符串,就不展示 */
  combinedRecommendDescription?: string;
  /** 是否是用户指定 */
  isDistributeSource?: boolean;
  /** 开思推广 开思推荐 有值就是有开思推荐  */
  ruleId?: string;
  /** 是否需要补充信息 */
  isSupplementResources?: boolean;
  /** 供应商标签,有值表示是广告供应商 */
  storeLabelContent?: string;
  /** 广告供应商跳转链接 */
  storeLabelUrl?: string;
  /** 是否系统报价无库存 */
  isQuotationNoInventory?: boolean;
  /** 方案id（自定义） */
  programmeId: string;
  /** 方案名称（自定义） */
  programmeName: string;
  /** 方案描述（自定义*/
  programmeDescription: string;
  /** 配件名称（自定义） */
  partName: string;
  /** 译码结果ID） */
  standardItemId?: string;
  /** 商家简称 */
  positioningName?: string;
}

export interface IQuoteStoreLayer {
  quoteItems: IQuoteResItem[];
  storeId: string; // key1
  productExpandNum: number;
}

export interface IQuoteSubLayer {
  qualityCode: string;
  labelAds: unknown[];
  subLayerName: string;
  showQualityName: string;
  subLayerPriceSortExpandNum: number;
  subLayerId: string;
  storeLayers: IQuoteStoreLayer[];
  subLayerExpandNum: number;
  showSubQualityName: string;
  subLayerSortIndex: number;
  showBrandName: string;
  brandName: string;
  qualityName: string;
  isShowAdvFlag: boolean;
  brandCode: string;
}

export interface IQuoteLayer {
  layerCode: string;
  layerName: string;
  subLayersNew: IQuoteSubLayer[];
  subLayers: unknown[];
  layerDescription: string;
  layerExpandNum: number;
  layerId: string;
  layerNameUrl: string;
  layerDescriptionHref: string;
}

export interface IQuoteStockStoreId {
  storeId: string;
}

export interface IQuoteDecodeLayer {
  layers: IQuoteLayer[];

  partsNum: string;
  supplementStoreIds: unknown[];
  storeHolidayDesc: string;
  qualitiesType: string[];
  recommendAmount: number;
  remarkImgs: unknown[];
  status4sValue: string;
  statusId: string;
  competitivePrice: number;
  quantity: number;
  decodeResultId: string; // key0
  decoderId: string;
  isFastOeParts: boolean;
  outOfStockStoreIds?: IQuoteStockStoreId[]; // 报缺货的商家
  systemFindGoodsStoreNum: number;
  categoryCode: string;
  statusDesc: string;
  standardNameCode: string;
  partsName: string;
  remark: string;
  qualities: IQuoteNeedsQualities[];

  // 自己拼接的数据
  quoteArray?: IQuoteResItem[];
  outOfStockStoreIdArray?: string[]; // 报缺货的商家
}

export interface IQuoteNeedsQualities {
  isChecked: boolean;
  orderNum: string;
  qualityId: string;
  qualityName: string;
}

export interface IQuoteNeedsLayer {
  needId: string; //
  decodeResults?: IQuoteDecodeLayer[];

  buyRemark: string;
  buyTypeId: string;
  buyTypeName: string;
  images: string[];
  isSupplierAdd: boolean;
  needsName: string;
  qualities: IQuoteNeedsQualities[];
  qualitiesType: string[];
  quantiry: number;
  remark: string;
  statusDesc: string;
  statusId: string;
  supplemetResourceList: unknown[];

  // 自己拼接的数据
  decodeResultArray?: IQuoteDecodeLayer[];
  decodeNameArray?: string[];
}

export interface IInquiryDetailRes {
  defaultStickyNumber: number;
  isSticky: boolean;
  userNeeds: IQuoteNeedsLayer[];
}

export interface IModifyMessageRes {
  isSuccess: boolean; // Joi.boolean().default(false).description('是否修改成功'),
}

export interface IGetInquiryQuoteRes {
  quoteStoreList: IQuoteStoreItem[];
  inquiryNeedPolling?: boolean;
  inquiryDetail: IInquiryInfo;
  inquiryId?: string;
  storeQuoteByRecommend?: IQuoteResItem[];
  groupedByProgrammeIdAndStoreId?: IGroupedByProgrammeIdAndStoreId[];
  inquiryCount?: number;
}

export interface IGroupedByProgrammeIdAndStoreId {
  items: IGroupedByStoreId[];
  /** 方案id */
  programmeId: string;
  /** 方案名称 */
  programmeName: string;
  /** 方案描述*/
  programmeDescription: string;
}
export interface IGroupedByStoreId {
  storeId: string;
  storeName: string;
  quoteItems: IQuoteResItem[];
}
