interface IProductItem {
  productId: string;
  version?: string;
  snapshotType?: string;
  quotationProductId?: string;
}

export interface IGetSuitesDetailPayload {
  products: IProductItem[];
}

interface ImageItem {
  imageId: string; // 图片id
  imageName: string; // 图片名称
  imageUrl: string; // 图片链接
}

interface ProductsItem {
  partName: string; // 单品名称
  quantity?: number; // 推荐数量
  partCode: string; // 单品号
  partCodeTrim: string; // 单品号trim
  productImage: ImageItem[]; // 单品图片
  remark: string; // 备注
}

interface SuiteDetailItem {
  suiteId: string; // 套件id,套件唯一标识，等同于 brand_code+merchant_id+suite_code
  suiteName: string; // 套件名称,商家内部唯一
  suiteTypeCode: string; // 套件关系代码
  suiteTypeName: string; // 套件关系描述
  remark: string; // 套件描述,或商家维护的推荐理由
  products: ProductsItem[]; // 套装商品列表
}

interface SuiteListItem {
  id?: string | null; // 查询Id,PLATMALL场景对应商品ID，INQUIRY场景对应报价结果ID，ORDER场景对应订单ID
  productId: string; // 商品ID
  version: string; // 版本号
  suiteDetail: SuiteDetailItem; // 套件详情信息
}

export interface IGetSuitesDetailResponse {
  suiteList?: SuiteListItem[]; // 套件列表
}
