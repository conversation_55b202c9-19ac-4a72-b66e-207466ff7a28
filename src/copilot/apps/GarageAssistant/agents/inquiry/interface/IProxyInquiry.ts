export interface IAddress {
  /** 收货地址id */
  addressId?: string;
  /** 市id */
  cityGeoId?: string;
  /** 市名字 */
  cityGeoName?: string;
  /** 收货人名称 */
  contactName?: string;
  /** 联系号码 */
  contactNumber?: string;
  /** 区id */
  countyGeoId?: string;
  /** 区名字 */
  countyGeoName?: string;
  /** 纬度 */
  latitude?: number;
  /** 经度 */
  longitude?: number;
  /** 省id */
  provinceGeoId?: string;
  /** 省名字 */
  provinceGeoName?: string;
  /** 街道id */
  villageGeoId?: string;
  /** 街道名字 */
  villageGeoName?: string;
}

export interface ICarImagesItem {
  /** 资源类型 */
  resourceType?: string;
  /** 资源路径 */
  resourceUrl?: string;
}

export interface IPreRecordTagListItem {
  /** 标签类型 */
  tagType?: string;
  /** 标签值 */
  tagValue?: string;
}

export interface IProxyUserNeedsItem {
  /** 描述 */
  description?: string;
  /** 是否已经译码 */
  hasResolved?: boolean;
  /** 需求图片 */
  imageUrls?: string[];
  /** 询价需求来源（电子目录 或者 人工录入） */
  inquirySource?: string;
  /** 客户需求（配件名称） */
  needsName?: string;
  /** 数量 */
  quantity?: number;
  /** 用户备注 */
  remark?: string;
}

export interface IVehicle {
  /** 车辆品牌Code */
  carBrandCode?: string;
  /** 车辆品牌ID */
  carBrandId?: string;
  /** 车辆品牌名字 */
  carBrandName?: string;
  /** 车型id */
  carModelId?: string;
  /** 车型名称 */
  carModelName?: string;
  /** epc车型ID */
  epcModelCode?: string;
  /** epc车型名称 */
  epcModelName?: string;
  /** 主机厂id */
  locationId?: string;
  /** 主机厂名称 */
  locationName?: string;
  /** 销售车型id */
  saleModelCode?: string;
  /** 销售车型名称 */
  saleModelName?: string;
  /** 车系英文名 */
  seriesEn?: string;
  /** 车系id */
  seriesId?: string;
  /** 车系中文名 */
  seriesZh?: string;
  /** vin码 */
  vin?: string;
}

export interface ICreateProxyInquiry {
  /** 是否事故车 */
  accidentInquiry?: boolean;
  /** 地址信息 */
  address?: IAddress;
  /** 是否全部译码 */
  allResolved?: boolean;
  /** 业务场景 */
  businessScenario?: string;
  /** 车辆图片信息 */
  carImages?: ICarImagesItem[];
  /** 主账号id */
  corporateId?: string;
  /** 主账号名称 */
  corporateName?: string;
  /** 维修厂公司id */
  garageCompanyId?: string;
  /** 维修厂公司名称 */
  garageCompanyName?: string;
  /** 集团用户id */
  groupUserId?: string;
  /** 集团用户名称 */
  groupUserName?: string;
  /** 询价原因【需求不明确 不支持车型 不懂操作 不愿询价 其他】 */
  inquiryReason?: string;
  /** /询价单类型 */
  inquiryType?: string;
  /** 保险公司代码 */
  insuranceCompanyCode?: string;
  /** 保险公司名称 */
  insuranceCompanyShortName?: string;
  /** 是否保险理赔 */
  insuranceDirect?: boolean;
  /** 发票类型 */
  invoiceType?: string;
  /** 是否匿名询价 */
  isAnonymous?: string;
  /** 是否新客 Y：新客，N：非新客 */
  isNewCustomize?: string;
  /** 是否为新版询价 */
  newVersion?: boolean;
  /** 不要替换件，Y：不需要替换件，N：需要替换件 */
  noReplacement?: string;
  /** 是否需要开票 需要：YES，不需要：NO */
  openInvoiceType?: string;
  /** 配件需求信息 */
  partImages?: ICarImagesItem[];
  /** 预录单标签信息 */
  preRecordTagList?: IPreRecordTagListItem[];
  /** 代录人id */
  proxyId?: string;
  /** 代录人姓名 */
  proxyName?: string;
  /** 采购员id */
  purchaserId?: string;
  /** 采购员姓名 */
  purchaserName?: string;
  /** 发布询价的勾选的品质 */
  qualities?: string[];
  /** 品质 */
  quality?: string;
  /** 报价分配方式 */
  quotedType?: string;
  /** 当前询价单号（重新询价时需要赋值） */
  reInquiryId?: string;
  /** 备注 */
  remark?: string;
  /** 是否代客询价 */
  replaceCustomerRecord?: boolean;
  /** 是否需要对象发票 */
  requireItemInvoice?: boolean;
  /** 用户是否选择过品牌（同一vin出现多个车架号） */
  selectBrandFlag?: string;
  /** 是否跳过译码 */
  skipDecode?: boolean;
  /** 来源 PC ANDROID IOS  */
  source?: string;
  /** 指定的店铺id */
  storeIds?: string[];
  /** 需求信息 */
  userNeeds?: IProxyUserNeedsItem[];
  /** 车辆信息 */
  vehicle?: IVehicle;
  /** VIN码图片 */
  vinPicture?: string;
}