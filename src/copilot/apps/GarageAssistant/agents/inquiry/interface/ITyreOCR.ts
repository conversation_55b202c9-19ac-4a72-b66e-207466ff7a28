interface ITreadPattern {
  name?: string; // 胎面花纹名称
  aliasName?: string; // 胎面花纹别名
}

interface IIdentifier {
  type?: string; // 标识符类型
  code?: string; // 标识符代码
  desc?: string; // 描述
}

interface IBoxBounding {
  x?: number; // 边界框的 x 坐标
  y?: number; // 边界框的 y 坐标
  width?: number; // 边界框的宽度
  height?: number; // 边界框的高度
}

interface ITimes {
  download?: number; // 下载时间
  tyreDetect?: number; // 轮胎检测时间
  textDetect?: number; // 文本检测时间
  textTransform?: number; // 文本转换时间
  textRec?: number; // 文本识别时间
  textParse?: number; // 文本解析时间
}

export interface ImageTyreSizePrediction {
  texts?: string[]; // 一系列文本信息
  boxBounding?: IBoxBounding; // 边界框信息
  times?: ITimes; // 时间
  brandCode?: string; // 品牌代码
  brandName?: string; // 品牌名称
  sectionWidth?: string; // 断面宽度
  flatnessRate?: string; // 扁平比
  typeCode?: string; // 轮胎类型代码
  rimDiameter?: string; // 轮辋直径
  loadRating?: string; // 负荷等级（即将废弃）
  loadIndex?: string; // 负荷指数
  speedRating?: string; // 速度级别
  treadPattern?: ITreadPattern; // 胎面花纹信息
  othersTreadPatterns?: ITreadPattern[]; // 其他胎面花纹列表（类型不确定，可根据实际情况调整）
  identifiers?: IIdentifier[]; // 标识符列表
  madeIn?: string; // 生产地
  warning?: string; // 警告信息
  message?: string; // 返回信息
}

export interface ImageTyreSizeRes {
  result: ImageTyreSizePrediction;
}
