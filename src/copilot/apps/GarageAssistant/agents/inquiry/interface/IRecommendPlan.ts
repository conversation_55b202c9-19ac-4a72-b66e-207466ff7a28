export interface ICreateRecommendPlanParams {
  requestId: string; //请求id，必填
  inquiryId: string; //询价单号，必填
  produceScene: string; //制作场景，必填,im场景填 IM_DIALOG
  sourceGroup: string; //来源业务组，必填,IM场景填 IM
  callbackUrl?: string; //询价单号，非必填，需要回调时传入POST请求的url
  chatMemory?: boolean; //是否使用历史消息进行问答，非必填。IM场景传true
  sessionId?: string; //会话ID，非必填。调用方传入用于关联上下文输入
  userMessageText?: string; //用户输入内容，非必填。
  updateSystemMessage?: boolean; //是否更新SystemMessage，非必填。IM场景传true
}
export interface ICreateRecommendPlanRes {
  requestId?: string; //请求id
  inquiryId?: string; //询价单号
  inquiryProgrammeGroupId?: string; //方案组id
}
export interface IGetRecommendPlanParams {
  programmeGroupId: string; //方案组，必填
  turns?: number; //轮次，非必填
  latestTurn?: boolean; //是否仅获取最新轮次的方案，非必填
}
interface IRecommendPlanItem {
  quotationProductId: string; // 报价结果id
  standardItemId: string; // 标准需求id（译码结果）
}
export interface IGetRecommendPlanRes {
  programmeId: string; // 询价单推荐方案id
  inquiryId: string; // 询价单号
  scenario: string; // 场景
  programmeName: string; // 推荐方案名称
  programmeDescription: string; // 推荐方案描述
  createdDate: number; // 方案创建时间
  programmeItems: IRecommendPlanItem[]; // 方案条目
  turns?: number; // 轮次
}
