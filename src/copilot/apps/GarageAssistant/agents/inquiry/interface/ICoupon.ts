export interface ICouponActivitiesResponse {
  /** 领取限制 */
  acquireMode?: string;
  /** 领取页面 */
  acquirePageTypeList?: string[];
  /** 领取页面字符串 */
  acquirePageTypeStr?: string;
  /** 最大领取次数 */
  acquireTimes?: number;
  /** 公司是否已经参与该活动(已经领取优惠券和能否领取优惠券),true为已参与或者不可领取优惠券,false为未参与或可领取优惠券 */
  acquired?: boolean;
  /** 优惠券活动状态：未开始、进行中、已结束 */
  activityStatus?: string;
  /** 所属的公司ID */
  attachCompanyId?: string;
  /** 所属的公司的名称 */
  attachCompanyName?: string;
  /** 优惠券类型(平台,商家) */
  attachType?: string;
  /** 平均订单价格 */
  avgOrderPrice?: number;
  /** 优惠券活动开始时间 */
  beginDate?: string;
  /** 能否领取优惠券,true为可领取优惠券,false为不可领取优惠券 */
  canAcquired?: boolean;
  /** 发放城市范围 */
  cityRange?: string;
  /** 发放城市范围的名称 */
  cityRangeName?: string;
  /** 适用品牌，此处为品牌ID */
  conditionBrands?: string[];
  /** 优惠券面值 */
  couponAmount?: number;
  /** 优惠券活动期内优惠券的最大发放数量 */
  couponNumber?: number;
  /** 优惠券模板业务范围: MALL_SCOPE:商城业务;INQUIRY_SCOPE:询报价业务 */
  couponTemplateBusinessScope?: string;
  /** 优惠券模板id */
  couponTemplateId?: string;
  /** 优惠券模板名称 */
  couponTemplateName?: string;
  /** 优惠券类型(满减券,折扣券) */
  couponType?: string;
  /** 优惠券有效期开始时间 */
  couponValidityBeginDate?: string;
  /** 优惠券有效天数 */
  couponValidityDays?: number;
  /** 优惠券有效期结束时间 */
  couponValidityEndDate?: string;
  /** 创建人 */
  createdBy?: string;
  /** 创建时间 */
  createdStamp?: string;
  /** 关联客户分组 */
  customerGroupCodes?: string[];
  /** 关联客户分组展示 */
  customerGroupCodesDesc?: string;
  /** 发放客户范围 */
  customerRange?: string;
  /** 优惠券活动结束时间 */
  endDate?: string;
  /** 优惠券活动id */
  id?: string;
  /** 最后修改人 */
  lastUpdatedBy?: string;
  /** 最后修改时间 */
  lastUpdatedStamp?: string;
  /** 优惠券活动名称 */
  name?: string;
  /** 是否需要手动领取 */
  needAcquire?: boolean;
  /** 新客订单数量 */
  newCustomOrderNumber?: number;
  /** 新客订单占比 */
  newCustomOrderRate?: number;
  /** 优惠券已领取数量 */
  obtainedNumber?: number;
  /** 订单数量 */
  orderNumber?: number;
  /** 备注 */
  remark?: string;
  /** 订单要求 */
  requireOrderAmount?: number;
  /** 是否发短信：true发送；false不发送 */
  sendMessageFlag?: string;
  /** 优惠券活动状态 1:启用 0:停用 */
  status?: string;
  /** 优惠券活动触发事件 */
  triggerEvent?: string;
  /** 触发事件的值 */
  triggerEventValue?: string;
  /** 是当个模板还是组合模板 */
  type?: string;
}

export interface ICouponConfig {
  inquiryCouponActivityId: string;
  inquiryCouponStartTIme: string;
  inquiryCouponEndTIme: string;
  inquiryPreviousOrdersNumForDay: number;
}
