import { IPlanQuoteItem } from "@/clients/quote/interface";
import { IInquiryInfo } from "./IInquiryInfo";

// 智能方案任务表参数
export interface IIntelligentPlanTaskParams {
  messageId: string;
  inquiryId: string;
  recommendContentsGroupId: string;
  finished: boolean;
  makerType?: string;
  turns: number; // 方案轮次
  hasSentTurn: number; // 发给前端的轮次
  inquiryprogrammeIds: string[]; // 方案id数组
  expiration: number;
  source: string;
  fromPage: string;
  isOpenInvoice: boolean;
  inquiryDetail?: IInquiryInfo;
}

export interface IGetIntelligentPlanParams {
  recommendContentsGroupId: string; //方案组，必填
  turns: number; //轮次，必填
  latestTurn?: boolean; //是否仅获取最新轮次的方案，非必填
}
// 推荐的方案条目
export interface IIntelligentProgrammeItem {
  _id?: string; //方案条目id
  name: string; //方案名称
  type: string; //方案类型;共享仓方案:YUN_FACILITY,单店集采方案:COLLECT
  source: string; //方案来源;机器学习:MACHINE_LEARNING,人工兜底:BACKUP
  storeId?: string; //店铺id
  storeName?: string; //店铺id
  facilityId?: string; //仓库id,仅单仓方案有值
  facilityName?: string; //仓库id,仅单仓方案有值
  reason: string; //原因
  standardItemResults: {
    standardItemId: string; //标准需求id（译码结果）
    quotationProductIds: string[]; //报价结果id
  }[]; //方案需求结果
}
interface IIntelligentStoreItem {
  storeId: string; //店铺id
  storeName: string; //店铺名称
  source: string; //方案来源;机器学习:MACHINE_LEARNING,人工兜底:BACKUP
  reason: string; //原因
}
export interface IGetIntelligentPlanRes {
  demandId: string; // 询价单号
  scenario: string; // 场景
  createdDate: number; // 方案创建时间
  recommendProgrammes: IIntelligentProgrammeItem[]; // 方案条目
  recommendStores: IIntelligentStoreItem[];
  turns: number; // 轮次
}

export interface IIntelligentPlan {
  id?: string; // 方案id
  _id?: string; // 方案id
  name?: string; //方案名称
  type?: string; //方案类型;共享仓方案:YUN_FACILITY,单店集采方案:COLLECT
  source?: string; //方案来源;机器学习:MACHINE_LEARNING,人工兜底:BACKUP
  inquiryId?: string; //询价单id
  storeId?: string; //店铺id,仅单店方案有值
  storeName?: string; //店铺名称,仅单店方案有值
  iconUri?: string; // 店铺等级
  levelValue?: number; // 供应商等级
  facilityId?: string; //仓库id,仅单仓方案有值
  facilityName?: string; //仓库名称,仅单仓方案有值
  defaultEta?: string; //时效信息
  deliveryWarehouse?: string; //发货仓库
  packagePrice?: number; //整单价格
  reason: string; //原因
  showFeature?: string; //展示特征
  quotes?: Partial<IPlanQuoteItem>[];
  positioningName?: string; // 商家简称;
  features?: string[]; // 方案特性
}

export interface IIntelligentQuote {
  needsName?: string; //需求名称
  partsName?: string;
  qualityId?: string;
  qualityName?: string;
  price?: string; // 展示价格
  btPrice?: string; // 税前价格
  atPrice?: string; // 税后价格
  departure?: string; // 仓库
  departureId?: string; // 仓库id
  quotationProductId?: string; // 报价id
  storeId?: string;
  storeName?: string;
  isOriginalAssort?: boolean; //是否原厂配套  true为原厂配套  fasle为非原厂配套
  spdFeatureDetail?: string; //SPD特征信息
  quantity?: number; // 数量
  standardItemId?: string; // 译码结果ID
  positioningName?: string; // 商家简称
}

export enum IntelligentPlanType {
  YUN_FACILITY = "YUN_FACILITY",
  COLLECT = "COLLECT",
  RECOMMENDED_STORE = "RECOMMENDED_STORE",
  COMPREHENSIVE = "COMPREHENSIVE",
  DEFAULT = "DEFAULT",
}

// 0占位、1-5钻、6皇冠
export const LevelIconRatio = [0, 1, 2, 3, 4, 5, 4.4];

/** levelCode -> levelValue */
export const StoreLevelMap: Record<string, number> = {
  L00: 1,
  L01: 2,
  L02: 3,
  L03: 4,
  L04: 5,
  L05: 6,
};
