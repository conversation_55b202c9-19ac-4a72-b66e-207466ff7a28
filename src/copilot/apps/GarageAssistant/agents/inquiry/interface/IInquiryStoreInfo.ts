import { IStockInfoItem } from "./IInquiryQuote";

export interface IInquiryStoreInfoRes {
  quoteNumber: number;
  unquoteNumber: number;
  inquiryStoreInfos: IInquiryStoreItem[];
}

/// 询报价商家信息
export interface IInquiryStoreItem {
  storeId: string; //店铺ID'
  storeName: string; //店铺名称'
  positioningName: string; //商家简称'
  quoteStatusId: string; //供应商报价状态 UNQUOTE 报价中，QUOTE 报价完成，EXPIRED 已过期'
  supplierCompanyId: string; //供应商公司ID'
  invoiceSupport: string; //供应商是否支持开票，YES(支持开票 NO(不支持开票 BOTH(都可以)'
  invoiceTypeSupport: string; //供应商支持的发票类型 "NORMAL" 普通发票 | "VAT" 增值税发票（专票）｜ BOTH(都可以)'
  canOpenItemInvoice: boolean; //能否开对项发票'
  wholeOrderQuotationStatus: string; //整单报价状态 DISABLE_WHOLE_DISTRIBUTE 不可整单分配 ENABLE_WHOLE_DISTRIBUTE 能整单分配 WHOLE_DISTRIBUTED 已整单分配'
  quoteStatusName: string; //报价状态名称'
  quoteNeedNumber: number; //已报价数量'
  unquoteNeedNumber: number; //缺货数量'
  itemCount: number; //需求已分配数量'
  buyStatus: string; //购买状态'
  isAppointSupplier: boolean; //是否指定供应商'
  iconUri: string; //供应商等级图片链接'
  levelValue: number; //供应商等级'
  sales: string; //销量'
  storeScore: string; //商家评分'

  isOnline: boolean; //是否在线'
  todayIsWeekday: boolean; //今天是否工作日'
  nextWeekdayDesc: string; //下一个工作日描述'
  nextWeekday: number; //下一个工作时间戳'
  is4SGuarantee: boolean; //是否4s联保'
  isExclusiveFlag: boolean; //是否是专属供应商'
  isBoschSupplier: boolean; //是否是博世授权经销商'

  isStrict: boolean; //是否是开思严选店铺'
  wholeOrderQuote?: boolean; //是否整单报出'
  hiddenItemCount?: number; // 隐藏的报出
  resolvedQuotedCount: number; //已报价sku数'
  resolvedTotalCount: number; //总sku数'

  couponsPromotions: unknown[];
  newCutOffTimes: unknown[];
  promotions: unknown[];
}

// 商家报价信息
export interface IQuoteStoreItem {
  inquiryId: string; // 询价单 id
  wholeOrderQuote: boolean; // 是否已整单报价
  hiddenItemCount: number; // 隐藏的报出
  storeId: string; //店铺ID'
  storeName: string; //店铺名称'
  quoteStatusId: string; //供应商报价状态 UNQUOTE 报价中，QUOTE 报价完成，EXPIRED 已过期'
  resolvedQuotedCount: number; //已报价sku数'
  resolvedTotalCount: number; //总sku数'
  stocks: IStockInfoItem[]; // 仓库列表

  isOpenInvoice: boolean; // true:开票，false：不开票
  priceRange?: IQuotePriceRange; // 含税价格区间
  btPriceRange?: IQuotePriceRange; // 不含税价格区间
  decodesArray: IQuoteDisplayItem[]; // 具体的报价结果
}

// 商家价格区间
export interface IInquiryStorePriceRes {
  storeId: string;
  priceRange: IQuotePriceRange; // 含税价格区间
  btPriceRange: IQuotePriceRange; // 不含税价格区间
}

export interface IQuotePriceRange {
  minPrice: number; // 最低价格
  maxPrice: number; // 最高价格
}

export interface IQuoteDisplayItem {
  partsName: string; // 译码结果名称
  needsName?: string; // 需求名称
  decodeArray: IQuoteDecodeItem[]; // 译码 + 报价结果
}

export interface IQuoteDecodeItem {
  qualityId: string;
  qualityName: string;
  price: string; // 含税价格
  btPrice: string; // 不含税价格
  departure: string; // 仓库
  departureId: string; // 仓库id
}
