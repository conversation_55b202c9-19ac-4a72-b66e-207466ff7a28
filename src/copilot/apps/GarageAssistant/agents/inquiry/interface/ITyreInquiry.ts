import { ITyreDemandResponse } from "@/interfaces/tyreDemand";

export interface ITyreQuoteDetailRes {
  demandId: string; // '询价单ID'),
  standardItemIdList?: string[]; // '标准需求id列表'),
  sortType?: string; // '排序值，COMPREHENSIVENESS-综合排序，DURATION-时效排序，PRICE-价格排序.默认COMPREHENSIVENESS-综合排序'),
  openInvoiceType: boolean; // '是否开票'),
  isSentry: boolean; // 是否哨兵模式
  userLoginId?: string;
  garageCompanyId?: string;
}

export interface ITyreQuoteResponse {
  list?: IQuoteResponseItem[]; // '需求的报价结果数据')
}

export interface IQuoteResponseItem {
  standardItemId?: string; // '标准需求id'
  partsNumber?: string; // '零件号'),
  partsName?: string; // '零件号名称'),
  tyreOrientation?: string; // '轮胎方位 TYRE_FRONT：前轮，TYRE_REAR：后轮，TYRE_COMMON：通用'),
  quotationProductList?: IQuoteProductData[]; // '报价结果集合')
}

export interface IQuoteProductData {
  quoteStatus?: string; // '报价店铺的状态inQuote: 报价中/outOfStock: 缺货.空字符或者没有这个字段表示正常已经'),
  demandId?: string; // '询价单id'),
  quotationProductId?: string; // '报价结果id'),
  quotationId?: string; // '报价单号'),
  quotationDemandItemId?: string; // '报价需求id'),
  statusId?: string; // '报价状态 SALABLE:可售'),
  partsNum?: string; // '商品零件号'),
  partsName?: string; // '报价结果名称'),
  quantity?: number; // '询价需求数量'),
  count?: number; // 购买数量
  remark?: string; // '备注'),
  partsBrandCode?: string; // '零件品牌'),
  standardNameCode?: string; // '标名'),
  categoryCode?: string; // '品类'),
  quotedType?: string; // '报价类型，AUTO系统，MANUALLY人工'),
  sourceId?: string; // '来源id，商品id.效验商品的库存用这个id'),
  sourceType?: string; // '来源类型，开思商品PRODUCT_ID:商品id'),
  locationId?: string; // '仓库id 人工报价的时候的仓库id'),
  locationName?: string; // '仓库名称 人工报价的时候的仓库名称'),
  stockUpType?: string; // '调货方式，FINISHED_GOODS现货，DISPATCH_GOODS调货'),
  stockUpTime?: number; // '调货天数'),
  createdBy?: string; // '报价员'),
  createdDate?: number; // '报价时间'),
  atPrice?: string; // '税后价格保留小数点后两位'),
  btPrice?: string; // '不含税价保留小数点后两位'),
  retailPrice?: string; // 零售价
  price?: string; // '购买的价格 小数点后两位'),
  productIconUrl?: string; // '报价结果商品图片'),
  quotationProductAttributeList?: IQuotationAttribute[]; // '报价结果属性'),
  tyreAttributeList?: IQuotationAttribute[]; // 轮胎属性
  organizationFlag?: string; // 'TIRE_BRAND_MANUFACTURER-厂家直销,TIRE_STRATEGIC_SUPPLIER-代理直销,TIRE_FACILITATOR-轮胎服务商'),
  facility?: IFacilityItem[]; // '仓库数据,第一个数据为展示数据'),
  promotions: IPromotions[]; // 优惠活动信息
  wholionLogisticsEtaV2DTO?: IWholionLogisticsEtaDTO; // '小狮物流ETA'),
  afterSalePolicys?: IAftersalePolicy[]; // '售后政策'),
  newAfterSalePolicy?: unknown[]; // 新售后政策
  storePackageInfo?: string; // '包邮信息 -1不显示; 0显示包邮标识; 大于0显示具体的金额包邮标识'),
  packageInfoShow?: string; // '包邮信息: 包邮、满200包邮、满2件包邮'),
  isPattern?: boolean; // '是否是配套花纹'),
  organization: IOrganization; // '店铺信息'),
  taxRate?: number; // 税率
  modelNameList?: string[]; // 配套花纹对应品牌
  originalPictureFlag?: IOriginalPictureFlag; // 原图轮胎信息
  standardItemId?: string; // '标准需求id'
  partsNumber?: string; // '需求零件号'),
  trialAdjustment?: number; // 试算单价优惠调节金额
  trialTotalAdjustment?: number; // 试算优惠调解金额
  trialTotalPrice?: number; // 试算结果总价
  trialUnitPrice?: number; // 试算结果单价
}

interface IQuotationAttribute {
  attributeType?: string; // `属性类型值: QUOTATION_PRODUCT_ORDER_STATUS-报价结果下单状态; STORE_FLAG-店铺标识; FACILITY-仓库;
  // INVENTORY-库存; AFTER_SALE-售后; size-轮胎尺寸; flatRatio-扁平比;
  // treadWidth-胎面宽; meridian-子午线; pattern-花纹;explosionProof-是否防爆; loadIndex-载重指数; speedLevel-速度级别;
  // originalTyre-原配胎标识; productionDate-生产日期; tyreOrientation-方位：前后轮),
  attributeValue?: string; // '属性值 店铺标识值：FACTORY_OUTLET厂家直销，TYRE_STRATEGIC_SUPPLIER代理直销/战略供应商'),
}

interface IFacilityItem {
  facilityId?: string; // '仓库id'),
  facilityName?: string; // '仓库名称'),
  inventory?: number; // '库存'),
  duration?: number; // '时效的小时时间'),
  distance?: number; // '距离'),
  durationDescription?: string[]; // '时效描述，\n表示换行.'),
  isSelected?: boolean; // 是否选中
}

interface IPromotions {
  promotionId?: string; // '活动ID'),
  promotionType?: string; // '优惠来源: 询价专享INQUIRY_FULLOFF， 商家通用STORE_FULLOFF'),
  promotionName?: string; // '活动名称'),
  discounts: IDiscounts[]; // 优惠梯度信息,
  isSelected?: boolean; // 是否选中，非接口返回，前端字段
}

interface IDiscounts {
  id?: string; // '梯度ID'),
  amountOffType?: string; // '优惠类型ID: 满减FULLCUT_NORMAL 满折FULLCUT_DISCOUNT'),
  requireAmount?: number; // '满足金额'),
  offAmount?: number; // '优惠额度或者折扣值'),
  promotionDesc?: string; // '满减描述')
}

interface IWholionLogisticsEtaDTO {
  productId?: string; // '商品ID'),
  iconUrl?: string; // '小狮配送的图标的url'),
  defaultEta?: string; // '(不支持小狮时为空）默认展示：无ETA时为 小狮快送，有ETA时 最快xx分钟达'),
  etaLabels?: IEtaLabelDetail[]; // '配送方式'),
}

interface IEtaLabelDetail {
  logisticsServiceType?: string; // '物流服务名【专送|班车送|拼单送|...'),
  etaShow?: string; // 'ETA预计送达时长（用于展示）'),
  distributionTime?: number; // '配送时长（单位分钟）'),
}

interface IAftersalePolicy {
  name?: string; // '名称'),
  key?: string; // 'key'),
  value?: string; // '售后value'),
  iconUri?: string; // '图标'),
  isShow?: boolean; // '是否展示'),
  content?: string; // '展示内容'),
}

interface IOriginalPictureFlag {
  originalPictureFlagName: string;
  originalPictureFlagDescription: string;
}

interface IOrganization {
  organizationId?: string; // '店铺id'),
  organizationName?: string; // '店铺名称'),
  statusId?: string; // '报价状态'),
  organizationLevel?: string; // '店铺等级'),
  iconUri?: string; // '等级图片地址'),
  hasCustomerServicesOnline?: boolean; // '是否有客服在线'),
  todayIsWeekday?: boolean; // '今天是否工作日'),
  nextWeekdayDesc?: string; // '下一个工作日描述'),
  openInvoiceType?: string; // '是否开票，YES需要开票， NO不需要发票，BOTH两者都支持'),
  invoiceType?: string; // '普票: normal/增值票: VAT'),
  createdDate?: number; // 报价时间
}

export type ITyreQuoteAndDemand = ITyreQuoteResponse & Partial<ITyreDemandResponse>;

export interface ITyreInquiryInfo {
  isProxyInquiry: boolean;
  userLoginId: string;
  garageCompanyId: string;
}