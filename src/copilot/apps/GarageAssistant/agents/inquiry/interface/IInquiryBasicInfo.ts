interface InquiryAdditionalImage {
  mediaType?: string;
  typeId: string;
  url: string;
}

interface InquiryVinModifyRecord {
  oldInquiryId?: string;
  newInquiryId?: string;
  oldVin?: string;
  newVin?: string;
  oldCarBrandId?: string;
  newCarBrandId?: string;
  remark?: string;
}

interface FeedbackCondition {
  staticFeedBack?: boolean;
  dynamicFeedBack?: boolean;
  longViewMins?: number | null;
  quotedRatio?: number | null;
  returnTimes?: number | null;
  quotedQuantity?: number | null;
  operationIntervalSeconds?: number | null;
  quotedRate?: number | null;
  numberOfQuotation?: number | null;
}

export interface IGetInquiryBasicInfoResponse {
  qualityShowType?: string;
  isSupplementResources?: boolean;
  isAccident?: boolean;
  isInsuranceDirect?: boolean;
  insuranceCompanyCode?: string;
  insuranceCompanyShortName?: string;
  remarks?: string;
  supplementResourcesTips?: string;
  isCanSupplementResources?: boolean;
  isSimpleInquiry?: boolean;
  seriesZh?: string;
  seriesEn?: string;
  locationName?: string;
  carBrandId?: string;
  carBrandName?: string;
  carModelId?: string;
  carModelName?: string;
  epcModelName?: string;
  epcModelCode?: string;
  seriesId?: string;
  locationId?: string;
  brandLogo?: string;
  contactNumber?: string;
  createdBy?: string;
  createdName?: string;
  createdStamp?: number;
  expiredStamp?: number;
  expiredDesc?: string;
  inquiryAddress?: string;
  inquiryId?: string;
  inquiryType?: string;
  statusDesc?: string;
  statusId?: string;
  invoiceType?: string;
  isNeedWholePrice?: boolean;
  isAnonymous?: boolean;
  isRequireItemInvoice?: boolean;
  isOpenInvoice?: boolean;
  remarkForStatusChanged?: string;
  vin?: string;
  vinPicture?: string;
  saleModelName?: string;
  saleModelCode?: string;
  qualitiesType?: string[];
  qualities?: object[];
  provinceGeoId?: string;
  cityGeoId?: string;
  countyGeoId?: string;
  villageGeoId?: string;
  addressId?: string;
  originalAssortDes?: string;
  boschPointTip?: string;
  picDemand?: Array<"NAMEPLATE" | "HEADSTOCK" | "TAILSTOCK">;
  picDemandDesc?: string[];
  picDemandUrls?: string[];
  inquiryAdditionalImages?: InquiryAdditionalImage[];
  markedType?: string;
  inquiryVinModifyRecord?: InquiryVinModifyRecord;
  feedbackCondition?: FeedbackCondition;
  energyType?: string;
  latitude?: number | null;
  longitude?: number | null;
}
