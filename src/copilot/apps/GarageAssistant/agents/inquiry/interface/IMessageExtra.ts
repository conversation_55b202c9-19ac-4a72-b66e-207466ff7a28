import { ICarModel } from "@/interfaces/inquiry";

interface ITip {
  /** 文本内容 */
  content?: string;
  /** 是否显示 */
  visible?: boolean;
}

export interface IMessageExtra {
  partNames?: string[];
  qualityNames?: string;
  // 地址信息tip
  addressTip?: ITip;
  // 车架号/配件tip
  vinPartsTip?: ITip;
  vinCode?: string; // 车架号
  inquiryId?: string; // 询价单号
  demandId?: string; // 轮胎询价单号
  carModel?: ICarModel;
}
