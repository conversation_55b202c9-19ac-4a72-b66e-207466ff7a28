interface IProductListItem {
  brandId: string; // 品牌id
  partType: string; // 品质: 原厂件(ORIGINAL_PARTS), 品牌件(BRAND_PARTS)
  partsNum: string; // 零件号
  quoteId: string; // 报价ID
  source: string; // 来源
  supplierCompanyId: string; // 供应商公司ID
  categoryCode: string; // 译码层级的品类code
  decodePartsNum: string; // 译码层级零件号
  standardNameCode: string; // 译码层级标名code
  productId: string; // 商品id
  storeId: string; // 店铺ID
}
export interface IInquiryExtraInfoV3Payload {
  productList: IProductListItem[];
  inquiryId?: string;
  inquiryType?: string;
  saleModelCode?: string;
}

interface StepPrice {
  buyMinNum: number; // 最小数目
  buyMaxNum?: number; // 最大数目
  price: number; // 买家含税价格
  btPrice?: number; // 买家税前价格
}

interface ImageAndDescInfo {
  resourceType: string; // 资源类型 | 视频图片 PICTURE 和 VIDEO
  photoDescription: string; // 图片描述的资源地址，视频缩略图
  resourceValue: string; // 资源地址
  transcodeStatus?: boolean; // 转码状态
}

interface NewSalePolicyChildItem {
  id?: string | null; // 服务id
  key?: string; // 售后类型
  value?: string; // 售后类型值，比如：12M
}

interface NewSalePolicyItem {
  id?: string | null; // 服务id
  key?: string; // 售后类型
  value?: string; // 售后类型值，比如：12M
  isShow?: boolean; // 是否显示
  name?: string | null; // 显示名称，比如：质保3个月
  content?: string | null; // 时间，比如：12个月
  iconUri?: string | null; // 图标
  groupId?: number | null; // 分组ID
  childList?: NewSalePolicyChildItem[]; // 聚合的子项(标识当前的key值受多个政策影响，记录影响因子)
  serviceCategoryCode?: string | null; // 标签类型,文本类:TEXT;赔偿类:COMPENSATION;退货类:RETURN;质保类:QA;假赔类:FC
  scenarioId?: string | null; // 应用场景ID
  desc?: string; // 标签描述
  categoryName?: string; // 标签类别名称
}

interface InquiryExtraInfo {
  quoteId: string; // 报价id
  imageAndDescInfos: ImageAndDescInfo[]; // 资源信息（排序规则系统报价-> 商城商品图;人工报价:上传视频>上传图）
  spdMainPicture?: string; // spd主图
  descriptions: string; // 描述信息: 系统报价->商品描述 | 人工报价->商品备注
  afterSalePolicys?: NewSalePolicyItem[]; // 新版售后服务
  hasDetail: boolean; // 是否有商品详情页
  stepPrices?: StepPrice[]; // 阶梯价格
  showPointLogo: boolean; // 是否展示有易加网积分的logo
  brandCouponDescList?: string[]; // 优惠券列表
  brandCouponName?: string; // 优惠券标签名称
  isRegularBuyer?: boolean; // 是否买过
  lastPurchaseDay?: string; // 最近购买时间
  brandTotalSales?: string; // 品牌总销量
  aftersaleRatio?: string; // 售后占比
  isShowIntroduction?: boolean; // 是否展示介绍
  aeRemark?: string; // 品牌件AE备注
  newAeRemark?: string; // 新版品牌件AE备注
  aeRemarkImageUri?: string[]; // 品牌件AE备注图片
}

export interface IGetInquiryExtraInfoV3Response {
  inquiryExtraInfo?: InquiryExtraInfo[]; // 询价单详情额外信息
}
