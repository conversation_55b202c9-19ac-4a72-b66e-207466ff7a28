import { ICarModel } from "@/interfaces/inquiry";
import { ITyreDemandResponse } from "@/interfaces/tyreDemand";

interface IOeResultsItem {
  partsNum: string;
  partsName: string;
  standardNameCode: string;
}

export interface INeedsItem {
  needsName: string;
  oeResults: IOeResultsItem[];
}
export interface INeedOeResult {
  needs: INeedsItem[];
  vin: string;
  userId: string;
}

export interface IInquiryInfo {
  inquiryId?: string;
  needs?: INeedsItem[];
  needsNames?: string[];
  saleModelName?: string;
  carModelName?: string;
  brandLogo?: string;
  carBrandId?: string;
  createdStamp?: number;
  createdName?: string;
  createdBy?: string;
  statusId?: string;
  qualityNames?: string;
  vin?: string;
  addressId?: string;
  openInvoiceType?: string;
  userId?: string;
  companyId?: string;
}

export interface IInquiryCardInfo extends IInquiryInfo {
  vinImage?: string;
  partNamesImages?: string[];
  carModel?: ICarModel;
  tyreInquiryDemand?: ITyreDemandResponse;
}

export interface IInquiryPayload {
  companyId: string;
  userId: string;
  isOpenInvoice: boolean;
  source: string;
  fromPage: string;
}

export interface IMerchantClue {
  type: string;
  id: string;
  data: IMerchantClueData;
}
interface IMerchantClueData {
  carModelName: string;
  carBrandId: string;
  inquiryId: string;
  vin: string;
  status: string; // 询价单状态（中文）
  userName: string; // 发布人
  createdStamp: number;
  needsName: string; // 需求拼接
  id: string; // 询价单 id
}

export interface IStoreInfo {
  storeName: string;
  storeId: string;
  iconUri?: string;
  levelValue?: number;
  type?: string; // 方案类型
}
