interface IGetPromotionsParamItem {
  quotationProductId: string; // 报价结果ID
  productId: string; // 商品id
  storeId: string; // 店铺id
}

export interface IGetPromotionsParams {
  inquiryId: string; // 询价单ID
  cityGeoId: string; // 收货城市id
  items: IGetPromotionsParamItem[];
}

export interface IGetInquiryPromotionsInfoItem {
  quotationProductId: string; // 报价结果ID
  promotions: IInquiryPromotionItem[];
}

interface IInquiryCouponItem {
  id: string; // 满减规则ID(商家活动code/询价单专享优惠ID)
  couponName: string; // 满减文案
  couponType: string; // 优惠类型
  couponTypeName: string; // 优惠类型中文名称
  limitAmount: number; // 满足限制金额
  discountAmount: number; // 折扣值或优惠金额
}

interface IInquiryPromotionItem {
  coupons: IInquiryCouponItem[];
  productCoverage: string; // 活动商品范围code： 全部商品(ALL_PRODUCTS) 指定商品(STORE_PRODUCT)
  productCoverageName: string; // 活动商品范围名称：全部商品(ALL_PRODUCTS) 指定商品(STORE_PRODUCT)
  promotionCode: string; // 活动唯一code
  promotionName: string; // 活动名称
  promotionType: string; // 促销活动类型
  showName: string; // 显示文案
  source: string; // 来源 : 商家(SUPPLIER), 询价(INQUIRY)
}
