export interface ITagInfoResult {
  activeState: string;
  labelStatus: string;
  bindStatus: string;
  businessSource: string;
  waybillId: string;
  isValidity: boolean;
  // 店铺信息
  storeId?: string;
  storeName?: string;
  // 厂商信息
  brandOwner?: {
    id: string;
    code: string;
    name: string;
  };
  isPermitted: boolean;
  hasCustomerServicesOnline: boolean;
  isOffline: boolean;
  target?: {
    labelCode: string;
    goodInfo: {
      orderId: string;
      itemId: string;
      itemIdType: string;
      orderDate: number;
      productName: string;
      brandName: string;
      buyerActualPrice: number;
      quantity: number;
    };
    shipment: {
      shipmentCommpany: string;
      shipmentNumber: string;
      customerName: string;
      receiverName: string;
      receiverPhone: string;
    };
  };
}
