export interface ICodeResult {
  stdnameobj: {
    // 标名
    stdName: string;
    stdNameCode: string;
  };
  code: string;
  codeTrim: string;
  //   配件名称
  name: string;
  //   品牌
  brandCode: string;
  //   1是oe，1是ae
  type: number;
}

export interface IExactStdname {
  status: number;
  input: {
    seqNum: string;
    word: string;
  };
  bestPick: string[];
  bestStdName: {
    stdName: string;
    code: string;
  };
  categoryObj: {
    stdName: string;
    code: string;
  };
  stdnameList: object[];
}

export interface IStandardCodeItem {
  stdName: string;
  code: string;
}

export interface IBombPicPayload {
  vin: string;
  userId?: string;
  oeCodeParamList: IOeCodeParamList[];
}

interface IOeCodeParamList {
  oeCode: string;
  stdNameCode: string;
}

export interface IBombPicData {
  partNumber: string;
  model: string;
  count: string;
  stdNameCode: string;
  images: IImage[];
}

export interface IImage {
  imageId: number;
  src: string;
  position: string;
  lableLeft: string;
  lableRight: string;
  lableTop: string;
  lableBottom: string;
}
