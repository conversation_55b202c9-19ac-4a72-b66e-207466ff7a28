export interface IGetTmsLablesPayload {
  businessType: string;
  addressId?: string;
  terminal: string;
  productList: IProductInfo[];
  inquiryId?: string;
}

interface IProductInfo {
  productId?: string;
  storeId?: string;
  quantity?: number;
  facilityId?: string;
  price?: string;
  invoiceType?: string;
}

interface Eta {
  etaShow?: string; // （不支持小狮时为空）默认展示：无ETA时为 小狮快送，有ETA时 最快xx分钟达
  logisticsServiceType?: string; // 物流服务名【专送|班车送|拼单送|...】
  distributionTime: number; // 配送时长（单位分钟）
}

interface WholionLogisticsEtaInfo {
  iconUrl?: string; // 小狮配送的图标的url
  defaultEta?: string; // （不支持小狮时为空）默认展示：无ETA时为 小狮快送，有ETA时 最快xx分钟达
  etaLabels: Eta[]; // （不支持小狮时为空）所有物流服务及对应ETA
}

interface ProductInfo {
  productId?: string; // 商品id
  packageInfoShow?: string; // 包邮信息: 包邮、满200包邮、满2件包邮
  activitySource?: string; // 活动来源（匹配上活动时不为空）店铺包邮活动STORE，平台包邮活动PLATFORM，强制包邮supplier_force
  wholionLogisticsEtaV2DTO?: WholionLogisticsEtaInfo; // 小狮物流ETA
}

export interface IGetTmsLablesResponse {
  productsInfo: ProductInfo[]; // 条目信息
}
