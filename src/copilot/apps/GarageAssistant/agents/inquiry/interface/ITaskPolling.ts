import { IGetInquiryQuoteRes } from "./IInquiryQuote";
import { ITyreQuoteAndDemand } from "./ITyreInquiry";

export interface IRecommendPlanTaskParams {
  inquiryId: string;
  inquiryProgrammeGroupId: string;
  finished: boolean;
  turns: number;
  source: string;
  fromPage: string;
  isOpenInvoice: boolean;
  inquiryProgrammeIds: string[];
  hasSentInquiryProgrammeIds: string[];
  updateInquiryCard?: boolean;
  messageId: string;
  hasSentTurn: number;
  expiration: number;
}

export interface IInquiryTaskParams {
  messageId: string;
  inquiryId: string;
  inquiryCount: number;
  inquiryNeedPolling: boolean;
  demandId: string;
  tyreNeedPolling: boolean;
  source: string;
  fromPage: string;
  isOpenInvoice: boolean;
  inquiryQuote?: IGetInquiryQuoteRes;
  tyreQuote?: ITyreQuoteAndDemand;
  updateInquiryCard?: boolean; // 询价卡片是否更新
  expiration: number;
  // 代客询价相关信息
  isProxyInquiry?: boolean;
  userLoginId?: string;
  garageCompanyId?: string;
}

export enum TaskType {
  INQUIRY = "INQUIRY",
  RECOMMEND_PLAN_TASK = "RECOMMEND_PLAN_TASK", //推荐方案任务
  INTELLIGENT_PLAN = "INTELLIGENT_PLAN", // 智能方案
  PURCHASE_PLAN = "PURCHASE_PLAN", // 采购方案
  QUOTE_PURCHASE_PLAN = "QUOTE_PURCHASE_PLAN", // 报价自动制作采购方案
}
