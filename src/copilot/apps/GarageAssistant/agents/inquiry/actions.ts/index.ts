import { ActionFactory } from "@/common/factory/ActionFactory";
import { AgentName, NluActionIntents } from "@/copilot/constants";

export function createNotShortVinCodeAction(vincode: string) {
  return ActionFactory.nlu(`【${vincode}】是零件号`, {
    agentName: AgentName.inquiryAgent,
    intent: NluActionIntents.NOT_SHORT_VINCODE,
    entities: [
      {
        name: "partName",
        value: vincode,
      },
    ],
  });
}
