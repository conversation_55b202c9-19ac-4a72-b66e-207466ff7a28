import { Context, Entity, SlotConfig } from "@casstime/copilot-core";
// import { AgentName } from "@/copilot/constants";
import _ from "lodash";

export const partNamesSlot: SlotConfig = {
  filling(entities: Entity[], context: Context) {
    const partNames = entities.filter((entity) => entity.name === "partName").map((entity) => entity.value);

    const beforePartNames = (_.get(context.slots, "partNames", []) as string[]) || [];
    // console.log("-->", partNames, context.getAgentMemory(AgentName.inquiryAgent));
    return [...new Set([...beforePartNames, ...partNames])].filter(Boolean);
  },
};
