import { Context, Entity, SlotConfig } from "@casstime/copilot-core";
import { Intents } from "@/copilot/constants";
import _ from "lodash";

export const qualitySlot: SlotConfig = {
  filling(entities: Entity[], context: Context) {
    const qualities = entities
      .filter((entity) => entity.name === "qualities")
      .map((entity) => entity.value);

    const beforeQuality =
      (_.get(context.slots, "qualities", []) as string[]) || [];

    return [...new Set([...beforeQuality, ...qualities])].filter(Boolean);
  },
};
