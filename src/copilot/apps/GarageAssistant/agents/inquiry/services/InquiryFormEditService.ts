import { Entity } from "@casstime/copilot-core";
import { IQuality } from "@/copilot/constants";
import logger from "@/common/logger";
import { extractChars } from "@/common/utils";
import { executePrompt, extractJsonFromStream } from "@/copilot/helpers";

export type EditData = { partNames: string[]; qualities: string[] };
export type EditAction = {
  type: "partName" | "quality";
  action: "delete" | "add";
  entities: string[];
};

/**
 * 编辑询价表单
 */
export class InquiryFormEditService {
  /**
   * 根据用户输入解析出要进行的编辑操作
   * @param FormData 当前的表单数据
   * @param input 用户输入
   * @param entities 从用户输入中解析出的实体
   */
  async parseEditActions(formData: EditData, input: string, entities: Entity[]): Promise<EditAction[]> {
    const content = input?.replace(/\//g, " ");
    const entityValues = entities.map((e) => e.value).join("");

    // 用户输入内容和实体内容完全一致，不需要进行其他操作
    if (this.isSame(content, entityValues)) {
      return [];
    }

    const stream = await executePrompt("采购助手/采购助手表单编辑提示词", {
      formData: {
        partNames: formData.partNames || [],
        qualities: formData.qualities || [],
      },

      entities: {
        partNames: entities.filter((e) => e.name === "partName").map((e) => e.value),
      },

      message: content,
    });

    const actions = await extractJsonFromStream<EditAction[]>(stream).catch((err) => {
      logger.warn("执行表单编辑提示词失败", err);
      return [];
    });

    return this.filterValidActions(actions, formData, entities, input);
  }

  /**
   * 判断两个配件名是否相同
   * @param text1
   * @param text2
   * @returns
   */
  private isSame(text1: string, text2: string) {
    return extractChars(text1, "CHAR_CN_EN_NUM") === extractChars(text2, "CHAR_CN_EN_NUM");
  }

  /**
   * 过滤出合法的的编辑操作
   * @param actions
   * @param formData
   * @param entities
   * @returns
   */
  private filterValidActions(
    actions: EditAction[],
    formData: EditData,
    entities: Entity[],
    input: string
  ): EditAction[] {
    const partNames = [
      ...formData.partNames,
      ...entities.filter((e) => e.name === "partName").map((e) => e.value as string),
    ];
    const qualities = [
      ...formData.qualities,
      ...entities.filter((e) => e.name === "quality").map((e) => e.value as string),
    ];

    const deleteActions: EditAction[] = [];

    const filteredActions = actions
      .map((action) => {
        if (action.type === "partName") {
          action.entities = action.entities.map((entity) => {
            // 大模型生成的action.entities可能有些特殊字符，尽量使用nlp解析出的中的实体
            const same = partNames.find((name) => this.isSame(name, entity));
            return same || entity;
          });
          if (action.action === "add" && this.isSame(input, action.entities.join(""))) {
            deleteActions.push({
              type: "partName",
              action: "delete",
              entities: entities.filter((e) => e.name === "partName").map((e) => e.value),
            });
          }
        } else if (action.type === "quality") {
          action.entities = action.entities.filter((e) => qualities.includes(e));
        }
        return action;
      })
      .filter((action) => action.entities.length > 0);

    return filteredActions.concat(deleteActions);
  }

  /**
   * 根据编辑操作返回更新后的表单数据
   * @param formData
   * @param actions
   */
  applyEditActions(formData: EditData, actions: EditAction[]) {
    const result = { ...formData };
    let finalPartNames = formData.partNames || [];
    for (const { action, type, entities } of actions) {
      console.log("applyEditActions", action, type, entities, finalPartNames);
      if (type === "partName") {
        if (action === "delete") {
          finalPartNames = finalPartNames.filter((e) => !entities.includes(e));
        }

        if (action === "add") {
          finalPartNames = [...new Set([...finalPartNames, ...entities])];
        }
      }

      if (type === "quality") {
        if (action === "delete") {
          result.qualities = result.qualities.filter((e) => !entities.includes(e));
        }

        if (action === "add") {
          // 只能添加支持的品质
          const realEntities = entities.filter((entity) => Object.values(IQuality).includes(entity));
          result.qualities = [...new Set([...result.qualities, ...realEntities])];
        }
      }
    }
    result.partNames = finalPartNames;
    console.log("-->", JSON.stringify(result.partNames));
    return result;
  }
}
