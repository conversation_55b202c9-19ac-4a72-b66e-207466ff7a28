import {
  ConfigAction,
  ConfigStrategy,
  ConfigureReply,
  IConfigStrategy,
  ReplyTime,
  StrategyMsgTypeEnum,
  StrategyScemeEnum,
  StrategyTypeEnum,
} from "@/models";
import { Context, IAction, IMessage } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { Types } from "mongoose";
import { featureTogglesClient } from "../clients/FeatureTogglesClient";
import { IStrategyContentResult } from "../interface/IConfigure";
import { categoryService } from "../../fallback/services/CategoryService";

class ConfigureService {
  // 获取配置消息
  public async getConfigureReplys(sceme: StrategyScemeEnum, companyId: string): Promise<IMessage[]> {
    try {
      const configStrategys: IConfigStrategy[] = await this.findConfigStrategys(sceme, companyId);
      if (configStrategys.length === 0) {
        return [];
      }
      const result = await this.getConfigureReplysForStrategys(configStrategys, sceme);
      return result;
    } catch (error) {
      logger.warn(`查询配置的消息失败：${error}`);
      return [];
    }
  }

  public async getConfigureReplysForStrategys(strategys: IConfigStrategy[], sceme: StrategyScemeEnum) {
    try {
      const newStrategys = strategys.map(async (strategy: IConfigStrategy) => {
        const content = JSON.parse(strategy.content || "") || {};
        const strategyActions = strategy.actions || [];
        let actions: IAction[][] = [];
        if (strategyActions.length) {
          actions = await this.findConfigStrategyActions(strategyActions);
        }
        const strategyObj = { ...content, actions };
        if (sceme === StrategyScemeEnum.welcome) {
          strategyObj.extra = '{"isGreet":true,"vinPartsTip":{"content":"请输入车架号，或直接上传/拍摄车铭牌图"}}';
        }
        return strategyObj;
      });
      const result = await Promise.all(newStrategys);
      return this.parseMessageForStrategyContent(result);
    } catch (error) {
      logger.warn(`解析配置策略失败：${error}`);
      return [];
    }
  }

  // 解析策略内容成消息格式
  private async parseMessageForStrategyContent(contentList: IStrategyContentResult[]): Promise<IMessage[]> {
    const contentAsyncList = contentList.map(async (c) => {
      const { type, actions, extra = "", msgType, embed } = c;
      let basicMessage = {
        type,
        actions,
        extra: {},
      };
      if (extra) {
        try {
          basicMessage = { ...basicMessage, extra: JSON.parse(extra) || {} };
        } catch (error) {
          logger.warn(`解析配置策略extra失败：${error}`);
        }
      }
      if (type === StrategyTypeEnum.text) {
        if (msgType === StrategyMsgTypeEnum.fixed) {
          const { content = [] } = c;
          const randomIndex = Math.floor(Math.random() * content.length);
          const randomText = content[randomIndex];
          return {
            ...basicMessage,
            content: randomText,
            embed,
          };
        } else if (msgType === StrategyMsgTypeEnum.fastgpt) {
          return {
            ...basicMessage,
            content: "fastgpt 待生成",
            embed,
          };
        }
      } else if (type === StrategyTypeEnum.image) {
        const { imageUrl } = c;
        return {
          ...basicMessage,
          imageUrl,
        };
      } else if (type === StrategyTypeEnum.video) {
        const { videoUrl, thumb, duration } = c;
        return {
          ...basicMessage,
          videoUrl,
          thumb,
          duration,
        };
      } else {
        return basicMessage;
      }
    });
    const result = await Promise.all(contentAsyncList);
    return result as IMessage[];
  }

  // 匹配策略
  public async findConfigStrategys(sceme: StrategyScemeEnum, companyId: string): Promise<IConfigStrategy[]> {
    try {
      const strategiesResult = [];
      const strategys = await ConfigStrategy.find({ sceme, enabled: true }).sort({ priority: -1 }).lean().exec();
      const filterStrategys = strategys.filter((s) => !!s.content);
      if (filterStrategys.length > 0) {
        const strategysForFeatureToggleList = [];
        for (let index = 0; index < filterStrategys.length; index++) {
          const strategy = filterStrategys[index];
          if (!strategy.featureToggle) {
            strategiesResult.push(strategy);
            break;
          } else {
            strategysForFeatureToggleList.push(featureTogglesClient.getIsPilotArea(companyId, strategy.featureToggle));
          }
        }
        // 没有特性开关的策略
        if (strategysForFeatureToggleList.length > 0) {
          // 请求特性开关，判断用户是否在特性开关里面
          const featureToggleRes = await Promise.all(strategysForFeatureToggleList);
          for (let index = 0; index < featureToggleRes.length; index++) {
            const { enabled, featureId } = featureToggleRes[index];
            if (enabled) {
              const currentStrategy = filterStrategys.find((s) => s.featureToggle === featureId);
              return currentStrategy ? [currentStrategy] : [];
            }
          }
        }
      }
      return strategiesResult;
    } catch (error) {
      logger.warn(`查询配置的策略失败：${error}`);
      return [];
    }
  }

  public async findConfigStrategysForCategory(sceme: StrategyScemeEnum, companyId: string): Promise<IConfigStrategy[]> {
    try {
      const strategiesResult = [];
      const strategys = await ConfigStrategy.find({ sceme, enabled: true }).sort({ priority: -1 }).lean().exec();
      const filterStrategys = strategys.filter((s) => !!s.content);
      if (filterStrategys.length > 0) {
        const strategysForFeatureToggleList = [];
        for (let index = 0; index < filterStrategys.length; index++) {
          const strategy = filterStrategys[index];
          if (!strategy.featureToggle) {
            strategiesResult.push(strategy);
          } else {
            strategysForFeatureToggleList.push(featureTogglesClient.getIsPilotArea(companyId, strategy.featureToggle));
          }
        }
        // 有特性开关的策略
        if (strategysForFeatureToggleList.length > 0) {
          // 请求特性开关，判断用户是否在特性开关里面
          const featureToggleRes = await Promise.all(strategysForFeatureToggleList);
          for (let index = 0; index < featureToggleRes.length; index++) {
            const { enabled, featureId } = featureToggleRes[index];
            if (enabled) {
              const currentStrategy = filterStrategys.find((s) => s.featureToggle === featureId);
              if (currentStrategy) {
                strategiesResult.push(currentStrategy);
              }
            }
          }
        }
      }
      return strategiesResult;
    } catch (error) {
      logger.warn(`查询配置的策略失败：${error}`);
      return [];
    }
  }

  // 查找策略配置的 actions
  private async findConfigStrategyActions(actionArray: string[][]): Promise<IAction[][]> {
    const collection = { _id: { $in: actionArray.flat() } };
    const configActions = await ConfigAction.find(collection).lean().exec();
    const firstFloorActions: IAction[][] = [];
    actionArray.forEach((action) => {
      const secondFloorActions: IAction[] = [];
      action.forEach((a) => {
        const actionStr = configActions.find((c) => c._id.toString() === a)?.action || "";
        if (actionStr) {
          const result = JSON.parse(actionStr) as IAction;
          secondFloorActions.push(result);
        }
      });
      firstFloorActions.push(secondFloorActions);
    });
    return firstFloorActions;
  }

  // 更新配置消息次数
  public async updateReplyTime(userLoginId: string, configureReplyId: Types.ObjectId) {
    try {
      // 是否有配置记录
      const item = await ReplyTime.findOne({ userLoginId, configureReplyId });
      if (item) {
        // 找到了文档，更新 time 字段的值
        item.time += 1;
        await item.save(); // 保存更新后的文档
      } else {
        // 如果没有匹配的文档，插入新文档
        const { _id: replyTime } = await ReplyTime.create({
          userLoginId,
          configureReplyId,
          time: 1,
        });
        await ConfigureReply.updateOne({ _id: configureReplyId }, { $push: { replyTimes: replyTime } });
      }
    } catch (error) {
      logger.warn(`更新配置回复消息次数失败：${error}`);
    }
  }

  // 配置消息的更新时间
  public async getReplyTimeUpdateTime(userLoginId: string, configureReplyId: Types.ObjectId): Promise<number> {
    try {
      const msg = await ReplyTime.findOne({ userLoginId, configureReplyId }).lean().exec();
      return msg?.updatedAt?.getTime() || 0;
    } catch {
      return 0;
    }
  }
  // 获取可用于回复的配置消息
  public async getReplysFromConfig(context: Context) {
    const { payload } = context;

    const configStrategys = await configureService.findConfigStrategysForCategory(
      StrategyScemeEnum.fallback_reply,
      payload?.companyId || ""
    );
    const categoryNames = configStrategys.filter((s) => !!s.categoryName).map((c) => c.categoryName!);
    const currentIntent = await categoryService.parseConfigureIntents(context, Array.from(new Set(categoryNames)));
    if (!currentIntent) {
      return [];
    }
    const currentConfigStrategies = configStrategys.filter((strategy) => strategy.categoryName === currentIntent) || [];
    if (!currentConfigStrategies.length) {
      return [];
    }
    const configureReplys = await configureService.getConfigureReplysForStrategys(
      currentConfigStrategies,
      StrategyScemeEnum.fallback_reply
    );
    return configureReplys;
  }
}
export const configureService = new ConfigureService();
