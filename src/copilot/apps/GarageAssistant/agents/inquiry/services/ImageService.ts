import { IHandleWritingDto, ILabel, IMatchNeedsData } from "@/interfaces/client";
import { ICommandMessage, IImageMessage, Service } from "@casstime/copilot-core";
import _ from "lodash";
import { imageClient } from "../clients/ImageClient";
import { TyreSpec } from "@/common/utils";
import { renderRichtext, ThinkingSteps } from "@/copilot/richtext";
import { getOrCreateReplyMsgId } from "@/copilot/helpers";
import { extractPartNameEntities, extractQualityEntities, extractVinCodeEntities } from "@/copilot/helpers/entities";
import { tyreService } from "./TyreService";
import { rotateOssImage } from "@/common/utils";
import { MessageReporter } from "@/messages";

export class ImageService extends Service {
  public getImageUrl(): string {
    const message = this.context.lastMessage as IImageMessage;
    const imageUrl = message.imageUrl;
    if (imageUrl) {
      return imageUrl;
    }
    const { params } = this.context.lastMessage as ICommandMessage;
    return params?.imageUrl || this.context.slots["imageUrl"];
  }

  createIndicator(label: string) {
    let steps = (this.context.getTempData("steps") as { label: string; done: boolean }[]) || [];
    steps = steps.map((item) => ({ ...item, done: true }));
    steps.push({ label, done: false });
    this.context.setTempData("steps", steps);
    return {
      type: "richtext",
      content: renderRichtext(ThinkingSteps, { steps: steps.slice(-2) }),
    };
  }

  async recognizeVinByProOcr(): Promise<string[]> {
    const ocrResults = await imageClient.recognizeImageByProOcr(this.getImageUrl(), ["text"]);
    const vinLike = ocrResults
      .map((item) => extractVinCodeEntities(item.text))
      .flat()
      .filter(Boolean);
    return vinLike.map((item) => item.value);
  }

  /**
   * 获取回复消息的id
   * @returns
   */
  getReplyMsgId() {
    return getOrCreateReplyMsgId(this.context);
  }

  getReporter() {
    const reporterKey = "__SHARED_IMAGE_MESSAGE_REPORTER__";
    let reporter = this.context.getTempData<MessageReporter>(reporterKey);
    if (reporter) {
      return reporter;
    }
    const msgId = this.getReplyMsgId();
    reporter = new MessageReporter(msgId);
    this.context.setTempData(reporterKey, reporter);
    return reporter;
  }

  getOrSetImageLabel(label?: ILabel): ILabel | void {
    if (label) {
      return this.context.setTempData("__SHARED_IMAGE_LABEL__", label);
    } else {
      return this.context.getTempData("__SHARED_IMAGE_LABEL__");
    }
  }

  /**
   * 设置候选类别
   * @param candidates
   */
  setClassCandidates(candidates: { label: string; prob: number }[]) {
    this.context.setTempData("candidates", candidates);
  }

  /**
   * 获取候选类别
   * @returns
   */
  getClassCandidates(): { label: string; prob: number }[] {
    return (this.context.getTempData("candidates") as { label: string; prob: number }[]) || [];
  }

  public getVIN(ocrRecognizeRes: IHandleWritingDto[]) {
    const vinCodes: string[] = [];
    ocrRecognizeRes.forEach((isNotNeedResItem) => {
      const { words } = isNotNeedResItem;
      if (!words) {
        return false;
      }
      const vinCodeEntities = extractVinCodeEntities(words);
      if (vinCodeEntities.length) {
        vinCodes.push(...vinCodeEntities.map((entity) => entity.value));
      }
    });
    return vinCodes;
  }

  public getQuality(ocrRecognizeRes: IHandleWritingDto[]) {
    const qualities: string[] = [];
    ocrRecognizeRes
      .filter((item) => !item.isNeed)
      .forEach((isNotNeedResItem) => {
        const { words } = isNotNeedResItem;
        if (words) {
          const qualityList = extractQualityEntities(words);
          if (qualityList.length > 0) {
            qualities.push(...qualityList.map((entity) => entity.value));
          }
        }
      });
    return qualities;
  }

  /**
   * 识别截图类别
   */
  public parseScreenShootCategory(ocrRecognizeRes: IHandleWritingDto[]) {
    const rulesConfig = {
      申请售后页面: [/^申请售后$/, /原厂零件号/, /金币抵用/],
      支付成功: [/支付成功/, /支付方式/, /支付时间/],
      VIN分类选择: [/分类选择/, /车辆详情/],
      查油液页面: [/查油液/, /查保养/, /查EPC/, /推荐用量/],
      VINF6智数车辆详情: [/F6智数/, /车辆详情|养护详情/],
      智能目录: [/智能目录/, /车型品牌/, /车系/, /年款/],
      // 车辆详情: https://cass-upload.oss-cn-shenzhen.aliyuncs.com/misc/return/2025-05-10/584D15AA-D9C2-4599-87B3-E0982DD25E19.jpg
      车辆详情: [/车型详情/, /维修手册/, /机油用量/, /返回/],
      VIN汽修宝: [/汽修宝/],
      VIN跑街令车型配置详情: [/跑街令车型配置详情/],
      订单跟踪详情: [/订单跟踪详情/, /运单号/, /物流公司/],
      物流跟踪详情: [/物流跟踪详情/, /运单号/, /物流公司/],
      订单已完成: [/已完成/, /收货地址/, /申请售后/, /评价/],
      订单卖家已发货: [/卖家已发货/, /申请售后/, /询价单/],
      退货发起页面: [/退货/, /退货类型/, /退货原因/, /退货说明/, /提交/],
      退货服务详情: [/服务详情/, /提交申请/, /处理进度/],
      港澳台车辆登记文件: [/車輛登記文件/, /額定功率/],
      微信支付截图: [/支付/, /账单详情/, /我的账单/, /￥/],
      售后记录列表页面: [/售后记录/, /全部/, /申请单/, /未完成/, /已完成/, /零件号/],
      轮胎筛选页面: [/类型/, /品牌/, /花纹特性/, /是否防爆/, /清空选择/, /全部/, /确定/],
    } as const;
    for (const [category, rules] of Object.entries(rulesConfig)) {
      const matched = rules.every((rule) => {
        return ocrRecognizeRes.filter((item) => item.words).some((item) => rule.test(item.words?.trim() || ""));
      });
      if (matched) {
        return category as keyof typeof rulesConfig;
      }
    }
    return null;
  }

  public getReplyByPage(page: string | null) {
    const pageReplayTextMap = {
      申请售后页面:
        "检测到图片是【申请售后】页面截图，你可以进入此页面进行退货、申诉和质保申请。如遇到其他问题，可以咨询【人工客服】，我们客服团队会为你提供专业解答。",
      支付成功:
        "识别到图片是支付结算页面截图。如需确认支付详情、解决支付过程中遇到的问题、了解支付成功后的后续步骤，或咨询退款、取消订单、配送、售后服务等事宜，请联系【人工客服】。",
      订单跟踪详情:
        "检测到图片是【订单跟踪详情】页面截图，您可以在此页面查询物流状态。如果发现物流长时间未更新、或者其他问题，可以咨询【人工客服】，我们客服团队会为你提供专业解答。",
      物流跟踪详情:
        "检测到图片是【物流跟踪详情】页面截图，您可以在此页面查询物流状态。如果发现物流长时间未更新、或者其他问题，可以咨询【人工客服】，我们客服团队会为你提供专业解答。",
      订单已完成:
        "检测到图片是【订单已完成】页面截图，您可以在此页面查看物流信息、申请售后。如有其他问题可以咨询【人工客服】，我们客服团队会为你提供专业解答。",
      订单卖家已发货:
        "检测到图片是【卖家已发货】页面截图，您可以在此页面查看物流信息、申请售后。如有其他问题可以咨询【人工客服】，我们客服团队会为你提供专业解答。",
      退货发起页面:
        "检测到图片是【退货】页面截图，您可以在此页面发起退货，注意需要填写表单必填项(所有带*号的选项)。如遇到其他问题，可以咨询【人工客服】，我们客服团队会为你提供专业解答。",
      退货服务详情:
        "检测到图片是【服务详情】页面截图，您可以在此页面查看退货进度、退款金额等。如遇到其他问题，可以咨询【人工客服】，我们客服团队会为你提供专业解答。",
      微信支付截图:
        "检测到图片是【支付】页面截图，您可以查看支付详情。如有其他问题可以咨询【人工客服】，我们客服团队会为你提供专业解答。",
      售后记录列表页面:
        "检测到图片是【售后记录】页面截图，您可以在此页面查看售后进度。如有其他问题可以咨询【人工客服】，我们客服团队会为你提供专业解答。",
      轮胎筛选页面:
        "检测到图片是【轮胎筛选】页面截图，您可以在此页面进行轮胎规格筛选。如果想要轮胎询价，您可以直接发送轮胎规格如325/40R22，马上为您询价",
    };
    if (!page) {
      return null;
    }
    return pageReplayTextMap[page as keyof typeof pageReplayTextMap] || null;
  }

  /**
   * 判断是否是有效的配件名称
   * @param text
   * @returns
   */
  private isValidPartName(text: string) {
    return !/^[\d,.。，]+$/.test(text);
  }

  // 工单识别配件、轮胎规格
  public async handleOcrRecognizeRes(ocrRecognizeRes: IHandleWritingDto[]) {
    // 对手写工单识别结果提取 轮胎规格
    const ocrRecognizeWordsMatched: string[] = [];
    let hasTyreSizeWords = false;
    // 匹配轮胎规格
    const ocrRecognizeWords = ocrRecognizeRes.map((item) => item.words).filter(Boolean) as string[];
    ocrRecognizeWords
      .filter((text) => this.isValidPartName(text)) // 过滤纯数字
      .forEach((text) => {
        const specs = TyreSpec.parse(text);
        if (specs?.length) {
          const tyreSizes = specs.map((spec) => `（轮胎）${TyreSpec.stringify(spec)}`);
          ocrRecognizeWordsMatched.push(...tyreSizes);
          hasTyreSizeWords = true;
        }
      });

    // 配件纠错
    const partNames = await this.checkListCorrection(ocrRecognizeRes);

    // 配件提取 轮胎规格
    const { partNames: partNamesMatched, hasTyreSize } = tyreService.matchTyreSize(partNames);

    // 聚合数据
    let partNamesNew = [...new Set([...ocrRecognizeWordsMatched, ...partNamesMatched])].filter(Boolean);
    if (hasTyreSize || hasTyreSizeWords) {
      partNamesNew = partNamesNew.filter((item) => item !== "轮胎");
    }
    // 去掉一个字的配件
    partNamesNew = partNamesNew.filter((item) => item.length > 1);

    return partNamesNew || [];
  }

  /**
   * @deprecated 请直接使用 utils 内的 rotateOssImage
   */
  public rotateImage = rotateOssImage;

  private async checkListCorrection(ocrRecognizeRes: IHandleWritingDto[]) {
    const initPartNames = ocrRecognizeRes
      .filter((item) => item.isNeed)
      .map((item, index) => {
        return {
          name: item.words || "",
          serialNum: index + 1,
        };
      });
    const initPartNamesChunk = _.chunk(initPartNames, 20);
    const partNamesList: any[] = [];
    initPartNamesChunk.forEach((partNames) => {
      partNamesList.push(imageClient.getMatchNeeds({ needs: partNames }));
    });
    const resultList = await Promise.all(partNamesList);
    const results: IMatchNeedsData[] = resultList.reduce((pre, cur) => {
      pre.push(...cur);
      return pre;
    }, []);
    const allPartNames = (results || [])
      .sort((a, b) => (a.serialNum || 0) - (b.serialNum || 0))
      .map((item) => {
        const threshold = item.threshold || 0;
        const similar = item.similar || 0;
        if (similar > threshold) {
          // 纠错
          return item.stdName || "";
        } else {
          // 不纠错
          return item.name || "";
        }
      });

    // 分离长度小于等于3的配件名称
    const shortPartNames = allPartNames.filter((name) => name.length <= 3);
    const normalPartNames = allPartNames.filter((name) => name.length > 3);
    // 对短名称进行实体解析，判断哪些是有效的
    const partNameEntities = await extractPartNameEntities(shortPartNames.join(","));
    // 获取有效的短名称列表（用于判断，不替换原始名称）
    const validShortNameArray = [...new Set(partNameEntities.map((entity) => entity.value))];
    const validShortPartNames =
      shortPartNames.filter((name) => {
        return validShortNameArray.length && validShortNameArray.some((validName) => name.includes(validName));
      }) || [];
    // 只保留有效的短名称（使用原始名称）和正常名称
    return allPartNames.filter((name) => [...normalPartNames, ...validShortPartNames].includes(name)) || [];
  }
}
