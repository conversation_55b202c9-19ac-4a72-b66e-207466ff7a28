import { IMessage, MessageFactory } from "@casstime/copilot-core";
import { IInquiryCardInfo } from "../interface/index";
import { commandIntents } from "@/copilot/constants";

class TrackService {
  // 发布询价成功后埋点
  public inquiryPubliceTrack(inquiryCardInfo: IInquiryCardInfo = {}): IMessage[] {
    const { inquiryId, tyreInquiryDemand = {} } = inquiryCardInfo;
    const { demandId } = tyreInquiryDemand;
    const messages: IMessage[] = [];
    if (inquiryId) {
      const inquiryIdParams = {
        eventName: "ai_chat_inquiry",
        isRecordUser: true,
        extra: {
          resInquiryId: inquiryId,
          vinImageUrl: inquiryCardInfo.vinImage,
          partsImageUrl: inquiryCardInfo.partNamesImages,
          resQualityNames: inquiryCardInfo.qualityNames,
          fromScreen: "AIChatScreen",
          demandId: undefined,
        },
      };
      messages.push(MessageFactory.command(commandIntents.TRACK_COMMAND, inquiryIdParams));
    }
    if (demandId) {
      const demandIdParams = {
        eventName: "ai_chat_inquiry",
        isRecordUser: true,
        extra: {
          demandId,
          resInquiryId: undefined,
          fromScreen: "AIChatScreen",
        },
      };
      messages.push(MessageFactory.command(commandIntents.TRACK_COMMAND, demandIdParams));
    }

    return messages;
  }
}

export const trackService = new TrackService();
