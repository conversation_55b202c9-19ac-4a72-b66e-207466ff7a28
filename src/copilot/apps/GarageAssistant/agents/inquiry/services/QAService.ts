import { qdrantClient } from "@/clients/qdrant";
import { embedQuery } from "@/clients/llm";
import { QAAnswer, QAQuestion } from "@/models";
import logger from "@/common/logger";
import { Types } from "mongoose";
import { IQdResult } from "@/clients/qdrant/interface";
import assert from "node:assert";

class QAService {
  searchRelatedQAByInput = async (input: string) => {
    const embeddedInput = await embedQuery(input);
    const [qdQuestions, qdAnswers] = await Promise.all([
      qdrantClient.searchQuestion({ vector: embeddedInput, limit: 5 }),
      qdrantClient.searchAnswer({ vector: embeddedInput, limit: 3 }),
    ]);
    const scores: Record<string, number> = {};
    const getScoreAndId = (arr: IQdResult[]) => {
      const ids = arr.map(({ payload, score }) => {
        if (!scores[payload.mongoId]) {
          scores[payload.mongoId] = score;
        } else {
          scores[payload.mongoId] = Math.max(scores[payload.mongoId], score);
        }
        return Types.ObjectId(payload.mongoId);
      });
      return ids;
    };
    const questionIds = getScoreAndId(qdQuestions);
    const answerIds = getScoreAndId(qdAnswers);

    return {
      questionIds,
      answerIds,
      scores,
    };
  };
  getRelatedQAByIds = async ({
    questionIds,
    answerIds,
  }: {
    questionIds: Types.ObjectId[];
    answerIds: Types.ObjectId[];
  }) => {
    const questionItems = await QAQuestion.aggregate([
      {
        $match: {
          $or: [
            {
              _id: { $in: questionIds },
              enabled: true,
            },
            {
              answerId: { $in: answerIds },
              enabled: true,
            },
          ],
        },
      },
      {
        $lookup: {
          from: QAAnswer.collection.name,
          localField: "answerId",
          foreignField: "_id",
          as: "answer",
        },
      },
      {
        $unwind: "$answer",
      },
      {
        $match: {
          "answer.enabled": true,
        },
      },
      {
        $sort: {
          priority: -1,
        },
      },
      {
        $group: {
          _id: "$answerId",
          questionId: { $first: "$_id" },
          question: { $first: "$question" },
          answer: { $first: "$answer.answer" },
          answerId: { $first: "$answerId" },
        },
      },
      {
        $project: {
          _id: 0,
        },
      },
    ]);
    return questionItems.map((item) => {
      return {
        question: item.question,
        questionId: item.questionId.toString(),
        answer: item.answer,
        answerId: item.answerId.toString(),
        score: 0,
      };
    });
  };
  searchRelatedQA = async (input: string) => {
    try {
      const { questionIds, answerIds, scores } = await this.searchRelatedQAByInput(input);
      let relatedQA = await this.getRelatedQAByIds({ questionIds, answerIds });
      relatedQA = relatedQA.map((item) => {
        item.score = scores[item.questionId] || scores[item.answerId];
        return item;
      });
      relatedQA.sort((a, b) => b.score - a.score);
      return relatedQA;
    } catch (error: unknown) {
      assert(error instanceof Error);
      logger.error(`搜索相关问题失败:${input},${error.message}`);
      return [];
    }
  };
}
export const qaService = new QAService();
