import {
  AdditionalFormat,
  Context,
  IAction,
  ICommandMessage,
  IImageMessage,
  IMarkdownMessage,
  IMessage,
  IPayload,
  IRichTextMessage,
  ITextMessage,
  MessageFactory,
  SlotChanged,
} from "@casstime/copilot-core";
import { FormNames, InquiryFormData, InquiryFormErrorField, IOePartNameItem } from "../forms";
import {
  AgentName,
  AppendNeedNluAction,
  CommandActionIntents,
  commandIntents,
  EditPartNameAction,
  Intents,
  IQuality,
  MaintainAddressAction,
  NeedInvoiceAction,
  NoNeedInvoiceAction,
  createNewInquiryAction,
  qualities,
  ScanVinCodeAction,
  SendTyreImageAction,
  ToCassServiceAction,
  WorkOrderRecognizeAction,
} from "@/copilot/constants";
import { ErrorCode, FeatureTogglesEnum, VersionEnum } from "@/common/enums";
import { ITyreDemandResponse } from "@/interfaces/tyreDemand";
import { inquiryClient } from "../clients/InquiryClient";
import { tyreClient } from "../clients/TyreClient";
import logger from "@/common/logger";
import {
  IAppendUserNeed,
  ICarModel,
  ICustomInquiryDetailParams,
  IInquiryAdditionalImagesItem,
  InquiryDetailParams,
  InquiryReParameter,
  InquiryTypeEnum,
  IUserNeedsItem,
} from "@/interfaces/inquiry";
import { tyreService } from "./TyreService";
import { configureService } from "./ConfigureService";
import ValidateError, { ValidateErrorCode } from "@/common/error/ValidateError";
import { ActionFactory } from "@/common/factory/ActionFactory";
import { Counter, StrategyScemeEnum } from "@/models";
import {
  ICouponConfig,
  IGetInquiryQuoteRes,
  IInquiryPayload,
  IMessageExtra,
  INeedOeResult,
  IQuoteDecodeItem,
  IQuoteDisplayItem,
  IQuoteLayer,
  IQuoteResItem,
  IQuoteStoreItem,
  IStockInfoItem,
  ITyreQuoteAndDemand,
} from "../interface";
import {
  createCarModelEmbed,
  createEmbed,
  createInquiryInfoEmbed,
  createTips,
  createTyreInquiryEmbed,
  createEmptyQuoteEmbed,
  createViewHeight,
  createInquiryMoreQuoteEmbed,
  createInquiryQuoteEmbed,
  createTyreMoreQuoteEmbed,
  createEmbedBlock,
  createTyreSizeTips,
  createCouponsEmbed,
  createRecommendInfoEmbed,
  createRecommendQuoteEmbed,
  createA9tAirforceEnterEmbed,
  getEmbedFormat,
} from "../xml";
import { formatTime, getApolloConfig, isTimeBetween, TyreSpec, Version } from "@/common/utils";
import { IInquiryCardInfo, IInquiryInfo } from "../interface";
import {
  ActivityStatusEnum,
  DecodeErrorStatusList,
  DecodingStatusList,
  IDialogueBusinessEnum,
  InquiryQuoteOverStatusList,
  InquiryQuoteStatus,
} from "../enum";
import { couponService } from "./CouponService";
import { config } from "@casstime/apollo-config";
import { featureTogglesClient } from "../clients/FeatureTogglesClient";
import { ICreateRecommendPlanParams, IGetRecommendPlanRes } from "../interface/IRecommendPlan";
import _ from "lodash";
import { InquiryIntents } from "../parsers/inquiryIntentClassifier";
import {
  IGetIntelligentPlanRes,
  IIntelligentPlan,
  IIntelligentProgrammeItem,
  IntelligentPlanType,
  StoreLevelMap,
} from "../interface/IIntelligentPlan";
import { tmsClient } from "@/clients/tms";
import { IProductItem, IWholionLogisticsPayload } from "@/clients/tms/interface";
import { quoteClient } from "@/clients/quote";
import { IGetSPDOriginalPayload, IPlanQuoteItem } from "@/clients/quote/interface";
import { storeClient } from "@/clients/store";
import { createTyreQuoteCard } from "../copilot-xml";
import { createNotShortVinCodeAction } from "../actions.ts";
import { inquiryClient as inquiryClientGlobal } from "@/clients/inquiry";
import { createAccidentGreetMsg, createImg, createNewDefaultGreetMsg } from "../../richtext";
import { createAirforceGreetMsg } from "../../richtext";
import { ACCIDENT_BG_IMAGE, GARAGE_GREET_BACK } from "../constant";
import { AIRFORCE_BG_IMAGE } from "../constant";
import { renderRichtext } from "@/copilot/richtext";
import { AccidentTips } from "@/copilot/richtext/components/accident/AccidenTips";
import FastGptApi from "@/common/clients/FastGptApi";
import { PurchasePlanFeatureEnum } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/interfaces/plan";
import { maindataClient } from "../clients/MaindataClient";
import { partsMindClient } from "@/clients/parts-mind";
import { IRepairScenariosSlot } from "../interface/IRepariScenarios";
import { SelectRelatedParts } from "@/copilot/richtext/components/accident/AccidentParts/InquirySelectRelatedParts";
import { PartListWithEditBtn } from "@/copilot/richtext/components/accident/AccidentParts/AvailbleAccidentParts";
import { NewDefaultGreetMsg } from "../../richtext/components/NewDefaultGreetMsg";
import { purchaseService } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/services/PurchaseService";
import { IBombPicPayload } from "../interface/ICodeList";
import { ICarImagesItem, ICreateProxyInquiry, IProxyUserNeedsItem } from "../interface/IProxyInquiry";

export interface IReplyMessagePayload {
  answerMsg?: string;
  showCarModelMsg?: boolean;
  hiddenCarModelEmbed?: boolean;
  showInvoiceMsg?: boolean;
  isOnlyShowGuideMsg?: boolean;
  guideMsg?: string;
  tipsContent?: string;
  actions?: IAction[][];
  replyCommandMsg?: ICommandMessage;
  messageId?: string;
}

class InquiryService {
  // 展示配件，超过10条，中间省略
  private getPartsStr(partsNames?: string[]) {
    if (!partsNames || !partsNames.length) return "";

    return partsNames
      .map((name, idx) => {
        if (partsNames.length <= 10) {
          return `${idx + 1}.${name}`;
        }
        if (idx < 5) {
          return `${idx + 1}.${name}`;
        }
        if (idx === 5) {
          return "......";
        }
        if (idx >= partsNames.length - 5) {
          return `${idx + 1}.${name}`;
        }
      })
      .filter(Boolean)
      .join("\n");
  }

  // 展示品质
  private getQualitiesStr(qualityList?: string[]) {
    if (!qualityList || !qualityList.length) return "";
    return qualityList.map((id) => IQuality[id]).join("、");
  }

  // 查看询价单按钮
  private getCheckInquiryDetailAction(inquiryId: string) {
    return ActionFactory.command("查看报价", commandIntents.VIEW_INQUIRY, {
      params: {
        inquiryId,
      },
    });
  }

  // 回填消息按钮
  private createFillInputTextAction(title: string, value: string, replace: boolean = false) {
    return ActionFactory.command(title, CommandActionIntents.FILL_INPUT_TEXT, {
      params: {
        value,
        replace,
      },
    });
  }

  // 切换车架号按钮
  private getVinCodeSelectAction(carModel: ICarModel) {
    return ActionFactory.nlu(`${carModel?.saleModelName || carModel?.model || ""}(${carModel?.vinCode})`, {
      intent: Intents.inquiry,
      slots: {
        carModel,
        vinCode: carModel?.vinCode,
        prevCarModel: null,
      },
      entities: [],
      agentName: AgentName.inquiryAgent,
    });
  }

  // 一键询价按钮
  private createSubmitInquiryFormAction(formData: InquiryFormData) {
    return ActionFactory.nlu(
      "发布询价",
      {
        intent: commandIntents.IMMEDIATE_INQUIRY,
        agentName: AgentName.inquiryAgent,
        slots: { ...formData },
      },
      "primary"
    );
  }

  // 选择车架号
  private createSelectVinCodeAction(vinCode: string) {
    return ActionFactory.nlu(vinCode, {
      intent: InquiryIntents.询报价,
      agentName: AgentName.inquiryAgent,
    });
  }

  // 默认欢迎语
  public getGreetMessage(): ITextMessage {
    return MessageFactory.text("您好，智能采购助手为您服务！我可以帮您快速发布询价", {
      actions: [[ScanVinCodeAction, WorkOrderRecognizeAction]],
      extra: {
        addressTip: { visible: true },
        vinPartsTip: { content: "请输入车架号，或直接上传/拍摄车铭牌图" },
        isGreet: true,
      },
    });
  }

  // 新默认欢迎语
  public getDefaultGreetMessage(): IRichTextMessage {
    const content = `<image uri="${GARAGE_GREET_BACK}" style="width: 582;height:108 " />`;
    const message: IMessage = {
      type: "richtext",
      content: content,
      tips: createNewDefaultGreetMsg(),
      extra: {
        isGreet: true,
      },
    };
    return message;
  }

  // 获取引导欢迎语消息
  private async getGuideReplyMessages(companyId: string, noSupportGuideMsg: boolean): Promise<IMessage[]> {
    if (noSupportGuideMsg) return [];
    const allGuideMsgs = await configureService.getConfigureReplys(StrategyScemeEnum.guide, companyId);
    const guideMsgs: IMessage[] = [];
    if (allGuideMsgs.length) {
      guideMsgs.push(allGuideMsgs[0] as IMessage);
    }
    return guideMsgs;
  }

  // 获取事故通道欢迎语
  public async getAccidentGreetMessage() {
    // 事故通道欢迎语
    const content = `<image uri="${ACCIDENT_BG_IMAGE}" style="width: 600; height: 148;" />`;
    const message: IMessage = {
      type: "richtext",
      content,
      tips: createAccidentGreetMsg(),
      extra: {
        isGreet: true,
      },
    };
    return message;
  }

  // 获取空调维修采购通道欢迎语
  public async getAirforceGreetMessage() {
    //获取空调维修标签配置
    const airforceGreetLabel: string[] = getApolloConfig("AIRFORCE_GREET_LABEL") || [];
    const greetMsg = createAirforceGreetMsg(airforceGreetLabel);
    const content = `<image uri="${AIRFORCE_BG_IMAGE}" style="width: 600; height: 110;" />`;
    const message: IMessage = {
      type: "richtext",
      content,
      tips: greetMsg,
      extra: {
        isGreet: true,
      },
    };
    return message;
  }
  // IM智能采购助手欢迎语
  public async getProxyInquiryGreetMessage() {
    const content = `你好，请提供车架号(VIN码)和需要采购的零配件名称，可协助客户发布询价，生成采购方案`;
    const message: IMessage = {
      type: "text",
      content,
      extra: {
        isGreet: true,
      },
    };
    return message;
  }
  // 请求配置欢迎语
  public async getConfigureGreetMessage(companyId: string, noSupportGuideMsg = true) {
    const replyMessages: IMessage[] = [];
    // 查询是否有配置的欢迎语
    const [greetReplyMessages, guideReplyMessages] = await Promise.all([
      configureService.getConfigureReplys(StrategyScemeEnum.welcome, companyId),
      this.getGuideReplyMessages(companyId, noSupportGuideMsg),
    ]);
    // 引导欢迎语
    if (guideReplyMessages.length) {
      replyMessages.push(guideReplyMessages[0]);
    }
    if (greetReplyMessages.length > 0) {
      // 自定义欢迎语按钮
      const greetMsg = greetReplyMessages[0];
      const { enabled } = await featureTogglesClient.getIsPilotArea(companyId, FeatureTogglesEnum.AICOUPON);
      if (enabled) {
        const benefitsCouponButtonText = _.get(config.get("GRANT_COUPONS"), "benefitsCouponButtonText", "领福利");
        greetMsg.actions?.[0].push(
          ActionFactory.nlu(
            benefitsCouponButtonText,
            {
              intent: InquiryIntents.领福利,
              agentName: AgentName.inquiryAgent,
            },
            "primary"
          )
        );
      }
      // replyMessages.push(greetMsg);
      replyMessages.push(this.getDefaultGreetMessage());
    } else {
      replyMessages.push(this.getDefaultGreetMessage());
    }

    // 默认欢迎语
    return replyMessages;
  }

  // 获取与服务器时间差
  public getDiffStampCommandMessage(createdAt: string | number) {
    const current = new Date();
    const diffStamp = new Date(createdAt).getTime() - current.getTime();
    return MessageFactory.command(Intents.diffStamp, {
      diffStamp,
    });
  }

  // 通过校验，可发布询价
  public getSubmitInquiryFormMessage(
    formData: InquiryFormData,
    vinCodeChanged: boolean,
    extra: {
      repairScenarios?: IRepairScenariosSlot;
      id: string;
      appVersion?: Version;
      slotsChanges: Record<string, SlotChanged>;
    }
  ): IReplyMessagePayload {
    const { __ADDITIONALPARTS__, inquiryId, __NOASKAPPEND__ } = formData;
    const actions: IAction[][] = []; // 按钮
    let tipsContent = "";
    const noSupportHideOnRichText = extra?.appVersion?.isLessThan(VersionEnum.SIX_9_0);

    if (
      extra?.repairScenarios?.accidentCode &&
      extra.repairScenarios.partNames?.length &&
      !extra.repairScenarios.selectedPartNames?.length
    ) {
      // 有事故场景且没有选择过配件，推荐相关配件
      const recommendPartNames: string[] = [];
      const selectedRecommendParts = _.intersection(extra.repairScenarios.partNames, formData.partNames || []);

      extra.repairScenarios.partNames.forEach((partName) => {
        if (!(formData.partNames || []).includes(partName)) {
          recommendPartNames.push(partName);
        }
      });
      if (recommendPartNames.length && selectedRecommendParts.length) {
        tipsContent += renderRichtext(SelectRelatedParts, {
          partNames: recommendPartNames,
          selectedPartNames: formData.partNames || [],
          messageId: extra.id,
          content: "",
          source: "relatedParts" as const,
        });
      }
    }

    let guideMsg = "";
    if (__NOASKAPPEND__ === false && inquiryId) {
      // 询问是否追加
      let newPartNames = formData.partNames || [];
      const partNameChangeObj = extra?.slotsChanges?.partNames;
      if (partNameChangeObj?.changed) {
        // 新询价配件只保留新输入的
        newPartNames = _.difference(formData.partNames, partNameChangeObj.before);
      }
      actions[0] = [AppendNeedNluAction, createNewInquiryAction(newPartNames)];
      guideMsg = `是要在原来的单据追加，还是发布新询价单？`;
    } else {
      guideMsg = `${__ADDITIONALPARTS__ ? "已为你追加配件，" : ""}还有其他配件要一起问价吗？`;
      actions[0] = [noSupportHideOnRichText && EditPartNameAction, this.createSubmitInquiryFormAction(formData)].filter(
        Boolean
      ) as IAction[];
    }

    return { guideMsg, actions, tipsContent, showCarModelMsg: vinCodeChanged, showInvoiceMsg: true };
  }

  // 提示输入表单缺少的信息
  public getFillInquiryFormMessage(
    formData: InquiryFormData,
    error: Error,
    extra: {
      repairScenarios?: IRepairScenariosSlot;
      appVersion?: Version;
    }
  ) {
    let showCarModelMsg = false;
    let isOnlyShowGuideMsg = false;
    let guideMsg = ""; // 引导语
    let tipsContent = ""; // 指引
    let replyCommandMsg: ICommandMessage | null = null;
    let hiddenCarModelEmbed = false;
    const actions: IAction[][] = []; // 按钮
    const { inquiryId } = formData;

    const noSupportHideOnRichText = extra?.appVersion?.isLessThan(VersionEnum.SIX_9_0);

    if (ValidateError.isValidateError<{ key: string; errorVinCode?: string }>(error)) {
      const {
        code,
        data: { key, errorVinCode },
        message,
      } = error;
      if (extra?.repairScenarios?.accidents?.length && !extra.repairScenarios.accidentCode) {
        // 有事故场景且没有选择过事故场景，展示tips
        tipsContent += renderRichtext(AccidentTips, {
          repairScenarios: extra?.repairScenarios?.accidents,
          carBrandName: formData.carModel?.carBrandName || "",
          brandCode: formData.carModel?.carBrandCode || formData.carModel?.carBrandId || "",
          vehicleTypeCode: formData.carModel?.vehicleTypeClass || "",
        });
      }
      if (code === ValidateErrorCode.MISSING_REQUIRED_FIELD) {
        // 缺少必要字段
        switch (key) {
          // 缺少车架号和零配件信息
          case InquiryFormErrorField.VINCODE_PARTNAME:
            guideMsg = message;
            tipsContent += renderRichtext(NewDefaultGreetMsg, {});
            actions[0] = [ScanVinCodeAction, WorkOrderRecognizeAction];
            break;
          // 没有追加的配件
          case InquiryFormErrorField.APPEND_PARTNAME:
            isOnlyShowGuideMsg = true;
            guideMsg = message;
            actions[0] = [WorkOrderRecognizeAction];
            break;
          // 缺少轮胎规格
          case InquiryFormErrorField.TYRESIZE:
            guideMsg = message;
            actions[0] = [SendTyreImageAction, noSupportHideOnRichText && EditPartNameAction].filter(
              Boolean
            ) as IAction[];
            tipsContent = createTyreSizeTips();
            break;
          // 缺少车架号
          case InquiryFormErrorField.VINCODE:
            guideMsg = message;
            actions[0] = [ScanVinCodeAction, noSupportHideOnRichText && EditPartNameAction].filter(
              Boolean
            ) as IAction[];
            break;
          // 解析车型失败
          case InquiryFormErrorField.CARMODEL:
            isOnlyShowGuideMsg = true;
            guideMsg = message;
            actions[0] = [ScanVinCodeAction];
            if (errorVinCode) {
              actions[0].push(this.createFillInputTextAction("修改原车架号", errorVinCode, true));
            }
            break;
          // 缺少配件
          case InquiryFormErrorField.PARTNAME: {
            showCarModelMsg = true;
            guideMsg = message;
            actions[0] = [WorkOrderRecognizeAction];
            break;
          }
          // 缺少发票信息
          case InquiryFormErrorField.INVOICE:
            isOnlyShowGuideMsg = true;
            guideMsg = message;
            actions[0] = [NeedInvoiceAction, NoNeedInvoiceAction];
            break;
          // 缺少地址信息
          case InquiryFormErrorField.ADDRESS:
            isOnlyShowGuideMsg = true;
            guideMsg = message;
            actions[0] = [MaintainAddressAction];
            break;
          // 缺少其他必要信息
          case InquiryFormErrorField.TO_CASS_SERVICE:
            isOnlyShowGuideMsg = true;
            guideMsg = message;
            actions[0] = noSupportHideOnRichText ? [ToCassServiceAction] : [];
            break;
        }
      } else if (code === ValidateErrorCode.LENGTH_ERROR) {
        // 超出限定值
        switch (key) {
          // 超过配件限定值
          case InquiryFormErrorField.PARTNAME:
            isOnlyShowGuideMsg = true;
            guideMsg = message;
            actions[0] = noSupportHideOnRichText ? [EditPartNameAction] : [];
            break;
          case InquiryFormErrorField.PARTNAME_SUBMIT:
            guideMsg = message;
            actions[0] = [
              noSupportHideOnRichText && EditPartNameAction,
              this.createSubmitInquiryFormAction(formData),
            ].filter(Boolean) as IAction[];
            break;
          case InquiryFormErrorField.VINCODE:
            guideMsg = message;
            if (errorVinCode) {
              actions[0] = [createNotShortVinCodeAction(errorVinCode!)];
              actions[1] = [this.createFillInputTextAction("修改原车架号", errorVinCode, true)];
            }
            break;
        }
      } else if (code === ValidateErrorCode.DUPLICATE_VALUE) {
        // 重复值
        switch (key) {
          // 重复车架号
          case InquiryFormErrorField.VINCODE:
            isOnlyShowGuideMsg = true;
            guideMsg = message;
            tipsContent = "";
            actions[0] = [WorkOrderRecognizeAction];
            break;
          // 切换车架号
          case InquiryFormErrorField.CHANGE_VINCODE:
            isOnlyShowGuideMsg = true;
            hiddenCarModelEmbed = true;
            tipsContent = "";
            guideMsg = error.message;
            actions[0] = [this.getVinCodeSelectAction(formData.prevCarModel as ICarModel)];
            actions[1] = [this.getVinCodeSelectAction(formData.carModel as ICarModel)];
            break;
          // 存在有效询价单
          case InquiryFormErrorField.INQUIRYID:
            guideMsg = message;
            showCarModelMsg = true;
            tipsContent = "";
            actions[0] = [AppendNeedNluAction, createNewInquiryAction(formData.partNames || [])];
            actions[1] = [this.getCheckInquiryDetailAction(inquiryId || "")];
            break;
          // 多车型
          case InquiryFormErrorField.CARMODEL:
            replyCommandMsg = MessageFactory.command(Intents.inquirySelectBrand, {
              brands: formData.carModels,
            });
            break;
          // 多个车架号
          case InquiryFormErrorField.MULTIPLE_VINCODE:
            isOnlyShowGuideMsg = true;
            hiddenCarModelEmbed = true;
            tipsContent = "";
            guideMsg = error.message;
            formData.vinCodes?.slice(0, 3).forEach((vinCode) => {
              actions.push([this.createSelectVinCodeAction(vinCode)]);
            });
            break;
        }
      } else if (code === ValidateErrorCode.INVALID_VALUE_TYPE) {
        switch (key) {
          case InquiryFormErrorField.VINCODE:
            isOnlyShowGuideMsg = true;
            guideMsg = message;
            actions[0] = [ScanVinCodeAction];
            break;
        }
      }
    }
    if (replyCommandMsg) {
      return {
        showCarModelMsg,
        isOnlyShowGuideMsg,
        guideMsg,
        tipsContent,
        actions,
        replyCommandMsg,
        hiddenCarModelEmbed,
      };
    }
    return { showCarModelMsg, isOnlyShowGuideMsg, guideMsg, tipsContent, actions, hiddenCarModelEmbed };
  }

  // 根据表单校验结果，生成回复消息
  public createInquiryFormReplyMessage(
    formData: InquiryFormData,
    replyMsg: IReplyMessagePayload,
    appVersion: Version,
    extraInfo?: any
  ): IMarkdownMessage {
    const {
      guideMsg = "",
      tipsContent,
      actions,
      showCarModelMsg,
      showInvoiceMsg,
      isOnlyShowGuideMsg,
      hiddenCarModelEmbed,
    } = replyMsg; // 响应语和引导语
    let carModelMsg = ""; // 车型信息
    let quantityMsg = ""; // 品质信息
    let partNameMsg = ""; // 配件信息
    let invoiceMsg = ""; // 发票信息
    let carModelRichText = ""; // 车型富文本
    let partNameRichText = ""; // 配件富文本
    const extra: IMessageExtra = {}; // 提示信息
    const { vinCode, carModel, qualities, partNames, invoice } = formData;

    const noSupportHideOnRichText = appVersion.isLessThan(VersionEnum.SIX_9_0);

    // 车型信息
    if (vinCode) {
      const { saleModelName, carBrandName, brandLogo } = carModel || {};
      const carModelShowName = saleModelName || carBrandName || "";
      if (showCarModelMsg) {
        carModelMsg = `车型是：<font fontWeight="bold">${carModelShowName}(${vinCode})</font>\n`;
      }
      // 富文本
      const logoUri = brandLogo || "";
      carModelRichText = hiddenCarModelEmbed ? "" : createCarModelEmbed(logoUri, carModelShowName, vinCode);
    }
    if (qualities?.length) {
      const qualityNames = qualities.join("、");
      quantityMsg = `需要的品质是：<font fontWeight="bold">${qualityNames}</font>\n`;
      extra.qualityNames = qualityNames;
    }
    if (partNames?.length) {
      partNameMsg = `准备询价的配件有：\n<font fontWeight="bold">${this.getPartsStr(formData.partNames)}</font>\n`;
      extra.partNames = partNames;
      // 富文本
      partNameRichText = renderRichtext(PartListWithEditBtn, {
        partNames: partNames,
        showBtn: !noSupportHideOnRichText,
      });
    }
    if (vinCode || qualities?.length || partNames?.length) {
      extra.addressTip = { visible: true };
      extra.vinPartsTip = {
        content: vinCode ? "支持批量粘贴配件清单，或直接上传/拍摄工单图片" : "请输入车架号，或直接上传/拍摄车铭牌图",
      };
    }
    // 单独轮胎询价场景不展示发票信息
    if (showInvoiceMsg && invoice?.openInvoiceType && !tyreService.isAllTyreSpec(partNames)) {
      invoiceMsg = `<font color="gray" fontSize="28">（发票：${
        invoice.openInvoiceType === "YES" ? "" : "不"
      }开票）</font>\n`;
    }
    const answerMsg = partNameMsg && !showCarModelMsg ? "好的，您" : "";
    const msg =
      isOnlyShowGuideMsg && guideMsg
        ? guideMsg
        : [answerMsg, carModelMsg, quantityMsg, partNameMsg, invoiceMsg, guideMsg].filter(Boolean).join("");
    const isShowNotShortVinCodeAction = showCarModelMsg && vinCode && vinCode.length < 17;
    if (isShowNotShortVinCodeAction) {
      // 输入短车架号场景，增加按钮让用户在误识别场景可以纠错
      actions?.unshift([createNotShortVinCodeAction(vinCode!)]);
    }
    // 旧版本处理
    const noSupportRichText = appVersion.isLessThan(VersionEnum.FIVE_18_0);
    if (noSupportRichText) {
      return MessageFactory.markdown(msg, { actions, extra });
    } else {
      const others: Partial<IMarkdownMessage> = { extra: { ...extra, content: guideMsg } };
      const embed = createEmbed("richtext", [carModelRichText, partNameRichText]);
      const tips = createTips("richtext", tipsContent);
      if (embed.content) others.embed = embed;
      if (tips) {
        others.tips = tips;
        // TODO：短车架场景特殊处理
        others.actions = [];
        if (isShowNotShortVinCodeAction) {
          others.actions.push([createNotShortVinCodeAction(vinCode!)]);
        }
        // TODO：维护地址按钮
        const addressAction = actions?.flat().filter((action) => action.text === "添加收货地址");
        if (addressAction?.length) {
          others.actions.push(addressAction);
          others.tips = undefined;
        }
      } else {
        others.actions = actions;
      }

      // 提示点击人工客服
      const partsLimit: number = config.get("CUSTOMER_SERVICE_LIMIT") || 15;
      const businessId = extraInfo?.businessId || "";
      if (partNames?.length && partNames.length > partsLimit && businessId === IDialogueBusinessEnum.ACCIDENT) {
        const img = createImg();
        others.tips = createTips("richtext", [tips?.content || "", img.content].filter(Boolean).join("\n"));
      }
      return MessageFactory.markdown("", others);
    }
  }

  /**
   * 获取空调安装说明
   * @param intent
   */
  public async getAirConditionerExpertStream(
    prompt: string,
    context: Context,
    onChunk: (chunk: string) => void,
    onComplete: () => void
  ) {
    const fastGptApi = new FastGptApi({
      baseUrl: config.get("FASTGPT_BASE_URL_PROD"),
      apiKey: "fastgpt-lyutJCgj08t5rYwUMeaEQMadTe33UmRFUjgTfrL9OMaaRpj670x1MgCdBh",
    });
    const { lastMessage, sessionId } = context;
    try {
      await fastGptApi.sendChatMessage(
        {
          inputs: {},
          query: prompt,
          platform: "fastgptChat",
          conversationId: sessionId,
          user: lastMessage.fromUser!,
        },
        (event) => {
          if (event.event === "message" && event.answer) {
            onChunk(event.answer);
          }

          if (event.event === "message_end") {
            onComplete();
          }

          console.log("event", event);
        }
      );
    } catch (error) {
      logger.warn(`fastGptApi - 生成空调安装指南出错：${error}`);
      throw error;
    }
  }

  // 保存询价图片
  public saveNluImage(
    formData: InquiryFormData,
    lastMessage: IImageMessage,
    slotsChanges: Record<string, SlotChanged>
  ) {
    const imageSlots: { vinImages?: Record<string, string>; partNamesImages?: string[] } = {};
    if (slotsChanges?.vinCode?.changed) {
      const prevVinImages = formData?.vinImages || {};
      const vinImages = {
        ...prevVinImages,
        [slotsChanges.vinCode.after]: lastMessage.imageUrl,
      };
      imageSlots.vinImages = vinImages;
    }
    if (slotsChanges?.partNames?.changed) {
      const prevPartNamesImages = formData?.partNamesImages || [];
      const partNamesImages = prevPartNamesImages.concat(lastMessage.imageUrl);
      imageSlots.partNamesImages = partNamesImages;
    }
    return imageSlots;
  }

  // 生成询价卡片消息
  public getInquiryFormMessage(inquiryCardInfo: IInquiryCardInfo) {
    const {
      inquiryId,
      needsNames,
      saleModelName,
      carModelName,
      createdStamp,
      createdName,
      statusId,
      vinImage = "",
      partNamesImages = [],
      vin,
      carModel,
      tyreInquiryDemand,
      qualityNames,
    } = inquiryCardInfo;
    return MessageFactory.form(
      FormNames.inquiryForm,
      {
        inquiryId,
        partNames: needsNames,
        saleModelName,
        carModelName,
        inquiryTime: createdStamp || tyreInquiryDemand?.createdDate,
        userName: createdName,
        inquiryStatus: statusId,
        vinImage,
        partNamesImages,
        vin,
        carModel,
        tyreInquiryDemand,
        qualityNames,
      },
      {}
    );
  }

  public async getInquiryDetail(body: ICustomInquiryDetailParams, isProxyInquiry?: boolean): Promise<IInquiryInfo> {
    // 代客询价需要传入采购员
    const inquiryDetail = isProxyInquiry
      ? await inquiryClient.getCustomInquiryDetailV2(body)
      : await inquiryClient.getInquiryDetailV2(body);
    const {
      needs = [],
      saleModelName,
      carModelName,
      createdStamp,
      createdName,
      statusId,
      brandLogo,
      carBrandId,
      vin,
      addressId,
      inquiryId,
    } = inquiryDetail?.data || {};
    // 裁剪需求对象
    const needsNames =
      needs?.map((need) => {
        const { oeResults = [], needsName } = need;
        if (oeResults.length > 0) {
          const { partsNum = "", partsName = "" } = oeResults[0];
          const partsNumNew = partsNum.split(" ").join("");
          if (partsNumNew === needsName) {
            return `${partsName}(${needsName})`;
          }
        }
        return needsName;
      }) || [];
    return {
      needsNames,
      saleModelName,
      carModelName,
      createdStamp,
      createdName,
      statusId,
      brandLogo,
      carBrandId,
      vin,
      addressId,
      inquiryId,
    };
  }

  public async getNeedOeResults(body: InquiryDetailParams): Promise<INeedOeResult | undefined> {
    try {
      const inquiryDetail = await inquiryClient.getInquiryDetailV2(body);
      const { needs = [], vin = "", userId = "" } = inquiryDetail?.data || {};

      const needsOeResults =
        needs?.map((need) => {
          const oeResults = need.oeResults.map((oe) => {
            const { partsNum = "", partsName = "", standardNameCode } = oe;
            return { partsNum, partsName, standardNameCode };
          });
          return { oeResults, needsName: need.needsName };
        }) || [];
      return { needs: needsOeResults, vin, userId };
    } catch (error) {
      logger.warn(`getNeedOeResults - 获取询价单详情出错：${error}`);
    }
  }

  public async isHasEpcImage(body: InquiryDetailParams) {
    const needOeResults = await inquiryService.getNeedOeResults(body);
    if (!needOeResults || !needOeResults.needs.length) {
      return false;
    }
    const { needs, vin, userId } = needOeResults;
    const payload: IBombPicPayload = {
      vin,
      userId,
      oeCodeParamList: needs
        .map((item) => item.oeResults)
        .flat()
        .map((item) => {
          const { partsNum, standardNameCode: stdNameCode } = item;
          return { oeCode: partsNum.split(" ").join(""), stdNameCode };
        }),
    };
    const oeImages = await maindataClient.searchOeImages(payload);
    return Boolean(oeImages.length);
  }

  // 询价单信息从微服务接口获取
  public async getInquiryInfoFromService(inquiryId: string): Promise<IInquiryInfo> {
    const inquiryDetail = await inquiryClientGlobal.getInquiryInfo(inquiryId);
    const { addressInfos, inquiryBaseInfos, inquiryCarInfos, inquiryUserInfos } = inquiryDetail || {};
    const { addressId } = addressInfos || {};
    const { engineType, carModelName, carBrandId, vin } = inquiryCarInfos || {};
    const { createdStamp, userName, statusId, openInvoiceType, createdBy } = inquiryBaseInfos || {};
    const { garageCompanyId } = inquiryUserInfos || {};

    return {
      needsNames: [],
      saleModelName: engineType,
      carModelName,
      createdStamp: Number(createdStamp),
      createdName: userName,
      statusId,
      brandLogo: `https://parts-images.oss-cn-shenzhen.aliyuncs.com/brand_logo/${carBrandId}.png`,
      carBrandId,
      vin,
      addressId,
      inquiryId,
      openInvoiceType,
      userId: createdBy,
      companyId: garageCompanyId,
    };
  }

  // 生成报价结果消息
  public getInquiryQuoteMessage(params: any) {
    return MessageFactory.form("inquiryResult", params, {});
  }

  // 发布（全车件/轮胎）询价
  public async inquiryPublish(formData: InquiryFormData, onlyTyreEntities = false, businessId: string) {
    let inquiryDetail: IInquiryInfo = {};
    let tyreInquiryDemand: ITyreDemandResponse = {};
    // 是否代客询价
    const isProxyInquiry = formData.isProxyInquiry;

    const userNeeds = formData.oePartNames?.length
      ? formData.oePartNames
      : formData.partNames?.map((item) => ({ partsName: item }));
    // 全车件询价配件
    const commonUserNeeds: IOePartNameItem[] = onlyTyreEntities
      ? []
      : userNeeds?.filter((need) => !TyreSpec.includesTyreSpec(need.partsName)) || [];

    // 创建询价单、轮胎询价单，追加需求
    const [
      { inquiryId: createInquiryId, message: createInquiryMessage, errorCode: createInquiryErrorCode },
      { appendInquiryId, message: appendInquiryMessage, errorCode: appendInquiryErrorCode },
      { demandId, message: tyreInquiryMessage, errorCode: tyreInquiryErrorCode },
    ] = await Promise.all([
      this.inquiryCreate(formData, commonUserNeeds, businessId),
      this.appendInquiryOesCreate(formData, commonUserNeeds),
      tyreService.tyreInquiryCreate(formData),
    ]);
    const inquiryId = createInquiryId || appendInquiryId;
    if (inquiryId) {
      const inquiryDetailData = await this.getInquiryDetail(
        {
          inquiryId,
          source: formData.source || "",
          fromPage: formData.fromPage || "",
          userLoginId: formData.user?.userId,
          garageCompanyId: formData.user?.companyId,
        },
        isProxyInquiry
      );
      inquiryDetail = {
        ...inquiryDetailData,
        inquiryId,
        userId: formData.user?.userId,
        companyId: formData.user?.companyId,
      };
    }
    if (demandId) {
      const tyreInquiryDemandData = await tyreClient.getTyreInquiryDemand(demandId);
      tyreInquiryDemand = tyreInquiryDemandData?.data || {};
      // 补充品牌、花纹、防爆信息
      tyreInquiryDemand.originalItems?.forEach((originalItem) => {
        const tyreSpecs = TyreSpec.parse(originalItem.description || "");
        const { sectionWidth, flatnessRate, rimDiameter } = tyreSpecs?.[0] || {};
        const matchOriginalItem = formData?.originalItems?.find(
          (item) => item.treadWidth === sectionWidth && item.ratio === flatnessRate && item.size === rimDiameter
        );
        if (matchOriginalItem) {
          const { brandCode, brandName, pattern, explosionProofMark } = matchOriginalItem;
          originalItem.brandCode = brandCode;
          originalItem.brandName = brandName;
          originalItem.pattern = pattern;
          originalItem.explosionProofMark = explosionProofMark;
        }
      });
    }
    // 增加品质字段
    const qualityNames = formData?.qualities?.join("、");
    if (qualityNames) {
      inquiryDetail.qualityNames = qualityNames;
    } else {
      inquiryDetail.qualityNames = "全部品质";
    }
    return {
      inquiryDetail,
      tyreInquiryDemand,
      inquiryId,
      demandId,
      message: createInquiryMessage || appendInquiryMessage || tyreInquiryMessage,
      errorCode: createInquiryErrorCode || appendInquiryErrorCode || tyreInquiryErrorCode,
    };
  }

  // 创建全车件询价单
  public async inquiryCreate(formData: InquiryFormData, commonUserNeeds: IOePartNameItem[], businessId: string) {
    // 处理代客询价
    if (formData.isProxyInquiry) {
      return this.proxyInquiryCreate(formData, commonUserNeeds);
    }
    // 处理全车价询价
    // 不发布全车件询价
    if (!formData?.vinCode || !commonUserNeeds.length || formData?.inquiryId) {
      return { inquiryId: "", message: "", errorCode: null };
    }
    // 默认品质数组
    const defaultQualities = Object.values(qualities);
    // 反向映射，将品质名称映射成品质id
    const reverseIQuality = Object.fromEntries(Object.entries(IQuality).map(([key, value]) => [value, key]));
    const inquiryQualities = (formData.qualities || []).map((item) => reverseIQuality[item]);
    // 原始需求
    const userNeeds: IUserNeedsItem[] = commonUserNeeds!.map((need) => {
      const { partsName, oeCode } = need;
      const needItem: IUserNeedsItem = {
        originalNeed: oeCode || partsName,
        needsName: oeCode || partsName,
        quantity: 1,
        isFastOe: false,
        hasResolved: false,
        isSuggest: false,
        imageUrls: [],
        qualityType: "DEFAULT_QUALITY",
        publishQualityList: inquiryQualities.length ? inquiryQualities : defaultQualities,
        qualityList: inquiryQualities.length ? inquiryQualities : defaultQualities,
        userLoginName: formData.user?.userName || "",
        ...need,
      };
      return needItem;
    });
    // 车型信息
    const {
      carBrandCode = "",
      carBrandId = "",
      carBrandName = "",
      model,
      locationId,
      locationName = "",
      seriesId,
      seriesEn,
      seriesZh,
      saleModelCode,
      saleModelName,
    } = formData.carModel!;

    // 开票信息
    const isOpenInvoice = formData.invoice?.openInvoiceType === "YES";
    const isRequireItemInvoice = formData.invoice?.isRequireItemInvoice || false;

    // 收货地址
    const {
      latitude,
      longitude,
      provinceGeoName = "",
      provinceGeoId,
      cityGeoId,
      cityGeoName = "",
      countyGeoId = "",
      countyGeoName = "",
      villageGeoId = "",
      villageGeoName = "",
      id = "",
      contactNumber = `${formData.user?.cellphone}` || "",
    } = formData.address!;

    const inquiryAdditionalImages: IInquiryAdditionalImagesItem[] = [];
    formData.partNamesImages?.forEach((image) => {
      inquiryAdditionalImages.push({
        mediaType: "PICTURE",
        typeId: "HAND_WRITE_NEEDS",
        url: image,
      });
    });
    const vin = formData.vinCode || "";
    const isShortVin = vin.length < 17;
    const type = isShortVin ? InquiryTypeEnum.HK_MC_TW_INQUIRY : InquiryTypeEnum.WHOLE_CAR_PARTS;
    const inquiryParams: InquiryReParameter = {
      vin,
      noReplacement: "N",
      carBrandId: carBrandCode || carBrandId,
      carBrandName: carBrandName, //   车型
      carModelName: model,
      userName: formData.user?.userName || "",
      contactNumber: contactNumber || "",
      isOpenInvoice,
      qualities: inquiryQualities.length ? inquiryQualities : defaultQualities,
      quotedType: "SYSTEMHANDLER", //  系统分配
      // storeIds: [],
      source: formData.source || "",
      isSelectBrandFlag: false,
      isAnonymous: false,
      // vinPicture: '',
      userNeeds: userNeeds,
      isRequireItemInvoice,
      provinceGeoId: provinceGeoId,
      cityGeoId: cityGeoId,
      countyGeoId: countyGeoId,
      provinceGeoName: provinceGeoName,
      cityGeoName: cityGeoName,
      countyGeoName: countyGeoName,
      villageGeoId: villageGeoId,
      villageGeoName: villageGeoName,
      locationId,
      locationName,
      seriesId,
      seriesZh,
      seriesEn,
      saleModelCode,
      saleModelName,
      isSkipDecode: false,
      // picDemand: [],
      // picDemandUrls: [],
      // partsListUrls: [],
      isAccidentInquiry: false,
      // remarks:'',
      longitude: longitude,
      latitude: latitude, // 地址段
      addressId: id,
      isInsuranceDirect: false,
      inquiryAdditionalImages,
      shoppingListTag: [
        {
          tagType: "INQUIRY_PLATFORM",
          tagValue: "INTELLIGENT_PROCUREMENT_ASSISTANT",
        },
      ],
      type,
    };
    if (formData.reInquiryId) {
      inquiryParams.reInquiryId = formData.reInquiryId;
    }
    if (businessId === IDialogueBusinessEnum.ACCIDENT) {
      // 事故通道
      inquiryParams.inquiryAttributeRequests = [
        {
          attributeType: "INTELLIGENT_PROCUREMENT_ASSISTANT_CHANNEL",
          attributeValue: "ACCIDENT",
        },
      ];
    } else if (businessId === IDialogueBusinessEnum.AIR_CONDITIONER) {
      // 空调通道
      inquiryParams.inquiryAttributeRequests = [
        {
          attributeType: "INTELLIGENT_PROCUREMENT_ASSISTANT_CHANNEL",
          attributeValue: "AIR_CONDITIONER",
        },
      ];
    }
    return inquiryClient.createInquiry(inquiryParams);
  }

  // 代客询价创建询价单
  public async proxyInquiryCreate(formData: InquiryFormData, commonUserNeeds: IOePartNameItem[]) {
    // 不发布代客询价
    if (!formData?.vinCode || !commonUserNeeds.length || formData?.inquiryId) {
      return { inquiryId: "", message: "", errorCode: null };
    }
    // 收货地址
    const {
      latitude,
      longitude,
      provinceGeoName = "",
      provinceGeoId,
      cityGeoId,
      cityGeoName = "",
      countyGeoId = "",
      countyGeoName = "",
      villageGeoId = "",
      villageGeoName = "",
      id = "",
      contactNumber = `${formData.user?.cellphone}` || "",
    } = formData.address!;
    const inquiryAdditionalImages: ICarImagesItem[] = [];
    formData.partNamesImages?.forEach((image) => {
      inquiryAdditionalImages.push({
        resourceType: "HAND_WRITE_NEEDS",
        resourceUrl: image,
      });
    });
    // 车型信息
    const {
      carBrandCode = "",
      carBrandId = "",
      carBrandName = "",
      model,
      locationId,
      locationName = "",
      seriesId,
      seriesEn,
      seriesZh,
      saleModelCode,
      saleModelName,
    } = formData.carModel!;
    // 开票信息
    const isOpenInvoice = formData.invoice?.openInvoiceType === "YES";
    const isRequireItemInvoice = formData.invoice?.isRequireItemInvoice || false;
    // 默认品质数组
    const defaultQualities = Object.values(qualities);
    // 反向映射，将品质名称映射成品质id
    const reverseIQuality = Object.fromEntries(Object.entries(IQuality).map(([key, value]) => [value, key]));
    const inquiryQualities = (formData.qualities || []).map((item) => reverseIQuality[item]);
    const vin = formData.vinCode || "";
    const userNeeds: IProxyUserNeedsItem[] = commonUserNeeds!.map((need) => {
      const { partsName, oeCode } = need;
      const needItem: IProxyUserNeedsItem = {
        hasResolved: false,
        imageUrls: [],
        needsName: oeCode || partsName,
        quantity: 1,
        description: "",
        inquirySource: "MANUAL_ENTRY",
      };
      return needItem;
    });
    const payload: ICreateProxyInquiry = {
      accidentInquiry: false,
      address: {
        addressId: id,
        cityGeoId,
        cityGeoName,
        contactName: formData.user?.userName || "",
        contactNumber,
        countyGeoId,
        countyGeoName,
        latitude,
        longitude,
        provinceGeoId,
        provinceGeoName,
        villageGeoId,
        villageGeoName,
      },
      allResolved: false,
      carImages: inquiryAdditionalImages,
      garageCompanyId: formData.user?.companyId || "",
      garageCompanyName: formData.user?.companyName || "",
      inquiryReason: "其他",
      inquiryType: "COMMON_INQUIRY",
      insuranceDirect: false,
      isAnonymous: "0",
      isNewCustomize: "N",
      noReplacement: "N",
      openInvoiceType: isOpenInvoice ? "YES" : "NO",
      proxyId: formData.operator?.userId || "",
      proxyName: formData.operator?.userName || "",
      purchaserId: formData.user?.userId || "",
      purchaserName: formData.user?.userName || "",
      qualities: inquiryQualities.length ? inquiryQualities : defaultQualities,
      quotedType: "SYSTEMHANDLER",
      reInquiryId: formData.reInquiryId || "",
      replaceCustomerRecord: true,
      requireItemInvoice: isRequireItemInvoice,
      selectBrandFlag: "N",
      skipDecode: false,
      source: formData.source || "",
      userNeeds,
      newVersion: true,
      vehicle: {
        carBrandCode: carBrandCode || carBrandId,
        carBrandId: carBrandId || carBrandCode,
        carBrandName,
        carModelName: model,
        locationId,
        locationName,
        seriesId,
        seriesEn,
        seriesZh,
        saleModelCode,
        saleModelName,
        vin,
      },
      preRecordTagList: [
        {
          tagType: "INQUIRY_PLATFORM",
          tagValue: "INTELLIGENT_PROCUREMENT_ASSISTANT",
        },
      ],
    };
    return inquiryClient.proxyInquiryCreate(payload);
  }

  // 追加需求
  public async appendInquiryOesCreate(formData: InquiryFormData, commonUserNeeds: IOePartNameItem[]) {
    // 处理代客询价追加需求
    if (formData.isProxyInquiry) {
      return this.appendProxyInquiryOesCreate(formData, commonUserNeeds);
    }
    // 处理全车件追加需求
    if (commonUserNeeds.length && formData?.inquiryId) {
      const appendUserNeeds = commonUserNeeds.map((need) => {
        const { partsName, oeCode } = need;
        const newNeed: IAppendUserNeed = {
          needsName: oeCode || partsName,
          isFastOe: false,
          isSuggest: false,
          quantity: 1,
          imageUrls: [],
          ...need,
        };
        return newNeed;
      });
      try {
        const { errorCode, isAppendOe, message } = await inquiryClient.appendInquiryOes({
          inquiryId: formData.inquiryId,
          appendUserNeeds,
        });
        if (errorCode === ErrorCode.SUCCESS && isAppendOe) {
          return { appendInquiryId: formData.inquiryId, message };
        }
        return { appendInquiryId: "", message, errorCode };
      } catch (error) {
        logger.error(`追加需求失败:${error}`);
      }
    }
    return { appendInquiryId: "", message: "", errorCode: null };
  }

  // 代客询价追加需求
  public async appendProxyInquiryOesCreate(formData: InquiryFormData, commonUserNeeds: IOePartNameItem[]) {
    if (commonUserNeeds.length && formData?.inquiryId) {
      const appendUserNeeds = commonUserNeeds.map((need) => {
        const { partsName, oeCode } = need;
        const newNeed: IAppendUserNeed = {
          needsName: oeCode || partsName,
          isFastOe: false,
          isSuggest: false,
          quantity: 1,
          imageUrls: [],
          ...need,
        };
        return newNeed;
      });
      try {
        const { errorCode, isAppendOe, message } = await inquiryClient.appendProxyInquiryOes({
          inquiryId: formData.inquiryId,
          appendUserNeeds,
          userLoginId: formData.operator?.userId || "",
        });
        if (errorCode === ErrorCode.SUCCESS && isAppendOe) {
          return { appendInquiryId: formData.inquiryId, message };
        }
        return { appendInquiryId: "", message, errorCode };
      } catch (error) {
        logger.error(`追加需求失败:${error}`);
      }
    }
    return { appendInquiryId: "", message: "", errorCode: null };
  }

  // 获取询价单报价数量
  public async getInquiryCount(inquiryId: string) {
    try {
      const inquiryCountResult = await inquiryClient.getInquiryCount(inquiryId);
      return inquiryCountResult?.data?.inquiryCount || 0;
    } catch (error) {
      logger.error(`获取询价单报价数量: ${error}`);
    }
    return 0;
  }

  // 获取询价单报价详情
  public async getInquiryQuote(
    inquiryId: string,
    headers: Record<string, string>,
    source: string = "",
    fromPage: string = "",
    isOpenInvoice = false
  ) {
    let inquiryCount = 0;
    try {
      // 查询inquiryDetail
      const [{ data: quoteResult }, { data: quoteStoreInfos }, inquiryDetail] = await Promise.all([
        inquiryClient.getInquiryQuoteDetail(inquiryId),
        inquiryClient.getInquiryStoreInfo(inquiryId),
        this.getInquiryDetail({
          inquiryId,
          source,
          fromPage,
        }),
      ]);

      if (!quoteResult || !quoteStoreInfos) {
        return undefined;
      }
      // quoteStatusId: string; //供应商报价状态 UNQUOTE 报价中，QUOTE 报价完成，EXPIRED 已过期'
      const { userNeeds } = quoteResult;

      userNeeds.forEach((needItem) => {
        needItem.decodeResults?.forEach((decode) => {
          let decodeQuoteArray: IQuoteResItem[] = [];
          decode.layers.forEach((layer) => {
            layer.subLayersNew.forEach((subLayer) => {
              subLayer.storeLayers.forEach((item) => {
                decodeQuoteArray = [...decodeQuoteArray, ...item.quoteItems];
              });
            });
          });

          // 缺货的商家清单
          decode.outOfStockStoreIdArray = decode.outOfStockStoreIds?.map((item) => {
            return item.storeId || "";
          });
          // 所有的报价信息
          decode.quoteArray = decodeQuoteArray;
        });
      });

      const { inquiryStoreInfos } = quoteStoreInfos;

      // 已报价的商家列表
      const quoteStoreArray: IQuoteStoreItem[] = [];

      inquiryStoreInfos.forEach((storeItem) => {
        const {
          positioningName,
          storeId,
          storeName,
          quoteStatusId,
          resolvedQuotedCount,
          resolvedTotalCount,
          wholeOrderQuote = false,
          hiddenItemCount = 0,
        } = storeItem;
        /*
      商家没有有效报价时，按供应商选页面，顶部商家定位和下方商家列表中，把该商家隐藏；如果所有商家都没有有效报出，显示缺省页。
      这个有效报价，我们就按照你们返回的店铺简称（别名）来做过滤。
     */
        if (positioningName) {
          const decodeArray: IQuoteDisplayItem[] = [];
          const stockArrayMap: Map<string, IStockInfoItem> = new Map();

          userNeeds.forEach((needItem) => {
            // 1.无法译码
            const isDecodeError = DecodeErrorStatusList.includes(needItem.statusId);
            // 2.译码中
            const isDecoding = DecodingStatusList.includes(needItem.statusId);
            if (!isDecodeError && !isDecoding) {
              needItem.decodeResults?.forEach((decodeItem) => {
                // 3.缺货
                const isOut = decodeItem.outOfStockStoreIdArray?.includes(storeId);
                // 4.必须有品质报价
                const quoteArray = decodeItem.quoteArray || [];
                if (!isOut && quoteArray.length > 0) {
                  const quotes: IQuoteDecodeItem[] = [];
                  quoteArray.forEach((item) => {
                    if (item.storeId === storeId) {
                      const qualityName = (item.partTypeDesc || "") === "原厂件" ? "原厂件" : item.brandName || "";
                      const { partType = "", price = "", btPrice = "", departure = "", departureId = "" } = item;
                      quotes.push({
                        qualityName,
                        qualityId: partType,
                        price,
                        btPrice,
                        departure,
                        departureId,
                      });
                      item.stockInfo?.forEach((stockInfo) => {
                        stockArrayMap.set(stockInfo.facilityId || "", stockInfo);
                      });
                      inquiryCount += 1;
                    }
                  });

                  // 5.有报价结果才展示
                  if (quotes.length > 0) {
                    decodeArray.push({
                      partsName: decodeItem.partsName,
                      needsName: needItem.needsName,
                      decodeArray: quotes,
                    });
                  }
                }
              });
            }
          });

          // 发货的仓库数组
          const stockArray: IStockInfoItem[] = Array.from(stockArrayMap.values()).reverse();

          if (decodeArray.length > 0) {
            // 供应商报价信息
            const quoteStoreItem: IQuoteStoreItem = {
              inquiryId,
              wholeOrderQuote,
              hiddenItemCount,
              decodesArray: decodeArray,
              storeId,
              storeName,
              quoteStatusId,
              resolvedQuotedCount,
              resolvedTotalCount,
              stocks: stockArray,
              isOpenInvoice,
            };
            quoteStoreArray.push(quoteStoreItem);
          }
        }
      });

      // 最多展示三个商家的报价
      const quoteStoreList = quoteStoreArray.length > 3 ? quoteStoreArray.splice(0, 3) : quoteStoreArray;

      // 默认值
      let quoteStatusId: string = InquiryQuoteStatus.UNQUOTE;
      let isWholeOrderQuote = false;

      // 有报价时
      if (quoteStoreList.length > 0) {
        // 没有完成报价的商家列表
        const unQuoteList = quoteStoreList.filter((item) => {
          return !InquiryQuoteOverStatusList.includes(item.quoteStatusId);
        });

        // 没有全部报价的商家列表
        const noWholeOrderQuote = quoteStoreList.filter((item) => {
          return !item.wholeOrderQuote;
        });
        // 供应商报价状态 UNQUOTE 报价中，QUOTE 报价完成，EXPIRED 已过期'
        quoteStatusId = unQuoteList.length > 0 ? InquiryQuoteStatus.UNQUOTE : InquiryQuoteStatus.QUOTE;
        // 是否全部商家已经整单报价
        isWholeOrderQuote = noWholeOrderQuote.length === 0;
      }
      const inquiryNeedPolling = !(InquiryQuoteOverStatusList.includes(quoteStatusId) || isWholeOrderQuote);
      // 要展示的报价信息
      return {
        inquiryCount,
        inquiryId,
        quoteStoreList,
        inquiryNeedPolling,
        inquiryDetail: { ...inquiryDetail, inquiryId },
      };
    } catch (error) {
      logger.error(`获取询价单报价数量: ${error}`);
    }
    return {
      inquiryCount,
      inquiryId,
      quoteStoreList: [],
      inquiryNeedPolling: true,
      inquiryDetail: { inquiryId },
    };
  }

  // 生成富文本询价卡片
  public createInquiryCardRichText(
    inquiryDetail: IInquiryCardInfo,
    tyreInquiryDemand: ITyreDemandResponse
  ): Partial<ITextMessage> {
    const {
      brandLogo = "",
      carModelName = "",
      saleModelName = "",
      vin = "",
      inquiryId = "",
      createdName = "",
      createdStamp = Date.now(),
      needsNames = [],
    } = inquiryDetail;
    // 生成嵌入内容
    const carModelRichText = createCarModelEmbed(brandLogo, saleModelName || carModelName, vin);
    const inquiryInfoRichText = createInquiryInfoEmbed(needsNames, inquiryId, createdName, createdStamp);
    const tyreInquiryRichText = createTyreInquiryEmbed(tyreInquiryDemand);

    const embed = createEmbed("richtext", [
      carModelRichText,
      [tyreInquiryRichText, inquiryInfoRichText].filter(Boolean).join(createViewHeight(10)),
    ]);

    const richText: Partial<ITextMessage> = {};
    if (embed.content) {
      richText.embed = embed;
    }
    return richText;
  }

  // 生成询价信息卡片的富文本
  public createEmptyQuoteRichText(
    inquiryDetail: IInquiryInfo = {},
    tyreInquiryDemand: ITyreDemandResponse = {},
    quote = false, // 是否有报价
    isA9tAirforceEnter = false // 是否专属客服入口试点
  ): AdditionalFormat {
    const {
      brandLogo = "",
      carModelName = "",
      saleModelName = "",
      vin = "",
      inquiryId = "",
      needsNames = [],
      createdName = "",
      createdStamp = Date.now(),
    } = inquiryDetail;
    isA9tAirforceEnter = false; // 发布询价，去掉导购卡片
    // 轮胎询价单头部
    const { demandId = "" } = tyreInquiryDemand;
    const tyreHeaderRichText = createTyreInquiryEmbed(tyreInquiryDemand);
    // 全车件询价单头部
    const carModelRichText = createCarModelEmbed(brandLogo, saleModelName || carModelName, vin);
    const inquiryInfoRichText = createInquiryInfoEmbed(needsNames, inquiryId, createdName, createdStamp);
    const inquiryHeaderRichText = [carModelRichText, inquiryInfoRichText].join("\n");

    const emptyQuoteRichText = createEmptyQuoteEmbed();
    const tyreHeaderEmbedList: string[] = [];
    const carModelEmbedList: string[] = [];
    if (quote) {
      if (demandId) tyreHeaderEmbedList.push(tyreHeaderRichText);
      if (inquiryId) carModelEmbedList.push(inquiryHeaderRichText);
    } else {
      if (demandId) tyreHeaderEmbedList.push(tyreHeaderRichText, emptyQuoteRichText);
      if (inquiryId) carModelEmbedList.push(inquiryHeaderRichText, emptyQuoteRichText);
    }
    // 增加专属客服入口(全车件询价)
    const A9tAirforceEnterRichText = inquiryId && isA9tAirforceEnter ? createA9tAirforceEnterEmbed(inquiryDetail) : "";
    const tyreHeaderEmbed = createEmbed("richtext", tyreHeaderEmbedList);
    const carModelEmbed = createEmbed("richtext", carModelEmbedList);
    const a9tAirforceEnterEmbed = getEmbedFormat("richtext", A9tAirforceEnterRichText);
    const embed = createEmbedBlock([tyreHeaderEmbed, carModelEmbed, a9tAirforceEnterEmbed]);
    return embed;
  }

  // 生成富文本轮胎报价结果卡片
  public createTyreInquiryQuoteRichText(tyreQuote: ITyreQuoteAndDemand = {}): AdditionalFormat {
    if (!tyreQuote || !tyreQuote.demandId) return { type: "richtext", content: "" };
    const { list = [], demandId = "" } = tyreQuote;
    // 报价中
    const emptyQuoteRichText = createEmptyQuoteEmbed();
    // 头部询价信息
    const tyreHeaderRichText = createTyreInquiryEmbed(tyreQuote);
    // 报价结果
    const tyreQuoteRichText = createTyreQuoteCard(list, demandId);
    // 查看更多
    const tryeMoreQuoteRichText = createTyreMoreQuoteEmbed(tyreQuote?.demandId);
    const tyreContents = tyreQuoteRichText ? [tyreQuoteRichText, tryeMoreQuoteRichText] : [emptyQuoteRichText];
    const tyreQuoteEmbed = createEmbed("richtext", [tyreHeaderRichText, ...tyreContents]);
    const embed = createEmbedBlock([tyreQuoteEmbed]);
    return embed;
  }

  // 生成富文本全车件询价报价卡片
  public async createInquiryQuoteRichText(
    inquiryQuote?: IGetInquiryQuoteRes,
    source: string = "",
    isA9tAirforceEnter = false
  ): Promise<AdditionalFormat> {
    isA9tAirforceEnter = false;
    if (!inquiryQuote || !inquiryQuote.inquiryId) return { type: "richtext", content: "" };
    const { quoteStoreList, inquiryDetail = {}, inquiryId = "" } = inquiryQuote;
    const {
      brandLogo = "",
      carModelName = "",
      saleModelName = "",
      vin = "",
      needsNames = [],
      createdName = "",
      createdStamp = Date.now(),
    } = inquiryDetail;
    // 报价中
    const emptyQuoteRichText = createEmptyQuoteEmbed();
    // 询价信息
    const carModelRichText = createCarModelEmbed(brandLogo, saleModelName || carModelName, vin);
    const inquiryInfoRichText = createInquiryInfoEmbed(needsNames, inquiryId, createdName, createdStamp);
    const inquiryHeaderRichText = [carModelRichText, inquiryInfoRichText].join("\n");
    // 报价结果
    const inquiryQuoteRichText = await createInquiryQuoteEmbed(quoteStoreList, inquiryDetail, source);
    // 查看更多
    const inquiryMoreQuoteRichText = createInquiryMoreQuoteEmbed(inquiryId);
    const inquiryContents = inquiryQuoteRichText
      ? [inquiryQuoteRichText, inquiryMoreQuoteRichText]
      : [emptyQuoteRichText];
    // 增加专属客服入口
    const A9tAirforceEnterRichText = isA9tAirforceEnter ? createA9tAirforceEnterEmbed(inquiryDetail) : "";
    const a9tAirforceEnterEmbed = getEmbedFormat("richtext", A9tAirforceEnterRichText);
    const inquiryQuoteEmbed = createEmbed("richtext", [inquiryHeaderRichText, ...inquiryContents]);
    const embed = createEmbedBlock([inquiryQuoteEmbed, a9tAirforceEnterEmbed]);
    return embed;
  }
  // 生成富文本推荐方案卡片
  public async createRecommendRichText(
    inquiryQuote?: IGetInquiryQuoteRes,
    source: string = "",
    isOpenInvoice: boolean = false,
    isA9tAirforceEnter = false
  ): Promise<AdditionalFormat> {
    if (!inquiryQuote || !inquiryQuote.inquiryId) return { type: "richtext", content: "" };
    const {
      quoteStoreList,
      inquiryDetail = {},
      inquiryId = "",
      storeQuoteByRecommend = [],
      groupedByProgrammeIdAndStoreId = [],
    } = inquiryQuote;
    isA9tAirforceEnter = false;
    const { brandLogo = "", carModelName = "", saleModelName = "", vin = "" } = inquiryDetail;
    // 报价中
    const emptyQuoteRichText = createEmptyQuoteEmbed();
    // 询价信息
    const carModelRichText = createCarModelEmbed(brandLogo, saleModelName || carModelName, vin);
    const inquiryInfoRichText = createRecommendInfoEmbed(inquiryId);
    const inquiryHeaderRichText = [carModelRichText, inquiryInfoRichText].join("\n");
    // 报价结果
    const inquiryQuoteRichText = await createRecommendQuoteEmbed(
      quoteStoreList,
      inquiryDetail,
      source,
      storeQuoteByRecommend,
      groupedByProgrammeIdAndStoreId,
      isOpenInvoice
    );
    // 查看更多
    const inquiryMoreQuoteRichText = createInquiryMoreQuoteEmbed(inquiryId);
    const inquiryContents = inquiryQuoteRichText
      ? [inquiryQuoteRichText, inquiryMoreQuoteRichText]
      : [emptyQuoteRichText];
    // 增加专属客服入口
    const A9tAirforceEnterRichText = isA9tAirforceEnter ? createA9tAirforceEnterEmbed(inquiryDetail) : "";
    const a9tAirforceEnterEmbed = getEmbedFormat("richtext", A9tAirforceEnterRichText);
    const inquiryQuoteEmbed = createEmbed("richtext", [inquiryHeaderRichText, ...inquiryContents]);
    const embed = createEmbedBlock([inquiryQuoteEmbed, a9tAirforceEnterEmbed]);
    return embed;
  }
  // 提取聊天记录中最近的询价单车架号和车型信息
  public async getVinFromMessages(messages: IMessage[]) {
    let vinCode = "";
    let inquiryId = "";
    let carModel: ICarModel = {};
    const msg = messages.find((item) => {
      const { extra = {}, type } = item;
      if (extra?.inquiryId) {
        vinCode = extra?.vinCode;
        carModel = extra?.carModel;
        inquiryId = extra?.inquiryId;
      } else if (type === "form") {
        vinCode = item.formData?.vin;
        carModel = item.formData?.carModel;
        inquiryId = item.formData?.inquiryId;
      }
      return Boolean(vinCode) && Boolean(carModel);
    });
    // 未找到合适的询价消息
    if (!msg) return {};
    return { vinCode, carModel, inquiryId };
  }
  // 创建询价优惠券 计数key
  createInquiryCouponCounterKey = (companyId: string): string => {
    // key  INQUIRYCOUPON-${companyId}-${DATE}
    const currentDate = formatTime(Date.now(), "YYYY-MM-DD");
    return `INQUIRYCOUPON-${companyId}-${currentDate}`;
  };

  // 领取优惠券
  public async grantInquiryCoupons(companyId: string) {
    try {
      const { enabled } = await featureTogglesClient.getIsPilotArea(companyId, FeatureTogglesEnum.AICOUPON);
      if (!enabled) {
        return;
      }
      const couponsConfig = (config.get("GRANT_COUPONS") as ICouponConfig) || {};
      const {
        inquiryCouponActivityId = "",
        inquiryCouponStartTIme = "",
        inquiryCouponEndTIme = "",
        inquiryPreviousOrdersNumForDay = 0,
      } = couponsConfig;
      const couterKey = this.createInquiryCouponCounterKey(companyId);
      const inquiryCouponsCounter = await Counter.findOne({ key: couterKey }).exec();
      const currentValue = inquiryCouponsCounter?.value || 0;
      if (
        isTimeBetween(Date.now(), [inquiryCouponStartTIme, inquiryCouponEndTIme]) &&
        currentValue < inquiryPreviousOrdersNumForDay
      ) {
        const [acquireRes, couponActivityRes] = await Promise.all([
          couponService.acquireCoupons(companyId, inquiryCouponActivityId),
          couponService.getCouponActivitiy(inquiryCouponActivityId),
        ]);
        if (couponActivityRes && couponActivityRes.statusCode === 200) {
          // 活动进行中
          if (couponActivityRes.result?.activityStatus === ActivityStatusEnum.ONGOING) {
            if (acquireRes && acquireRes.statusCode === 200) {
              // 领取优惠券成功
              // 计数 +1
              if (inquiryCouponsCounter) {
                await Counter.updateOne({ key: couterKey }, { value: currentValue + 1 });
              } else {
                await Counter.create({ key: couterKey, value: 1 });
              }
              const amount = couponActivityRes.result.couponAmount || 0;
              const couponEmbed = createCouponsEmbed(amount);
              const others: Partial<ITextMessage> = {};
              others.embed = createEmbed("richtext", [couponEmbed], "#fff");
              return {
                text: `您的￥${amount}元优惠券福利已到账，记得使用哦~`,
                others,
              };
            }
          }
        }
      }
    } catch (error) {
      logger.warn("领取询价单优惠券失败", error);
    }
  }
  // 获取推荐方案匹配报价详情
  public async getInquiryQuoteByRecommend(
    inquiryId: string,
    headers: Record<string, string>,
    source: string = "",
    fromPage: string = "",
    isOpenInvoice = false,
    recommendDetail: IGetRecommendPlanRes[]
  ) {
    try {
      // 查询inquiryDetail
      const [{ data: quoteResult }, { data: quoteStoreInfos }, inquiryDetail] = await Promise.all([
        inquiryClient.getInquiryQuoteDetail(inquiryId),
        inquiryClient.getInquiryStoreInfo(inquiryId),
        this.getInquiryDetail({
          inquiryId,
          source,
          fromPage,
        }),
      ]);
      if (!quoteResult || !quoteStoreInfos) {
        return undefined;
      }
      const { userNeeds } = quoteResult;
      let storeQuoteByRecommend: IQuoteResItem[] = [];
      userNeeds.forEach((needItem) => {
        needItem.decodeResults?.forEach((decode) => {
          let decodeQuoteArray: IQuoteResItem[] = [];
          decode.layers.forEach((layer) => {
            layer.subLayersNew.forEach((subLayer) => {
              subLayer.storeLayers.forEach((quoteStoreLayer) => {
                const quoteItems = quoteStoreLayer.quoteItems.map((quoteItem) => ({
                  ...quoteItem,
                  partName: decode.partsName || needItem.needsName,
                }));
                decodeQuoteArray = [...decodeQuoteArray, ...quoteItems];
              });
            });
          });
          const recommendDecodeQuoteArray = decodeQuoteArray.reduce((result, decodeQuoteItem) => {
            recommendDetail.forEach((recommendItem) => {
              recommendItem.programmeItems.forEach((programmeItem) => {
                if (programmeItem.quotationProductId === decodeQuoteItem.quotationProductId) {
                  result.push({
                    ...decodeQuoteItem,
                    programmeId: recommendItem.programmeId,
                    programmeName: recommendItem.programmeName,
                    programmeDescription: recommendItem.programmeDescription,
                  });
                }
              });
            });
            return result;
          }, [] as IQuoteResItem[]);
          // 缺货的商家清单
          decode.outOfStockStoreIdArray = decode.outOfStockStoreIds?.map((item) => {
            return item.storeId || "";
          });
          // 所有的报价信息
          decode.quoteArray = recommendDecodeQuoteArray;
          storeQuoteByRecommend = [...storeQuoteByRecommend, ...recommendDecodeQuoteArray];
        });
      });
      const groupedByProgrammeId = Object.values(
        storeQuoteByRecommend.reduce((acc, item) => {
          if (!acc[item.programmeId]) {
            acc[item.programmeId] = {
              programmeId: item.programmeId,
              programmeDescription: item.programmeDescription,
              items: {},
            };
          }

          if (!acc[item.programmeId].items[item.storeId || ""]) {
            acc[item.programmeId].items[item.storeId || ""] = {
              storeId: item.storeId,
              storeName: item.storeName,
              quoteItems: [],
            };
          }

          acc[item.programmeId].items[item.storeId || ""].quoteItems.push(item);
          return acc;
        }, {} as any)
      );
      // 将 items 对象转换为数组
      const groupedByProgrammeIdAndStoreId = groupedByProgrammeId.map((programme: any) => ({
        ...programme,
        items: Object.values(programme.items),
      }));
      const { inquiryStoreInfos } = quoteStoreInfos;

      // 已报价的商家列表
      const quoteStoreArray: IQuoteStoreItem[] = [];

      inquiryStoreInfos.forEach((storeItem) => {
        const {
          positioningName,
          storeId,
          storeName,
          quoteStatusId,
          resolvedQuotedCount,
          resolvedTotalCount,
          wholeOrderQuote = false,
          hiddenItemCount = 0,
        } = storeItem;
        /*
      商家没有有效报价时，按供应商选页面，顶部商家定位和下方商家列表中，把该商家隐藏；如果所有商家都没有有效报出，显示缺省页。
      这个有效报价，我们就按照你们返回的店铺简称（别名）来做过滤。
     */
        if (positioningName) {
          const decodeArray: IQuoteDisplayItem[] = [];
          const stockArrayMap: Map<string, IStockInfoItem> = new Map();

          userNeeds.forEach((needItem) => {
            // 1.无法译码
            const isDecodeError = DecodeErrorStatusList.includes(needItem.statusId);
            // 2.译码中
            const isDecoding = DecodingStatusList.includes(needItem.statusId);
            if (!isDecodeError && !isDecoding) {
              needItem.decodeResults?.forEach((decodeItem) => {
                // 3.缺货
                const isOut = decodeItem.outOfStockStoreIdArray?.includes(storeId);
                // 4.必须有品质报价
                const quoteArray = decodeItem.quoteArray || [];
                if (!isOut && quoteArray.length > 0) {
                  const quotes: IQuoteDecodeItem[] = [];
                  quoteArray.forEach((item) => {
                    if (item.storeId === storeId) {
                      const qualityName = (item.partTypeDesc || "") === "原厂件" ? "原厂件" : item.brandName || "";
                      const { partType = "", price = "", btPrice = "", departure = "", departureId = "" } = item;
                      quotes.push({
                        qualityName,
                        qualityId: partType,
                        price,
                        btPrice,
                        departure,
                        departureId,
                      });
                      item.stockInfo?.forEach((stockInfo) => {
                        stockArrayMap.set(stockInfo.facilityId || "", stockInfo);
                      });
                    }
                  });

                  // 5.有报价结果才展示
                  if (quotes.length > 0) {
                    decodeArray.push({
                      partsName: decodeItem.partsName,
                      decodeArray: quotes,
                    });
                  }
                }
              });
            }
          });

          // 发货的仓库数组
          const stockArray: IStockInfoItem[] = Array.from(stockArrayMap.values()).reverse();

          if (decodeArray.length > 0) {
            // 供应商报价信息
            const quoteStoreItem: IQuoteStoreItem = {
              inquiryId,
              wholeOrderQuote,
              hiddenItemCount,
              decodesArray: decodeArray,
              storeId,
              storeName,
              quoteStatusId,
              resolvedQuotedCount,
              resolvedTotalCount,
              stocks: stockArray,
              isOpenInvoice,
            };
            quoteStoreArray.push(quoteStoreItem);
          }
        }
      });

      // 要展示的报价信息
      return {
        inquiryId,
        quoteStoreList: quoteStoreArray,
        inquiryDetail: { ...inquiryDetail, inquiryId },
        storeQuoteByRecommend,
        groupedByProgrammeIdAndStoreId,
      };
    } catch (error) {
      logger.error("获取推荐方案匹配报价详情失败", error);
    }
  }
  public async createRecommendPlan(inquiryId: string, payload: IPayload) {
    try {
      const { companyId = "", appVersion = "" } = payload || {};
      const noSupportRecommendPlan = appVersion?.isLessThan(VersionEnum.FIVE_19_0);

      if (!companyId || noSupportRecommendPlan) {
        return false;
      }
      const { enabled: isNotNeedPlan } = await featureTogglesClient.getIsPilotArea(
        companyId,
        FeatureTogglesEnum.AI_RECOMMENDED_PLAN
      );
      if (!isNotNeedPlan) {
        const params: ICreateRecommendPlanParams = {
          inquiryId,
          requestId: inquiryId,
          produceScene: "IM_DIALOG",
          callbackUrl: `${config.get("API_INTRA_BASE_URL")}copilot-server/copilot/recommend_plan`,
          sourceGroup: "IM",
        };
        inquiryClient.createRecommendPlan(params);
      }
      return !isNotNeedPlan;
    } catch (error) {
      logger.warn("制作推荐方案失败", error);
      return false;
    }
  }

  public async getIsPilotArea(companyId: string, feature: FeatureTogglesEnum) {
    try {
      const { enabled } = await featureTogglesClient.getIsPilotArea(companyId, feature);
      return enabled || false;
    } catch (error) {
      logger.error(error);
    }
    return false;
  }
  // 获取智能方案匹配报价详情
  public async getInquiryQuoteByIntelligent(
    inquiryPayload: IInquiryPayload,
    intelligentDetail: IGetIntelligentPlanRes,
    inquiryDetail: IInquiryInfo
  ) {
    try {
      const { recommendProgrammes, recommendStores } = intelligentDetail;
      const { userId, companyId, isOpenInvoice } = inquiryPayload;
      const { addressId = "", inquiryId = "" } = inquiryDetail;
      const recommendStoreIds = recommendStores.map((item) => item.storeId);
      // 1-查询询价单相关信息
      const [{ quotes: quoteResult }, { data: quoteStoreInfos }, recommendStoreLevels] = await Promise.all([
        purchaseService.getQuoteFromInquiryBodyDetail(inquiryId),
        inquiryClient.getInquiryStoreInfo(inquiryId),
        storeClient.getStoreLevelinfo(recommendStoreIds),
      ]);
      if (!quoteResult || !quoteStoreInfos) {
        return [];
      }
      const { inquiryStoreInfos } = quoteStoreInfos;
      const intelligentPlans: IIntelligentPlan[] = [];

      // 2-获取推荐方案
      recommendProgrammes.forEach((programme) => {
        const { storeId, standardItemResults } = programme;
        // 补充店铺名称和等级
        const storeInfo = inquiryStoreInfos.find((item) => item.storeId === storeId);
        const { storeName = "", iconUri = "", levelValue } = storeInfo || {};
        const quotes: IPlanQuoteItem[] = [];

        const quotesByStandardItemIds = _.groupBy(quoteResult, (quoteItem) => quoteItem.standardItemId);
        Object.entries(quotesByStandardItemIds).forEach(([standardItemId]) => {
          const standardItem = standardItemResults.find((item) => item.standardItemId === standardItemId);
          if (standardItem) {
            const quote = quoteResult.find((item) => standardItem.quotationProductIds[0] === item.quotationProductId);
            if (quote) quotes.push(quote);
          }
        });

        intelligentPlans.push({
          ...programme,
          inquiryId,
          storeName,
          iconUri,
          levelValue,
          quotes,
        });
      });

      // 3-获取推荐店铺方案
      recommendStores.forEach((store) => {
        const storeInfo = recommendStoreLevels?.find((item) => item.storeId === store.storeId) || {};
        // 补充店铺等级
        const { storeLevelCode = "", storeLevelUrl } = storeInfo;
        intelligentPlans.push({
          ...store,
          inquiryId,
          iconUri: storeLevelUrl,
          levelValue: StoreLevelMap?.[storeLevelCode],
          type: IntelligentPlanType.RECOMMENDED_STORE,
        });
      });

      // 4-查询物流时效/spd/配套品牌
      const products: IProductItem[] = this.extractProducts(intelligentPlans, inquiryId, isOpenInvoice);
      const wholionLogisticsPayload: IWholionLogisticsPayload = {
        postalAddressId: addressId,
        userLoginId: userId,
        garageCompanyId: companyId,
        businessType: "INQUIRY",
        terminal: "APP",
        products,
      };
      const wholionLogistics = await this.getWholionLogistics(wholionLogisticsPayload);

      // 5-补充信息
      intelligentPlans.forEach((plan) => {
        const { quotes = [] } = plan;

        // 5.1-共享仓方案处理
        if (plan.type === IntelligentPlanType.YUN_FACILITY) {
          const quotationProductIds = quotes.map((item) => item.quotationProductId).filter(Boolean);
          // 补充小狮物流信息
          const wholionLogistic = wholionLogistics?.find((item) => quotationProductIds?.includes(item.productId || ""));
          plan.defaultEta = wholionLogistic?.defaultEta || "";
        }

        // 5.2-补充发货仓库
        const quotesUniqByDepartureId = _.uniqBy(quotes, "departureId").filter((item) => item.location);
        plan.deliveryWarehouse =
          quotesUniqByDepartureId.length > 1 ? "多仓发货" : quotesUniqByDepartureId?.[0]?.locationName || "";

        // 5.3-单店方案补充商家信息
        const quotesUniqByStoreId = _.uniqBy(quotes, "storeId").filter((item) => item.storeId);
        if (quotesUniqByStoreId.length === 1) {
          plan.storeId = quotesUniqByStoreId[0].storeId;
          plan.storeName = quotesUniqByStoreId[0].storeName;
        }

        // 5.4-整单价格
        let packagePrice = 0;
        quotes
          .filter((quote) => quote.price)
          .forEach((quote) => {
            const { showPrice, quantity = 1, price = 0 } = quote;
            packagePrice += (showPrice || price) * quantity;
          });
        plan.packagePrice = packagePrice;
      });

      // 要展示的报价信息
      return intelligentPlans;
    } catch (error) {
      logger.error("获取智能方案匹配报价详情失败", error);
    }
    return [];
  }

  public async getInquiryQuoteByPurchase(
    intelligentDetail: IGetIntelligentPlanRes,
    inquiryDetail: IInquiryInfo,
    quoteItems: IPlanQuoteItem[]
  ) {
    try {
      let planIndex = 1;
      const { recommendProgrammes, recommendStores } = intelligentDetail;
      const { inquiryId = "", openInvoiceType, addressId = "", userId = "", companyId = "" } = inquiryDetail;
      // 1-查询询价单相关信息
      const intelligentPlans: IIntelligentPlan[] = [];

      // 2-获取推荐方案
      recommendProgrammes.forEach((programme) => {
        const { storeId, standardItemResults } = programme;
        const quotes: IPlanQuoteItem[] = [];
        const quotesByStandardItemIds = _.groupBy(quoteItems, (quoteItem) => quoteItem.standardItemId);
        Object.entries(quotesByStandardItemIds).forEach(([standardItemId]) => {
          const standardItem = standardItemResults.find((item) => item.standardItemId === standardItemId);
          if (standardItem) {
            const quote = quoteItems.find((item) => standardItem.quotationProductIds[0] === item.quotationProductId);
            if (quote) quotes.push(quote);
          }
        });

        // 补充店铺名称和等级
        const { storeName = "" } = quoteItems.find((item) => item.storeId === storeId) || {};
        intelligentPlans.push({
          ...programme,
          inquiryId,
          storeName,
          iconUri: "",
          levelValue: 0,
          quotes,
          name: `方案${planIndex++}`,
        });
      });

      // 3-获取推荐店铺方案
      recommendStores.forEach((store) => {
        intelligentPlans.push({
          ...store,
          inquiryId,
          iconUri: "",
          type: IntelligentPlanType.RECOMMENDED_STORE,
          name: `方案${planIndex++}`,
        });
      });

      //4-查询物流时效
      const isOpenInvoice = openInvoiceType === "YES";
      const products: IProductItem[] = this.extractProducts(intelligentPlans, inquiryId, isOpenInvoice);
      const wholionLogisticsPayload: IWholionLogisticsPayload = {
        postalAddressId: addressId,
        userLoginId: userId,
        garageCompanyId: companyId,
        businessType: "INQUIRY",
        terminal: "APP",
        products,
      };
      const wholionLogistics = await tmsClient.getWholionLogistics(wholionLogisticsPayload);

      // 5-补充信息
      intelligentPlans.forEach((plan) => {
        const { quotes = [] } = plan;
        // 5.1-共享仓方案处理
        if (plan.type === IntelligentPlanType.YUN_FACILITY) {
          const quotationProductIds = quotes.map((item) => item.quotationProductId).filter(Boolean);
          // 补充小狮物流信息
          const wholionLogistic = wholionLogistics?.find((item) => quotationProductIds?.includes(item.productId || ""));
          plan.defaultEta = wholionLogistic?.defaultEta || "";
        }
        // 5.2-单店方案补充商家信息
        const quotesUniqByStoreId = _.uniqBy(quotes, "storeId").filter((item) => item.storeId);
        if (quotesUniqByStoreId.length === 1) {
          const { storeId, storeName, positioningName } = quotesUniqByStoreId[0];
          plan.storeId = storeId;
          plan.storeName = storeName;
          plan.positioningName = positioningName;
        }
        // 5.3-整单价格
        let packagePrice = 0;
        quotes
          .filter((quote) => quote.price)
          .forEach((quote) => {
            const { showPrice, quantity = 1, price = 0 } = quote;
            packagePrice += (showPrice || price) * quantity;
          });
        plan.packagePrice = packagePrice;

        // 5.4-补充发货仓库
        const quotesUniqByDepartureId = _.uniqBy(quotes, (quote) => quote.location).filter((item) => item.location);
        plan.deliveryWarehouse =
          quotesUniqByDepartureId.length > 1 ? "多仓发货" : quotesUniqByDepartureId?.[0]?.locationName || "";
      });

      // 6-生成方案名称
      intelligentPlans.forEach((plan) => {
        const { type, name: defaultName, quotes = [], features } = plan;
        const highQualityQuotes =
          quotes?.filter((item) => item.qualityName?.includes("原厂") || item.categoryOriginalAssort) || [];
        const highQualityRatio = highQualityQuotes.length / (quotes?.length || 1);
        let name = defaultName;
        if (type === IntelligentPlanType.YUN_FACILITY) {
          name = `小时达`;
        } else if (features?.includes(PurchasePlanFeatureEnum.STRICT)) {
          name = `开思严选`;
        } else if (
          features?.includes(PurchasePlanFeatureEnum.ORIGINAL_BRAND) ||
          features?.includes(PurchasePlanFeatureEnum.OEM) ||
          highQualityRatio > 0.7
        ) {
          name = `高品质方案`;
        } else if (type === IntelligentPlanType.COLLECT) {
          name = `${plan.positioningName || plan.storeName}集采`;
        } else {
          name = `多商家组合`;
        }
        plan.name = name;
      });

      // 要展示的报价信息
      return intelligentPlans;
    } catch (error) {
      logger.error("获取智能方案匹配报价详情失败", error);
    }
    return [];
  }

  private async getWholionLogistics(wholionLogisticsPayload: IWholionLogisticsPayload) {
    try {
      return tmsClient.getWholionLogistics(wholionLogisticsPayload) || [];
    } catch (error) {
      logger.error("获取小狮物流信息失败", error);
    }
    return [];
  }

  private async getSPDOriginal(payload: IGetSPDOriginalPayload) {
    try {
      return quoteClient.getSPDOriginal(payload) || [];
    } catch (error) {
      logger.error("获取小狮物流信息失败", error);
    }
    return [];
  }

  private getQuoteItemsFromLayers(layers: IQuoteLayer[] = []): IQuoteResItem[] {
    const quotes: IQuoteResItem[] = [];
    layers.forEach((layer) => {
      layer?.subLayersNew?.forEach((subLayer) => {
        subLayer?.storeLayers?.forEach((storeLayer) => {
          const { quoteItems = [] } = storeLayer;
          quotes.push(...quoteItems);
        });
      });
    });
    return quotes;
  }

  private extractProducts(
    intelligentPlans: IIntelligentPlan[],
    inquiryId: string = "",
    isOpenInvoice: boolean = false
  ) {
    const products: IProductItem[] = [];
    intelligentPlans
      .filter((plan) => plan.type === IntelligentPlanType.YUN_FACILITY)
      .forEach((plan) => {
        const { facilityId = "", quotes = [] } = plan;
        quotes.forEach((quote) => {
          const { quotationProductId = "", price, storeId = "" } = quote;
          const product = {
            facilityId,
            productId: quotationProductId,
            storeId,
            inquiryId,
            quantity: 1,
            price: String(price),
            invoiceType: isOpenInvoice ? "A" : "B",
          };
          products.push(product);
        });
      });
    return products;
  }

  private extractQuotationProductIds(recommendProgrammes: IIntelligentProgrammeItem[]) {
    const quotationProductIds: string[] = [];
    recommendProgrammes.forEach((programme) => {
      const { standardItemResults } = programme;
      standardItemResults?.forEach((result) => {
        result?.quotationProductIds?.forEach((productId) => {
          if (!quotationProductIds.includes(productId)) {
            quotationProductIds.push(productId);
          }
        });
      });
    });
    return quotationProductIds;
  }

  /** 查询车辆维修场景 */
  public async getRepairScenarios({
    partNames,
    brandCode,
    vehicleTypeClass,
    prompt,
  }: {
    partNames: string[];
    brandCode: string;
    vehicleTypeClass: string;
    prompt?: string;
  }) {
    const stdCodes = [];
    if (partNames.length) {
      const stdnameobjs = await maindataClient.getStandardCodeList(partNames);
      stdCodes.push(...stdnameobjs.map((item) => item.code));
    }
    const repairScenarios = await partsMindClient.getRepairScenarios({
      brandCode: brandCode,
      vehicleTypeCode: vehicleTypeClass,
      stdCodes,
      prompt,
    });
    return repairScenarios.accidents;
  }
}

export const inquiryService = new InquiryService();
