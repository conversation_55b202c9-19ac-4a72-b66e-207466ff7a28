import {
  IAddress,
  IOriginalItemsItem,
  IPropertyItem,
  ITireInquiryVehicleItem,
  ITireSpecificationsItem,
  ITyreInquiryReParameter,
  PropertyTypeCodeEnum,
  SpecificationSourceEnum,
} from "@/interfaces/inquiry";
import { tyreClient } from "../clients/TyreClient";
import { PARTS_MAX_NUM } from "@/copilot/constants";
import { IPayload } from "@casstime/copilot-core";
import { inquiryClient } from "../clients/InquiryClient";
import { InquiryFormData } from "../forms";
import { TyreSpec } from "@/common/utils";
import logger from "@/common/logger";
import { ImageTyreSizePrediction, ITyreQuoteAndDemand } from "../interface";
import { parseTyreSpec } from "@/common/utils/tyre";
import { ITyreInquiryInfo } from "../interface/ITyreInquiry";

/**
 * 封装轮胎相关业务逻辑，不要传context到service
 */
export class TyreService {
  /** 创建轮胎询价单 */
  public async tyreInquiryCreate(formData: InquiryFormData) {
    if (!formData.originalItems?.length) {
      return { demandId: "", message: "", errorCode: null };
    }
    // 获取车型信息
    let vehicleModels: ITireInquiryVehicleItem[] = [];
    if (formData.vinCode) {
      try {
        const { data } = await inquiryClient.getVehicleModels(formData.vinCode);
        vehicleModels = data;
      } catch (error) {
        logger.warn(`获取车型信息失败：${error}`);
      }
    }
    const tyreInquiryParams = this.handleTyreInquiryParams(formData, vehicleModels, formData.originalItems);
    if (formData.isProxyInquiry) {
      // 处理代理询价
      tyreInquiryParams.userId = formData.user?.userId || "";
      tyreInquiryParams.garageCompanyId = formData.user?.companyId || "";
      return tyreClient.createProxyTyreInquiry(tyreInquiryParams);
    }
    return tyreClient.createTyreInquiry(tyreInquiryParams);
  }

  // 获取轮胎询价单详情和报价
  public async getTyreInquiryDemandAndQuote(demandId: string, inquiryInfo: ITyreInquiryInfo): Promise<ITyreQuoteAndDemand> {
    // 获取询价时间
    let createdTime;
    try {
      const { data: tyreDemand } = await tyreClient.getTyreInquiryDemand(demandId);
      createdTime = tyreDemand?.createdDate;
    } catch (error) {
      logger.warn(`获取轮胎询价单详情失败：${error}`);
    }
    let tyreDetail;
    if (inquiryInfo.isProxyInquiry) {
      // 代客询价
      const { data } = await tyreClient.getProxyTyreQuoteDetail({
        demandId,
        openInvoiceType: false,
        isSentry: false,
        userLoginId: inquiryInfo.userLoginId || "",
        garageCompanyId: inquiryInfo.garageCompanyId || "",
      });
      tyreDetail = data
    } else {
      // 普通询价（包括没有传递 inquiryInfo 的情况）
      const { data } = await tyreClient.getTyreQuoteDetail({
        demandId,
        openInvoiceType: false,
        isSentry: false,
      });
      tyreDetail = data;
    }
    return { ...tyreDetail, demandId, createdDate: createdTime || new Date().getTime() };
  }

  // 处理轮胎询价参数
  private handleTyreInquiryParams(
    formData: InquiryFormData,
    vehicleModels: ITireInquiryVehicleItem[],
    originalList: (IOriginalItemsItem & ImageTyreSizePrediction)[]
  ) {
    const { vinCode, address: addressInfo, invoice, user, source = "" } = formData;

    // 用户信息
    const {
      companyName: garageCompanyName,
      userParentName: groupUserName,
      userParentId: corporateId,
      cellphone,
      userName,
    } = user || {};

    // 收货地址
    const address: IAddress = {
      ...addressInfo,
      addressId: addressInfo?.id || "",
      contactName: addressInfo?.receiverName || "",
      mobileNumber: addressInfo?.contactNumber || "",
      addressDetail: addressInfo?.address || "",
    };
    /** TODO 重新询价时,旧的询价单 */
    const oldDemandId = "";
    /** MASTER_DATA_AUTO: vin码自动带出规格，MANUAL_ENTRY:人工选择规格 */
    let specificationSource = SpecificationSourceEnum.MANUAL_ENTRY;
    // 轮胎规格
    const originalItems = originalList.map((originalItem) => {
      if (originalItem.originalItemResources) {
        // 有轮胎图片，即是拍图询价
        specificationSource = SpecificationSourceEnum.PHOTO_RECOGNIZE;
      } else if (originalItem.specification || originalItem.tyreOrientation) {
        // 如果规格中有 specification 或者 tyreOrientation 属性，即是vin码自动带出规格
        specificationSource = SpecificationSourceEnum.MASTER_DATA_AUTO;
      }
      return {
        ...originalItem,
        ratio: originalItem.ratio || "-", // 扁平比默认为-
        sourceId: SpecificationSourceEnum.MANUAL_ENTRY, // 固定值
        quantity: 1, // 固定值
      };
    });

    const tyreInquiryParams: ITyreInquiryReParameter = {
      address,
      demandType: "TYRE_DEMAND", // 轮胎询价
      vin: vinCode || "-",
      originalItems,
      openInvoiceType: invoice?.openInvoiceType || "NO",
      contactNumber: String(cellphone),
      garageCompanyName,
      corporateId,
      groupUserName,
      source,
      userName,
      specificationSource,
      oldDemandId,
      platformSource: "INTELLIGENT_PROCUREMENT_ASSISTANT", // 智能采购助手标识
    };
    // 有车型信息则加上
    if (vehicleModels?.length) {
      tyreInquiryParams.vehicle = vehicleModels[0];
    }

    return tyreInquiryParams;
  }

  /**
   * 检查配件清单中是否包含轮胎规格
   * @param partNames
   * @returns
   */
  public isAnyTyreSpec(partNames?: string[]) {
    if (!partNames?.length) {
      return false;
    }
    return partNames.some((partName) => TyreSpec.includesTyreSpec(partName));
  }

  /**
   * 将配件中的轮胎规格序列化
   * @param partNames
   */
  public stringifyTyreSpec(partNames: string[]) {
    let result: string[] = [];
    const hasTyreSize = this.isAnyTyreSpec(partNames);
    partNames?.forEach((text) => {
      const specs = TyreSpec.parse(text);
      if (specs?.length) {
        const tyreSizes = specs.map((spec) => `（轮胎）${TyreSpec.stringify(spec)}`);
        result.push(...tyreSizes);
      } else {
        result.push(text);
      }
    });
    if (hasTyreSize) {
      result = result.filter((item) => !item.endsWith("轮胎"));
    }
    return result;
  }

  /**
   * 是否所有配件都是轮胎规格
   * @param partNames
   * @returns
   */
  public isAllTyreSpec(partNames?: string[]) {
    if (!partNames?.length) {
      return false;
    }
    return partNames.every((partName) => TyreSpec.includesTyreSpec(partName));
  }

  /**
   * 使用stringifyTyreSpec 和 isAnyTyreSpec 替代
   * @deprecated
   * @param partNames
   * @returns
   */
  public matchTyreSize(partNames: string[]) {
    return { partNames: this.stringifyTyreSpec(partNames), hasTyreSize: this.isAnyTyreSpec(partNames) };
  }

  // 处理轮胎规格
  public async handleTyreSize(formData: InquiryFormData, payload: IPayload) {
    const { partNames = [], vinCode } = formData;
    const { headers } = payload;
    let originalItems: ITireSpecificationsItem[] = [];
    let specifications: string[] = [];
    let partNamesNew: string[] = [];
    let noVinCodeTyre: boolean = false;
    // 是否有“轮胎”
    const hasTyre = partNames?.some((partName) => partName.endsWith("轮胎"));
    // 是否有轮胎规格
    const tyreSizes = partNames?.filter((partName) => TyreSpec.includesTyreSpec(partName));
    if (hasTyre && !tyreSizes?.length) {
      // 有轮胎且没有规格，根据 VIN 查规格
      if (vinCode && headers) {
        try {
          const { data } = await tyreClient.getTyreSpecifications(vinCode);
          noVinCodeTyre = Boolean(!data?.length);
          originalItems = data;
          if (partNames?.length + originalItems.length > PARTS_MAX_NUM) {
            originalItems = originalItems.slice(0, PARTS_MAX_NUM - partNames?.length);
          }
          specifications = originalItems.map((originalItem) => {
            const { specification } = originalItem;
            return `（轮胎）${specification}`;
          });
        } catch (error) {
          logger.warn(`根据 VIN 查轮胎规格失败：${error}`);
        }
      }
    } else {
      // 映射originalItems
      tyreSizes?.forEach((tyreSize) => {
        // 解析轮胎规格
        const originals = parseTyreSpec(tyreSize)?.map((item) => {
          const { treadWidth = "", ratio = "", meridian = "", size = "" } = item;
          const specification = `（轮胎）${TyreSpec.stringify({
            sectionWidth: treadWidth,
            flatnessRate: ratio,
            typeCode: meridian,
            rimDiameter: size,
          })}`;
          return { ...item, specification };
        });

        originalItems.push(...originals);
      });
      if (originalItems.length) {
        const { data } = await tyreClient.getOptionalTyreSpecifications();
        originalItems = originalItems.map((originalItem) => {
          const specificationsCode = this.parseSpecificationsCode(data?.property || [], originalItem);
          return { ...originalItem, ...specificationsCode };
        });
      }
    }
    if (originalItems.length) {
      partNamesNew = partNames?.concat(specifications).filter((partName) => !partName.endsWith("轮胎")) || [];
    } else {
      partNamesNew = partNames || [];
    }

    return {
      partNamesNew,
      originalItems,
      noVinCodeTyre,
    };
  }

  // 获取胎面宽、扁平比、尺寸等code信息
  private parseSpecificationsCode(property: IPropertyItem[], originalItem: ITireSpecificationsItem) {
    // 扁平比默认为-
    const { treadWidth, ratio = "-", size } = originalItem;
    let treadWidthCode, ratioCode, sizeCode;
    property?.forEach((propertyItem) => {
      switch (propertyItem.propertyTypeCode) {
        // 胎面宽
        case PropertyTypeCodeEnum.TreadWidth:
          treadWidthCode = propertyItem.valueList?.find(
            (value) => value.propertyValueName === treadWidth
          )?.propertyValueCode;
          break;
        // 扁平比
        case PropertyTypeCodeEnum.RatioCode:
          ratioCode = propertyItem.valueList?.find((value) => value.propertyValueName === ratio)?.propertyValueCode;
          break;
        // 尺寸
        case PropertyTypeCodeEnum.Size:
          sizeCode = propertyItem.valueList?.find((value) => value.propertyValueName === size)?.propertyValueCode;
          break;
        default:
          break;
      }
    });
    return { treadWidthCode, ratioCode, sizeCode };
  }

  public parseTyreSize(tyreSize: ImageTyreSizePrediction, imageUrl: string) {
    const {
      sectionWidth = "",
      flatnessRate = "",
      typeCode = "R",
      rimDiameter = "",
      brandCode = "",
      brandName = "",
      speedRating = "",
    } = tyreSize;
    const specification = `（轮胎）${TyreSpec.stringify({
      sectionWidth,
      flatnessRate,
      typeCode,
      rimDiameter,
    })}`;
    const treadWidth = String(sectionWidth);
    const ratio = String(flatnessRate);
    const size = String(rimDiameter);

    if (!sectionWidth || !rimDiameter) {
      // 规格异常
      return;
    }
    const originalTyre = tyreSize.identifiers?.find((item) => item.type === "原厂标识")?.code;
    const explosionProofMark = tyreSize.identifiers?.find((item) => item.type === "防爆标识")?.code;
    const sidewallEnhance = tyreSize.identifiers?.find((item) => item.type === "胎侧增强")?.code;
    const tyreFeature = tyreSize.identifiers?.find((item) => item.type === "轮胎特性")?.desc;
    const pattern = tyreSize.treadPattern?.name;
    let whetherImport; // 是否进口标识
    if (tyreSize?.madeIn) {
      whetherImport = tyreSize.madeIn === "CHINA" ? "国产" : "进口";
    }
    const originalItems = [
      {
        brandCode,
        brandName,
        speedLevel: speedRating,
        treadWidth,
        ratio,
        meridian: typeCode,
        size,
        originalTyre,
        explosionProofMark,
        sidewallEnhance,
        tyreFeature,
        pattern,
        whetherImport,
        originalItemResources: [
          {
            resourceType: "OLD_TYRE_PICTURE", // 图片类型   旧胎图：OLD_TYRE_PICTURE
            resourceValue: imageUrl, // 图片路径
          },
        ],
        specification,
      },
    ];
    return originalItems;
  }
}

export const tyreService = new TyreService();
