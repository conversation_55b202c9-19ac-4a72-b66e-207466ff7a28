import { EditAction, EditData, InquiryFormEditService } from "../InquiryFormEditService";

let service: InquiryFormEditService;
beforeAll(() => {
  service = new InquiryFormEditService();
});

describe("InquiryFormEditService.applyEditActions", () => {
  const data: { description: string; formData: EditData; actions: EditAction[]; result: EditData }[] = [
    {
      description: "添加品质、添加配件",
      formData: {
        partNames: ["机油格"],
        qualities: [],
      },
      actions: [
        {
          type: "quality",
          action: "add",
          entities: ["原厂件"],
        },
        {
          type: "partName",
          action: "add",
          entities: ["机油滤清器", "空气滤清器"],
        },
      ],
      result: {
        partNames: ["机油格", "机油滤清器", "空气滤清器"],
        qualities: ["原厂件"],
      },
    },
    {
      description: "删除品质、删除配件",
      formData: {
        partNames: ["机油格", "机油滤清器", "空气滤清器"],
        qualities: ["原厂件"],
      },
      actions: [
        {
          type: "quality",
          action: "delete",
          entities: ["原厂件"],
        },
        {
          type: "partName",
          action: "delete",
          entities: ["机油滤清器", "空气滤清器"],
        },
      ],
      result: {
        partNames: ["机油格"],
        qualities: [],
      },
    },
    {
      description: "替换配件",
      formData: {
        partNames: ["火花塞"],
        qualities: [],
      },
      actions: [
        { action: "delete", type: "partName", entities: ["火花塞"] },
        { action: "add", type: "partName", entities: ["机油格"] },
      ],
      result: {
        partNames: ["机油格"],
        qualities: [],
      },
    },
    {
      description: "添加和删除配件",
      formData: {
        partNames: ["控制器"],
        qualities: [],
      },
      actions: [
        { action: "add", type: "partName", entities: ["电力变频控制器"] },
        { type: "partName", action: "delete", entities: ["控制器"] },
      ],
      result: {
        partNames: ["电力变频控制器"],
        qualities: [],
      },
    },
  ];

  test.each(data)("should return the correct result for %s", ({ formData, actions, result }) => {
    expect(service.applyEditActions(formData, actions)).toEqual(result);
  });
});
