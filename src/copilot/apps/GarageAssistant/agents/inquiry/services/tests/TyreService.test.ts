import { TyreService } from "../TyreService";

let service: TyreService;
beforeAll(() => {
  service = new TyreService();
});

describe("isAllPartNamesIsTireSpecifications", () => {
  test("should return true", () => {
    const partNames: string[][] = [];
    partNames.push(["（轮胎）255/60 R17"]);
    partNames.push(["(轮胎)255/60 R17"]);
    partNames.push(["轮胎 255/60 R17"]);
    partNames.push(["轮胎 255/60R17"]);
    partNames.push(["255/60 R17"]);
    partNames.push(["255/60R17"]);
    partNames.push(["（轮胎）255/60 R17"]);
    partNames.push(["（轮胎）255/60 R19", "（轮胎）255/60 R17"]);

    partNames.forEach((pns) => {
      const result = service.isAllTyreSpec(pns);
      if (!result) {
        console.error(pns);
      }
      expect(result).toBeTruthy();
    });
  });

  test("should return false", () => {
    const partNames: any[] = [];
    partNames.push(["轮胎"]);
    partNames.push(["机油格", "（轮胎）255/60 R19"]);
    partNames.push(["（轮胎）255/60 R19", "机油格", "（轮胎）255/60 R19"]);
    partNames.push(["轮胎", "（轮胎）255/60 R19"]);

    partNames.forEach((pns) => {
      const result = service.isAllTyreSpec(pns);
      if (result) {
        console.error(pns);
      }
      expect(result).toBeFalsy();
    });
  });
});

describe(TyreService.prototype.isAnyTyreSpec.name, () => {
  const data = [
    { input: "￥4,390.13", output: { hasTyreSize: false } },
    { input: "4,390.13", output: { hasTyreSize: false } },
    { input: "2554018", output: { hasTyreSize: true } },
    { input: "255/40R18", output: { hasTyreSize: true } },
  ];
  for (const { input, output } of data) {
    test(input, () => {
      expect(service.isAnyTyreSpec([input])).toBe(output.hasTyreSize);
    });
  }
});
