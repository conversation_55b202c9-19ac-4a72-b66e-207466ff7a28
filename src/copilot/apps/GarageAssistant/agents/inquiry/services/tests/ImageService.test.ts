import { ImageService } from "../ImageService";

describe("ImageService", () => {
  let imageService: ImageService;
  let mockContext: any;

  beforeEach(() => {
    mockContext = {
      lastMessage: {},
      slots: {},
      getTempData: jest.fn(),
      setTempData: jest.fn(),
    };

    imageService = new ImageService(mockContext);
  });

  describe("isValidPartName", () => {
    const data = [
      {
        text: "机油格",
        expected: true,
      },
      {
        text: "8,9,10,11",
        expected: false,
      },
    ];
    data.forEach(({ text, expected }) => {
      it(`should return ${expected} for part name "${text}"`, () => {
        expect(imageService["isValidPartName"](text)).toBe(expected);
      });
    });
  });
});
