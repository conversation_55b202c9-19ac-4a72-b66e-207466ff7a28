import { Service } from "@casstime/copilot-core";
import { Dialogue, IDialogue } from "@/models";
import { featureTogglesClient } from "../clients/FeatureTogglesClient";
import { FeatureTogglesEnum } from "@/common/enums";
import { IDialogueBusinessEnum } from "../enum";

/** 通道 */
export class ChannelService extends Service {
  async getDialogue() {
    const dialogueKey = "__SHARED_DIALOGUE__";
    let dialogue = this.context.getTempData<IDialogue>(dialogueKey);
    if (dialogue) {
      return dialogue;
    }
    const { lastMessage } = this.context;
    dialogue = await Dialogue.findById(lastMessage.dialogueId).lean();
    this.context.setTempData(dialogueKey, dialogue);
    return dialogue;
  }
  /** 是否能看到场景推荐
   * 1、空调、事故场景能看到
   * 2、采购助手场景开关打开，推荐
   */
  async getSceneEnable() {
    const dialogue = await this.getDialogue();
    if (
      IDialogueBusinessEnum.ACCIDENT === dialogue?.businessId ||
      IDialogueBusinessEnum.AIR_CONDITIONER === dialogue?.businessId
    ) {
      return true;
    }
    const { payload } = this.context;
    const aiChatScene = await featureTogglesClient.getIsPilotArea(payload.companyId, FeatureTogglesEnum.AI_CHAT_SCENE);
    return aiChatScene.enabled;
  }
}
