import logger from "@/common/logger";
import { couponClient } from "../clients/CouponClient";



class CouponService {
  public async acquireCoupons(companyId: string, couponActivityId: string) {
    try {
      const res = await couponClient.acquireCoupons(companyId, couponActivityId)
      return res;
      
    } catch (error) {
      logger.warn(`领取优惠券失败${error}`);
    }
  }

  public async getCouponActivitiy(couponActivityId: string) {
    try {
      const res = await couponClient.getCouponActivity(couponActivityId)
      return res;
      
    } catch (error) {
      logger.warn(`查询优惠券活动信息失败${error}`);
    }
  }
}

export const couponService = new CouponService();