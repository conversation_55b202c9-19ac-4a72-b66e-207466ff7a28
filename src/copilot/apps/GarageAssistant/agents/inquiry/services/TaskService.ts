import { TaskPollingStatus, ITaskPollingStatus } from "@/models";
import logger from "@/common/logger";

class TaskService {
  // 新建轮询任务
  public async createTaskPolling(task: ITaskPollingStatus) {
    try {
      await TaskPollingStatus.create(task);
    } catch (error) {
      logger.warn(`新建轮询任务失败：${error}`);
    }
  }

  // 更新轮询任务
  public async updateTaskPolling(taskFilter: Partial<ITaskPollingStatus>, taskUpdate: Partial<ITaskPollingStatus>) {
    try {
      await TaskPollingStatus.updateMany(taskFilter, { $set: taskUpdate });
    } catch (error) {
      logger.warn(`更新轮询任务失败：${error}`);
    }
  }

  // 更新或者插入轮询任务
  public async findOneAndUpdateTaskPolling(
    taskFilter: Partial<ITaskPollingStatus>,
    taskUpdate: Partial<ITaskPollingStatus>
  ) {
    try {
      return await TaskPollingStatus.findOneAndUpdate(taskFilter, { $set: taskUpdate }, { upsert: true, new: true });
    } catch (error) {
      logger.warn(`更新或者插入轮询任务失败：${error}`);
    }
  }

  // 查询轮询任务
  public async getTaskPolling(task: Partial<ITaskPollingStatus>) {
    try {
      const result = await TaskPollingStatus.find(task).sort({ createdAt: -1 }).lean().exec();
      return result;
    } catch (error) {
      logger.warn(`查询轮询任务失败：${error}`);
      return [];
    }
  }
}

export const taskService = new TaskService();
