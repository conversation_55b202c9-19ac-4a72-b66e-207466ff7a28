import { CopilotMessage, ICopilotMessage } from "@/models";
import { IMessage, IPayload } from "@casstime/copilot-core";
import logger from "@/common/logger";
import dayjs from "dayjs";
import { FilterQuery } from "mongoose";

class MessageService {
  // 获取用户最近一条消息
  public async getLastShowMessage(payload: IPayload) {
    try {
      const { app, companyId, data: msg } = payload;
      const condition: FilterQuery<ICopilotMessage> = {
        // 默认过滤不展示的消息
        type: {
          $nin: ["command", "echo", "system"] as (RegExp | "system")[],
        },
        owner: `${app}:${msg.fromUser}:${companyId}`,
        dialogueId: msg.dialogueId,
      };
      const result = await CopilotMessage.findOne(condition).sort({ createdAt: -1 }).lean().exec();
      return result;
    } catch (error) {
      logger.warn(`获取最后一条消息失败：${error}`);
      return {};
    }
  }

  // 获取用户指定时间的历史消息
  public async getLastMessageList(
    payload: IPayload,
    timestamp: number = dayjs().startOf("day").valueOf() // 默认是当天的历史记录
  ): Promise<IMessage[]> {
    try {
      const { app, companyId, data: msg } = payload;
      const condition: FilterQuery<ICopilotMessage> = {
        type: { $nin: ["command", "echo", "system"] as any },
        owner: `${app}:${msg.fromUser}:${companyId}`,
        createdAt: { $gt: timestamp },
        dialogueId: msg.dialogueId,
      };
      const result = await CopilotMessage.find(condition).sort({ createdAt: -1 }).lean().exec();
      return result;
    } catch (error) {
      logger.warn(`获取用户历史消息失败：${error}`);
      return [];
    }
  }

  /** 查询消息 */
  public async getMessageById(id: string) {
    try {
      const result = await CopilotMessage.findById(id).lean().exec();
      return result;
    } catch (error) {
      logger.warn(`获取消息失败：${error}`);
      return null;
    }
  }
}

export const messageService = new MessageService();
