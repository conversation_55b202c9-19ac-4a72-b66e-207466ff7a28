import { CommandActionIntents, YUAN } from "@/copilot/constants";
import { IQuoteProductData, IQuoteResponseItem } from "../interface";
import { createGotoTireQuoteDetailAction } from "./utils";
import { $stringify, $xml } from "@casstime/copilot-xml";
import { ICommandAction } from "@casstime/copilot-core";
import { useRequestMeta } from "@/common/asyncStore";
import { VersionEnum } from "@/common/enums";
import _ from "lodash";

/**
 *
 * @param list
 * @param demandId
 * @returns 轮胎报价卡片
 */
export function createTyreQuoteCard(list: IQuoteResponseItem[], demandId: string): string {
  if (!list.length) return "";
  const { appVersion } = useRequestMeta();
  const supportTyrePurchase = !appVersion?.isLessThan(VersionEnum.SIX_2_0);
  const action = createGotoTireQuoteDetailAction(demandId);
  // 前往采购
  const gotoTireQuoteDetailButton = $stringify({
    type: "view",
    props: { style: { display: supportTyrePurchase ? "none" : "flex", alignItems: "flex-end" } },
    children: [
      {
        type: "button",
        props: { action, style: { borderColor: "#E51E1E" }, textStyle: { color: "#E51E1E" } },
        children: [{ type: "literal", value: "前往采购" }],
      },
    ],
  });

  const listView = list
    .filter((item) => item.quotationProductList?.length)
    .slice(0, 1)
    .map((item) => {
      const { partsNumber, quotationProductList = [] } = item;
      return `
      <view>
        <text style="font-weight: bold;font-size: 30;">${partsNumber}</text>
        ${quotationProductList
          .slice(0, 3)
          .map((quote, index) => createTyreQuoteItemCard(quote, index, supportTyrePurchase))
          .join(" ")}
      </view>`;
    });

  return `
    <view>
      ${listView.join("\n")}
      ${gotoTireQuoteDetailButton}
    </view>
  `;
}

/**
 *
 * @param quote 报价条目
 * @param index 序号
 * @param supportTyrePurchase 是否显示一键采购按钮
 * @returns 轮胎报价条目卡片
 */
function createTyreQuoteItemCard(quote: IQuoteProductData, index: number, supportTyrePurchase = false): string {
  if (!quote) return "";
  const {
    partsName,
    btPrice,
    organization = {},
    quoteStatus,
    quotationProductId,
    locationId,
    demandId,
    sourceId,
    price,
    quantity,
    promotions,
  } = quote;
  const { organizationId, organizationName } = organization;
  const outOfStock = quoteStatus === "outOfStock";
  const title = outOfStock ? "缺货" : partsName;
  const showPrice = outOfStock ? "--" : `${YUAN} ${btPrice}`;
  const display = outOfStock || !supportTyrePurchase ? "none" : "flex";
  const action = {
    type: "command",
    text: "一键采购",
    command: CommandActionIntents.IMMEDIATE_PURCHASE_TYRE,
    params: {
      facilityId: locationId,
      demandId,
      openInvoiceType: false,
      quotationProductId,
      quantity,
      storeId: organizationId,
      sourceId,
      price,
      promotions,
    },
  } as ICommandAction;
  return $stringify({
    type: "view",
    props: { style: { flexDirection: "row" } },
    children: [
      {
        type: "text", // 1-序目
        props: { style: { fontSize: 24, fontWeight: "bold" } },
        children: [{ type: "literal", value: `${index + 1}.` }],
      },
      {
        type: "view", // 2-报价条目
        props: { style: { flexDirection: "column", width: "100%" } },
        children: [
          {
            type: "text", //2.1-条目
            props: { style: { fontSize: 24, fontWeight: "bold" } },
            children: [{ type: "literal", value: title! }],
          },
          {
            type: "text", // 2.2-店铺
            props: { style: { fontSize: 24 } },
            children: [{ type: "literal", value: `店铺：${organizationName}` }],
          },
          {
            type: "view", // 2.3价格
            props: { style: { flexDirection: "row" } },
            children: [
              {
                type: "text",
                props: { style: { fontSize: 24 } },
                children: [{ type: "literal", value: "价格：" }],
              },
              {
                type: "text",
                props: { style: { fontSize: 24, fontWeight: "bold", color: "#E51E1E" } },
                children: [{ type: "literal", value: showPrice }],
              },
            ],
          },
          // 2.4-一键采购
          ...$xml`<form
          style="display:${display};flex-direction:row;justify-content:flex-end;align-items:center;"
          action="${_.escape(JSON.stringify(action))}"
          >
            <spinbox 
            name="quantity" 
            max="99" 
            errMsg="最多选择99条"
            >
              ${String(quantity || 1)}
            </spinbox>
            <submit
            textStyle="color: #E51E1E;"
            style="border-color: #E51E1E;margin-left:20;"
            >
            一键采购
            </submit>
          </form>`,
        ],
      },
    ],
  });
}
