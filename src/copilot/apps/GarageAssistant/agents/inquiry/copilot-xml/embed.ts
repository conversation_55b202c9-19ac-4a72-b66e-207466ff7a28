import { $stringify, $xml, IXmlNode } from "@casstime/copilot-xml";
import { IIntelligentPlan, IntelligentPlanType, LevelIconRatio } from "../interface/IIntelligentPlan";
import { AppName, commandIntents, IMTeamId } from "@/copilot/constants";
import { ICommandAction } from "@casstime/copilot-core";
import { IInquiryInfo, IMerchantClue, IStoreInfo } from "../interface";
import dayjs from "dayjs";
import { createGotoQuotationResultAction, getMerchantClue } from "./utils";
import { QuoteCard } from "./components/QuoteCard";
import { InquiryQuoteTabs } from "../enum";
import { IPlanQuoteItem } from "@/clients/quote/interface";
import { INeedDecodeItem } from "@/service/interface";
import { PurchasePlanFeatureEnum } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/interfaces/plan";
import { createViewDetailAction } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/richtext/action";
import { ButtonText } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/richtext/action/interface";
import { useRequestMeta } from "@/common/asyncStore";
import { VersionEnum } from "@/common/enums";
import { renderRichtext } from "@/copilot/richtext";
import { QuotePurchaseButton } from "../../richtext/components/IntelligentPlan/QuotePurchaseButton";

const MerchantServiceIcon = "https://cass-upload.oss-cn-shenzhen.aliyuncs.com/copilot/production/merchant.png";
const A9tAirforceEnterIcon = "https://cass-upload.oss-cn-shenzhen.aliyuncs.com/copilot/production/a9tAirforceEnter.png";

// 暂无方案报价
export function createEmptyQuoteEmbed(inquiryDetail: IInquiryInfo, isA9tAirforceEnter: boolean) {
  isA9tAirforceEnter = false;
  const { inquiryId = "" } = inquiryDetail;
  const merchantClue = getMerchantClue(inquiryDetail);
  const action = createGotoQuotationResultAction(inquiryId, 1);

  const inquiryHeaderView = createInquiryHeaderCard(inquiryDetail);
  const gotoQuoteDetailView = $stringify({
    type: "view",
    props: { style: { alignItems: "flex-end" } },
    children: [
      {
        type: "button",
        props: { action },
        children: [{ type: "literal", value: "查看单据详情" }],
      },
    ],
  });

  const a9tAirforceEnterView = isA9tAirforceEnter ? createA9tAirforceEnterCard(merchantClue) : "";

  const emptyQuoteView = $stringify({
    type: "view",
    props: { style: { backgroundColor: "#F7F8FA", borderRadius: 16, padding: 20 } },
    children: $xml`${inquiryHeaderView}${gotoQuoteDetailView}`,
  });

  return {
    type: "richtext",
    content: [emptyQuoteView, a9tAirforceEnterView].filter(Boolean).join(`<view></view>`),
  };
}
// 智能报价方案报价
export function createIntelligentPlanEmbed({
  intelligentPlans,
  inquiryDetail,
  needDecodeList,
  isA9tAirforceEnter,
  noInquiryHeader = false,
  disableAction = false,
  appName = AppName.AIPurchasePlanAssistant,
  planGroupId,
}: {
  intelligentPlans: IIntelligentPlan[];
  inquiryDetail: IInquiryInfo;
  needDecodeList: INeedDecodeItem[];
  isA9tAirforceEnter: boolean;
  noInquiryHeader?: boolean;
  disableAction?: boolean;
  appName?: AppName;
  planGroupId: string;
}) {
  isA9tAirforceEnter = false;

  // 补充缺货需求展示
  const plans = intelligentPlans.map((plan) => {
    const { quotes } = plan;
    const newQuotes: Partial<IPlanQuoteItem>[] = needDecodeList.map((item) => {
      const { decodeResultId, needsName, partsName = "" } = item;
      const quote = quotes?.find((q) => q.standardItemId === decodeResultId);
      if (!quote) {
        return { needsName, partsName };
      }
      return quote;
    });

    return { ...plan, quotes: newQuotes };
  });

  // 跳转商家客服参数
  const merchantClue = getMerchantClue(inquiryDetail);
  const inquiryHeaderView = noInquiryHeader ? "" : createInquiryHeaderCard(inquiryDetail);
  const plansView = plans
    .map((plan) => {
      const { type } = plan;
      switch (type) {
        case IntelligentPlanType.YUN_FACILITY:
          return createYunFacilityPlan({
            intelligentPlan: plan,
            merchantClue,
            appName,
            planGroupId,
          });
        case IntelligentPlanType.COLLECT:
          return createCollectPlan({
            intelligentPlan: plan,
            merchantClue,
            appName,
            planGroupId,
          });
        case IntelligentPlanType.RECOMMENDED_STORE:
          return createRecommendStorePlan(plan, merchantClue);
        default:
          return createComprehensivePlan({
            intelligentPlan: plan,
            merchantClue,
            appName,
            planGroupId,
          });
      }
    })
    .join(`<hr style="margin-vertical: 20;"></hr>`);
  const checkMoreView = appName === AppName.AIPurchasePlanAssistant ? null : createCheckMoreCard(inquiryDetail.inquiryId || "");

  let quoteMessageView = `<view style="background-color: #F7F8FA; border-radius: 16;padding: 20;">${[
    inquiryHeaderView,
    plansView,
    checkMoreView,
  ]
    .filter(Boolean)
    .join(`<hr style="margin-vertical: 20;"></hr>`)}</view>`;
  const a9tAirforceEnterView = isA9tAirforceEnter ? createA9tAirforceEnterCard(merchantClue) : "";

  // 哨兵模式不支持点击跳转
  if (disableAction) {
    quoteMessageView = quoteMessageView.replace(/action="[^"]*"\s*/g, 'action="{}" ');
  }

  return {
    type: "richtext",
    content: [quoteMessageView, a9tAirforceEnterView].filter(Boolean).join(`<view style="height: 24;"></view>`),
  };
}

// 专属客服入口卡片
function createA9tAirforceEnterCard(merchantClue: IMerchantClue) {
  const query = {
    teamId: IMTeamId.CASS_SERVICE, // 开思客服
    clue: merchantClue,
    referer: "AIChatScreen",
  };
  const navigate = `cassapp://route/native/im/conversation?query=${JSON.stringify(query)}`;
  const action = {
    type: "command",
    text: "专属客服",
    command: commandIntents.commonNavigate,
    params: { navigate },
  } as ICommandAction;
  return $stringify({
    type: "view",
    props: { style: {}, action },
    children: [
      {
        type: "image",
        props: { uri: A9tAirforceEnterIcon, style: { position: "relative", width: 580, height: 189 } },
      },
    ],
  });
}

// 询价信息卡片
function createInquiryHeaderCard(inquiryDetail: IInquiryInfo) {
  const {
    brandLogo = "",
    saleModelName,
    carModelName = "",
    vin = "",
    needsNames = [],
    inquiryId = "",
    createdStamp,
    createdName,
  } = inquiryDetail;
  // 车型卡片
  const carModelView = $stringify({
    type: "view",
    props: { style: { flexDirection: "row", alignItems: "center" } },
    children: [
      { type: "image", props: { uri: brandLogo, style: { width: 72, height: 72, marginRight: 20 } } },
      {
        type: "view",
        props: { style: { flex: 1, marginRight: 10 } },
        children: [
          {
            type: "text",
            props: { style: { fontWeight: "bold" } },
            children: [{ type: "literal", value: saleModelName || carModelName }],
          },
          {
            type: "text",
            children: [{ type: "literal", value: vin }],
          },
        ],
      },
    ],
  });
  const action = createGotoQuotationResultAction(inquiryId);
  const inquiryView = $stringify({
    type: "view",
    props: { action },
    children: [
      {
        type: "text",
        props: { style: { fontWeight: "bold", fontSize: 30 }, numberOfLines: 2 },
        children: [{ type: "literal", value: needsNames.join("、") }],
      },
      {
        type: "text",
        children: [{ type: "literal", value: `询价单号: ${inquiryId}` }],
      },
      {
        type: "text",
        children: [{ type: "literal", value: `询价时间: ${dayjs(createdStamp).format("YYYY-MM-DD HH:mm")}` }],
      },
      {
        type: "text",
        children: [{ type: "literal", value: `询价人: ${createdName}` }],
      },
    ],
  });
  return `<view>${carModelView}${inquiryView}</view>`;
}

// 查看更多卡片
function createCheckMoreCard(inquiryId: string) {
  const query = {
    inquiryId,
    fromScreen: "AIChatScreen",
    pageIndex: InquiryQuoteTabs.STORE,
  };
  const navigate = `cassapp://route/native/inquiry/quotationResult?query=${JSON.stringify(query)}`;
  return $stringify({
    type: "view",
    props: { style: { flexDirection: "row", alignItems: "center", marginLeft: 24, flexWrap: "wrap" } },
    children: [
      {
        type: "text",
        props: { style: { color: "#646566" } },
        children: [{ type: "literal", value: "如果想看更多报价，可点击" }],
      },
      {
        type: "link",
        props: { style: { marginLeft: 10 }, to: navigate },
        children: [{ type: "literal", value: "查看更多" }],
      },
    ],
  });
}

// 云仓方案
function createYunFacilityPlan({
  intelligentPlan,
  merchantClue,
  appName,
  planGroupId,
}: {
  intelligentPlan: IIntelligentPlan;
  merchantClue: IMerchantClue;
  appName?: AppName;
  planGroupId: string;
}) {
  const {
    storeName = "",
    storeId = "",
    facilityName = "",
    packagePrice = 0,
    defaultEta,
    quotes = [],
    inquiryId = "",
    type,
    name = "",
    _id,
  } = intelligentPlan;
  const storeInfo = { storeName, storeId, type };
  const quotationProductIds = quotes.map((quote) => quote.quotationProductId || "").filter(Boolean);
  const facilityView = facilityName
    ? `<text style="color: #32B418;font-weight: bold;font-size: 30">${facilityName}</text>`
    : "";
  const storeView = storeName ? createStoreCard(merchantClue, storeInfo) : "";
  const defaultEtaView = defaultEta ? `<text style="font-size: 24;">${defaultEta}</text>` : "";
  const reasonView = name ? `<text>【${name}】</text>` : "";
  const quotesView = createQuoteCard(quotes, merchantClue, IntelligentPlanType.YUN_FACILITY, packagePrice);
  const quoteConfirmButton = createQuotePurchaseButtonCard({
    quotationProductIds,
    inquiryId,
    pageIndex: 2,
    storeId,
    appName,
    planGroupId,
    planId: _id?.toString() || '',
  });
  const content = [facilityView, storeView, defaultEtaView, reasonView, quotesView, quoteConfirmButton]
    .filter(Boolean)
    .join("");
  return `<view>${content}</view>`;
}

// 整单方案
function createCollectPlan({
  intelligentPlan,
  merchantClue,
  appName,
  planGroupId,
}: {
  intelligentPlan: IIntelligentPlan;
  merchantClue: IMerchantClue;
  appName?: AppName
  planGroupId: string;
}) {
  const {
    storeName = "",
    storeId = "",
    deliveryWarehouse = "",
    packagePrice = 0,
    iconUri,
    levelValue,
    name,
    quotes = [],
    type,
    features,
    _id,
  } = intelligentPlan;
  const storeInfo = { storeName, storeId, iconUri, levelValue, type };
  const { id: inquiryId } = merchantClue;
  const quotationProductIds = quotes.map((quote) => quote.quotationProductId || "").filter(Boolean);
  const storeView = createStoreCard(merchantClue, storeInfo);
  const deliveryWarehouseView = deliveryWarehouse ? `<text style="font-size: 24;">${deliveryWarehouse}</text>` : "";
  const wholePlan = features?.[0] === PurchasePlanFeatureEnum.WHOLE_DISTRIBUTE;
  const reasonView = name && !wholePlan ? `<text>【${name}】</text>` : "";
  const quotesView = createQuoteCard(quotes, merchantClue, IntelligentPlanType.COLLECT, packagePrice);
  const quoteConfirmButton = createQuotePurchaseButtonCard({
    quotationProductIds,
    inquiryId,
    pageIndex: 1,
    storeId,
    appName,
    planGroupId,
    planId: _id?.toString() || '',
  });
  const content = [storeView, deliveryWarehouseView, reasonView, quotesView, quoteConfirmButton]
    .filter(Boolean)
    .join("");
  return `<view>${content}</view>`;
}

// 推荐店铺方案
function createRecommendStorePlan(intelligentPlan: IIntelligentPlan, merchantClue: IMerchantClue) {
  const { storeName = "", storeId = "", iconUri, levelValue, name, type } = intelligentPlan;
  const storeInfo = { storeName, storeId, iconUri, levelValue, type };
  const storeView = createStoreCard(merchantClue, storeInfo);
  const reasonView = name ? `<text>【${name}】</text>` : "";
  const quotesView = `<text style="font-size: 28;">-报价中...</text>`;
  const content = [storeView, reasonView, quotesView].filter(Boolean).join("");
  return `<view>${content}</view>`;
}

// 综合方案
function createComprehensivePlan({
  intelligentPlan,
  merchantClue,
  appName,
  planGroupId,
}: {
  intelligentPlan: IIntelligentPlan;
  merchantClue: IMerchantClue;
  appName?: AppName;
  planGroupId: string;
}) {
  const {
    storeName = "",
    storeId = "",
    facilityName = "",
    packagePrice = 0,
    defaultEta,
    name,
    quotes = [],
    inquiryId = "",
    type,
    features,
    _id,
  } = intelligentPlan;
  const storeInfo = { storeName, storeId, type };
  const quotationProductIds = quotes.map((quote) => quote.quotationProductId || "").filter(Boolean);
  const facilityView = facilityName
    ? `<text style="color: #32B418;font-weight: bold;font-size: 30">${facilityName}</text>`
    : "";
  const storeView = storeName
    ? createStoreCard(merchantClue, storeInfo)
    : `<text style="color: #008CF5;font-weight: bold;font-size: 30" >多商家方案</text>`;
  const defaultEtaView = defaultEta ? `<text style="font-size: 24;">${defaultEta}</text>` : "";
  const reasonView = name && features?.length ? `<text>【${name}】</text>` : "";
  const quotesView = createQuoteCard(quotes, merchantClue, IntelligentPlanType.COMPREHENSIVE, packagePrice);
  const quoteConfirmButton = createQuotePurchaseButtonCard({
    quotationProductIds,
    inquiryId,
    pageIndex: 2,
    storeId,
    appName,
    planId: _id?.toString() || '',
    planGroupId,
  });
  const content = [facilityView, storeView, defaultEtaView, reasonView, quotesView, quoteConfirmButton]
    .filter(Boolean)
    .join("");
  return `<view>${content}</view>`;
}

// 店铺卡片
function createStoreCard(merchantClue: IMerchantClue, storeInfo: IStoreInfo) {
  const { storeId, storeName, iconUri, levelValue, type } = storeInfo;
  const query = {
    storeId,
    clue: merchantClue,
  };
  const navigate = `cassapp://route/native/im/conversation?query=${JSON.stringify(query)}`;
  // 跳转商家按钮
  const action = {
    type: "command",
    text: storeName,
    command: commandIntents.commonNavigate,
    params: { navigate },
  } as ICommandAction;
  const children: IXmlNode[] = [
    {
      type: "text",
      props: { style: { color: "#008CF5", fontSize: type === IntelligentPlanType.YUN_FACILITY ? 24 : 30 } },
      children: [{ type: "literal", value: storeName }],
    },
  ];
  // 店铺等级(可选项)
  if (iconUri) {
    children.push({
      type: "image",
      props: {
        uri: iconUri,
        style: { height: 25, width: 25 * LevelIconRatio[levelValue || 1], marginLeft: 12 },
      },
    });
  }
  return $stringify({
    type: "view",
    props: {
      style: { flexDirection: "row", justifyContent: "space-between" },
    },
    children: [
      {
        type: "view",
        props: { style: { flexDirection: "row", alignItems: "center", flexWrap: "wrap", maxWidth: 360 }, action },
        children,
      },
      {
        type: "view",
        props: {
          style: {
            flexDirection: "row",
            alignItems: "center",
            maxHeight: 40,
            paddingLeft: 16,
            paddingRight: 16,
            borderRadius: 20,
            borderWidth: 1,
            borderColor: "#C8C9CC",
          },
          action,
        },
        children: [
          {
            type: "image",
            props: { uri: MerchantServiceIcon, style: { width: 28, height: 28, marginRight: 6 } },
          },
          {
            type: "text",
            props: { style: { fontSize: 24, lineHeight: 40, color: "#008CF5" } },
            children: [{ type: "literal", value: "沟通报价" }],
          },
        ],
      },
    ],
  });
}

// 报价卡片
function createQuoteCard(
  quotes: Partial<IPlanQuoteItem>[],
  merchantClue: IMerchantClue,
  type: IntelligentPlanType,
  packagePrice?: number
) {
  return new QuoteCard({ quotes, merchantClue, type, packagePrice }).render();
}

// 跳转报价或者采购确认页按钮卡片
function createQuotePurchaseButtonCard({
  quotationProductIds,
  inquiryId,
  pageIndex,
  storeId,
  appName,
  planId,
  planGroupId,
}: {
  quotationProductIds: string[],
  inquiryId: string,
  pageIndex: number,
  storeId: string,
  appName?: AppName,
  planId: string;
  planGroupId: string;
}) {

  const { appVersion } = useRequestMeta();
  const noSupportPurchaseModal = appVersion?.isLessThan(VersionEnum.SIX_7_0);

  let purchaseAction = createViewDetailAction({
    planGroupId,
    planId,
    inquiryId,
    text: ButtonText.GO_TO_PURCHASE,
    isChecked: true,
  }) as ICommandAction;
  const purchaseDetailAction = createViewDetailAction({
    planGroupId,
    planId,
    inquiryId,
    text: ButtonText.GO_TO_PURCHASE,
    isChecked: false,
  }) as ICommandAction;
  const quoteAction = createGotoQuotationResultAction(inquiryId, pageIndex, storeId);

  if ((noSupportPurchaseModal || !planId) && appName === AppName.GarageAssistant) {
    purchaseAction = {
      type: "command",
      text: ButtonText.GO_TO_PURCHASE,
      command: commandIntents.IMMEDIATE_PURCHASE,
      params: { inquiryId, quotationProductIds },
    } as ICommandAction;
  }

  return renderRichtext(QuotePurchaseButton, {
    purchaseAction,
    purchaseText: ButtonText.GO_TO_PURCHASE,
    detailAction: appName === AppName.AIPurchasePlanAssistant ? purchaseDetailAction : quoteAction,
    detailText: appName === AppName.AIPurchasePlanAssistant ? '查看详情' : "查看商家更多报价"
  })
}
