import { ICommandAction } from "@casstime/copilot-core";
import { IInquiryInfo, IMerchantClue } from "../interface";
import { CommandActionIntents, commandIntents } from "@/copilot/constants";
import { InquiryQuoteTabs } from "../enum";

export function getMerchantClue(inquiryDetail: IInquiryInfo): IMerchantClue {
  const {
    inquiryId = "",
    carModelName = "",
    saleModelName = "",
    carBrandId = "",
    vin = "",
    statusId = "",
    createdName = "",
    createdStamp = new Date().getTime(),
    needsNames,
  } = inquiryDetail;
  // 跳转商家客服参数
  return {
    type: "INQUIRY",
    id: inquiryId,
    data: {
      carModelName: carModelName || saleModelName,
      carBrandId,
      inquiryId,
      vin,
      status: statusId, // 询价单状态（中文）
      userName: createdName, // 发布人
      createdStamp,
      needsName: needsNames?.join("、") || "", // 需求拼接
      id: inquiryId, // 询价单 id
    },
  };
}

export function createGotoQuotationResultAction(
  inquiryId: string,
  pageIndex = InquiryQuoteTabs.PARTNAME,
  storeId?: string
): ICommandAction {
  const query = {
    inquiryId,
    storeId,
    fromScreen: "AIChatScreen",
    pageIndex,
  };
  const navigate = `cassapp://route/native/inquiry/quotationResult?query=${JSON.stringify(query)}`;
  return {
    type: "command",
    text: inquiryId,
    command: commandIntents.commonNavigate,
    params: { navigate },
  };
}

export function createGotoTireQuoteDetailAction(inquiryId: string): ICommandAction {
  const query = {
    inquiryId,
    fromScreen: "AIChatScreen",
  };
  const navigate = `cassapp://route/rn/TireQuoteDetail?query=${JSON.stringify(query)}`;
  return {
    type: "command",
    text: inquiryId,
    command: commandIntents.commonNavigate,
    params: { navigate },
  };
}

export function createGotoEpcScreenAction(inquiryId: string): ICommandAction {
  return {
    type: "command",
    text: CommandActionIntents.GO_TO_EPC,
    command: CommandActionIntents.GO_TO_EPC,
    params: { inquiryId },
  };
}
