import { $props, $stringify, ITextNode } from "@casstime/copilot-xml";
import { IMerchantClue, IntelligentPlanType } from "../../interface";
import { XMLComponent } from "./XMLComponent";
import { CustomerServiceLink } from "./CustomerServiceLink";
import { IPlanQuoteItem } from "@/clients/quote/interface";

export interface QuoteItemProps {
  quote: Partial<IPlanQuoteItem>;
  quoteStoreIds: string[];
  quoteDepartureIds: string[];
  merchantClue?: IMerchantClue;
  type?: IntelligentPlanType;
  index: number;
}

export class QuoteItem extends XMLComponent<QuoteItemProps> {
  private generateQuoteName() {
    const { partsName } = this.props.quote;
    // 1-展示报价条目
    return partsName;
  }
  private renderEmpty() {
    const name = this.generateQuoteName();
    const { index } = this.props;
    return $stringify({
      type: "view",
      props: { style: { flexDirection: "row" } },
      children: [
        {
          type: "text",
          props: { style: { fontSize: 28, fontWeight: "bold", marginRight: 8 } },
          children: [{ type: "literal", value: `${index + 1}.` }],
        },
        {
          type: "view",
          props: { style: { flexDirection: "row", alignItems: "center", flexWrap: "wrap" } },
          children: [
            {
              type: "text",
              props: { style: { fontSize: 28, fontWeight: "bold", marginRight: 16 } },
              children: [{ type: "literal", value: `${name}:` }],
            },
            {
              type: "text",
              props: { style: { color: "#979899", fontSize: 28 } },
              children: [{ type: "literal", value: "暂无报价" }],
            },
          ],
        },
      ],
    });
  }

  render() {
    const { quote } = this.props;
    const { price } = quote;

    if (!price) {
      return this.renderEmpty();
    }

    const quoteView = this.renderQuoteView();

    // 2-展示配套品牌/SPD/仓库/店铺等信息
    const furtherInfoView = this.renderFurtherInfoView();

    return `<view>${quoteView}${furtherInfoView}</view>`;
  }

  renderTag(text: string) {
    return `<text ${$props<ITextNode>({
      style: { color: "#FC6405", marginRight: 12, fontSize: 24 },
    })}>${text}</text>`;
  }

  // 私有方法，用于渲染更多信息视图
  private renderFurtherInfoView() {
    // 从props中解构出merchantClue和quote对象
    const { quote } = this.props;
    const { locationName = "", storeName = "", storeId, categoryOriginalAssort, spdFeatureDetail } = quote;

    let furtherInfo = this.renderRemark(categoryOriginalAssort, spdFeatureDetail);

    const { type } = this.props;
    if (type === IntelligentPlanType.YUN_FACILITY) {
      const quotesUniqByStoreId = this.props.quoteStoreIds;
      // 报价店铺数量大于1，非整单
      if (quotesUniqByStoreId.length > 1) {
        furtherInfo += this.renderStoreName(storeId!, storeName);
      }
    } else if (type === IntelligentPlanType.COLLECT) {
      // 是否多仓发货
      const departureIds = this.props.quoteDepartureIds;
      const multiDepartures = new Set(departureIds).size > 1;
      if (multiDepartures) {
        furtherInfo += this.renderDeparture(locationName);
      }
    } else if (type && [(IntelligentPlanType.DEFAULT, IntelligentPlanType.COMPREHENSIVE)].includes(type)) {
      furtherInfo += this.renderStoreName(storeId!, storeName);
      furtherInfo += this.renderDeparture(locationName);
    }
    return `<view ${$props({
      style: { flexDirection: "row", flexWrap: "wrap", marginLeft: 34 },
    })}>${furtherInfo}</view>`;
  }

  private renderDeparture(locationName: string) {
    return $stringify({
      type: "text",
      props: { style: { color: "#979899", fontSize: 24 } },
      children: [{ type: "literal", value: locationName }],
    });
  }

  private renderStoreName(storeId: string, storeName: string) {
    const { merchantClue } = this.props;
    // 跳转商家按钮
    return new CustomerServiceLink({ storeId, storeName, clue: merchantClue }).render();
  }

  private renderRemark(categoryOriginalAssort: boolean | undefined, spdFeatureDetail: string | undefined) {
    const originalAssortView = categoryOriginalAssort ? this.renderTag("配套") : "";

    const spdFeatureDetailView = spdFeatureDetail ? this.renderTag(spdFeatureDetail) : "";

    const furtherInfo = [originalAssortView, spdFeatureDetailView]
      .filter(Boolean)
      .join(`<text style="color: #FC6405;margin-right: 8;">/</text>`);
    return furtherInfo;
  }

  private renderQuoteView() {
    const { index } = this.props;
    const { qualityName = "", showPrice } = this.props.quote;
    // 1-展示报价条目
    const name = this.generateQuoteName();
    return $stringify({
      type: "view",
      props: { style: { flexDirection: "row" } },
      children: [
        {
          type: "text",
          props: { style: { fontSize: 28, fontWeight: "bold", marginRight: 8 } },
          children: [{ type: "literal", value: `${index + 1}.` }],
        },
        {
          type: "view",
          props: { style: { flexDirection: "row", alignItems: "center", flexWrap: "wrap" } },
          children: [
            {
              type: "text",
              props: { style: { fontSize: 28, fontWeight: "bold", marginRight: 16 } },
              children: [{ type: "literal", value: `${name}:` }],
            },
            {
              type: "text",
              props: { style: { fontSize: 28, marginRight: 16 } },
              children: [{ type: "literal", value: qualityName }],
            },
            {
              type: "text",
              props: { style: { fontSize: 28 } },
              children: [
                { type: "literal", value: ` ¥ ${typeof showPrice === "number" ? showPrice.toFixed(2) : "-"}` },
              ],
            },
          ],
        },
      ],
    });
  }
}
