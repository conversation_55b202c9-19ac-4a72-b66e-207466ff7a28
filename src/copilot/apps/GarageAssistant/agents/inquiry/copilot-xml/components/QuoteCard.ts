import { IMerchantClue, IntelligentPlanType } from "../../interface";
import { XMLComponent } from "./XMLComponent";
import { QuoteItem } from "./QuoteItem";
import _ from "lodash";
import { IPlanQuoteItem } from "@/clients/quote/interface";

export interface QuoteCardProps {
  quotes: Partial<IPlanQuoteItem>[];
  merchantClue: IMerchantClue;
  type: IntelligentPlanType;
  packagePrice?: number;
}

export class QuoteCard extends XMLComponent<QuoteCardProps> {
  /**
   * 有整单价格时，渲染整单价格
   * @returns
   */
  renderPackagePriceView() {
    const { packagePrice } = this.props;
    return packagePrice ? `<text>整单价格:  ¥ ${packagePrice.toFixed(2)}</text>` : "";
  }

  render() {
    const { quotes, merchantClue, type } = this.props;
    const content = quotes
      .map((quote, index) => {
        // 最多只展示5条，多余的用...表示
        // if (quotes.length > 5) {
        //   if (index === 4) return `<text style="font-size: 28; fontWeight: bold;margin-left: 36;">...</text>`;
        //   if (index < quotes.length - 1 && index > 4) return "";
        // }
        const quoteDepartureIds = quotes.map((item) => item.location!).filter(Boolean);
        const quoteStoreIds = _.uniqBy(quotes, "storeId")
          .map((item) => item.storeId!)
          .filter(Boolean);
        return new QuoteItem({ quote, index, merchantClue, type, quoteDepartureIds, quoteStoreIds }).render();
      })
      .filter(Boolean)
      .join("");

    return `<view style="margin-left:24;">${this.renderPackagePriceView()}${content}</view>`;
  }
}
