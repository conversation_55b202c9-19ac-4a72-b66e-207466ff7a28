import { commandIntents } from "@/copilot/constants";
import { IMerchantClue } from "../../interface";
import { XMLComponent } from "./XMLComponent";
import { ICommandAction } from "@casstime/copilot-core";
import { $props, IImageNode } from "@casstime/copilot-xml";

export interface CustomerServiceLinkProps {
  storeId: string;
  storeName: string;
  clue?: IMerchantClue;
  icon?: string | XMLComponent;
}
const ICON_MERCHANT_SERVICE = "https://cass-upload.oss-cn-shenzhen.aliyuncs.com/copilot/production/merchant.png";

export class CustomerServiceLink extends XMLComponent<CustomerServiceLinkProps> {
  render() {
    const { storeId, storeName, clue, icon: iconUrl = ICON_MERCHANT_SERVICE } = this.props;
    // 跳转商家按钮
    const query = {
      storeId,
      clue: clue,
    };
    const navigate = `cassapp://route/native/im/conversation?query=${JSON.stringify(query)}`;
    const action = {
      type: "command",
      text: storeName,
      command: commandIntents.commonNavigate,
      params: { navigate },
    } as ICommandAction;
    const icon =
      typeof iconUrl === "string"
        ? `<image ${$props<IImageNode>({ uri: iconUrl, style: { width: 28, height: 28, marginRight: 12 } })} />`
        : iconUrl.render();
    return `
        <view ${$props({ action, style: { flexDirection: "row", alignItems: "center" } })}>
            <text ${$props({ style: { color: "#979899", fontSize: 24, marginRight: 12 } })}>${storeName}</text>
            ${icon}
        </view>
    `;
  }
}
