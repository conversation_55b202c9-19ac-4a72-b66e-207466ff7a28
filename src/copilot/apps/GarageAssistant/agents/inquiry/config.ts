import { AgentName, commandIntents, imageIntents, Intents, NluActionIntents, NluIntents } from "@/copilot/constants";
import { Agent } from "@casstime/copilot-core";
import {
  inquiryHandler,
  inquiryUpdate<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON><PERSON>,
  submit<PERSON><PERSON><PERSON><PERSON><PERSON>,
  inquiry<PERSON>uo<PERSON><PERSON><PERSON><PERSON>,
  immediateIn<PERSON>ry<PERSON><PERSON><PERSON>,
  recognize<PERSON><PERSON><PERSON><PERSON><PERSON>,
  imageClassify<PERSON><PERSON><PERSON>,
  vinI<PERSON><PERSON><PERSON><PERSON>,
  checklist<PERSON><PERSON><PERSON><PERSON><PERSON>,
  tyre<PERSON><PERSON><PERSON><PERSON><PERSON>,
  newInquiry<PERSON>andler,
  confirmInvoice,
  editAddress,
  restart<PERSON>andler,
  editEntities<PERSON>andler,
  reR<PERSON>og<PERSON>ze<PERSON>mage<PERSON><PERSON><PERSON>,
  directAppend<PERSON>arts<PERSON>andler,
  buyT<PERSON><PERSON><PERSON><PERSON>,
  rotate<PERSON><PERSON><PERSON><PERSON><PERSON>,
  quote<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  noVin<PERSON><PERSON><PERSON><PERSON><PERSON>,
  viewOrder<PERSON>andler,
  viewIn<PERSON>ry<PERSON>andler,
  similarPartsImageHandler,
  noSimilartPartsHandler,
  selectedSimilartPartsHandler,
  notShortVin<PERSON><PERSON><PERSON><PERSON><PERSON>,
  InquiryStartHand<PERSON>,
  EpcAppend<PERSON><PERSON>s<PERSON>and<PERSON>,
  scanVI<PERSON><PERSON>and<PERSON>,
} from "./handlers";
import { InquiryIntents } from "./parsers/inquiryIntentClassifier";
import { grantCouponHandler } from "./handlers/grantCouponsHandle";
import { IntentCodes } from "./enum";
import { AccidentTogglePartsHandler } from "./handlers/commands/accidentTogglePartsHandler";
import { noWantPartsHandler } from "./handlers/commands/noWantPartsHandler";
import { AppendPartsHandler } from "./handlers/appendPartsHandler";
import { ReInquiryHandler } from "./handlers/reInquiryHandler";
import { savePlanHandler } from "./handlers/commands/savePlanHandler";

export function configureHandlers(agent: Agent) {
  // 询价私有意图
  agent.handle(`@command/${commandIntents.inquiryStart}`, InquiryStartHandler);
  agent.handle(`@command/${commandIntents.inquiryUpdateField}`, inquiryUpdateFieldHandler);

  agent.handle(`@command/${commandIntents.inquiryPressSubmitForm}`, validateFormHandler);
  agent.handle(`@command/${commandIntents.inquirySubmitForm}`, submitFormHandler);
  agent.handle(`@command/${commandIntents.inquiryQuote}`, inquiryQuoteHandler);

  agent.handle(`@command/${commandIntents.DIRECT_APPEND_PARTS}`, directAppendPartsHandler);

  agent.handleCommand(commandIntents.VIEW_ORDER, viewOrderHandler);

  agent.handleCommand(commandIntents.VIEW_INQUIRY, viewInquiryHandler);

  // 图片处理私有意图
  agent.handle(
    [imageIntents.classifyImage, imageIntents.tyreImage, imageIntents.checklistImage],
    recognizeImageHandler
  );
  agent.handle(`@command/${commandIntents.recognizeImage}`, imageClassifyHandler);
  agent.handle(`@command/${commandIntents.recognizeVinImage}`, vinImageHandler);
  agent.handle(`@command/${commandIntents.recognizeChecklistImage}`, checklistImageHandler);
  agent.handle(`@command/${commandIntents.recognizeTyreImage}`, tyreImageHandler);

  // 重新识别图片
  agent.handle(commandIntents.reRecognizeImage, reRecognizeImageHandler);

  // 旋转图片
  agent.handle(NluActionIntents.ROTATE_IMAGE, rotateImageHandler);

  // 相似配件识别
  agent.handle(NluActionIntents.IMAGE_REC_PARTS_SIMILAR_NO, noSimilartPartsHandler);

  agent.handle(NluActionIntents.APPEND_PARTS, AppendPartsHandler);
  agent.handle(NluActionIntents.RE_INQUIRY, ReInquiryHandler);

  agent.handleCommand(commandIntents.IMAGE_REC_PARTS_SIMILAR, similarPartsImageHandler);
  agent.handleCommand(commandIntents.IMAGE_REC_PARTS_SIMILAR_SELECTED, selectedSimilartPartsHandler);

  /**
   * 处理询价意图
   */

  agent.handle(commandIntents.IMMEDIATE_INQUIRY, immediateInquiryHandler);

  agent.handle(
    [Intents.inquiry, InquiryIntents.询报价, InquiryIntents.输入车架号, InquiryIntents.买配件],
    inquiryHandler
  );

  agent.handle(InquiryIntents.买轮胎, buyTyreHandler);

  agent.handle(Intents.newInquiry, newInquiryHandler);

  agent.handle(InquiryIntents.修改开票信息, confirmInvoice);

  agent.handle(InquiryIntents.修改地址, editAddress);

  agent.handle(InquiryIntents.重新开始, restartHandler);

  agent.handle([InquiryIntents.修改品质, InquiryIntents.修改配件信息], editEntitiesHandler);

  agent.handle(InquiryIntents.领福利, grantCouponHandler);

  agent.handle(IntentCodes.QUOTE_FILTER, quoteFilterHandler);

  agent.handle(IntentCodes.无车架号, noVinCodeHandler);

  agent.handle(NluActionIntents.NOT_SHORT_VINCODE, notShortVinCodeHandler);

  /**
   * 不支持的场景，给其他Agent处理
   */
  agent.handle(InquiryIntents.其他, async (context) => {
    return context.reRoute();
  });

  /**
   * 商品推荐逻辑
   */
  agent.handle(`@command/${commandIntents.productRecommend}`, async (context) => {
    return context.routeTo(AgentName.productAgent, {
      intent: "油品推荐",
    });
  });

  /** 事故场景推荐配件勾选 */
  agent.handleCommand(commandIntents.ACCIDENT_TOGGLE_PARTS, AccidentTogglePartsHandler);

  /** 没有想要的配件 */
  agent.handle(NluActionIntents.NO_WANT_PARTS, noWantPartsHandler);

  /** 保存方案 */
  agent.handle(NluActionIntents.INQUIRY_PLAN_SAVE, savePlanHandler);

  /** epc追加配件 */
  agent.handle(NluIntents.EPC_APPEND_PARTS, EpcAppendPartsHandler);

  /** 扫VIN码 */
  agent.handle(NluIntents.SCAN_VIN, scanVINHandler);
}
