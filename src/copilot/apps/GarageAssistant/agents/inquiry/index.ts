import { Agent, Context, ITextMessage } from "@casstime/copilot-core";
import { parseEntities, parseIntent } from "./parsers";
import logger from "@/common/logger";
import { partNamesSlot, qualitySlot, vinCodeSlot } from "./slots";
import { FormNames, inquiryForm, quoteFilterForm } from "./forms";
import { SlotNames } from "./slots/SlotNames";
import { configureHandlers } from "./config";
import { AgentName } from "@/copilot/constants";
import { inquirySubmitClassifier } from "./classifiers";
import { quoteFilterClassifier } from "./classifiers/quoteFilterClassifier";
import { intentClassifier } from "./classifiers/intentClassifier";
import { selectActionClassifier } from "./classifiers/selectActionClassifier";

/**
 * 发布询价Agent
 */
const agent = new Agent(AgentName.inquiryAgent);

agent.registerSlot(SlotNames.vinCode, vinCodeSlot);
agent.registerSlot(SlotNames.partNames, partNamesSlot);
agent.registerSlot(SlotNames.qualities, qualitySlot);
agent.registerSlot(SlotNames.address, { lifetime: "forever" });
agent.registerSlot(SlotNames.invoice, { lifetime: "forever" });
agent.registerSlot(SlotNames.user, { lifetime: "forever" });
agent.registerSlot(SlotNames.source, { lifetime: "forever" });
agent.registerSlot(SlotNames.fromPage, { lifetime: "forever" });
agent.registerSlot(SlotNames.operator, { lifetime: "forever" });
agent.registerSlot(SlotNames.isProxyInquiry, { lifetime: "forever" });

agent.registerForm(FormNames.inquiryForm, inquiryForm);
agent.registerForm(FormNames.quoteFilterForm, quoteFilterForm);

agent.registerIntentClassifier(intentClassifier, true);
agent.registerIntentClassifier(inquirySubmitClassifier, false);
agent.registerIntentClassifier(quoteFilterClassifier, false);
agent.registerIntentClassifier(selectActionClassifier, false);
/**
 * 第一次进入Agent
 */
agent.onEnter((context) => {
  logger.info("进入询价Agent");
});

/**
 * 离开当前Agent
 */
agent.onLeave((context) => {
  logger.info("离开询价Agent");
});

/**
 * 设置实体解析器
 */
agent.setEntitiesParser(async (context) => {
  const entities = await parseEntities(context);
  context.setEntities(entities);
});

function isOil(context: Context) {
  // 只判断文本消息
  if (context.lastMessage.type !== "text") {
    return false;
  }

  // 多个实体
  const entities = context.entities.filter((e) => e.name === "partName");
  if (entities.length > 1 || context.slots[SlotNames.partNames]?.length > 0) {
    return false;
  }

  // 带VIN码
  if (context.slots[SlotNames.vinCode]) {
    return false;
  }

  const content = context.lastMessage.content;
  const entityValue: string = entities[0]?.value || "";
  return entityValue.endsWith("机油") || /机油|\dW-\d+/.test(content);
}

configureHandlers(agent);

export default agent;
