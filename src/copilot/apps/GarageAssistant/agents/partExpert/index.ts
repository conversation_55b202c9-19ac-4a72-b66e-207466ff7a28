import { Agent<PERSON>ame, AI_DISCLAIMER } from "@/copilot/constants";
import { Agent, MessageFactory } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { Types } from "mongoose";
import FastGptApi, { ResponseEvent } from "@/common/clients/FastGptApi";
import config from "@casstime/config";
import { PartExpertIntents } from "./intent";
import { ActionFactory } from "@/common/factory/ActionFactory";
import { InquiryIntents } from "../inquiry/parsers/inquiryIntentClassifier";
import { parseEntities } from "../inquiry/parsers";
import { extractVinCodeEntities } from "@/copilot/helpers";
import { inquiryClient } from "../inquiry/clients/InquiryClient";

/**
 * Agent
 */
const agent = new Agent(AgentName.partExpert);

// 配置 fastgpt
const fastGptApi = new FastGptApi({
  baseUrl: config.get("FASTGPT_BASE_URL"),
  apiKey: config.get("FASTGPT_PARTEXPERTFAQ_KEY"),
});

/**
 * 第一次进入Agent
 */
agent.onEnter(() => {
  logger.info("进入 partExpertAgent");
});

/**
 * 离开当前Agent
 */
agent.onLeave(() => {
  logger.info("离开 partExpertAgent");
});

/**
 * 设置意图解析器
 */
agent.setIntentParser(async (context) => {
  context.setIntent(PartExpertIntents.问答);
});

agent.handle(PartExpertIntents.问答, async (context) => {
  const { lastMessage, sessionId } = context;
  const nextAgent = context.prevMessageAgent === agent.name ? null : context.prevMessageAgent || AgentName.inquiryAgent;
  if (lastMessage.type === "text" || lastMessage.type === "voice") {
    const messageId = new Types.ObjectId().toString();
    const factory = MessageFactory.with({ id: messageId });
    let query = lastMessage.content!;
    const tokens: string[] = [];
    context.setReplyMode("stream");
    const entities = await parseEntities(context);
    const doSend = async (ev: ResponseEvent) => {
      console.log("ev_result->", JSON.stringify(ev));
      if (ev.event === "message" && ev.answer) {
        tokens.push(ev.answer);
      }
      if (tokens.length > 2) {
        context.reply(factory.markdown(tokens.join("")));
      }
      if (ev.event === "message_end") {
        context.reply(
          factory.markdown(tokens.join(""), {
            disclaimer: AI_DISCLAIMER,
            actions: [
              [
                ActionFactory.nlu("去询价", {
                  agentName: AgentName.inquiryAgent,
                  entities: entities,
                  intent: InquiryIntents.询报价,
                }),
              ],
            ],
          })
        );
      }
    };

    const prevModel = context.slots["model"];
    const memory = await context.getAgentMemory(AgentName.inquiryAgent);
    const model = memory.slots["carModel"]?.model || memory.slots["carModel"]?.modelName;

    if (model && prevModel !== model) {
      context.mergeSlots({ model });
      query = `车型: ${model}\n${query}`;
    }
    if (!model) {
      const vinCode = entities.find((entity) => entity.name === "vinCode")?.value;
      if (vinCode) {
        // (2)查询车型信息~
        try {
          const { list: carModelNameRes } = await inquiryClient.getVinCarModel(vinCode || "");
          if (carModelNameRes?.length) {
            const carModel = carModelNameRes[0].model;
            if (carModel) {
              query = `${query}\nPS: 车架号 ${vinCode} 对应的车型是 ${carModel}`;
              context.mergeSlots({ model: carModel });
            }
          }
        } catch (err) {
          logger.error("配套专家获取车型信息失败", err);
        }
      }
    }
    logger.info("配套专家获取车型信息", query);
    context.reply(factory.text("请稍候..."));
    const interval = setInterval(() => {
      context.reply(factory.text("正在思考中..."));
    }, 1000 * 3);
    try {
      await fastGptApi.sendChatMessage(
        {
          inputs: {},
          query: query,
          platform: "fastgptChat",
          conversationId: sessionId,
          user: lastMessage.fromUser!,
        },
        (event) => {
          if (event.event === "message" && event.answer) {
            clearInterval(interval);
          }
          doSend(event);
        }
      );
    } catch (error) {
      logger.warn(`fastGptApi-partExpertAgent 出错：${error}`);
    } finally {
      clearInterval(interval);
    }
    if (tokens.join("") === "") {
      context.reply(factory.text("服务器繁忙，请稍后再试"));
    }
  }
  await context.activateAgent(nextAgent);
});

export default agent;
