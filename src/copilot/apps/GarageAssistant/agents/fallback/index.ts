import { <PERSON><PERSON><PERSON>, AI_DISCLAIMER, GoToInquiryAction, OrderListAction, ToCassServiceAction } from "@/copilot/constants";
import { Agent, MarkdownStreamReplier, IAction, MessageFactory } from "@casstime/copilot-core";
import { generateFallbackStreamText } from "@/copilot/apps/GarageAssistant/generator";
import { stringifyHistory, stringifyMessage } from "@/common/utils/message";
import logger from "@/common/logger";
import { Types } from "mongoose";
import { QueryRunner } from "./runner/QueryRunner";
import { queryMatchedBrands } from "./runner/tools/queryMatchedBrands";
import { QueryRewriter } from "./runner/concrete/QueryRewriter";
import { ToolsPicker } from "./runner/concrete/ToolsPicker";
import { ToolsExecutor } from "./runner/concrete/ToolsExecutor";
import { ResponseAssembler } from "./runner/concrete/ResponseAssembler";
import { caculator } from "./runner/tools/calculator";
import { search } from "./runner/tools/search";
import { qaService } from "../inquiry/services/QAService";
import { IQA } from "@/models";
import { removeUselessTokens } from "@/copilot/helpers/llm";
import { ActionFactory } from "@/common/factory/ActionFactory";
import { InquiryIntents } from "../inquiry/parsers/inquiryIntentClassifier";
import { createAddressAction } from "../inquiry/utils";
import { ChannelService } from "../inquiry/services/ChannelService";
import { inquiryService } from "../inquiry/services/InquiryService";
import { IDialogueBusinessEnum } from "../inquiry/enum";
import { AppName, VersionEnum } from "@/common/enums";

/**
 * 发布询价Agent
 */
const agent = new Agent(AgentName.fallbackAgent);

/**
 * 第一次进入Agent
 */
agent.onEnter(() => {
  logger.info("进入Fallback Agent");
});

/**
 * 离开当前Agent
 */
agent.onLeave(() => {
  logger.info("离开Fallback Agent");
});

/**
 * 设置意图解析器
 */
agent.setIntentParser(async (context) => {
  context.setIntent("问答");
});

agent.handle(["问答"], async (context) => {
  const { historyMessages, lastMessage } = context;
  const history = stringifyHistory(historyMessages.slice(-9));

  let nextAgent: null | string = context.prevMessageAgent || AgentName.inquiryAgent;
  if (nextAgent === agent.name) {
    nextAgent = null;
  }
  // 工具调用，处理兜底问题
  if (process.env.FALLBACK_RUNNER) {
    const runner = new QueryRunner(context, [queryMatchedBrands, caculator, search]);
    runner.setRewriter(new QueryRewriter(context));
    runner.setToolsPicker(new ToolsPicker(context));
    runner.setToolsExecutor(new ToolsExecutor(context));
    runner.setAssembler(new ResponseAssembler(context));
    const stream = await runner.run(stringifyMessage(lastMessage, false));
    const replier = MarkdownStreamReplier.for(context);
    await replier.reply(stream);
    return;
  }

  let relatedQA: IQA[] = [];

  if (lastMessage.type === "text" || lastMessage.type === "voice") {
    relatedQA = await qaService.searchRelatedQA(lastMessage.content || "");
  }
  // 用AI生成回复
  let txt = "";
  const id = new Types.ObjectId().toString();
  const factoryWithId = MessageFactory.with({ id });
  const actions: IAction[][] = [];
  actions[0] = [];
  const msgContent = stringifyMessage(lastMessage, false).trim();
  try {
    // 设为流式输出
    context.setReplyMode("stream");
    const msgStream = await generateFallbackStreamText(history, msgContent, relatedQA);
    const replier = MarkdownStreamReplier.for(context);
    const msg = await replier.reply(msgStream, { id });
    txt = msg.content;
  } catch (error) {
    txt = "非常抱歉，我无法回答您的问题。您可以点击下方按钮，联系人工客服";
    logger.warn(`生成流式fallback消息失败：${error}`);
  }
  if (/联系|沟通|客服/.test(txt)) {
    actions[0].push(ToCassServiceAction);
  }
  if (txt.includes("地址")) {
    actions[0].push(createAddressAction());
  }
  if (txt.includes("订单")) {
    actions[0].push(OrderListAction);
  }
  if (/^[0-9]{5,}$/.test(msgContent)) {
    actions[0].push(
      ActionFactory.nlu(`【${msgContent}】是零件号`, {
        entities: [{ name: "partName", value: msgContent }],
        intent: InquiryIntents.买配件,
        agentName: AgentName.inquiryAgent,
      })
    );
  }
  actions[0].push(GoToInquiryAction);

  txt = removeUselessTokens(txt);
  context.reply(
    factoryWithId.markdown(txt, {
      actions,
      disclaimer: AI_DISCLAIMER,
    })
  );
  await context.activateAgent(nextAgent);
});

agent.handle(["重新开始"], async (context) => {
  const { payload, intent, prevMessageAgent } = context;
  // 获取通道类型
  const channelService = context.getService(ChannelService);
  const dialogue = await channelService.getDialogue();
  const businessId = dialogue?.businessId || "";

  let nextAgent: null | string = prevMessageAgent || AgentName.inquiryAgent;
  if (nextAgent === agent.name) {
    nextAgent = null;
  }

  // 重新开始 -> 回复欢迎语
  if (intent === "重新开始") {
    switch (businessId) {
      case IDialogueBusinessEnum.ACCIDENT: {
        // 事故通道
        const greetMsg = await inquiryService.getAccidentGreetMessage();
        context.reply(greetMsg);
        break;
      }
      case IDialogueBusinessEnum.AIR_CONDITIONER: {
        // 空调通道
        const greetMsg = await inquiryService.getAirforceGreetMessage();
        context.reply(greetMsg);
        break;
      }
      default: {
        if (payload.app === AppName.ProxyInquiry) {
          const greetMsg = await inquiryService.getProxyInquiryGreetMessage();
          context.reply(greetMsg);
        } else {
          const noSupportGuideMsg = payload?.appVersion?.isLessThan(VersionEnum.FIVE_14_6);
          const greetMsgs = await inquiryService.getConfigureGreetMessage(payload.companyId || "", noSupportGuideMsg);
          greetMsgs.forEach((greetMsg) => {
            context.reply(greetMsg);
          });
        }
      }
    }
  }

  await context.activateAgent(nextAgent);
});
export default agent;
