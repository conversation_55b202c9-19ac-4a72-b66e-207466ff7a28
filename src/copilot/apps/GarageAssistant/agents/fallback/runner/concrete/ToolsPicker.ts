import { LLMMessageTemplate, LLMRunnableBuilder } from "@/copilot/helpers/llm/tools";
import { AbstractToolsPicker } from "../abstract/AbstractToolsPicker";
import { StructuredTool } from "@langchain/core/tools";
import logger from "@/common/logger";

const prompt = `
帮我选择一些工具，这些工具可能可以解答用户问题。
{tools}
挑选最相关的3个工具，输出为JSON：{{"tools": ["<工具1>", "<工具2>", "<工具3>"]}}
用户Query: "{query}"
`.trim();

const runner = LLMRunnableBuilder.create()
  .addPrompt(LLMMessageTemplate.create("user", prompt))
  .build<{ tools: string[] }>({ type: "json" });

/**
 * 根据用户Query生成改写版本
 */
export class ToolsPicker extends AbstractToolsPicker {
  async pick(tools: StructuredTool[], query: string): Promise<StructuredTool[]> {
    const result = await runner.invoke({
      tools: tools.map((tool) => `- ${tool.name}: ${tool.description}`).join("\n"),
      query,
    });
    const selectedTools = tools.filter((tool) => result.tools.includes(tool.name));
    logger.info(`Selected tools: ${selectedTools.map((tool) => tool.name).join(", ")}`);
    return selectedTools;
  }
}
