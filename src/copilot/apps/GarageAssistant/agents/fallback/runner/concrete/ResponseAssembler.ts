import { LLMMessageTemplate, LLMRunnableBuilder } from "@/copilot/helpers/llm/tools";
import { AbstractResponseAssembler, QueryResponse } from "../abstract/AbstractResponseAssembler";
import { IterableReadableStream } from "@casstime/copilot-core/dist/replier/StreamReplier";
import logger from "@/common/logger";

const prompt = `
用户的问题可能可以在以下Q/A库中找到答案，请根据用户的问题，组织语言回复用户。

<qa>
{qa}
</qa>

要求：
1. 回复简洁明了，不要重复Q/A库中的内容。
2. 如果Q/A库中没有答案，请回复“抱歉，我无法回答这个问题”。

用户: "{query}"
`.trim();

const runner = LLMRunnableBuilder.create()
  .addPrompt(LLMMessageTemplate.create("user", prompt))
  .build({ type: "string" });

/**
 * 根据用户Query生成改写版本
 */
export class ResponseAssembler extends AbstractResponseAssembler {
  async assemble(responses: QueryResponse[], rawInput: string): Promise<IterableReadableStream<string>> {
    logger.info("assemble", responses);
    return runner.stream({
      query: rawInput,
      qa: responses.map((r) => `Q: ${r.query}\nA: ${r.response}`).join("\n\n"),
    });
  }
}
