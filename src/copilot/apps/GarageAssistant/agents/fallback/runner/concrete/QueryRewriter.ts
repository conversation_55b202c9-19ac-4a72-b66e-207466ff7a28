import { LLMMessageTemplate, LLMRunnableBuilder } from "@/copilot/helpers/llm/tools";
import { AbstractQueryRewriter } from "../abstract/AbstractQueryRewriter";
import logger from "@/common/logger";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";

const prompt = `
你是一个智能客服系统的Query改写助手，请根据以下任务要求对用户Query进行改写：
1. **明确用户意图**：如果Query模糊或不完整，请生成更明确的版本。
2. **添加上下文**：如果Query缺少上下文，请补充相关信息。
3. **生成问题形式**：如果Query是陈述句，请改写成问题形式。
4. **生成纠错版本**：如果Query可能存在拼写或语法错误，请生成纠正版本。
5. **生成FAQ匹配版本**：如果Query需要匹配FAQ库，请生成FAQ相关版本。
6. **生成多意图版本**：如果Query可能包含多个意图，请拆分成多个子Query。

请根据以下用户Query生成改写版本，尽可能覆盖上述任务要求，并确保改写后的Query清晰、准确、简洁。
将改写后的版本输出到JSON数组，[{{"rewritten": "<子问题>"}}]，最多2条。
如果用户意图很明确，则不需要生成改写版本。
用户Query: "{query}"
`.trim();

const runner = LLMRunnableBuilder.create()
  .addPrompt(LLMMessageTemplate.create("user", prompt))
  .build<{ rewritten: string }[]>({ type: "json" });

/**
 * 根据用户Query生成改写版本
 */
export class QueryRewriter extends AbstractQueryRewriter {
  async rewrite(query: string): Promise<string[]> {
    const rewritten = await runner.invoke({ query }, { callbacks: [llmLogCallback] });
    const rewrittenQueries = rewritten.map((r) => r.rewritten);
    logger.info(`QueryRewriter: rewritten ${query} to ${rewrittenQueries.join("\n ")}`);
    return rewrittenQueries;
  }
}
