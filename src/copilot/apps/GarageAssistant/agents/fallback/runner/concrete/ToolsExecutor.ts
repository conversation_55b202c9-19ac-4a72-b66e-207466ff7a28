import { StructuredTool } from "@langchain/core/tools";
import { AbstractToolsExecutor } from "../abstract/AbstractToolsExecutor";
import { chatglm_chat } from "@/clients/llm";
import logger from "@/common/logger";
import { HumanMessage } from "@langchain/core/messages";

export class ToolsExecutor extends AbstractToolsExecutor {
  async execute(tools: StructuredTool[], query: string): Promise<string> {
    const llmWithTools = chatglm_chat.bindTools(tools);
    const messages = [new HumanMessage(query)];
    let aiMessage = await llmWithTools.invoke(messages);
    const toolsByName = tools.reduce(
      (acc, tool) => {
        acc[tool.name] = tool;
        return acc;
      },
      {} as { [key: string]: StructuredTool }
    );
    messages.push(aiMessage);
    if (aiMessage.tool_calls?.length) {
      for (const toolCall of aiMessage.tool_calls) {
        const selectedTool = toolsByName[toolCall.name];
        const toolMessage = await selectedTool.invoke(toolCall);
        console.log(toolMessage);
        messages.push(toolMessage);
      }
      aiMessage = await llmWithTools.invoke(messages);
      messages.push(aiMessage);
    }
    console.log(messages);
    logger.info(`ToolsExecutor: ${query} -> ${JSON.stringify(aiMessage, null, 2)}`);
    return aiMessage.content.toString();
  }
}
