import { Context } from "@casstime/copilot-core";
import { AbstractQueryRewriter } from "./abstract/AbstractQueryRewriter";
import { AbstractToolsPicker } from "./abstract/AbstractToolsPicker";
import { AbstractToolsExecutor } from "./abstract/AbstractToolsExecutor";
import { AbstractResponseAssembler } from "./abstract/AbstractResponseAssembler";
import assert from "node:assert";
import { StructuredTool } from "@langchain/core/tools";

export class QueryRunner {
  private rewriter?: AbstractQueryRewriter;
  private toolsPicker?: AbstractToolsPicker;
  private toolsExecutor?: AbstractToolsExecutor;
  private assembler?: AbstractResponseAssembler;

  public setRewriter(rewriter: AbstractQueryRewriter) {
    this.rewriter = rewriter;
  }

  public setToolsPicker(toolsPicker: AbstractToolsPicker) {
    this.toolsPicker = toolsPicker;
  }

  public setToolsExecutor(toolsExecutor: AbstractToolsExecutor) {
    this.toolsExecutor = toolsExecutor;
  }
  public setAssembler(assembler: AbstractResponseAssembler) {
    this.assembler = assembler;
  }

  constructor(protected context: Context, protected tools: StructuredTool[]) {}

  private async executeTools(rewrittenQuery: string) {
    assert(this.toolsPicker, "toolsPicker is not set");
    const tools = await this.toolsPicker.pick(this.tools, rewrittenQuery);
    assert(this.toolsExecutor, "toolsExecutor is not set");
    const response = await this.toolsExecutor.execute(tools, rewrittenQuery);
    return {
      query: rewrittenQuery,
      response: response,
    };
  }

  public async run(rawInput: string) {
    if (!this.rewriter || !this.toolsPicker || !this.toolsExecutor || !this.assembler) {
      throw new Error("QueryRunner is not fully configured");
    }

    // rewrite query
    const rewrittenQueries = await this.rewriter.rewrite(rawInput);
    // pick and execute tools
    const responses = await Promise.all(rewrittenQueries.map((rewrittenQuery) => this.executeTools(rewrittenQuery)));

    // assemble response
    return this.assembler.assemble(responses, rawInput);
  }
}
