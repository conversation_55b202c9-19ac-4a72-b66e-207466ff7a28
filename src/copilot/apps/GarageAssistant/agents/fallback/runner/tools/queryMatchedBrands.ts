import { DynamicStructuredTool } from "@langchain/core/tools";
import { z } from "zod";

export const queryMatchedBrands = new DynamicStructuredTool({
  name: "queryMatchedBrands",
  description: "查询配件的配套品牌",
  schema: z.object({
    vehicle: z.string().describe("车辆型号"),
    parts: z.string().describe("零配件名称，如果存在多个，用`、`隔开"),
  }),
  func: async ({ vehicle, parts }: { vehicle: string; parts: string }) => {
    return `没有查询到对应的配套品牌`;
  },
});
