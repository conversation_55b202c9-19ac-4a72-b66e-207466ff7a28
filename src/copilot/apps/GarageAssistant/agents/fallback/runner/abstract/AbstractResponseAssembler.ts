import { Context } from "@casstime/copilot-core";
import { IterableReadableStream } from "@casstime/copilot-core/dist/replier/StreamReplier";

export type QueryResponse = {
  query: string;
  response: string;
};

export abstract class AbstractResponseAssembler {
  constructor(protected context: Context) {}

  abstract assemble(responses: QueryResponse[], rawInput: string): Promise<IterableReadableStream<string>>;
}
