import { Category, IntentClassifier } from "@/copilot/helpers/llm/tools";
import { ConfigCategory, IConfigCategory } from "@/models";
import { Context } from "@casstime/copilot-core";
import logger from "@/common/logger";

class CategoryService {
  public async parseConfigureIntents(context: Context, categoryNames: string[]) {
    try {
      const intents = this.filterCategoryIds(categoryNames);
      const configCategories = await this.findConfigCategories(intents);
      const fallbackClassifier = this.generateFallbackClassifier(configCategories);
      const intent = await fallbackClassifier.classify(context, Array.from(intents));
      if (intents.includes(intent)) {
        return intent;
      }
      return "";
    } catch (error) {
      logger.warn(`根据分类解析意图失败${error}`);
      return "";
    }
  }

  private filterCategoryIds(categoryIds: string[]): string[] {
    return (
      categoryIds.filter((item) => /[\u4e00-\u9fa5]/.test(item)) || [] //过滤掉不含中文的意图
    );
  }

  private async findConfigCategories(categoryNames: string[]) {
    try {
      const configCategories = await ConfigCategory.find({ name: { $in: categoryNames } })
        .lean()
        .exec();
      return configCategories || [];
    } catch (error) {
      logger.warn(`查询配置分类失败：${error}`);
      return [];
    }
  }

  private generateFallbackClassifier(configCategories: IConfigCategory[]) {
    const fallbackClassifier = IntentClassifier.create();
    configCategories.forEach((c) => {
      let category = Category.create(c.name!);
      if (c.description) {
        category = category.describe(c.description);
      }
      if (c.aliases?.length) {
        category = category.alias(c.aliases);
      }
      if (c.examples?.length) {
        c.examples.forEach((example) => {
          if (example) {
            category = category.example(example);
          }
        });
      }
      fallbackClassifier.addCategory(category);
    });
    return fallbackClassifier;
  }
}

export const categoryService = new CategoryService();
