import { IHandlerFunction, ITextMessage, MessageFactory } from "@casstime/copilot-core";
import { LLMRunnableBuilder, LLMMessageTemplate } from "@/copilot/helpers/llm/tools";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
import productClient from "@/clients/product";
import _ from "lodash";
import { AgentName } from "@/common/enums";

const actionPrompt = `
背景：用户正在使用开思采购助手购买机油

任务：请根据输入提取机油的品牌(oil_brand)、品质(quality)、粘度(viscosity)以及适用车型(car_model)，输出为JSON。

要求：
1. 如果用户没有提供相关字段，字段填充 \`null\`；
2. 直接输出为JSON，确保格式正确，以 \`\`\`json 开头；

示例:
## 示例1
输入：壳牌机油 -> {{ 
    "oil_brand": "壳牌", 
    "quality": null, 
    "viscosity": null, 
    "car_model": null 
}}

## 示例2
输入: 奥迪原厂机油 5W-30 -> {{ 
    "oil_brand": null, 
    "quality": "原厂", 
    "viscosity": "5W-30", 
    "car_model": "奥迪" 
}}

输入：{input}
`.trim();

const generator = LLMRunnableBuilder.create()
  .addPrompt(LLMMessageTemplate.create("user").addText(actionPrompt.trim()))
  .build<{
    oil_brand: string | null;
    quality: string | null;
    viscosity: string | null;
    car_model: string | null;
  }>({ type: "json" });

type SearchRes = Awaited<ReturnType<typeof productClient.getShoppingMallProductListV2>>;

type AttrValue = {
  attrValue?: string;
  attrValueShow?: string;
};

function extractProducts(data: SearchRes["data"]) {
  const top3 = data?.data?.productListVO
    ?.map((item) => {
      const attrs = item.attrs?.reduce(
        (acc, cur) => {
          cur.attrShowVOS?.forEach((item) => {
            if (item.attrName) {
              acc[item.attrName] = item.attrValues || [];
            }
          });
          return acc;
        },
        {} as Record<string, AttrValue[]>
      );
      return {
        ...item,
        productId: item.productId!,
        attrs,
      };
    })
    .slice(0, 3);
  return top3;
}

async function fulfillProducts(userId: string, data: { productId: string }[]) {
  const productIds = data?.map((item) => item.productId) || [];
  const products = await productClient.getProductBatchInfo(userId, productIds, [
    "VEHICLES",
    "INVENTORY",
    "PRICE",
    "ATTRS",
    "CGJ_CATAGORY",
    "STANDARD_NAME",
  ]);
  return products;
}

function renderProducts(products: Awaited<ReturnType<typeof fulfillProducts>>) {
  return `
<view>
    ${products
      .map((item) => {
        const query = {
          productId: item.productId,
        };
        const params = {
          navigate: `cassapp://route/rn/ProductDetail?query=${decodeURIComponent(JSON.stringify(query))}`,
        };

        return `
            <view>
                <view style="flex-direction: row;">
                    <image style="width:100;height:100;" uri="${item.productInfo?.smallImageUrl}"></image>
                    <view style="flex:1">
                        <text style="font-weight: bold">${item.productInfo?.productName}</text>
                        <view style="flex-direction: row; justify-content: space-between;">
                            <text>价格: ${item.price?.price}</text>
                            <text>库存: ${item.inventory?.[0]?.inventoryValue}</text>
                        </view>
                    </view>
                </view>
                <view style="justify-content: center; align-items: center;">
                    <button type="command" command="COMMON-NAVIGATE" params="${_.escape(
                      JSON.stringify(params)
                    )}">查看详情</button>
                </view>
            </view>`;
      })
      .join("\n")}
</view>
`.trim();
}

export const oilHandler: IHandlerFunction = async (context) => {
  const lastMessage = context.lastMessage as ITextMessage;
  const content = lastMessage.content;
  const result = await generator.invoke({ input: content }, { callbacks: [llmLogCallback] });
  const keywords = Object.keys(result)
    .map((key) => result[key as keyof typeof result])
    .filter(Boolean)
    .concat("机油")
    .join(" ");
  const data = await productClient.getShoppingMallProductListV2({ keywords: keywords, platform: "APP", pageSize: 5 });
  if (data.code !== 200) {
    context.reply(
      MessageFactory.text(data.errorMessage || `没有找到合适的机油，你可以发起询价`, {
        actions: [
          [
            {
              type: "nlu",
              text: "发起询价",
              nlu: {
                agentName: AgentName.inquiryAgent,
                intent: "询报价",
                slots: {
                  partNames: [keywords],
                },
              },
            },
          ],
        ],
      })
    );
    return;
  }
  const list = extractProducts(data.data) || [];
  console.log(JSON.stringify(data.data));
  const products = await fulfillProducts(context.userId!, list);
  const richText = renderProducts(products);
  context.reply(
    MessageFactory.text("为您找到以下机油", {
      embed: { type: "richtext", content: richText },
      actions: [
        [
          {
            type: "nlu",
            text: "发起询价",
            nlu: {
              agentName: AgentName.inquiryAgent,
              intent: "询报价",
              slots: {
                partNames: [keywords],
              },
            },
          },
        ],
      ],
    })
  );
};
