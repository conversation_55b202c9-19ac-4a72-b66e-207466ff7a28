import { Agent<PERSON><PERSON> } from "@/copilot/constants";
import { Agent } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { oilHandler } from "./handlers/oilHandler";



/**
 * 商品推荐Agent
 */
const agent = new Agent(AgentName.productAgent);

/**
 * 第一次进入Agent
 */
agent.onEnter(() => {
  logger.info("进入 商品推荐Agent");
});

/**
 * 离开当前Agent
 */
agent.onLeave((context) => {
  logger.info("离开 商品推荐Agent", context.agentName);
});

/**
 * 设置意图解析器
 */
agent.setIntentParser(async (context) => {
  context.setIntent("油品推荐"); // 这里直接返回意图，不进行解析
});

agent.handle("油品推荐", oilHandler);

export default agent;
