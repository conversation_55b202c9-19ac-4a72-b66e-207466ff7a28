import { ActionFactory } from "@/common/factory/ActionFactory";
import { commandIntents, IMTeamId } from "@/copilot/constants/Intents";
import { ACCIDENT_AI_CHAT_SCREEN, FROM_AI_CHAT } from "../../inquiry/constant";
import { AgentName } from "@/common/enums";

/** 人工客服 */
export function toCassService() {
  return ActionFactory.command("人工客服", commandIntents.commonNavigate, {
    theme: "primary",
    params: {
      navigate: "im/conversation",
      params: {
        teamId: IMTeamId.CASS_SERVICE,
        fromScreen: ACCIDENT_AI_CHAT_SCREEN,
        referer: ACCIDENT_AI_CHAT_SCREEN,
        messageText: FROM_AI_CHAT, // 文本消息
        isDialog: true,
      },
      platform: "native",
    },
  });
}

export function createAirforceGreetAction(label: string) {
  return ActionFactory.nlu(label, {
    agentName: AgentName.partInfoAgent,
  });
}
