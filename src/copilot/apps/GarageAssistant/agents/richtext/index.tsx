import { React } from "@casstime/copilot-xml";
import { renderToXML } from "@casstime/copilot-xml";
import { AccidentGreetMsg } from "./components/AccidentGreetMsg";
import { AirforceGreetMsg } from "./components/AirforceGreetMsg";
import { CustomerServiceImg } from "./components/CustomerServiceImg";
import { NewDefaultGreetMsg } from "./components/NewDefaultGreetMsg";
import { NoWantPartsReply } from "./components/NoWantPartsReply";

/** 创建事故通道欢迎语 */
export function createAccidentGreetMsg() {
  return {
    type: "richtext",
    content: renderToXML(<AccidentGreetMsg />),
  };
}
/** 创建空调通道欢迎语 */
export function createAirforceGreetMsg(airforceGreetLabel: string[]) {
  return {
    type: "richtext",
    content: renderToXML(<AirforceGreetMsg airforceGreetLabel={airforceGreetLabel} />),
  };
}

/** 创建人工客服图片 */
export function createImg() {
  const content = renderToXML(<CustomerServiceImg />);
  return {
    type: "richtext",
    content,
  };
}
/** 创建新欢迎语 */
export function createNewDefaultGreetMsg() {
  const content = renderToXML(<NewDefaultGreetMsg />);
  return {
    type: "richtext",
    content,
  };
}

/** 没有想要的配件回复语 */
export function createNoWantPartsReply(parts: string[]) {
  const content = renderToXML(<NoWantPartsReply parts={parts} />);
  return {
    type: "richtext",
    content,
  };
}