import { React, View, Text, StyleSheet } from "@casstime/copilot-xml";
export function NewDefaultGreetMsg() {
  return (
    <View>
      <View style={styles.flex}>
        <Text style={styles.text}>您可以</Text>
      </View>
      <View style={styles.btnFlex}>
        <Text style={styles.btnIcon}>●</Text>
        <Text style={styles.btnText}> 扫VIN码</Text>
      </View>
      <View style={styles.btnFlex}>
        <Text style={styles.btnIcon}>●</Text>
        <Text style={styles.btnText}>拍工单</Text>
      </View>
      <View style={styles.btnFlex}>
        <Text style={styles.btnIcon}>●</Text>
        <Text style={styles.btnText}>告诉我配件名称、配件编码</Text>
      </View>
      <View style={styles.btnFlex}>
        <Text style={styles.btnIcon}>●</Text>
        <Text style={styles.btnText}>告诉我车辆有故障</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  flex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginTop: 16,
  },
  icon: {
    marginLeft: 29,
    width: 36,
    height: 36,
  },
  text: {
    marginLeft: 4,
    color: "#646566",
    fontSize: 28,
  },
  btnFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    marginLeft: 24,
    marginRight: 24,
    paddingRight: 24,
  },
  btnIcon: {
    fontSize: 14,
    color: "#646566",
    marginRight: 4,
  },
  btnText: {
    marginLeft: 8,
    color: "#646566",
    fontSize: 26,
  },
});
