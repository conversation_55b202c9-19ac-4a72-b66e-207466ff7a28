import { React, View, Text, StyleSheet } from "@casstime/copilot-xml";
import { createAirforceGreetAction } from "../actions";

interface AccidentGreetButtonGrid {
  labels: string[];
}

export const AccidentGreetButtonGrid = ({ labels }: AccidentGreetButtonGrid) => {
  return (
    <View style={styles.faultOptionsContainer}>
      {labels.map((val, rowIndex) => (
        <View style={styles.faultButton} key={rowIndex} action={createAirforceGreetAction(val)}>
          <Text style={styles.faultButtonText}>{val}</Text>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  faultOptionsContainer: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
    overflow: "hidden",
    marginLeft: 24,
  },
  selectedBtn: {
    color: "#E51E1E",
    paddingTop: 7,
    paddingBottom: 7,
    paddingRight: 16,
    paddingLeft: 16,
    borderColor: "#E51E1E",
    borderRadius: 8,
    borderWidth: 1,
    backgroundColor: "#FFF4F4",
    marginTop: 10,
    marginBottom: 10,
    marginRight: 20,
  },
  selectedText: {
    fontSize: 24,
    color: "#E51E1E",
  },
  faultButton: {
    color: "#2A2B2C",
    paddingTop: 7,
    paddingBottom: 7,
    paddingRight: 16,
    paddingLeft: 16,
    borderColor: "#C8C9CC",
    borderRadius: 8,
    borderWidth: 1,
    backgroundColor: "#ffffff",
    marginTop: 10,
    marginBottom: 10,
    marginRight: 20,
  },
  faultButtonText: {
    fontSize: 24,
    color: "#2A2B2C",
  },
});
