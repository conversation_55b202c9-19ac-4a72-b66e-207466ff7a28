import { React, View, Text } from "@casstime/copilot-xml";
import { StoreCard } from "../StoreCard";
import { PurchasePlanFeatureEnum } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/interfaces/plan";
import { QuoteCard } from "../QuoteCard";
import { IIntelligentPlan, IMerchantClue, IntelligentPlanType } from "../../../../inquiry/interface";
import { QuotePurchaseButtonCard } from "../QuotePurchaseButtonCard";
import { AppName } from "@/common/enums";

export const CollectPlan = ({
  intelligentPlan,
  merchantClue,
  disableAction,
  appName,
  planGroupId,
}: {
  intelligentPlan: IIntelligentPlan;
  disableAction: boolean;
  merchantClue: IMerchantClue;
  appName?: AppName;
  planGroupId: string;
}) => {
  const {
    storeName = "",
    storeId = "",
    deliveryWarehouse = "",
    packagePrice = 0,
    iconUri,
    levelValue,
    name,
    quotes = [],
    type,
    features,
    _id,
  } = intelligentPlan;
  const storeInfo = { storeName, storeId, iconUri, levelValue, type };
  const { id: inquiryId } = merchantClue;
  const quotationProductIds = quotes.map((quote) => quote.quotationProductId || "").filter(Boolean);
  const wholePlan = features?.[0] === PurchasePlanFeatureEnum.WHOLE_DISTRIBUTE;

  return (
    <View>
      <StoreCard disableAction={disableAction} merchantClue={merchantClue} storeInfo={storeInfo} />
      {deliveryWarehouse && <Text style={{ fontSize: 24 }}>{deliveryWarehouse}</Text>}
      {name && !wholePlan && <Text>【{name}】</Text>}
      <QuoteCard
        disableAction={disableAction}
        quotes={quotes}
        merchantClue={merchantClue}
        type={IntelligentPlanType.COLLECT}
        packagePrice={packagePrice}
      />
      <QuotePurchaseButtonCard
        disableAction={disableAction}
        quotationProductIds={quotationProductIds}
        inquiryId={inquiryId}
        pageIndex={1}
        storeId={storeId}
        appName={appName}
        planGroupId={planGroupId}
        planId={_id?.toString() || ""}
      />
    </View>
  );
};
