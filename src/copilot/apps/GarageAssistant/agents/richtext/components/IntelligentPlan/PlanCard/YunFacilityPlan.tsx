import { AppName } from "@/common/enums";
import { React, View, Text } from "@casstime/copilot-xml";
import { IIntelligentPlan, IMerchantClue, IntelligentPlanType } from "../../../../inquiry/interface";
import { StoreCard } from "../StoreCard";
import { QuoteCard } from "../QuoteCard";
import { QuotePurchaseButtonCard } from "../QuotePurchaseButtonCard";

export const YunFacilityPlan = ({
  intelligentPlan,
  merchantClue,
  appName,
  planGroupId,
  disableAction,
}: {
  intelligentPlan: IIntelligentPlan;
  merchantClue: IMerchantClue;
  appName?: AppName;
  disableAction: boolean;
  planGroupId: string;
}) => {
  const {
    storeName = "",
    storeId = "",
    facilityName = "",
    packagePrice = 0,
    defaultEta,
    quotes = [],
    inquiryId = "",
    type,
    name = "",
    _id,
  } = intelligentPlan;
  const storeInfo = { storeName, storeId, type };
  const quotationProductIds = quotes.map((quote) => quote.quotationProductId || "").filter(Boolean);

  return (
    <View>
      {facilityName && <Text style={{ color: "#32B418", fontWeight: "bold", fontSize: 30 }}>{facilityName}</Text>}
      {storeName && <StoreCard disableAction={disableAction} merchantClue={merchantClue} storeInfo={storeInfo} />}
      {defaultEta && <Text style={{ fontSize: 24 }}>{defaultEta}</Text>}
      {name && <Text>{name}</Text>}
      <QuoteCard
        disableAction={disableAction}
        quotes={quotes}
        merchantClue={merchantClue}
        type={IntelligentPlanType.YUN_FACILITY}
        packagePrice={packagePrice}
      />
      <QuotePurchaseButtonCard
        disableAction={disableAction}
        quotationProductIds={quotationProductIds}
        inquiryId={inquiryId}
        pageIndex={2}
        storeId={storeId}
        appName={appName}
        planGroupId={planGroupId}
        planId={_id?.toString() || ""}
      />
    </View>
  );
};
