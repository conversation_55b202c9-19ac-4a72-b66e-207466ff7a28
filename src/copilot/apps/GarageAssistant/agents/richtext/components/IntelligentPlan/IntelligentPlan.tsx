import { AppName } from "@/common/enums";
import { INeedDecodeItem } from "@/service/interface";
import { React, View, XMLStyle } from "@casstime/copilot-xml";
import { IInquiryInfo, IIntelligentPlan, IntelligentPlanType } from "../../../inquiry/interface";
import { IPlanQuoteItem } from "@/clients/quote/interface";
import { getMerchantClue } from "../../../inquiry/copilot-xml/utils";
import { InquiryHeaderCard } from "./inquiryHeaderCard";
import { A9tAirforceEnterCard } from "./A9tAirforceEnterCard";
import { CheckMoreCard } from "./CheckMoreCard";
import { YunFacilityPlan } from "./PlanCard/YunFacilityPlan";
import { CollectPlan } from "./PlanCard/CollectPlan";
import { RecommendStorePlan } from "./PlanCard/RecommendStorePlan";
import { ComprehensivePlan } from "./PlanCard/ComprehensivePlan";

/**
 * 报价方案列表
 */
export const IntelligentPlan = ({
  intelligentPlans,
  inquiryDetail,
  needDecodeList,
  isA9tAirforceEnter,
  noInquiryHeader = false,
  disableAction = false,
  appName = AppName.AIPurchasePlanAssistant,
  planGroupId,
  hasEpcImage = false,
}: {
  intelligentPlans: IIntelligentPlan[];
  inquiryDetail: IInquiryInfo;
  needDecodeList: INeedDecodeItem[];
  isA9tAirforceEnter: boolean;
  noInquiryHeader?: boolean;
  disableAction?: boolean;
  appName?: AppName;
  planGroupId: string;
  hasEpcImage?: boolean;
}) => {
  isA9tAirforceEnter = false;
  const plans = intelligentPlans.map((plan) => {
    const { quotes } = plan;
    const newQuotes: Partial<IPlanQuoteItem>[] = needDecodeList.map((item) => {
      const { decodeResultId, needsName, partsName = "" } = item;
      const quote = quotes?.find((q) => q.standardItemId === decodeResultId);
      if (!quote) {
        return { needsName, partsName };
      }
      return quote;
    });

    return { ...plan, quotes: newQuotes };
  });

  const merchantClue = getMerchantClue(inquiryDetail);

  const quotationPlanView = plans.map((plan) => {
    switch (plan.type) {
      case IntelligentPlanType.YUN_FACILITY:
        return (
          <YunFacilityPlan
            disableAction={disableAction}
            intelligentPlan={plan}
            merchantClue={merchantClue}
            planGroupId={planGroupId}
            appName={appName}
          />
        );
      case IntelligentPlanType.COLLECT:
        return (
          <CollectPlan
            disableAction={disableAction}
            intelligentPlan={plan}
            merchantClue={merchantClue}
            planGroupId={planGroupId}
            appName={appName}
          />
        );
      case IntelligentPlanType.RECOMMENDED_STORE:
        return <RecommendStorePlan disableAction={disableAction} intelligentPlan={plan} merchantClue={merchantClue} />;
      default:
        return (
          <ComprehensivePlan
            disableAction={disableAction}
            intelligentPlan={plan}
            merchantClue={merchantClue}
            planGroupId={planGroupId}
            appName={appName}
          />
        );
    }
  });
  const quotationView = (
    <View style={{ backgroundColor: "#F7F8FA", borderRadius: 16, padding: 20 }}>
      {!noInquiryHeader && (
        <InquiryHeaderCard disableAction={disableAction} inquiryDetail={inquiryDetail} hasEpcImage={hasEpcImage} />
      )}
      {quotationPlanView}
      {appName !== AppName.AIPurchasePlanAssistant && (
        <CheckMoreCard inquiryId={inquiryDetail?.inquiryId || ""} hasQuote={true} />
      )}
    </View>
  );
  const planView = (
    <>
      {quotationView}
      {isA9tAirforceEnter && (
        <>
          <View style={{ height: 24 }} />
          <A9tAirforceEnterCard merchantClue={getMerchantClue(inquiryDetail)} disableAction={disableAction} />
        </>
      )}
    </>
  );
  insertHr(quotationPlanView);
  insertHr(quotationView.props.children || []);
  insertHr(planView.props.children || []);
  return planView;
};

export const Hr = ({ styles }: { styles?: XMLStyle }) => {
  return <View style={{ backgroundColor: "#dddee0", height: 1, marginTop: 20, marginBottom: 20, ...styles }}></View>;
};

const insertHr = (children: React.JSX.Element[]) => {
  if (!children?.length) {
    return children;
  }
  for (let i = children.length - 1; i > 0; i--) {
    children.splice(i, 0, <Hr />);
  }
  return children;
};
