import { React, View } from "@casstime/copilot-xml";
import { InquiryHeaderCard } from "./inquiryHeaderCard";
import { IInquiryInfo } from "../../../inquiry/interface";
import { Hr } from "./IntelligentPlan";
import { CheckMoreCard } from "./CheckMoreCard";

export const InTheQuotationCard = ({
  disableAction,
  inquiryDetail,
}: {
  disableAction: boolean;
  inquiryDetail: IInquiryInfo;
}) => {
  const { inquiryId = "" } = inquiryDetail;

  return (
    <View style={{ backgroundColor: "#F7F8FA", padding: 24, borderRadius: 16 }}>
      <InquiryHeaderCard disableAction={disableAction} inquiryDetail={inquiryDetail} />
      <Hr />
      <CheckMoreCard inquiryId={inquiryId} hasQuote={false} />
    </View>
  );
};
