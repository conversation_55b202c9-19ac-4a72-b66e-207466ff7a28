import { React, View, Image, Text } from "@casstime/copilot-xml";
import { IInquiryInfo } from "../../../inquiry/interface";
import { createGotoEpcScreenAction, createGotoQuotationResultAction } from "../../../inquiry/copilot-xml/utils";
import dayjs from "dayjs";
import { EPC_ICON } from "@/common/image";

export const InquiryHeaderCard = ({
  inquiryDetail,
  disableAction,
  hasEpcImage = false,
}: {
  disableAction: boolean;
  inquiryDetail: IInquiryInfo;
  hasEpcImage?: boolean;
}) => {
  const {
    brandLogo = "",
    saleModelName,
    carModelName = "",
    vin = "",
    needsNames = [],
    inquiryId = "",
    createdStamp,
    createdName,
  } = inquiryDetail;
  const action = createGotoQuotationResultAction(inquiryId);
  const epcAction = createGotoEpcScreenAction(inquiryId);

  return (
    <View>
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        <Image uri={brandLogo} style={{ width: 72, height: 72, marginRight: 20 }} />
        <View style={{ flex: 1, marginRight: 10 }}>
          <Text style={{ fontWeight: "bold" }}>{saleModelName || carModelName}</Text>
          <Text>{vin}</Text>
        </View>
      </View>

      <View>
        <View action={hasEpcImage ? epcAction : undefined} style={{ flexDirection: "row", alignItems: "center" }}>
          {hasEpcImage ? <Image uri={EPC_ICON} style={{ width: 40, height: 40, marginRight: 5 }} /> : null}
          <Text style={{ fontWeight: "bold", fontSize: 30, flex: 1 }} numberOfLines={1}>
            {needsNames.join("、")}
          </Text>
        </View>
        <View action={disableAction ? undefined : action}>
          <View style={{ flexDirection: "row" }}>
            <Text>询价单号:</Text>
            <Text style={{ color: "#008CF5" }}>{inquiryId}</Text>
          </View>
          <Text>询价时间:{dayjs(createdStamp).format("YYYY-MM-DD HH:mm")}</Text>
          <Text>询价人:{createdName}</Text>
        </View>
      </View>
    </View>
  );
};
