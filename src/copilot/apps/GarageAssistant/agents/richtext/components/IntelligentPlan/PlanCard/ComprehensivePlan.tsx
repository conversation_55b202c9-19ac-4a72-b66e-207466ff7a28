import { React, View, Text } from "@casstime/copilot-xml";
import { StoreCard } from "../StoreCard";
import { QuoteCard } from "../QuoteCard";
import { IIntelligentPlan, IMerchantClue, IntelligentPlanType } from "../../../../inquiry/interface";
import { QuotePurchaseButtonCard } from "../QuotePurchaseButtonCard";
import { AppName } from "@/common/enums";

export const ComprehensivePlan = ({
  intelligentPlan,
  merchantClue,
  disableAction,
  appName,
  planGroupId,
}: {
  intelligentPlan: IIntelligentPlan;
  disableAction: boolean;
  merchantClue: IMerchantClue;
  appName?: AppName;
  planGroupId: string;
}) => {
  const {
    storeName = "",
    storeId = "",
    facilityName = "",
    packagePrice = 0,
    defaultEta,
    name,
    quotes = [],
    inquiryId = "",
    type,
    features,
    _id,
  } = intelligentPlan;
  const storeInfo = { storeName, storeId, type };
  const quotationProductIds = quotes.map((quote) => quote.quotationProductId || "").filter(Boolean);

  return (
    <View>
      {facilityName && <Text style={{ color: "#32B418", fontWeight: "bold", fontSize: 30 }}>{facilityName}</Text>}
      {storeName && <StoreCard disableAction={disableAction} merchantClue={merchantClue} storeInfo={storeInfo} />}
      {!storeName && <Text style={{ fontSize: 30, fontWeight: "bold", color: "#008cf5" }}>多商家方案</Text>}
      {defaultEta && <Text style={{ fontSize: 24 }}>{defaultEta}</Text>}
      {name && !!features?.length && <Text>【{name}】</Text>}
      <QuoteCard
        quotes={quotes}
        disableAction={disableAction}
        merchantClue={merchantClue}
        type={IntelligentPlanType.COMPREHENSIVE}
        packagePrice={packagePrice}
      />
      <QuotePurchaseButtonCard
        disableAction={disableAction}
        quotationProductIds={quotationProductIds}
        inquiryId={inquiryId}
        pageIndex={2}
        appName={appName}
        storeId={storeId}
        planId={_id?.toString() || ""}
        planGroupId={planGroupId}
      />
    </View>
  );
};
