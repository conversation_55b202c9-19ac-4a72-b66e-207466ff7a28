import { React, View, Text } from "@casstime/copilot-xml";
import { IMerchantClue, IntelligentPlanType } from "../../../inquiry/interface";
import { IPlanQuoteItem } from "@/clients/quote/interface";
import { QuoteItemCard } from "./QuoteItemCard";
import _ from "lodash";

export interface QuoteCardProps {
  quotes: Partial<IPlanQuoteItem>[];
  merchantClue: IMerchantClue;
  type: IntelligentPlanType;
  packagePrice?: number;
  disableAction: boolean;
}

export const QuoteCard = (props: QuoteCardProps) => {
  const { quotes, merchantClue, type, packagePrice, disableAction } = props;

  return (
    <View style={{ marginLeft: 24 }}>
      {packagePrice && <Text>整单价格: ¥ {packagePrice.toFixed(2)}</Text>}
      {quotes.map((quote, index) => {
        const quoteDepartureIds = quotes.map((item) => item.location!).filter(Boolean);
        const quoteStoreIds = _.uniqBy(quotes, "storeId")
          .map((item) => item.storeId!)
          .filter(Boolean);
        return (
          <QuoteItemCard
            disableAction={disableAction}
            quote={quote}
            index={index}
            type={type}
            quoteDepartureIds={quoteDepartureIds}
            quoteStoreIds={quoteStoreIds}
            merchantClue={merchantClue}
          />
        );
      })}
    </View>
  );
};
