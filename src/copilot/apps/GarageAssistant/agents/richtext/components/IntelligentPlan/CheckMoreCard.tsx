import { React, View, Text, Link, Image } from "@casstime/copilot-xml";
import { InquiryQuoteTabs } from "../../../inquiry/enum";
import { LOADING_ICON, DONE_ICON } from "@/common/image";

/** 查看更多 */
export const CheckMoreCard = ({ inquiryId, hasQuote }: { inquiryId: string; hasQuote: boolean }) => {
  const query = {
    inquiryId,
    fromScreen: "AIChatScreen",
    pageIndex: InquiryQuoteTabs.STORE,
  };
  const navigate = `cassapp://route/native/inquiry/quotationResult?query=${JSON.stringify(query)}`;

  if (hasQuote) {
    return (
      <View style={{ justifyContent: "center", alignItems: "center", flexDirection: "row" }}>
        <Image uri={DONE_ICON} style={{ width: 36, height: 36, marginRight: 10 }}></Image>
        <Link to={navigate}>查看报价明细</Link>
      </View>
    );
  }

  return (
    <View style={{ justifyContent: "center", alignItems: "center", flexDirection: "row" }}>
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
          overflow: "hidden",
          width: 36,
          height: 36,
        }}
      >
        <Image uri={LOADING_ICON} style={{ width: 60, height: 60, marginRight: 10 }}></Image>
      </View>
      <Text style={{ textAlign: "center" }}>报价中，</Text>
      <Link to={navigate}>查看报价明细</Link>
    </View>
  );
};
