import { IMTeamId, commandIntents } from "@/copilot/constants";
import { ICommandAction } from "@casstime/copilot-core";
import { React, View, Image } from "@casstime/copilot-xml";
import { IMerchantClue } from "../../../inquiry/interface";

const A9tAirforceEnterIcon = "https://cass-upload.oss-cn-shenzhen.aliyuncs.com/copilot/production/a9tAirforceEnter.png";

/** 专属客服卡片 */
export const A9tAirforceEnterCard = ({
  merchantClue,
  disableAction,
}: {
  merchantClue: IMerchantClue;
  disableAction: boolean;
}) => {
  const query = {
    teamId: IMTeamId.CASS_SERVICE, // 开思客服
    clue: merchantClue,
    referer: "AIChatScreen",
  };
  const navigate = `cassapp://route/native/im/conversation?query=${JSON.stringify(query)}`;
  const action = {
    type: "command",
    text: "专属客服",
    command: commandIntents.commonNavigate,
    params: { navigate },
  } as ICommandAction;

  return (
    <View action={disableAction ? undefined : action}>
      <Image uri={A9tAirforceEnterIcon} style={{ position: "relative", width: 580, height: 189 }} />
    </View>
  );
};
