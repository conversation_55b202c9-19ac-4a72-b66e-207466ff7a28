import { commandIntents } from "@/copilot/constants";
import { ICommandAction } from "@casstime/copilot-core";
import { React, View, Image, Text } from "@casstime/copilot-xml";
import { IMerchantClue, IntelligentPlanType, IStoreInfo, LevelIconRatio } from "../../../inquiry/interface";

const MerchantServiceIcon = "https://cass-upload.oss-cn-shenzhen.aliyuncs.com/copilot/production/merchant.png";

export const StoreCard = ({
  merchantClue,
  storeInfo,
  disableAction,
}: {
  disableAction: boolean;
  merchantClue: IMerchantClue;
  storeInfo: IStoreInfo;
}) => {
  const { storeId, storeName, iconUri, levelValue, type } = storeInfo;
  const query = {
    storeId,
    clue: merchantClue,
  };

  const navigate = `cassapp://route/native/im/conversation?query=${JSON.stringify(query)}`;
  // 跳转商家按钮
  let action = {
    type: "command",
    text: storeName,
    command: commandIntents.commonNavigate,
    params: { navigate },
  } as ICommandAction | undefined;
  if (disableAction) {
    action = undefined;
  }

  return (
    <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
      <View style={{ flexDirection: "row", alignItems: "center", flexWrap: "wrap", maxWidth: 360 }} action={action}>
        <Text style={{ color: "#008CF5", fontSize: type === IntelligentPlanType.YUN_FACILITY ? 24 : 30 }}>
          {storeName}
        </Text>
        {iconUri && (
          <Image
            uri={iconUri}
            style={{ height: 25, width: 25 * LevelIconRatio[levelValue || 1], marginLeft: 12 }}
          ></Image>
        )}
      </View>
      <View
        action={action}
        style={{
          flexDirection: "row",
          alignItems: "center",
          maxHeight: 40,
          paddingLeft: 16,
          paddingRight: 16,
          borderRadius: 20,
          borderWidth: 1,
          borderColor: "#C8C9CC",
        }}
      >
        <Image uri={MerchantServiceIcon} style={{ width: 28, height: 28, marginRight: 6 }}></Image>
        <Text style={{ fontSize: 24, lineHeight: 40, color: "#" }}>沟通报价</Text>
      </View>
    </View>
  );
};
