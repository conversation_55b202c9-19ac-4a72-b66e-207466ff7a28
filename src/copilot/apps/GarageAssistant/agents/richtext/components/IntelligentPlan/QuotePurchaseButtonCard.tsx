import { useRequestMeta } from "@/common/asyncStore";
import { AppName, VersionEnum } from "@/common/enums";
import { createViewDetailAction } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/richtext/action";
import { ButtonText } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/richtext/action/interface";
import { commandIntents } from "@/copilot/constants";
import { ICommandAction } from "@casstime/copilot-core";
import { React, Text, View } from "@casstime/copilot-xml";
import { createGotoQuotationResultAction } from "../../../inquiry/copilot-xml/utils";

interface IQuotePurchaseButtonCardProps {
  quotationProductIds: string[];
  inquiryId: string;
  pageIndex: number;
  storeId: string;
  appName?: AppName;
  planId: string;
  planGroupId: string;
  disableAction: boolean;
}
// 跳转报价或者采购确认页按钮卡片
export const QuotePurchaseButtonCard = ({
  quotationProductIds,
  inquiryId,
  pageIndex,
  storeId,
  appName,
  planId,
  planGroupId,
  disableAction,
}: IQuotePurchaseButtonCardProps) => {
  const { appVersion } = useRequestMeta();
  const noSupportPurchaseModal = appVersion?.isLessThan(VersionEnum.SIX_7_0);

  let purchaseAction = createViewDetailAction({
    planGroupId,
    planId,
    inquiryId,
    text: ButtonText.GO_TO_PURCHASE,
    isChecked: true,
  }) as ICommandAction | undefined;
  const purchaseDetailAction = createViewDetailAction({
    planGroupId,
    planId,
    inquiryId,
    text: ButtonText.GO_TO_PURCHASE,
    isChecked: false,
  }) as ICommandAction;
  const quoteAction = createGotoQuotationResultAction(inquiryId, pageIndex, storeId);

  if ((noSupportPurchaseModal || !planId) && appName === AppName.GarageAssistant) {
    purchaseAction = {
      type: "command",
      text: ButtonText.GO_TO_PURCHASE,
      command: commandIntents.IMMEDIATE_PURCHASE,
      params: { inquiryId, quotationProductIds },
    } as ICommandAction;
  }
  const purchaseText = ButtonText.GO_TO_PURCHASE;
  let detailAction: ICommandAction | undefined =
    appName === AppName.AIPurchasePlanAssistant ? purchaseDetailAction : quoteAction;
  const detailText = appName === AppName.AIPurchasePlanAssistant ? "查看详情" : "查看商家更多报价";
  if (disableAction) {
    purchaseAction = undefined;
    detailAction = undefined;
  }
  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "flex-end",
        flexWrap: "wrap",
        marginTop: 16,
      }}
    >
      <View
        action={detailAction}
        style={{
          width: appName === AppName.AIPurchasePlanAssistant ? 152 : 256,
          height: 48,
          alignItems: "center",
          borderRadius: 24,
          borderWidth: 1,
          borderColor: "#DCDEE0",
        }}
      >
        <Text style={{ fontSize: 26 }}>{detailText}</Text>
      </View>
      <View
        action={purchaseAction}
        style={{
          width: 152,
          height: 48,
          alignItems: "center",
          marginLeft: 16,
          borderRadius: 24,
          borderWidth: 1,
          borderColor: "#E51E1E",
        }}
      >
        <Text style={{ fontSize: 26, color: "#E51E1E" }}>{purchaseText}</Text>
      </View>
    </View>
  );
};
