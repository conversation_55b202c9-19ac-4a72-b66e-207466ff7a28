import { React, View, Text, Image } from "@casstime/copilot-xml";
import { IMerchantClue, IntelligentPlanType } from "../../../inquiry/interface";
import { IPlanQuoteItem } from "@/clients/quote/interface";
import { commandIntents } from "@/copilot/constants";
import { ICommandAction } from "@casstime/copilot-core";

export interface QuoteItemProps {
  quote: Partial<IPlanQuoteItem>;
  quoteStoreIds: string[];
  quoteDepartureIds: string[];
  merchantClue?: IMerchantClue;
  type?: IntelligentPlanType;
  index: number;
  disableAction: boolean;
}
export const QuoteItemCard = (props: QuoteItemProps) => {
  const { quote, index, type, merchantClue, quoteStoreIds, quoteDepartureIds, disableAction } = props;

  if (!quote.price) {
    return (
      <View style={{ flexDirection: "row" }}>
        <Text style={{ fontSize: 28, fontWeight: "bold", marginRight: 8 }}>{index + 1}.</Text>
        <View style={{ flexDirection: "row", alignItems: "center", flexWrap: "wrap" }}>
          <Text style={{ fontSize: 28, fontWeight: "bold", marginRight: 16 }}>
            {quote.partsName || quote.needsName}:
          </Text>
          <Text style={{ color: "#979899", fontSize: 28 }}>暂无报价</Text>
        </View>
      </View>
    );
  }
  const {
    locationName = "",
    storeName = "",
    partsName,
    qualityName,
    storeId,
    categoryOriginalAssort,
    spdFeatureDetail,
    showPrice,
  } = quote;
  return (
    <View>
      <View style={{ flexDirection: "row" }}>
        {/* QuoteView */}
        <Text style={{ fontSize: 28, fontWeight: "bold", marginRight: 8 }}>{index + 1}.</Text>
        <View style={{ flexDirection: "row", alignItems: "center", flexWrap: "wrap" }}>
          <Text style={{ fontSize: 28, fontWeight: "bold", marginRight: 16 }}>{partsName}:</Text>
          <Text style={{ fontSize: 28, marginRight: 16 }}>{qualityName}</Text>
          <Text style={{ fontSize: 28 }}>¥ {typeof showPrice === "number" ? showPrice.toFixed(2) : "-"}</Text>
        </View>
      </View>

      {/* FurtherInfoView */}
      <View style={{ flexDirection: "row", flexWrap: "wrap", marginLeft: 34 }}>
        {categoryOriginalAssort && <Text style={{ color: "#FC6405", marginRight: 12, fontSize: 24 }}>配套</Text>}
        {categoryOriginalAssort && spdFeatureDetail && <Text style={{ marginRight: 8, color: "#FC6405" }} />}
        {spdFeatureDetail && (
          <Text style={{ color: "#FC6405", marginRight: 12, fontSize: 24 }}>{spdFeatureDetail}</Text>
        )}

        {/* 报价店铺数量大于1，非整单 */}
        {type === IntelligentPlanType.YUN_FACILITY && quoteStoreIds.length > 1 && (
          <CustomerServiceLink
            disableAction={disableAction}
            storeId={storeId!}
            storeName={storeName}
            clue={merchantClue}
          />
        )}
        {/* 是否多仓发货 */}
        {type === IntelligentPlanType.COLLECT && new Set(quoteDepartureIds).size > 1 && (
          <Text style={{ color: "#979899", fontSize: 24 }}>{locationName}</Text>
        )}
        {type && [(IntelligentPlanType.DEFAULT, IntelligentPlanType.COMPREHENSIVE)].includes(type) && (
          <>
            <CustomerServiceLink
              disableAction={disableAction}
              storeId={storeId!}
              storeName={storeName}
              clue={merchantClue}
            />
            <Text style={{ color: "#979899", fontSize: 24 }}>{locationName}</Text>
          </>
        )}
      </View>
    </View>
  );
};

const ICON_MERCHANT_SERVICE = "https://cass-upload.oss-cn-shenzhen.aliyuncs.com/copilot/production/merchant.png";

interface CustomerServiceLinkProps {
  storeId: string;
  storeName: string;
  clue?: IMerchantClue;
  icon?: string;
  disableAction: boolean;
}
export const CustomerServiceLink = (props: CustomerServiceLinkProps) => {
  const { storeId, storeName, clue, icon: iconUrl = ICON_MERCHANT_SERVICE, disableAction } = props;
  // 跳转商家按钮
  const query = {
    storeId,
    clue: clue,
  };
  const navigate = `cassapp://route/native/im/conversation?query=${JSON.stringify(query)}`;
  let action = {
    type: "command",
    text: storeName,
    command: commandIntents.commonNavigate,
    params: { navigate },
  } as ICommandAction | undefined;
  if (disableAction) {
    action = undefined;
  }
  return (
    <View style={{ flexDirection: "row", alignItems: "center" }} action={action}>
      <Text style={{ color: "#979899", fontSize: 24, marginRight: 12 }}>{storeName}</Text>
      {typeof iconUrl === "string" && <Image uri={iconUrl} style={{ width: 28, height: 28, marginRight: 12 }} />}
    </View>
  );
};
