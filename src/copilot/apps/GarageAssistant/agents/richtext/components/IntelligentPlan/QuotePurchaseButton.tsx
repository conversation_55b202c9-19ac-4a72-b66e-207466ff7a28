import { ICommandAction } from "@casstime/copilot-core";
import { React, View, Text } from "@casstime/copilot-xml";

interface IQuotePurchaseButtonProps {
  purchaseAction: ICommandAction;
  purchaseText: string;
  detailAction: ICommandAction;
  detailText: string;
}

export const QuotePurchaseButton = ({
  purchaseAction,
  purchaseText,
  detailAction,
  detailText,
}: IQuotePurchaseButtonProps) => {
  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "flex-end",
        flexWrap: "wrap",
        marginTop: 16,
      }}
    >
      <View
        action={detailAction}
        style={{
          width: 256,
          height: 48,
          alignItems: "center",
          borderRadius: 24,
          borderWidth: 1,
          borderColor: "#DCDEE0",
        }}
      >
        <Text style={{ fontSize: 26 }}>{detailText}</Text>
      </View>
      <View
        action={purchaseAction}
        style={{
          width: 152,
          height: 48,
          alignItems: "center",
          marginLeft: 16,
          borderRadius: 24,
          borderWidth: 1,
          borderColor: "#E51E1E",
        }}
      >
        <Text style={{ fontSize: 26, color: "#E51E1E" }}>{purchaseText}</Text>
      </View>
    </View>
  );
};
