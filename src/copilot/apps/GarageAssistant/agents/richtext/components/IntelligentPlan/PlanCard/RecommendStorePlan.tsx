import { React, View, Text } from "@casstime/copilot-xml";
import { StoreCard } from "../StoreCard";
import { IIntelligentPlan, IMerchantClue } from "../../../../inquiry/interface";

interface IRecommendStorePlanProps {
  intelligentPlan: IIntelligentPlan;
  merchantClue: IMerchantClue;
  disableAction: boolean;
}

export const RecommendStorePlan = ({ intelligentPlan, merchantClue, disableAction }: IRecommendStorePlanProps) => {
  const { storeName = "", storeId = "", iconUri, levelValue, name, type } = intelligentPlan;
  const storeInfo = { storeName, storeId, iconUri, levelValue, type };

  return (
    <View>
      <StoreCard disableAction={disableAction} merchantClue={merchantClue} storeInfo={storeInfo} />
      {name && <Text>【{name}】</Text>}
      <Text style={{ fontSize: 28 }}>报价中...</Text>
    </View>
  );
};
