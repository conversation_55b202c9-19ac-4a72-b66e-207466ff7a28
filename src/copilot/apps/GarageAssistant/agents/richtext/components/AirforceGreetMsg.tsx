import { React, View, Text, StyleSheet } from "@casstime/copilot-xml";
import { AccidentGreetButtonGrid } from "./AccidentGreetButtonGrid";

export function AirforceGreetMsg({ airforceGreetLabel }: { airforceGreetLabel: string[] }) {
  return (
    <View style={styles.container}>
      <Text style={styles.thirdTitle}>快告诉我 你遇到了什么空调故障呢～</Text>
      <View style={styles.downView}>
        {/* 故障选项按钮区域 */}
        <AccidentGreetButtonGrid labels={airforceGreetLabel} />

        <View style={styles.youCan}>
          <View style={styles.flex}>
            <Text style={styles.text}>您还可以</Text>
          </View>
          <View style={styles.btnFlex}>
            <Text style={styles.btnIcon}>●</Text>
            <Text style={styles.btnText}>扫VIN码</Text>
          </View>
          <View style={styles.btnFlex}>
            <Text style={styles.btnIcon}>●</Text>
            <Text style={styles.btnText}>输入配件</Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    overflow: "hidden",
    width: 600,
  },
  downView: {
    backgroundColor: "#fff",
    width: "100%",
  },
  thirdTitle: {
    fontSize: 28,
    color: "#FC6405",
    marginLeft: 24,
    marginTop: 30,
    fontWeight: "bold",
  },
  youCan: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    flexWrap: "wrap",
    alignItems: "flex-start",
    marginBottom: 8,
    backgroundColor: "#fff",
    marginRight: 24,
    marginLeft: 24,
    paddingRight: 24,
  },
  flex: {
    display: "flex",
    flexDirection: "row",
    flexWrap: "nowrap",
    alignItems: "center",
    marginBottom: 4,
    marginTop: 8,
  },
  text: {
    marginLeft: 4,
    color: "#646566",
    fontSize: 28,
  },
  btnFlex: {
    display: "flex",
    flexDirection: "row",
    flexWrap: "nowrap",
    alignItems: "center",
    width: "100%",
    marginLeft: 18,
  },
  btnIcon: {
    fontSize: 14,
    color: "#646566",
    marginRight: 4,
  },
  btnText: {
    marginLeft: 6,
    fontSize: 26,
    color: "#646566",
  },
});
