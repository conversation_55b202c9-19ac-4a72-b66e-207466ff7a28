import { React, View, Text, StyleSheet } from "@casstime/copilot-xml";
export function AccidentGreetMsg() {
  return (
    <View style={styles.youCan}>
      <View style={styles.flex}>
        <Text style={styles.text}>您可以</Text>
      </View>
      <View style={styles.btnFlex}>
        <Text style={styles.btnIcon}>●</Text>
        <Text style={styles.btnText}>拍工单</Text>
      </View>
      <View style={styles.btnFlex}>
        <Text style={styles.btnIcon}>●</Text>
        <Text style={styles.btnText}>拍事故图片</Text>
      </View>
      <View style={styles.btnFlex}>
        <Text style={styles.btnIcon}>●</Text>
        <Text style={styles.btnText}>扫VIN码</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  youCan: {
    width: "100%",
    display: "flex",
    flexDirection: "column",
    flexWrap: "wrap",
    alignItems: "flex-start",
    marginBottom: 8,
    backgroundColor: "#fff",
    marginRight: 24,
    marginLeft: 24,
    paddingRight: 24,
  },
  flex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
    marginTop: 16,
  },
  text: {
    marginLeft: 4,
    color: "#646566",
    fontSize: 28,
  },
  btnFlex: {
    display: "flex",
    flexDirection: "row",
    flexWrap: "nowrap",
    alignItems: "center",
    marginLeft: 18,
  },
  btnText: {
    marginLeft: 6,
    fontSize: 26,
    color: "#646566",
  },
  btnIcon: {
    fontSize: 14,
    color: "#646566",
    marginRight: 4,
  },
});
