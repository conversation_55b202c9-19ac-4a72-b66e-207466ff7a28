import { AgentName, VersionEnum } from "@/common/enums";
import { parseUnCompletedJsonMarkdown, stringifyHistory } from "@/common/utils";
import { AI_DISCLAIMER } from "@/copilot/constants";
import { createChatCompletion, doubaoDeepSeek_chat, renderPrompt } from "@/copilot/helpers";
import {
  Handler,
  IAction,
  IMarkdownMessage,
  ITextMessage,
  IVoiceMessage,
  MarkdownStreamReplier,
  MessageFactory,
} from "@casstime/copilot-core";
import { Types } from "mongoose";
import { partsMindClient } from "@/clients/parts-mind";
import { renderRichtext, ThinkingSteps } from "@/copilot/richtext";
import { AccidentParts } from "@/copilot/richtext/components/accident/AccidentParts";
import { llmLogCallback } from "@/copilot/helpers/llm/callbacks";
import _ from "lodash";
import { IRepairScenariosSlot } from "../../inquiry/interface/IRepariScenarios";
import { PartListWithEditBtnEmbed } from "@/copilot/richtext/components/accident/AccidentParts/AvailbleAccidentParts";
export class QAHandler extends Handler {
  entitiesMap: Record<string, string> = {};

  handle = async () => {
    const { prevMessageAgent, entities } = this.context;
    entities?.forEach((entity) => {
      this.entitiesMap[entity.name] = entity.value;
    });
    const nextAgent = prevMessageAgent === AgentName.partInfoAgent ? null : prevMessageAgent || AgentName.inquiryAgent;
    await this.replyQA();
    await this.context.activateAgent(nextAgent);
  };
  replyQA = async () => {
    const { lastMessage } = this.context;
    if (!(lastMessage.type === "text" || lastMessage.type === "voice")) {
      return;
    }
    const messageId = new Types.ObjectId().toString();
    // 设为流式输出
    this.context.setReplyMode("stream");
    const msgStream = await createChatCompletion([
      {
        role: "system",
        content: await renderPrompt("采购助手/采购助手汽配问答Agent提示词", {
          history: stringifyHistory(this.context.historyMessages, 5, { system: "你", user: "用户" }),
        }),
      },
      {
        role: "user",
        content: lastMessage.content!,
      },
    ]);
    const replier = MarkdownStreamReplier.for(this.context);
    const message = await replier.reply(msgStream, { id: messageId });

    this.context.reply(message, {
      disclaimer: AI_DISCLAIMER,
    });
    this.context.reply({
      ...message,
      tips: {
        type: "richtext",
        content: renderRichtext(ThinkingSteps, { steps: [{ label: "", done: false }] }),
      },
      disclaimer: AI_DISCLAIMER,
    });
    const accidentCode = await this.getAccidentCode();
    const relatedParts = await this.getAccidentRelatedParts(accidentCode);
    const slotPartNames = this.getInquiryAgentSlotPartNames();
    const partNameStream = await this.getRelatedPartsFromLLm({ answer: message.content!, relatedParts });
    let partNames: string[] = [];
    let partNameStr = "";
    for await (const item of partNameStream) {
      partNameStr += item.content;
      const repairPartNames = parseUnCompletedJsonMarkdown<string[]>(partNameStr) || [];
      partNames = repairPartNames.slice(0, repairPartNames.length - 1); // 最后一个配件可能是不完整的词语，去掉
      this.replyQAWithPartNames({ partNames, message, slotPartNames });
    }
    partNames = parseUnCompletedJsonMarkdown<string[]>(partNameStr) || [];
    this.setInquiryAgentRepariScenarios({ accidentCode, partNames });
    if (!partNames.length) {
      this.context.reply(message, {
        disclaimer: AI_DISCLAIMER,
      });
    } else {
      this.replyQAWithPartNames({ partNames, message, slotPartNames });
    }
  };
  replyQAWithPartNames = async ({
    partNames,
    message,
    slotPartNames,
  }: {
    partNames: string[];
    message: IMarkdownMessage;
    slotPartNames: string[];
  }) => {
    if (!partNames?.length) return;
    const { payload } = this.context;
    const lastMessage = this.context.lastMessage as ITextMessage | IVoiceMessage;
    const noSupportRichText = payload?.appVersion?.isLessThan(VersionEnum.FIVE_18_0);

    if (noSupportRichText) {
      const action: IAction = {
        type: "nlu",
        text: "查看配件价格",
        nlu: {
          intent: "inquiry",
          agentName: AgentName.inquiryAgent,
          slots: {
            partNames: partNames,
          },
        },
      };
      this.context.reply(
        MessageFactory.text("识别到配件：" + partNames.join("、") + "\n点击下方按钮即可查看报价", {
          actions: [[action]],
        })
      );
    } else {
      this.context.reply({
        ...message,
        embed: {
          type: "richtext",
          content: renderRichtext(PartListWithEditBtnEmbed, { partNames: slotPartNames, showBtn: false }),
        },
        tips: {
          type: "richtext",
          content: renderRichtext(AccidentParts, {
            messageId: message.id!,
            partNames: partNames,
            selectedPartNames: [],
            content: lastMessage.content || "",
            source: "accident" as const,
          }),
        },
      });
    }
  };
  getAccidentCode = async () => {
    const { lastMessage } = this.context;
    const { content } = lastMessage as ITextMessage;
    if (this.entitiesMap.accidentCode) {
      return this.entitiesMap.accidentCode;
    }
    const { scenarios } = await partsMindClient.getSimilarRepairScenarios({
      accidentText: content,
    });
    return scenarios[0].accidentCode;
  };
  getAccidentRelatedParts = async (accidentCode: string) => {
    const result = await partsMindClient.getAccidentRelatedParts({
      accidentCode: accidentCode || "",
      brandCode: this.entitiesMap.brandCode || "BMW",
      vehicleTypeCode: this.entitiesMap.vehicleTypeCode || "",
    });
    return _.uniq(result.parts.map((part) => part.part_std_name));
  };
  getRelatedPartsFromLLm = async ({ answer, relatedParts }: { answer: string; relatedParts: string[] }) => {
    const { lastMessage } = this.context;
    const { content } = lastMessage as ITextMessage;
    const prompt = await renderPrompt("采购助手/采购助手汽配问答配件提取", {
      input: content,
      answer,
      relatedParts,
    });
    const stream = await doubaoDeepSeek_chat.stream(prompt, { callbacks: [llmLogCallback] });
    return stream;
  };
  // 获取询价agent原有配件
  getInquiryAgentSlotPartNames = () => {
    const { prevMessageAgent } = this.context;
    const slotPartNames: string[] = [];
    if (prevMessageAgent === AgentName.inquiryAgent) {
      const inquiryAgentMemory = this.context.getAgentMemory(prevMessageAgent);
      const partNames = inquiryAgentMemory.slots.partNames || [];
      const partNameEntities = inquiryAgentMemory.entities
        ?.filter((entity) => entity.name === "partName")
        .map((entity) => entity.value);
      partNames.forEach((partName: string) => {
        if (!partNameEntities?.includes(partName)) {
          slotPartNames.push(partName);
        }
      });
      // 去除从当前用户输入中解析的配件
      inquiryAgentMemory.slots.partNames = slotPartNames;
    }
    return slotPartNames;
  };
  // 保存维修场景
  setInquiryAgentRepariScenarios = ({ accidentCode, partNames }: { accidentCode: string; partNames: string[] }) => {
    const { prevMessageAgent } = this.context;
    if (prevMessageAgent === AgentName.inquiryAgent) {
      const inquiryAgentMemory = this.context.getAgentMemory(prevMessageAgent);
      inquiryAgentMemory.slots.repairScenarios = {
        ...inquiryAgentMemory.slots.repairScenarios,
        accidentCode,
        partNames,
        selectedPartNames: [],
      } as IRepairScenariosSlot;
    }
  };
}
