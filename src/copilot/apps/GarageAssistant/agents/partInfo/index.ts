import { AgentName } from "@/copilot/constants";
import { Agent } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { QAHandler } from "./handlers/QAHandler";

/**
 * 发布询价Agent
 */
const agent = new Agent(AgentName.partInfoAgent);

/**
 * 第一次进入Agent
 */
agent.onEnter(() => {
  logger.info("进入 PartInfo Agent");
});

/**
 * 离开当前Agent
 */
agent.onLeave(() => {
  logger.info("离开 PartInfo Agent");
});

/**
 * 设置意图解析器
 */
agent.setIntentParser(async (context) => {
  context.setIntent("问答");
});

agent.handle("问答", QAHandler);

export default agent;
