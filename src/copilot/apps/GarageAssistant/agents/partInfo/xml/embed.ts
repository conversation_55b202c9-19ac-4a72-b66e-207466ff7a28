import { AgentName } from "@/common/enums";
import { AdditionalFormat } from "@casstime/copilot-core";
import _ from "lodash";

export function createEmbed(type: AdditionalFormat["type"], texts: string[]): AdditionalFormat {
  const text = texts.filter(Boolean).join(`<hr style="margin-vertical: 10;"></hr>`);
  if (!text) return { type, content: "" };
  const content = `
    <view style="background-color: #F7F8FA; border-radius: 16;padding: 20;">
      ${text}
    </view>
  `;
  return { type, content };
}

export function createRecognizePartNamesEmbed(partNames: string[]) {
  if (!partNames?.length) return "";
  return `
    <view>
      <text style="font-weight: bold;">识别到配件：</text>
      <text>${partNames.join("、")}</text>
      <view style="align-items: flex-end;margin-top: 20;">
        <button
        style="border-color: #E51E1E"
        textStyle="color: #E51E1E;"
        type="nlu"
        nlu="${_.escape(
          JSON.stringify({
            intent: "inquiry",
            agentName: AgentName.inquiryAgent,
            slots: {
              partNames: partNames,
            },
          })
        )}">查看价格</button>
      </view>
    </view>
  `;
}
