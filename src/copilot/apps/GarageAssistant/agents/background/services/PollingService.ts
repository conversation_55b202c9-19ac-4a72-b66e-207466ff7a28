import { ITaskPollingStatus, RecommendPlanGroup } from "@/models";
import {
  IGetInquiryQuoteRes,
  IInquiryPayload,
  IInquiryTaskParams,
  IRecommendPlanTaskParams,
  TaskType,
  IIntelligentPlanTaskParams,
  ITyreQuoteAndDemand,
  IQuoteResponseItem,
  IInquiryInfo,
} from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { taskService } from "../../inquiry/services/TaskService";
import { inquiryService } from "../../inquiry/services/InquiryService";
import { tyreService } from "../../inquiry/services/TyreService";
import { IAction, IMessage, ITextMessage, MessageFactory } from "@casstime/copilot-core";
import { commandIntents, Intents } from "@/copilot/constants";
import { inquiryClient } from "../../inquiry/clients/InquiryClient";
import { IGetRecommendPlanParams } from "../../inquiry/interface/IRecommendPlan";
import logger from "@/common/logger";
import { AppName, FeatureTogglesEnum } from "@/common/enums";
import { IGetIntelligentPlanParams } from "../../inquiry/interface/IIntelligentPlan";
import { createEmptyQuoteEmbed } from "../../inquiry/copilot-xml/embed";
import { messageService } from "../../inquiry/services/MessageService";
import { inquiryClient as inquiryClientGlobal } from "@/clients/inquiry";
import { InquiryStatus } from "@/clients/inquiry/interface";
import { IntentCodes } from "../../inquiry/enum";
import _ from "lodash";
import { createDataProvider } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/factory";
import { createTableXml } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/richtext";
import { purchasePlanService } from "@/service";
import { inquiryService as inquiryCommonService } from "@/service";
import { renderRichtext } from "@/copilot/richtext";
import { IntelligentPlan } from "../../richtext/components/IntelligentPlan";
import { ActionFactory } from "@/common/factory/ActionFactory";

class PollingService {
  public async processTask(dialogueId: string, companyId: string, headers: Record<string, string>, app: string) {
    const messages: IMessage[] = [];
    const tasks: ITaskPollingStatus[] = [];
    // 查询需要轮询的任务
    const pollingTasks = await taskService.getTaskPolling({
      dialogueId,
      done: false,
    });
    const isNeedInquiryPolling = app === AppName.ProxyInquiry;
    // 是否专属客服试点
    const isA9tAirforceEnter = await inquiryService.getIsPilotArea(companyId, FeatureTogglesEnum.A9T_AIRFORCE_ENTER);
    for (const pollingTask of pollingTasks) {
      switch (pollingTask.taskType) {
        // 询价轮询任务
        case TaskType.INQUIRY: {
          const { messageList, updateTask } = await this.processPollingInquiryTask(
            pollingTask,
            headers,
            isA9tAirforceEnter
          );
          messages.push(...messageList);
          tasks.push(updateTask);
          break;
        }
        // 推荐方案轮询任务
        case TaskType.RECOMMEND_PLAN_TASK: {
          const { messageList, updateTask } = await this.processPollingRecommendPlanTask(
            pollingTask,
            headers,
            isA9tAirforceEnter
          );
          messages.push(...messageList);
          tasks.push(updateTask);
          break;
        }
        // 智能方案任务
        case TaskType.INTELLIGENT_PLAN: {
          const { messageList, updateTask } = await this.processPollingIntelligentPlanTask(
            pollingTask,
            isA9tAirforceEnter,
            isNeedInquiryPolling
          );
          messages.push(...messageList);
          if (updateTask) {
            tasks.push(updateTask);
          }
          break;
        }
        // 采购方案任务
        case TaskType.PURCHASE_PLAN: {
          const { messageList, updateTask } = await this.processPollingAIPurchasePlanTask(pollingTask);
          messages.push(...messageList);
          if (updateTask) {
            tasks.push(updateTask);
          }
          break;
        }
        default:
          break;
      }
    }
    return { messages, tasks };
  }

  async processPollingInquiryTask(
    task: ITaskPollingStatus,
    headers: Record<string, string>,
    isA9tAirforceEnter = false
  ) {
    const { params = {} } = task;
    const { messageId, expiration } = params as IInquiryTaskParams;
    const { updateInquiryCard } = params as IInquiryTaskParams;
    const messageList: IMessage[] = [];

    // 0、判断轮询任务是否过期, 不存在过期时间或者messageId则结束轮询
    if (!messageId || !expiration || expiration < new Date().getTime()) {
      return {
        messageList,
        updateTask: { ...task, done: true },
      };
    }
    // 1、轮胎询价
    const { tyreMessages, tyreNeedPolling, tyreQuote } = await this.processTyreInquiryHandle(params);
    messageList.push(...tyreMessages);

    // 2、全车件询价
    const { inquiryMessages, inquiryQuote, inquiryNeedPolling, inquiryCount } = await this.processInquiryHandle(
      params,
      headers,
      isA9tAirforceEnter
    );
    messageList.push(...inquiryMessages);

    // 3、判断轮询任务是否结束
    const done = !(tyreNeedPolling || inquiryNeedPolling);
    if (!done) {
      messageList.push(
        MessageFactory.command(
          commandIntents.inquiryPolling,
          {},
          {
            background: true,
            reply: {
              type: "command",
              command: commandIntents.inquiryPolling,
              background: true,
              replyDelay: 10 * 1000,
              params: {},
            },
          }
        )
      );
    }

    // 4、更新轮询任务的params
    const pollingParams = {
      ...params,
      tyreNeedPolling,
      tyreQuote,
      inquiryQuote,
      inquiryNeedPolling,
      inquiryCount,
      updateInquiryCard,
    };
    return {
      messageList,
      updateTask: { ...task, done, params: pollingParams },
    };
  }

  public async processTyreInquiryHandle(params: Partial<IInquiryTaskParams>) {
    const { demandId, messageId, isProxyInquiry, userLoginId, garageCompanyId } = params;
    let { tyreQuote = {}, tyreNeedPolling } = params;
    let hasTryeQuote = false;
    const tyreMessages: IMessage[] = [];
    if (demandId && tyreNeedPolling) {
      // 构造 inquiryInfo 参数
      const inquiryInfo = {
        isProxyInquiry: isProxyInquiry || false,
        userLoginId: userLoginId || "",
        garageCompanyId: garageCompanyId || "",
      };
      const tyreQuoteNew = await tyreService.getTyreInquiryDemandAndQuote(demandId, inquiryInfo);
      // 过滤缺货并对报价排序
      const orderQuoteList = this.processQuoteList(tyreQuote, tyreQuoteNew.list);
      if (orderQuoteList?.length) {
        tyreQuoteNew.list = orderQuoteList;
      }
      tyreQuote = { ...tyreQuote, ...tyreQuoteNew };
      // 有轮胎报价才推送消息
      hasTryeQuote = Boolean(tyreQuote.list?.some((item) => item?.quotationProductList?.length));
      if (hasTryeQuote) {
        tyreNeedPolling = false;
        const tyreQuoteEmbed = inquiryService.createTyreInquiryQuoteRichText(tyreQuote);
        const suggestions = this.generateSuggestions(tyreQuote);
        const suggestionActions: IAction[] = suggestions.map((keyword) => ({
          type: "nlu",
          text: keyword,
          nlu: { intent: IntentCodes.QUOTE_FILTER },
        }));

        const messageParams: any = {
          id: messageId,
          embed: tyreQuoteEmbed,
          actions: [suggestionActions],
        };

        // 代理询价时添加复制询价单和分享维修厂按钮
        if (isProxyInquiry) {
          const tyreActions = this.getProxyTyreInquiryAppActions(demandId, tyreQuote, params);
          messageParams.actions.unshift(...tyreActions);
        }

        tyreMessages.push(MessageFactory.text("久等啦~已经帮您问到以下报价，您看下", messageParams));
      }
    }
    return {
      tyreNeedPolling,
      tyreQuote,
      hasTryeQuote,
      tyreMessages,
    };
  }

  async processInquiryHandle(
    params: Partial<IInquiryTaskParams>,
    headers: Record<string, string>,
    isA9tAirforceEnter = false
  ) {
    const { inquiryId, source, fromPage, isOpenInvoice, messageId } = params;
    let { inquiryQuote, inquiryNeedPolling, inquiryCount = 0 } = params;
    const inquiryMessages: IMessage[] = [];
    let count = inquiryCount;
    if (inquiryId && inquiryNeedPolling) {
      // 查询inquiryCount
      count = await inquiryService.getInquiryCount(inquiryId);
      if (count > inquiryCount) {
        inquiryQuote = await inquiryService.getInquiryQuote(inquiryId, headers, source, fromPage, isOpenInvoice);
        const inquiryCountReal = inquiryQuote?.inquiryCount || 0;
        if (inquiryCountReal > inquiryCount) {
          // 推送新的报价信息
          inquiryCount = inquiryCountReal;
          inquiryNeedPolling = inquiryQuote?.inquiryNeedPolling;
          const inquiryQuoteEmbed = await inquiryService.createInquiryQuoteRichText(
            inquiryQuote,
            source,
            isA9tAirforceEnter
          );
          inquiryMessages.push(
            MessageFactory.text("久等啦~已经帮您问到以下报价，您看下", { id: messageId, embed: inquiryQuoteEmbed })
          );
        }
      }
    }
    return {
      inquiryQuote,
      hasInquiryQuote: inquiryQuote?.quoteStoreList.length,
      inquiryNeedPolling,
      inquiryCount,
      inquiryMessages,
    };
  }
  async processRecommendPlanHandle(
    params: Partial<IRecommendPlanTaskParams>,
    headers: Record<string, string>,
    isA9tAirforceEnter = false
  ) {
    const {
      inquiryId,
      finished,
      source,
      fromPage,
      isOpenInvoice,
      inquiryProgrammeGroupId,
      turns,
      hasSentTurn,
      inquiryProgrammeIds = [],
    } = params;
    let updateParams = { ...params };
    let inquiryQuote: IGetInquiryQuoteRes = {} as IGetInquiryQuoteRes;
    const recommendMessages: IMessage[] = [];
    // 没有turns，则表示无方案无需发送，如果有，就跟已发送的turns比较
    const hasSent = !turns || hasSentTurn === turns;
    try {
      // 判断该方案是否已发送
      if (!hasSent && inquiryProgrammeGroupId) {
        const reqBody: IGetRecommendPlanParams = {
          programmeGroupId: inquiryProgrammeGroupId,
          turns,
        };
        const { statusCode, result = [] } = await inquiryClient.getPlanDetailById(reqBody);

        if (statusCode === 200 && result.length && inquiryId) {
          // 更新已发送的轮次和ids
          updateParams = { ...updateParams, hasSentInquiryProgrammeIds: inquiryProgrammeIds, hasSentTurn: turns };
          logger.info("更新已发送的轮次和方案id数组", { params: updateParams });

          // 根据报价id请求报价数据，
          const inquiryQuoteRes = await inquiryService.getInquiryQuoteByRecommend(
            inquiryId,
            headers,
            source,
            fromPage,
            isOpenInvoice,
            result
          );
          if (inquiryQuoteRes) {
            inquiryQuote = inquiryQuoteRes;
            const inquiryQuoteEmbed = await inquiryService.createRecommendRichText(
              inquiryQuoteRes,
              source,
              isOpenInvoice,
              isA9tAirforceEnter
            );
            recommendMessages.push(
              MessageFactory.text(`久等啦~已经帮您问到${result.length}个合适的报价方案，您看下`, {
                embed: inquiryQuoteEmbed,
              })
            );
          }
        }
      }
      return {
        recommendMessages,
        inquiryQuote,
        hasInquiryQuote: !!inquiryQuote.quoteStoreList?.length,
        done: !!(hasSent && finished),
        updateParams,
      };
    } catch (error) {
      logger.warn("轮询推送推荐方案失败", error);
      return {
        done: !!(hasSent && finished),
        updateParams,
      };
    }
  }
  async processPollingRecommendPlanTask(
    task: ITaskPollingStatus,
    headers: Record<string, string>,
    isA9tAirforceEnter = false
  ) {
    const messageList: IMessage[] = [];
    const { params = {} } = task;
    const { messageId, expiration } = params as IRecommendPlanTaskParams;
    // 判断轮询任务是否过期
    if (expiration < new Date().getTime()) {
      return {
        messageList,
        updateTask: { ...task, done: true },
      };
    }
    let { updateInquiryCard } = params as IRecommendPlanTaskParams;

    const {
      recommendMessages = [],
      done,
      updateParams,
      hasInquiryQuote,
      inquiryQuote,
    } = await this.processRecommendPlanHandle(params, headers, isA9tAirforceEnter);
    if (!updateInquiryCard && hasInquiryQuote) {
      updateInquiryCard = true;
      const emptyQuoteRichText = inquiryService.createEmptyQuoteRichText(
        inquiryQuote?.inquiryDetail,
        {},
        true,
        isA9tAirforceEnter
      );
      const message = MessageFactory.text("询价成功", { id: messageId, embed: emptyQuoteRichText });
      messageList.push(message);
    }
    messageList.push(...recommendMessages);

    if (!done) {
      messageList.push(
        MessageFactory.command(
          commandIntents.inquiryPolling,
          {},
          {
            background: true,
            reply: {
              type: "command",
              command: commandIntents.inquiryPolling,
              background: true,
              replyDelay: 10 * 1000,
              params: {},
            },
          }
        )
      );
    }
    return {
      updateTask: { ...task, params: { ...updateParams, updateInquiryCard }, done: !!done },
      messageList,
    };
  }

  async processIntelligentPlanHandle(
    params: IIntelligentPlanTaskParams,
    inquiryPayload: IInquiryPayload,
    isA9tAirforceEnter: boolean,
    isNeedInquiryPolling: boolean
  ) {
    const { recommendContentsGroupId, turns, messageId, inquiryDetail = {}, inquiryId, source, fromPage } = params;
    const recommendMessages: IMessage[] = [];
    let updateParams = { ...params };
    const reqBody: IGetIntelligentPlanParams = {
      recommendContentsGroupId,
      turns,
    };
    const { statusCode, result } = await inquiryClient.getIntelligentPlanDetailById(reqBody);
    if (statusCode === 200 && result) {
      // 根据报价id请求报价数据，
      const dataProvider = createDataProvider(inquiryId);
      await dataProvider.prefetch();
      const {
        quoteDetail: { quotes },
      } = await dataProvider.getAll();
      const [intelligentPlans, { needDecodeList }, recommendPlanGroup, hasEpcImage] = await Promise.all([
        inquiryService.getInquiryQuoteByPurchase(result, inquiryDetail, quotes),
        inquiryCommonService.getNeedDecodeList(inquiryId),
        RecommendPlanGroup.findOne({ inquiryId, firstRecommend: false, recommendContentsGroupId }).sort({
          createdAt: -1,
        }),
        inquiryService.isHasEpcImage({ inquiryId, source, fromPage }),
      ]);
      if (intelligentPlans.length) {
        recommendPlanGroup?.plans?.forEach((plan, idx) => {
          if (intelligentPlans[idx]) {
            intelligentPlans[idx]._id = plan._id;
          }
        });
        const planGroupId = recommendPlanGroup?._id?.toString() || "";

        const inquiryQuoteEmbed = {
          type: "richtext",
          content: renderRichtext(IntelligentPlan, {
            intelligentPlans,
            inquiryDetail,
            needDecodeList,
            isA9tAirforceEnter,
            appName: AppName.GarageAssistant,
            planGroupId,
            hasEpcImage,
          }),
        };
        // 将历史报价信息保存到extra中
        const extra = await this.savePrevQuoteToExtra(messageId);
        const messageParams: Partial<ITextMessage> = {
          id: messageId,
          embed: inquiryQuoteEmbed,
        };
        if (extra) {
          messageParams.extra = {
            ...extra,
            keepTop: true,
            needFetch: true,
            planGroupId,
            planInfo: intelligentPlans.map((item) => {
              return {
                planId: item._id || "",
                name: item.name,
                type: item.type,
                reason: item.reason,
                quoteIds:
                  item.quotes?.filter((item) => item.quotationProductId).map((item) => item.quotationProductId) || [],
              };
            }),
          };
        }
        if (isNeedInquiryPolling) {
          // app 等于 ProxyInquiry 时，才显示分享维修厂，复制询价单
          const actions = this.getProxyInquiryAppActions(inquiryId, inquiryDetail);
          messageParams.actions = (messageParams.actions || []).concat(actions);
        }
        recommendMessages.push(
          MessageFactory.text(`久等啦~已经帮您问到${intelligentPlans.length}个合适的报价方案，您看下`, messageParams)
        );
        // 更新已发送的轮次
        updateParams = { ...updateParams, hasSentTurn: turns };
      }
    }

    return {
      recommendMessages,
      updateParams,
    };
  }

  async processPollingIntelligentPlanTask(
    task: ITaskPollingStatus,
    isA9tAirforceEnter: boolean,
    isNeedInquiryPolling: boolean
  ) {
    const messageList: IMessage[] = [];
    const { companyId = "", userId = "", taskId, done } = task;
    let updateTask = { ...task };
    const params = task.params as IIntelligentPlanTaskParams;
    const {
      expiration,
      isOpenInvoice,
      source,
      fromPage,
      finished,
      inquiryDetail = {},
      messageId,
      hasSentTurn,
      turns,
    } = params;
    // 1-方案提前结束，暂无报价提示
    if (finished) {
      const inquiryQuoteEmbed = createEmptyQuoteEmbed(inquiryDetail, isA9tAirforceEnter);
      messageList.push(
        MessageFactory.text(`暂时未找到合适报价${isA9tAirforceEnter ? "，可以联系专属导购帮忙哦~" : ""}`, {
          id: messageId,
          embed: inquiryQuoteEmbed,
        })
      );
      updateTask.done = true;
      return {
        messageList,
        updateTask,
      };
    }

    // 2-询价单过期或者任务过期
    let noQuote = false;
    try {
      const inquiryResponse = await inquiryClientGlobal.getInquiryInfo(taskId);
      noQuote = ![InquiryStatus.QUOTE, InquiryStatus.UNQUOTE].includes(
        inquiryResponse?.inquiryBaseInfos?.statusId as InquiryStatus
      );
    } catch (error) {
      logger.error("查询询价单状态失败", error);
    }
    if (expiration < new Date().getTime() || noQuote) {
      updateTask.done = true;

      return {
        messageList,
        updateTask,
      };
    }

    if (!done && isNeedInquiryPolling) {
      messageList.push(
        MessageFactory.command(
          commandIntents.inquiryPolling,
          {},
          {
            background: true,
            reply: {
              type: "command",
              command: commandIntents.inquiryPolling,
              background: true,
              replyDelay: 10 * 1000,
              params: {},
            },
          }
        )
      );
    }

    // 3-没有新方案，不更新任务
    if (hasSentTurn >= turns) {
      return {
        messageList,
      };
    }

    // 4-查询新方案
    const inquiryPayload: IInquiryPayload = {
      companyId,
      userId,
      isOpenInvoice,
      source,
      fromPage,
    };
    const { recommendMessages, updateParams } = await pollingService.processIntelligentPlanHandle(
      params,
      inquiryPayload,
      isA9tAirforceEnter,
      isNeedInquiryPolling
    );
    if (recommendMessages.length) {
      messageList.push(...recommendMessages);
      updateTask = { ...updateTask, params: { ...updateParams } };
    }
    return {
      messageList,
      updateTask,
    };
  }

  async processPollingAIPurchasePlanHandle(params: IIntelligentPlanTaskParams) {
    const { recommendContentsGroupId, turns, messageId } = params;
    const { inquiryId = "" } = params?.inquiryDetail || {};
    const recommendMessages: IMessage[] = [];
    let updateParams = { ...params };
    const reqBody: IGetIntelligentPlanParams = {
      recommendContentsGroupId,
      turns,
    };
    const { statusCode, result } = await inquiryClient.getIntelligentPlanDetailById(reqBody);
    if (statusCode === 200 && result) {
      // 根据报价id请求报价数据，
      const dataProvider = createDataProvider(inquiryId);
      const {
        quoteDetail: { quotes },
        inquiryDetail,
        needDecodeList: { needDecodeList, needsNames },
      } = await dataProvider.getAll();
      inquiryDetail.needsNames = needsNames;
      const { userId = "", companyId = "", vin: vinCode } = inquiryDetail;
      // 方案信息增加特性组合
      const planGroupInfo = await purchasePlanService.fillAndSaveRecommendPlan({
        makerType: "MACHINE_LEARNING",
        userLoginId: userId,
        companyId: companyId,
        plans: result.recommendProgrammes,
        inquiryId,
        recommendContentsGroupId,
      });
      result.recommendProgrammes = planGroupInfo.plans;
      const intelligentPlans = await inquiryService.getInquiryQuoteByPurchase(result, inquiryDetail, quotes);
      if (intelligentPlans.length) {
        const tableParams = {
          tableData: intelligentPlans,
          isOpenInvoice: inquiryDetail.openInvoiceType === "YES",
          planGroupId: planGroupInfo.id,
          needs: needDecodeList,
        };
        const tableTsx = createTableXml(tableParams);
        // 方案信息
        const planInfo = intelligentPlans.map((item) => {
          return {
            planId: item._id || "", // 方案id
            name: item.name, // 方案名称
            type: item.type, // 方案类型;共享仓方案:YUN_FACILITY,单店集采方案:COLLECT
            reason: item.reason,
            quoteIds:
              item.quotes?.filter((item) => item.quotationProductId).map((item) => item.quotationProductId) || [],
          };
        });

        // 询价卡片
        const indicator = inquiryService.createEmptyQuoteRichText(inquiryDetail, {}, true, false);
        const messageParams: Partial<ITextMessage> = {
          id: messageId,
          embed: tableTsx,
          indicator,
          extra: { keepTop: true, vinCode, inquiryId, planInfo, needFetch: true, planGroupId: planGroupInfo.id },
        };

        recommendMessages.push(
          MessageFactory.text(
            `综合行业评价 + 商家优势 + 采购倾向，满足高品质和比价诉求，推荐你以下${intelligentPlans.length}个采购方案`,
            messageParams
          )
        );
        // 更新已发送的轮次
        updateParams = { ...updateParams, hasSentTurn: turns };
      }
    }

    return {
      recommendMessages,
      updateParams,
    };
  }

  async processPollingAIPurchasePlanTask(task: ITaskPollingStatus) {
    const messageList: IMessage[] = [];
    let updateTask = { ...task, done: true };
    const { recommendContentsGroupId, turns, finished } = (task.params as IIntelligentPlanTaskParams) || {};
    // 0-方案提前结束，不再轮询
    if (finished) {
      return {
        messageList,
        updateTask,
      };
    }
    // 1-暂无方案
    if (!recommendContentsGroupId) {
      updateTask.done = false;
      // 继续轮询
      messageList.push(
        MessageFactory.command(
          commandIntents.inquiryPolling,
          {},
          {
            background: true,
            reply: {
              type: "command",
              command: commandIntents.inquiryPolling,
              background: true,
              replyDelay: 10 * 1000,
              params: {},
            },
          }
        )
      );
      return {
        messageList,
      };
    }

    const params = task.params as IIntelligentPlanTaskParams;
    const tempParams = { ...params, recommendContentsGroupId, turns };

    // 3-推送方案
    const { recommendMessages, updateParams } = await pollingService.processPollingAIPurchasePlanHandle(tempParams);
    if (recommendMessages.length) {
      messageList.push(...recommendMessages);
      updateTask = { ...updateTask, params: { ...updateParams } };
    }
    return {
      messageList,
      updateTask,
    };
  }

  /** 将之前的报价信息保存到extra */
  async savePrevQuoteToExtra(messageId?: string) {
    if (!messageId) return;
    try {
      const prevMsg = (await messageService.getMessageById(messageId)) as ITextMessage;
      const { extra: prevExtra, embed, content, tips } = prevMsg;
      const extra = {
        ...prevExtra,
        updateCount: prevExtra?.updateCount ? prevExtra?.updateCount + 1 : 1,
        messages: [
          ...(prevExtra?.messages || []).slice(-9), // 最多保存10条记录
          {
            embed: embed,
            content: content,
            tips: tips,
          },
        ],
      };
      return extra;
    } catch (e) {
      logger.warn("保存历史报价信息到extra失败", e);
    }
  }

  /** 生成轮胎报价筛选推荐话语 */
  generateSuggestions(tyreQuote: ITyreQuoteAndDemand): string[] {
    const keywords = [
      "米其林",
      "倍耐力",
      "邓禄普",
      "韩泰",
      "锦湖",
      "优科豪马",
      "固特异",
      "普利司通",
      "马牌",
      "玲珑",
      "防爆",
      "静音棉",
    ];
    const flattenedQuotationProductList =
      tyreQuote?.list?.flatMap((item) => item.quotationProductList).filter(Boolean) || [];
    // 仓库
    const locationNames = flattenedQuotationProductList.map((product) => product?.locationName || "").filter(Boolean);
    const randomLocationName = locationNames[Math.floor(Math.random() * locationNames.length)] || "";
    // 报价title
    const partsNames: string[] = flattenedQuotationProductList?.map((product) => product?.partsName || "");
    const keywordsInPartsNames = keywords.filter((keyword) =>
      partsNames.some((item) => item.includes(keyword) && !item.includes("非" + keyword))
    );
    const suggestions = _.uniq(keywordsInPartsNames.concat(randomLocationName).concat("我要最便宜的")).filter(Boolean);
    return _.shuffle(suggestions).splice(0, 2);
  }

  private getMatchCount(partsName: string, brandName?: string, pattern?: string, explosionProofMark?: string) {
    let count = 0;
    if (brandName && partsName.includes(brandName)) count++;
    if (pattern && partsName.includes(pattern)) count++;
    if (explosionProofMark && partsName.includes("防爆") && !partsName.includes("非防爆")) count++;
    return count;
  }

  private processQuoteList(tyreQuote: ITyreQuoteAndDemand, quoteList: IQuoteResponseItem[] = []) {
    return quoteList
      ?.map((item) => {
        const matchOriginalItem = tyreQuote.originalItems?.find(
          (originalItem) => item.partsNumber === originalItem.description
        );
        const { brandName, pattern, explosionProofMark } = matchOriginalItem || {};
        const quotationProductList = item.quotationProductList
          ?.filter((product) => product?.quoteStatus !== "outOfStock")
          ?.sort((a, b) => {
            const countA = this.getMatchCount(a?.partsName || "", brandName, pattern, explosionProofMark);
            const countB = this.getMatchCount(b?.partsName || "", brandName, pattern, explosionProofMark);
            return countB - countA;
          });
        return { ...item, quotationProductList };
      })
      .filter((item) => item?.quotationProductList?.length);
  }
  // 全车件按钮
  private getProxyInquiryAppActions(inquiryId: string, inquiryDetail: IInquiryInfo) {
    const copyInquiryAction = ActionFactory.command("复制询价单", Intents.COPY_INQUIRY, {
      theme: "primary",
      params: {
        title: "已为您发起询价，询价单信息如下：",
        inquiryId: inquiryId,
        carModel: inquiryDetail.saleModelName,
        vinCode: inquiryDetail.vin,
        needNames: inquiryDetail.needsNames,
      },
    });
    const shareInquiryAction = ActionFactory.command("分享维修厂", Intents.SHARE_GARAGE, {
      theme: "primary",
      params: {
        clueType: "inquiry",
        clueId: inquiryId,
        companyId: inquiryDetail?.companyId,
        userId: inquiryDetail?.userId,
      },
    });
    return [[copyInquiryAction, shareInquiryAction]];
  }
  // 轮胎按钮
  private getProxyTyreInquiryAppActions(
    demandId: string,
    tyreQuote: ITyreQuoteAndDemand,
    params: Partial<IInquiryTaskParams>
  ) {
    const copyInquiryAction = ActionFactory.command("复制询价单", commandIntents.COPY_INQUIRY, {
      theme: "primary",
      params: {
        title: "已为您发起询价，询价单信息如下：",
        inquiryId: demandId,
        carModel: tyreQuote.saleModelName || "-",
        vinCode: tyreQuote.vin || "-",
        needNames: tyreQuote.originalItems?.map((item) => item.description).filter(Boolean),
      },
    });
    const shareInquiryAction = ActionFactory.command("分享维修厂", commandIntents.SHARE_GARAGE, {
      theme: "primary",
      params: {
        clueType: "inquiry",
        clueId: demandId,
        companyId: params?.garageCompanyId,
        userId: params?.userLoginId,
      },
    });
    return [[copyInquiryAction, shareInquiryAction]];
  }
}

export const pollingService = new PollingService();
