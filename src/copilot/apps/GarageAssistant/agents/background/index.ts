import { Agent, ICommandMessage } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { taskService } from "../inquiry/services/TaskService";
import { pollingService } from "./services/PollingService";
import { BackgroundIntents } from "./constans/enum";

/**
 * 后台任务Agent
 */
const agent = new Agent("background");

agent.onEnter(() => {
  logger.info("background agent enter");
});

agent.onLeave(() => {
  logger.info("background agent leave");
});

/**
 * 设置意图解析器
 */
agent.setIntentParser(async () => { });
agent.handleCommand(BackgroundIntents.INQUIRY_POLLIN, async (context) => {
  const { payload } = context;
  const { data, headers = {}, app } = payload;
  // 执行轮询任务
  const { messages, tasks } = await pollingService.processTask(data.dialogueId || "", payload.companyId, headers, app);
  // 更新轮询任务
  for (const task of tasks) {
    const { taskId, params, done, taskType } = task;
    await taskService.updateTaskPolling({ taskId, taskType, done: false }, { params, done });
  }

  // inquiryPolling的command消息需要去重后再回复,其他消息直接回复
  const commandMessages: ICommandMessage[] = [];
  messages.forEach((message) => {
    if (message.type === "command" && message.command === BackgroundIntents.INQUIRY_POLLIN) {
      commandMessages.push(message);
    } else {
      context.reply(message);
    }
  });
  if (commandMessages.length > 0) {
    context.reply(commandMessages[0]);
  }
});

export default agent;
