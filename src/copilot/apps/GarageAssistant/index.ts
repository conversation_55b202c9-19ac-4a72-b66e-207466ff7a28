import { Application } from "@casstime/copilot-core";
import inquiryAgent from "./agents/inquiry";
import fallbackAgent from "./agents/fallback";
import partInfoAgent from "./agents/partInfo";
import backgroundAgent from "./agents/background";
import productsAgent from "./agents/products";
import partExpertAgent from "./agents/partExpert";

import { AppName } from "../../constants";
import classifier from "./classifier";
import { SceneCodes } from "./constants";
import { beforeRoute } from "./middleware";

const app = new Application(AppName.GarageAssistant, {
  sessionExpireTime: 60 * 60 * 1000,
});

// 路由之前的中间件
app.onBeforeRoute(beforeRoute);

// 设置后台Agent
app.setBackgroundAgent(backgroundAgent);

// 设置场景分类器
app.setSceneClassifier(classifier);

// 注册场景路由
app.route(SceneCodes.询报价, inquiryAgent);

app.route(SceneCodes.汽配知识问答, partInfoAgent);

app.route(SceneCodes.商品推荐, productsAgent);

app.route(SceneCodes.配件专家, partExpertAgent);

app.route(SceneCodes.FALLBACK, fallbackAgent);

export default app;
