import { SceneCodes } from "./constants";
import inquiryAgent from "./agents/inquiry";
import fallbackAgent from "./agents/fallback";
import partInfoAgent from "./agents/partInfo";
import productsAgent from "./agents/products";
import partExpertAgent from "./agents/partExpert";
import { Agent, Code } from "@casstime/copilot-core";

interface Scene {
  code: Code;
  name: string;
  agent: Agent;
}

export const Scenes: Record<Code, Scene> = {
  [SceneCodes.询报价]: {
    code: SceneCodes.询报价,
    name: "询报价Agent",
    agent: inquiryAgent,
  },
  [SceneCodes.汽配知识问答]: {
    code: SceneCodes.汽配知识问答,
    name: "汽配知识问答Agent",
    agent: partInfoAgent,
  },
  [SceneCodes.商品推荐]: {
    code: SceneCodes.商品推荐,
    name: "商品推荐Agent",
    agent: productsAgent,
  },
  [SceneCodes.配件专家]: {
    code: SceneCodes.配件专家,
    name: "配套品牌Agent",
    agent: partExpertAgent,
  },
  [SceneCodes.FALLBACK]: {
    code: SceneCodes.FALLBACK,
    name: "FallbackAgent",
    agent: fallbackAgent,
  },
};
