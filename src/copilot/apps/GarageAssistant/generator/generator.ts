import logger from "@/common/logger";
import { createChatCompletion, createIMChatCompletion, executePrompt, renderPrompt } from "@/copilot/helpers/llm";
import { waitStreamContent } from "@/copilot/helpers/llm";
import { config } from "@casstime/apollo-config";

export async function generateFallbackStreamText(
  history: string,
  input: string,
  relatedQA: { question: string; answer: string }[]
) {
  const prompt = await renderPrompt("采购助手/采购助手兜底回复提示词", {
    question_answers: relatedQA,
    history: history,
  });
  logger.debug("prompt:\n", prompt);
  return createChatCompletion([
    { role: "system", content: prompt },
    { role: "user", content: input },
  ]);
}

export async function generateGuidanceText(input: string = "", answer: string = "", hasVinCode: boolean) {
  return executePrompt("采购助手/采购助手询价表单引导提示词", { input, answer, hasVinCode });
}

/**
 * 判断输入语句是否属于轮胎筛选意图
 * @param input
 * @returns {"A" | "B"}  A: 是，B: 不是
 */
export async function classifyTyreFilterIntent(input: string = "", history: string): Promise<"A" | "B"> {
  try {
    const stream = await executePrompt("采购助手/采购助手轮胎筛选意图判断提示词", { userInput: input, history });
    const content = await waitStreamContent(stream);
    return content as "A" | "B";
  } catch (error) {
    logger.info("classifyTyreFilterIntent", error);
    return "A";
  }
}

export async function generateImQAText(
  history: string,
  input: string,
  relatedQA: { question: string; answer: string }[]
) {
  const imQAPrompt = config.get("IM_QA_PROMPT");
  const prompt = await renderPrompt("采购助手/IM问答提示词", {
    question_answers: relatedQA,
    history: history,
    imQAPrompt: imQAPrompt,
  });

  logger.debug("prompt:\n", prompt);
  return createIMChatCompletion([
    { role: "system", content: prompt },
    { role: "user", content: input },
  ]);
}
