import { terminalApiV2Client } from "@/clients/terminalapi-v2";
import logger from "@/common/logger";
import { isDev } from "@/common/utils";
import { interceptRuleService } from "@/service";
import type { Context, IMessage, INLU } from "@casstime/copilot-core";

export const beforeRoute = async (context: Context) => {
  const lastMessage = context.lastMessage;
  // 带nlu时不进行拦截
  if (lastMessage.nlu) {
    return;
  }
  // 只拦截用户发送的文本消息和语音消息
  if (lastMessage.type === "text" || lastMessage.type === "voice") {
    const content = lastMessage.content;
    if (!content) {
      return;
    }

    const rule = await interceptRuleService.findMatchedRule(context.app, content).catch((err) => {
      // 规则匹配出错不影响业务
      logger.warn("findMatchedRule error", err);
      return null;
    });

    if (!rule) {
      return;
    }
    // 匹配到拦截规则
    const { response, responseType } = rule;
    if (responseType === "message") {
      logger.info(`用户(${lastMessage.fromUser})消息命中敏感词，拒绝回答。 ${content} -> 配置ID: ${(rule as any)._id}`);
      // 用配置中的消息回复用户，并中断后续流程
      context.reply(response as IMessage);
      context.break();
      return;
    }
    // 指定nlu
    if (responseType === "nlu") {
      context.lastMessage.nlu = response as INLU;
    }

    if (isDev() && !context.slots["source"]) {
      const [{ data: inquiryAddress }, { data: userInfo }, { data: invoiceData }] = await Promise.all([
        terminalApiV2Client.getDefaultAddress(),
        terminalApiV2Client.getCurrentUser(),
        terminalApiV2Client.getOpenInvoiceType(),
      ]);

      const { userName, cellphone, companyName, userParentName, userParentId } = userInfo;
      const params: Record<string, unknown> = {
        field: "inquiryInfo",
        user: {
          userName,
          cellphone,
          companyName,
          userParentName,
          userParentId,
        },
        source: "ANDROID",
        address: inquiryAddress || {},
        fromPage: "AIChatScreen",
      };
      if (invoiceData?.openInvoiceType) {
        params.invoiceData = invoiceData;
      }
    }
  }
};
