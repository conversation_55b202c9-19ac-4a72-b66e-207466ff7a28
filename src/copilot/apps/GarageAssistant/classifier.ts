import { CategoriesBuilder, Category } from "@/copilot/helpers/llm/tools";
import { IClassifier } from "@casstime/copilot-core";
import { SceneCodes } from "./constants";
import { stringifyHistory, stringifyMessage } from "@/common/utils/message";
import { hasVinCode } from "@/common/utils";
import logger from "@/common/logger";
import { Scenes } from "./Scenes";
import { executePrompt, extractJsonFromStream } from "@/copilot/helpers/llm";

const categoriesBuilder = CategoriesBuilder.create()
  .addCategory(
    Category.create(Scenes.询报价.name)
      .describe(
        "为用户提供询价或报价能力，以及车架号识别、工单解析等，可以处理零配件信息查询、零配件编码输入、车架号输入、修改发票信息等。用户输入仅包含配件信息时，路由到该Agent。"
      )
      .example(`我想询价 -> ${Scenes.询报价.name}`)
      .example(`前大灯支架 -> ${Scenes.询报价.name}`)
  )
  .addCategory(
    Category.create(Scenes.汽配知识问答.name)
      .describe(
        `仅提供汽配知识问答能力，包含询问故障原因、维修方案、配件特性、安装指导、汽车品牌特性等。不能处理售前、售中、售后、物流等其他问题`
      )
      .example(`大众漏水怎么办 -> ${Scenes.汽配知识问答.name}`)
      .example(`宝马5系大灯损坏 -> ${Scenes.汽配知识问答.name}`)
      .example(`申请售后 -> ${Scenes.FALLBACK.name}`)
  )
  .addCategory(
    Category.create(Scenes.配件专家.name)
      .describe(`用于查询配件的配套品牌或原厂品牌。`)
      .example(`奥迪机油格的配套品牌是什么 -> ${Scenes.配件专家.name}`)
      .example(`下摆臂配套品牌 -> ${Scenes.配件专家.name}`)
      .example(`奥迪机油格 -> ${Scenes.询报价.name}`)
  )
  .addCategory(
    Category.create(Scenes.FALLBACK.name)
      .describe("提供兜底能力，以上Agents均无法处理时，选择Fallback Agent")
      .alias("售后", "人工服务", "投诉建议", "找供应商")
      .example(`如何退货 -> ${Scenes.FALLBACK.name}`)
      .example(`这个中网不带标的 -> ${Scenes.FALLBACK.name}`)
  );

const classifier: IClassifier = {
  id: "SCENE_CLASSIFIER",
  classify: async (context, candidates) => {
    const message = context.lastMessage;
    // 如果用户输入了车架号，则直接选择询报价Agent
    if (message.type === "text" || message.type === "voice") {
      if (hasVinCode(message.content || "")) {
        const inquiryCode = candidates.find((code) => code === Scenes.询报价.code);
        if (inquiryCode) {
          return inquiryCode;
        }
      }
    }
    const scenes = Object.values(Scenes).filter((scene) => candidates.includes(scene.code));
    // 过滤出候选Agent中包含的类别，进行分类
    const { examples, categories } = categoriesBuilder
      .filter((c) => scenes.some((scene) => scene.name === c.name))
      .build();

    try {
      const stream = await executePrompt(
        "采购助手/采购助手场景分类提示词",
        {
          history: stringifyHistory(context.historyMessages.slice(-5)),
          categories,
          fallback_agent: Scenes.FALLBACK.name,
          message: stringifyMessage(context.lastMessage, false),
          examples,
        },
        { temperature: 0.2 }
      );
      const { agent } = await extractJsonFromStream<{ agent: string }>(stream);
      return scenes.find((scene) => scene.name === agent)?.code || SceneCodes.FALLBACK;
    } catch (error) {
      logger.warn(error, "场景分类出错，使用Fallback Agent兜底");
      return SceneCodes.FALLBACK;
    }
  },
};

export default classifier;
