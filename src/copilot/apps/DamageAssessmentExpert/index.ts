import { Application } from "@casstime/copilot-core";
import damageAssessmentExpertAgent from "./agents/damageAssessmentExpert";
import { AppName } from "@/common/enums";

const app = new Application(AppName.DamageAssessmentExpert);

const DEFAULT_SCENE = "FALLBACK";

app.setSceneClassifier({
  id: "DamageAssessmentExpertClassifier",
  classify: async () => {
    return DEFAULT_SCENE;
  },
});

app.route(DEFAULT_SCENE, damageAssessmentExpertAgent);

export default app;
