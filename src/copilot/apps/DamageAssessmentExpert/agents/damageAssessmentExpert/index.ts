import { AgentName, AI_DISCLAIMER, commandIntents } from "@/copilot/constants";
import { Agent, IMessage, MessageFactory } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { Types } from "mongoose";
import FastGptApi, { ResponseEvent } from "@/common/clients/FastGptApi";
import config from "@casstime/config";
import { messageService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/MessageService";

/**
 * Agent
 */
const agent = new Agent(AgentName.damageAssessmentExpert);

// 配置 fastgpt
const fastGptApi = new FastGptApi({
  baseUrl: config.get("FASTGPT_BASE_URL"),
  apiKey: config.get("FASTGPT_DAMAGEASSESSMENTEXPERT_KEY"),
});

/**
 * 第一次进入Agent
 */
agent.onEnter(() => {
  logger.info("进入 damageAssessmentExpertAgent");
});

/**
 * 离开当前Agent
 */
agent.onLeave(() => {
  logger.info("离开 damageAssessmentExpertAgent");
});

/**
 * 设置意图解析器
 */
agent.setIntentParser(async (context) => {
  context.setIntent("fallback");
});

agent.handleCommand(commandIntents.inquiryStart, async (context) => {
  // 查询最后一条展示的消息，避免重复发送欢迎语
  const lastShowMessage = (await messageService.getLastShowMessage(context.payload)) as IMessage;
  if (lastShowMessage?.extra?.isGreet) return;
  context.reply(
    MessageFactory.text("嗨，欢迎来找我。我是定损专家，可以帮你定损", {
      extra: { isGreet: true },
    })
  );
});

agent.handleFallback(async (context) => {
  const { lastMessage, sessionId } = context;
  let imgUrl = "";
  let query = "";
  if (lastMessage.type === "image") {
    imgUrl = lastMessage.imageUrl;
  }
  if (lastMessage.type === "text" || lastMessage.type === "voice") {
    query = lastMessage.content || "";
  }

  const messageId = new Types.ObjectId().toString();
  const factory = MessageFactory.with({ id: messageId });
  const tokens: string[] = [];
  context.setReplyMode("stream");
  const doSend = async (ev: ResponseEvent) => {
    console.log("ev_result->", JSON.stringify(ev));
    if (ev.event === "message") {
      tokens.push(ev.answer);
    }
    if (tokens.length > 2) {
      context.reply(factory.markdown(tokens.join("")));
    }
    if (ev.event === "message_end") {
      context.reply(
        factory.markdown(tokens.join(""), {
          disclaimer: AI_DISCLAIMER,
        })
      );
    }
  };
  try {
    await fastGptApi.sendChatMessage(
      {
        inputs: {},
        query: query,
        imgUrl,
        platform: "fastgptChat",
        conversationId: new Date().getTime().toString(),
        user: lastMessage.fromUser!,
      },
      (event) => {
        doSend(event);
      }
    );
  } catch (error) {
    logger.warn(`fastGptApi-damageAssessmentExpertAgent 出错：${error}`);
  }
});

export default agent;
