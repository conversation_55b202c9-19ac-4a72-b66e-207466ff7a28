import { HttpClient } from '@casstime/node-http-client';
import { getLogger } from '@casstime/node-logger';
import { config } from '@casstime/apollo-config';
import { createHash } from 'crypto';
import { isDev } from "@/common//utils/env";

// 常量提取
const API_CONFIG = {
  BASE_URL: config.get<string>("1CHEJIAN_BASE_URL"),
  APP_KEY: config.get<string>("1CHEJIAN_APPKEY"),
  APP_SECRET: config.get<string>("1CHEJIAN_APPSECRET")
};

// 工具函数：生成MD5并转换为Base64
function generateMd5Base64(input: string):string {
  return createHash('md5')
    .update(input)
    .digest('base64');
}

// 工具函数：生成随机字符串
function generateNonce(length = 6) {
  return Math.random().toString().slice(-length);
}

// 创建HTTP客户端实例
const httpClient = new HttpClient({
  baseUrl: API_CONFIG.BASE_URL,
  defaultTimeout: 20 * 1000, // copilot请求接口默认超时时间
  // 请求头生成器 - 添加API签名
  defaultHeadersProvider(path) {
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const nonce = generateNonce();
    // 路径提权
    const index = path.indexOf('?');
    const pathPart = index !== -1 ? path.substring(0, index) : path;
    
    // 构建签名原始字符串
    const signStr = `${API_CONFIG.APP_KEY}${timestamp}${nonce}${API_CONFIG.APP_SECRET}${pathPart}`;
    const sign = generateMd5Base64(signStr);
    
    return {
      appKey: API_CONFIG.APP_KEY,
      nonce,
      timestamp,
      sign
    };
  },
  
  // 请求响应后的日志记录
  onPostResponse(options, httpRes) {
    const logger = getLogger();
    const totalTime = httpRes.timingPhases?.total || 0;
    
    // 记录请求基本信息
    logger.info(
      {
        type: 'access',
        method: options.method,
        url: options.uri,
        ...httpRes.timingPhases,
        latency: Math.round(totalTime),
        statusCode: httpRes.statusCode
      },
      '%s %s %dms %d',
      options.method,
      options.uri,
      totalTime,
      httpRes.statusCode
    );
    
    // 调试环境不打日志，以免终端被大量的请求日志淹没
    if (!isDev()) {
      logger.info("request body %o , response body %o", options.json, httpRes.result);
    }
  }
});

export default httpClient;