// 假设这是你的服务文件，导入优化后的 httpClient
import httpClient from './client'; // 根据实际路径调整
import { ICustomerVehicleBaseItem, IRepairRecordDetailItem, IRreceivingCarTicketAdd } from "../interface/index";
import { HttpError } from "@/common/error";
import { TeamCode, ErrorCode } from "@/common/enums";

export class OneClient {
  async getOpenVehicleBaseInfo(plate: string) {
    try {
      const path = `open-api/fCLsGJ37xKNb?car_no=${encodeURIComponent(plate)}`;
      const {result} = await httpClient.get<ICustomerVehicleBaseItem>(path);
      return result;
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

   async getOpenVehicleRecordsInfo(carId: string, pageSize:number) {
    try {
      const path = `open-api/QejmYyZFVeVI?car_id=${carId}&page_size=${pageSize}`;
      const {result} = await httpClient.get<IRepairRecordDetailItem>(path);
      return result;
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  async saveOpenVehicleBaseInfo(receivingCarTicketAdd: IRreceivingCarTicketAdd) {
    try {
      const path = "open-api/7kNUKJAW1aKa";
      const {result} = await httpClient.post<number>(path, { json: receivingCarTicketAdd });
      return result;
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

}

export const oneClient = new OneClient();
