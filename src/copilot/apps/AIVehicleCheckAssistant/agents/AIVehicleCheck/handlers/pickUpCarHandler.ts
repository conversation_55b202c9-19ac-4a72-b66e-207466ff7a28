import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { createVehicleCenterDisplayViewXml} from "../richtext";
import { Types } from "mongoose";


export const pickUpCarHandler: IHandlerFunction = async (context) => {
  // 取出車牌號
  const receivingCarTicketItem = context.slots.receivingCarTicketItem;

  const plate = !receivingCarTicketItem ? null : receivingCarTicketItem.plate;
  if (!plate || plate.length === 0) {
    context.reply(MessageFactory.markdown("请先上传车牌号" ));
    return context.break();
  }

  const carId = !receivingCarTicketItem ? null : receivingCarTicketItem.carId;
  if (!carId || carId.length === 0) {
    context.reply(MessageFactory.markdown("车牌号在1号车间系统中不存在，请先创建车辆" ));
    return context.break();
  }

  const vehicleBaseInfoXmlId = new Types.ObjectId().toString();
  const embed = createVehicleCenterDisplayViewXml(vehicleBaseInfoXmlId, plate, receivingCarTicketItem);
  context.reply(MessageFactory.text("", {
    embed,
  }));
  
};