import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { ImageService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/ImageService";
import { getChatOpenAI, renderPrompt } from "@/copilot/helpers/llm";
import { Types } from "mongoose";
import { createVehicleCenterDisplayViewXml } from "../richtext";
import { IInteriorCheckItem } from "../interface/index";

export const inAppearanceHandler: IHandlerFunction = async (context) => {
  try {
    const receivingCarTicketItem = context.slots.receivingCarTicketItem;
    // 取出車牌號
    const plate = !receivingCarTicketItem ? null : receivingCarTicketItem.plate;
    if (!plate || plate.length === 0) {
      context.reply(MessageFactory.markdown("请先上传车牌号"));
      return context.break();
    }

    const { lastMessage } = context;
    if (lastMessage.type === "image") {
      const imageService = context.getService(ImageService);
      const imageUrl = imageService.getImageUrl();
      context.setReplyMode("stream");
      console.log("====1号车间意内饰-imageUrl===" + imageUrl);

      // 识别图片
      const messages = [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: await renderPrompt("接车助手/内饰", {}),
            },
            {
              type: "image_url",
              image_url: { url: imageUrl },
            },
          ],
        },
      ];
      const gpt4o_chat = getChatOpenAI({ fields: { temperature: 0.1 } });
      const msg = await gpt4o_chat.invoke(messages);
      // 安全获取文本内容
      let centerConsoleMsg = "";
      if (Array.isArray(msg.content)) {
        const textItem = msg.content.find(
          (item): item is { type: "text"; text: string } => typeof item !== "string" && item.type === "text"
        );
        centerConsoleMsg = textItem?.text || "";
      } else if (typeof msg.content === "string") {
        centerConsoleMsg = msg.content.trim();
      } else {
        centerConsoleMsg = "";
      }
      console.log("====1号车间车牌号解析-centerConsoleMsg==" + centerConsoleMsg);

      try {
        const newInteriorCheckItems: IInteriorCheckItem[] = JSON.parse(centerConsoleMsg);
        console.log("interiorCheckItems = " + newInteriorCheckItems);
        // 存在的检测项目
        let mergedItems = receivingCarTicketItem.interior_check_items;
        if (!mergedItems) {
          mergedItems = [];
        }
        // 增量检测项目存在，则处理
        if (newInteriorCheckItems && newInteriorCheckItems.length > 0) {
          newInteriorCheckItems.forEach((newItem) => {
            const exists = mergedItems.find(
              (item: IInteriorCheckItem) => Number(item.position_code) === Number(newItem.position_code)
            );
            if (!exists) {
              newItem.position_code = Number(newItem.position_code);
              mergedItems.push(newItem);
            } else {
              // 如果存在，则覆盖mergedItems
              mergedItems = mergedItems.map((item: IInteriorCheckItem) => {
                if (Number(item.position_code) === Number(newItem.position_code)) {
                  // 合并 item 和 newItem 的描述信息，换行符隔开
                  if (item.repair_description && newItem.repair_description.length > 0) {
                    newItem.repair_description = item.repair_description + " |\n" + newItem.repair_description;
                  }

                  return newItem;
                }
                return item;
              });
            }
          });
        }
        receivingCarTicketItem.interior_check_items = mergedItems;
        // 新的数据存到context中，方便后续使用
        context.mergeSlots({ receivingCarTicketItem: receivingCarTicketItem });

        // 接车单
        const mergedItemsAdd = mergedItems;
        const receivingCarTicketAdd = context.slots.receivingCarTicketAdd;
        receivingCarTicketAdd.interior_check_items = getInteriorPositionName(mergedItemsAdd);
        // 接车图片
        receivingCarTicketAdd.images.push({
          url: imageUrl,
          type: 3,
          width: 0,
        });
        context.mergeSlots({ receivingCarTicketAdd: receivingCarTicketAdd });
      } catch (error) {
        console.error("JSON 解析失败:", error);
      }
      const vehicleBaseInfoXmlId = new Types.ObjectId().toString();
      const embed = createVehicleCenterDisplayViewXml(vehicleBaseInfoXmlId, plate, receivingCarTicketItem);
      context.reply(
        MessageFactory.text("", {
          embed,
        })
      );
    }
  } catch (err) {
    logger.warn("车牌号处理异常", err);
  }
};

function getInteriorPositionName(mergedItems: IInteriorCheckItem[]): IInteriorCheckItem[] {
  // 添加所有枚举值
  for (const key in InteriorPosition) {
    if (mergedItems && mergedItems.length > 0) {
      const positionCode = Number(InteriorPosition[key as keyof typeof InteriorPosition]);
      const exists = mergedItems.find((item: IInteriorCheckItem) => Number(item.position_code) === positionCode);
      if (!exists) {
        mergedItems.push({
          position_code: positionCode,
          position_name: key,
          check_value_code: CheckResult.正常,
          repair_description: "",
        });
      }
    }
  }
  return mergedItems;
}

// 内饰检查部位枚举
enum InteriorPosition {
  中控门锁 = "1",
  内饰 = "2",
  后视镜 = "3",
  电动门窗 = "4",
  天窗 = "5",
  音响系统 = "6",
  电动座椅 = "7",
  随车物品确认 = "8",
  贵重物品确认 = "9",
  出风口 = "10",
  遮阳板 = "11",
  仪表系统 = "12",
  其他 = "13",
}

// 检查结果枚举
enum CheckResult {
  正常 = 0,
  维修建议1 = 1,
  维修建议2 = 2,
}
