import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { ImageService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/ImageService";
import { getChatOpenAI, renderPrompt } from "@/copilot/helpers/llm";
import { oneClient } from "../utils/OneClient";
import {
  IVehicleInfoResponse,
  IVehicleRecordsInfoPageResponse,
  IRreceivingCarTicketItem,
  ICustomerVehicleBaseItem,
  IServiceBaseInfoAdd,
  IServiceCarInfoAdd,
  IServiceCustomerInfoAdd,
  IDisplayCheckResultAdd,
} from "../interface/index";
import { Types } from "mongoose";
import { createVehicleBaseInfoViewXml } from "../richtext";

export const plateHandler: IHandlerFunction = async (context) => {
  try {
    // 清楚缓存信息
    context.clearSlots(["plate"]);
    context.clearSlots(["receivingCarTicketItem"]);
    context.clearSlots(["receivingCarTicketAdd"]);

    const { lastMessage } = context;
    console.log("====1号车间处理器===" + lastMessage);
    let plate = "";
    if (lastMessage.type === "image") {
      const imageService = context.getService(ImageService);
      const imageUrl = imageService.getImageUrl();
      context.setReplyMode("stream");
      console.log("====1号车间意图解析器-imageUrl===" + imageUrl);

      // 识别图片
      const messages = [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: await renderPrompt("接车助手/车牌", {}),
            },
            {
              type: "image_url",
              image_url: { url: imageUrl },
            },
          ],
        },
      ];
      const gpt4o_chat = getChatOpenAI({ fields: { temperature: 0.1 } });
      const msg = await gpt4o_chat.invoke(messages);
      // 安全获取文本内容
      let plateText = "";
      if (Array.isArray(msg.content)) {
        const textItem = msg.content.find(
          (item): item is { type: "text"; text: string } => typeof item !== "string" && item.type === "text"
        );
        plateText = textItem?.text || "";
      } else if (typeof msg.content === "string") {
        plateText = msg.content.trim();
      } else {
        plateText = "";
      }
      plate = getLicensePlate(plateText);
    } else if (lastMessage.type === "text") {
      plate = lastMessage.content;
    }
    context.reply(MessageFactory.markdown("识别到车牌：" + plate));

    // 將車牌號存入slot中
    context.mergeSlots({ plate: plate });

    // 根據車牌號查詢車輛信息
    const result = (await oneClient.getOpenVehicleBaseInfo(plate)) as IVehicleInfoResponse;
    if (!result || !result.data || !result.data.plate || !result.data.car_id) {
      context.reply(MessageFactory.markdown("未查询到该车牌对应的车辆信息。"));
      return;
    }

    const customerVehicleBase = result.data;
    // 將车辆存入slot中
    const receivingCarTicketItem: IRreceivingCarTicketItem = {
      carId: customerVehicleBase.car_id,
      plate: customerVehicleBase.plate,
    };
    context.mergeSlots({ receivingCarTicketItem: receivingCarTicketItem });
    // 接车单信息
    const receivingCarTicketAddSlot = receivingCarTicketAdd(customerVehicleBase);
    context.mergeSlots({ receivingCarTicketAdd: receivingCarTicketAddSlot });

    // 根据车辆ID查询服务记录
    const recordsInfoPage = (await oneClient.getOpenVehicleRecordsInfo(
      customerVehicleBase.car_id,
      1
    )) as IVehicleRecordsInfoPageResponse;
    if (
      recordsInfoPage != null &&
      recordsInfoPage.data != null &&
      recordsInfoPage.data.list != null &&
      recordsInfoPage.data.list.length > 0
    ) {
      customerVehicleBase.repair_record_details = recordsInfoPage.data.list;
      customerVehicleBase.repair_record_total = recordsInfoPage.data.total;
    }
    console.log(
      "====1号车间客户车辆信息-customerVehicleBase==" + customerVehicleBase.plate + "_" + customerVehicleBase.car_id
    );

    const vehicleBaseInfoXmlId = new Types.ObjectId().toString();
    const embed = createVehicleBaseInfoViewXml(vehicleBaseInfoXmlId, customerVehicleBase);
    if (plate.length === 0) {
      context.reply(MessageFactory.text("车牌号查询车辆信息失败", { embed }));
      return context.break();
    }
    context.reply(
      MessageFactory.text("", {
        embed,
      })
    );
  } catch (err) {
    logger.warn("车牌号处理异常", err);
  }
};

function getLicensePlate(msg: string) {
  try {
    // 确保输入是字符串类型
    if (typeof msg !== "string") {
      throw new Error("参数必须是字符串类型");
    }
    // 去除可能的前缀（如："识别到车牌："）
    const plateStr = msg.replace(/^.*?：/, "").trim();

    // 解析JSON数组
    const plateArray = JSON.parse(plateStr);

    // 获取第一个元素并去除空格
    return plateArray[0].replace(/\s/g, "");
  } catch (error) {
    console.error("解析车牌失败:", error);
    return null;
  }
}

export function receivingCarTicketAdd(customerVehicleBase: ICustomerVehicleBaseItem) {
  // 创建IRreceivingCarTicketAdd对象
  return {
    delete_images_version: 1,
    base_info: createServiceBaseInfo(customerVehicleBase),
    car_info: createServiceCarInfo(customerVehicleBase),
    customer_info: createServiceCustomerInfo(customerVehicleBase),
    display_check_result: createDisplayCheckResult(),
    interior_check_items: [],
    images: [],
  };
}
export function createDisplayCheckResult(): IDisplayCheckResultAdd {
  return {
    other_check_comment: "",
    display_check_items: [],
  };
}

export function createServiceBaseInfo(customerVehicleBase: ICustomerVehicleBaseItem): IServiceBaseInfoAdd {
  return {
    receiving_car_ticket_id: "",
    receiving_car_ticket_no: "",
    bill_type: 0,
    start_time: new Date().toISOString(),
    repair_man: customerVehicleBase.customer_name,
    repair_mobile: customerVehicleBase.customer_mobile,
    mileage: customerVehicleBase.mileage,
    rest_oil: 0,
  };
}

export function createServiceCarInfo(customerVehicleBase: ICustomerVehicleBaseItem): IServiceCarInfoAdd {
  return {
    car_id: customerVehicleBase.car_id,
    car_no: customerVehicleBase.plate,
    plate: customerVehicleBase.plate,
    mileage: customerVehicleBase.mileage,
    vin: customerVehicleBase.vin,
  };
}

export function createServiceCustomerInfo(customerVehicleBase: ICustomerVehicleBaseItem): IServiceCustomerInfoAdd {
  return {
    customer_id: customerVehicleBase.customer_id,
    customer_store_item_id: customerVehicleBase.customer_store_item_id,
    customer_name: customerVehicleBase.customer_name,
    customer_mobile: customerVehicleBase.customer_mobile,
    customer_type_id: customerVehicleBase.customer_type_id,
    print_customer_mobile: customerVehicleBase.customer_mobile,
    preferences: customerVehicleBase.preferences,
  };
}
