import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { AIVehicleCheckIntents } from "../intent";
import logger from "@/common/logger";
import { ImageService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/ImageService";
import { getChatOpenAI, renderPrompt } from "@/copilot/helpers/llm";

export const fallbackHandler: IHandlerFunction = async (context) => {
  try {
    logger.info("1号车间意图处理-fallbackHandler");
    const { lastMessage } = context;

    if (lastMessage.type === "image") {
      const imageService = context.getService(ImageService);
      const imageUrl = imageService.getImageUrl();
      context.setReplyMode("stream");
      console.log("====1号车间意图解析器-imageUrl===" + imageUrl);

      // 识别图片
      const messages = [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: await renderPrompt("接车助手/场景识别", {}),
            },
            {
              type: "image_url",
              image_url: { url: imageUrl },
            },
          ],
        },
      ];
      const gpt4o_chat = getChatOpenAI({ fields: { temperature: 0.1 } });
      const msg = await gpt4o_chat.invoke(messages);
      // 安全获取文本内容
      let text = "";
      if (Array.isArray(msg.content)) {
        const textItem = msg.content.find(
          (item): item is { type: "text"; text: string } => typeof item !== "string" && item.type === "text"
        );
        text = textItem?.text || "";
      } else if (typeof msg.content === "string") {
        text = msg.content.trim();
      } else {
        text = "";
      }

      if (!text) {
        context.reply(MessageFactory.text("未处理意图"));
      }

      if (text.indexOf("车牌") > -1) {
        return context.next(AIVehicleCheckIntents.车牌);
      } else if (text.indexOf("仪表") > -1) {
        return context.next(AIVehicleCheckIntents.仪表);
      } else if (text.indexOf("外观") > -1) {
        return context.next(AIVehicleCheckIntents.外观);
      } else if (text.indexOf("内饰") > -1) {
        return context.next(AIVehicleCheckIntents.内饰);
      } else {
        return context.next(AIVehicleCheckIntents.其它);
      }
    }
    return context.next(AIVehicleCheckIntents.其它);
  } catch (err) {
    logger.warn("意图分类失败", err);
    return context.next(AIVehicleCheckIntents.其它);
  }
};
