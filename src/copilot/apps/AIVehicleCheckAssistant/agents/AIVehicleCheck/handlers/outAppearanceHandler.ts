import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { ImageService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/ImageService";
import { getChatOpenAI, renderPrompt } from "@/copilot/helpers/llm";
import { Types } from "mongoose";
import { createVehicleCenterDisplayViewXml} from "../richtext";
import { IDisplayCheckItem, IDisplayCheckResult, IDisplayCheckItemAdd } from "../interface/index";

export const outAppearanceHandler: IHandlerFunction = async (context) => {
  try {
      const receivingCarTicketItem = context.slots.receivingCarTicketItem;
      // 取出車牌號
      const plate = !receivingCarTicketItem ? null : receivingCarTicketItem.plate;
      if (!plate || plate.length === 0) {
        context.reply(MessageFactory.markdown("请先上传车牌号" ));
        return context.break();
      }

      const { lastMessage } = context;
      console.log("====1号车间处理器===" + lastMessage);
      if(lastMessage.type === "image"){
      const imageService = context.getService(ImageService);
      const imageUrl = imageService.getImageUrl();
      context.setReplyMode("stream");
      console.log("====1号车间意外观-imageUrl===" + imageUrl);
       
      // 识别图片
      const messages = [
            {
              role: "user",
              content: [
                {
                  type: "text",
                  text: await renderPrompt("接车助手/外观", {}),
                },
                {
                  type: "image_url",
                  image_url: { url: imageUrl },
                },
              ],
            },
      ];
      const gpt4o_chat = getChatOpenAI({ fields: { temperature: 0.1 } });
      const msg = await gpt4o_chat.invoke(messages);
      // 安全获取文本内容
      let centerConsoleMsg = '';
      if (Array.isArray(msg.content)) {
        const textItem = msg.content.find(
          (item): item is { type: "text"; text: string } => 
            typeof item !== 'string' && item.type === 'text'
        );
        centerConsoleMsg = textItem?.text || '';
      } else if (typeof msg.content === 'string') {
        centerConsoleMsg = msg.content.trim();
      } else {
        centerConsoleMsg = '';
      }

      console.log("====1号车间车牌号解析-centerConsoleMsg==" + centerConsoleMsg);

      try {
        const newDisplayCheckItems: IDisplayCheckItem[] = JSON.parse(centerConsoleMsg);
        console.log("newDisplayCheckItems = " + newDisplayCheckItems);
        //增量增加检测项目
        let mergedItems = receivingCarTicketItem.display_check_items;
        // 增量检测项目存在，则处理
        if(newDisplayCheckItems && newDisplayCheckItems.length > 0){
            if(mergedItems && mergedItems.length > 0){
                newDisplayCheckItems.forEach(newItem => {
                  const exists = mergedItems.find(
                    (item: IDisplayCheckItem) => item.position_code === newItem.position_code
                  );
                  if (!exists) {
                    mergedItems.push(newItem);
                  }
                });
            }else{
              mergedItems = newDisplayCheckItems;
            }
        }
        receivingCarTicketItem.display_check_items = mergedItems;
        // 新的数据存到context中，方便后续使用
        context.mergeSlots({ receivingCarTicketItem: receivingCarTicketItem });

        // 接车单保存
        const displayCheckItemAdd: IDisplayCheckItemAdd = mergedItems.map((item: IDisplayCheckItem) => {
          // 获取position_id
          const position_id = item.position_code;
          // 获取check_values
          const display_check_result = item.display_check_result;
          let check_values: number[] = [];
          if(display_check_result && display_check_result.length > 0){
             check_values = item.display_check_result.map((result: IDisplayCheckResult) => result.code)
          }
          return {
            position_id: position_id,
            check_values: check_values
          }
        });
        const receivingCarTicketAdd = context.slots.receivingCarTicketAdd;
        receivingCarTicketAdd.display_check_result.display_check_items = displayCheckItemAdd;
        // 接车图片
        receivingCarTicketAdd.images.push({
          url: imageUrl,
          type: 1,
          width: 0,
        });
        context.mergeSlots({ receivingCarTicketAdd: receivingCarTicketAdd });
      } catch (error) {
        console.error("JSON 解析失败:", error);
      }

      const vehicleBaseInfoXmlId = new Types.ObjectId().toString();
      const embed = createVehicleCenterDisplayViewXml(vehicleBaseInfoXmlId, plate, receivingCarTicketItem);
      context.reply(MessageFactory.text("", {
        embed,
      }));
    }
  } catch (err) {
    logger.warn("车牌号处理异常", err);
  }

};