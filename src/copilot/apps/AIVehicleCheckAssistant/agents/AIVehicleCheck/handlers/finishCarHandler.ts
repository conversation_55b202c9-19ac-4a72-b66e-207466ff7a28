import { IHandlerFunction, MessageFactory} from "@casstime/copilot-core";
import {oneClient} from "../utils/OneClient";
import { ISaveVehicleInfoResponse } from "../interface/index";
import { createFinishVehicleBaseInfoViewXml} from "../richtext";
import { Types } from "mongoose";

export const finishCarHandler: IHandlerFunction = async (context) => {
  try {
    const receivingCarTicketAdd = context.slots.receivingCarTicketAdd;
  // 取出車牌號
  const plate = receivingCarTicketAdd && receivingCarTicketAdd.car_info  ? receivingCarTicketAdd.car_info.plate : null ;
  const car_id = receivingCarTicketAdd && receivingCarTicketAdd.car_info  ? receivingCarTicketAdd.car_info.car_id : null ;
  if (!plate || plate.length === 0 || !car_id ) {
    context.reply(MessageFactory.markdown("1号车间系统中, 车辆不存在" ));
    return context.break();
  }
  
  const result = await oneClient.saveOpenVehicleBaseInfo(receivingCarTicketAdd) as ISaveVehicleInfoResponse;
  if (!result || !result.data || !result.data.receiving_car_ticket_id) {
      context.reply(MessageFactory.markdown("接车单生成失败"));
      return;
  }
  // 接查单ID
  receivingCarTicketAdd.base_info.receiving_car_ticket_id = result.data.receiving_car_ticket_id;
  // 接车单编号
  receivingCarTicketAdd.base_info.receiving_car_ticket_no = result.data.receiving_car_ticket_no;

  const vehicleBaseInfoXmlId = new Types.ObjectId().toString();
  const embed = createFinishVehicleBaseInfoViewXml(vehicleBaseInfoXmlId, receivingCarTicketAdd);
  if (plate.length === 0) {
    context.reply(MessageFactory.text("车牌号查询车辆信息失败", { embed }));
    return context.break();
  }
  context.reply(MessageFactory.text("", {
    embed,
    }));
  } catch (error) {
    context.reply(MessageFactory.markdown("接车单生成失败,error:" + error ));
    return context.break();
  }
  
};