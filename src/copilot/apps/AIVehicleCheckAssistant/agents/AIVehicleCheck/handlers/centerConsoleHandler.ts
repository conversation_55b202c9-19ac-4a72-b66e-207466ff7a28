import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { ImageService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/ImageService";
import { getChatOpenAI, renderPrompt } from "@/copilot/helpers/llm";
import { IRreceivingCarTicketItem } from "../interface/index";
import { Types } from "mongoose";
import { createVehicleCenterDisplayViewXml } from "../richtext";

export const centerConsoleHandler: IHandlerFunction = async (context) => {
  try {
    const receivingCarTicketItem = context.slots.receivingCarTicketItem;
    // 取出車牌號
    const plate = !receivingCarTicketItem ? null : receivingCarTicketItem.plate;
    if (!plate || plate.length === 0) {
      context.reply(MessageFactory.markdown("请先上传车牌号"));
      return context.break();
    }

    const { lastMessage } = context;
    console.log("====1号车间处理器===" + lastMessage);
    if (lastMessage.type === "image") {
      const imageService = context.getService(ImageService);
      const imageUrl = imageService.getImageUrl();
      context.setReplyMode("stream");
      console.log("====1号车间意中控屏-imageUrl===" + imageUrl);

      // 识别图片
      const messages = [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: await renderPrompt("接车助手/中控屏", {}),
            },
            {
              type: "image_url",
              image_url: { url: imageUrl },
            },
          ],
        },
      ];
      const gpt4o_chat = getChatOpenAI({ fields: { temperature: 0.1 } });
      const msg = await gpt4o_chat.invoke(messages);
      // 安全获取文本内容
      let centerConsoleMsg = "";
      if (Array.isArray(msg.content)) {
        const textItem = msg.content.find(
          (item): item is { type: "text"; text: string } => typeof item !== "string" && item.type === "text"
        );
        centerConsoleMsg = textItem?.text || "";
      } else if (typeof msg.content === "string") {
        centerConsoleMsg = msg.content.trim();
      } else {
        centerConsoleMsg = "";
      }
      console.log("====1号车间车牌号解析-centerConsoleMsg==" + centerConsoleMsg);

      try {
        const centerConsoleItem: IRreceivingCarTicketItem = JSON.parse(centerConsoleMsg);
        if (centerConsoleItem) {
          // 油量
          const mileage = centerConsoleItem.mileage;
          if (mileage && mileage > 0) {
            receivingCarTicketItem.mileage = mileage;
          }
          // 油量
          const fuelCapacity = centerConsoleItem.fuelCapacity;
          if (fuelCapacity && fuelCapacity.length > 0) {
            receivingCarTicketItem.fuelCapacity = getFuelCapacityValue(fuelCapacity);
          }
          // 新的数据存到context中，方便后续使用
          context.mergeSlots({ receivingCarTicketItem: receivingCarTicketItem });

          // 接车单
          const receivingCarTicketAdd = context.slots.receivingCarTicketAdd;
          // 里程
          receivingCarTicketAdd.base_info.mileage = receivingCarTicketItem.mileage;
          // 油量
          receivingCarTicketAdd.base_info.rest_oil = receivingCarTicketItem.fuelCapacity;
          // 接车图片
          receivingCarTicketAdd.images.push({
            url: imageUrl,
            type: 0,
            width: 0,
          });
          context.mergeSlots({ receivingCarTicketAdd: receivingCarTicketAdd });
        } else {
          context.reply(MessageFactory.markdown("仪表盘识别失败，请重新拍照"));
        }
      } catch {
        context.reply(MessageFactory.markdown("仪表盘识别失败，请重新拍照"));
      }

      const vehicleBaseInfoXmlId = new Types.ObjectId().toString();
      const embed = createVehicleCenterDisplayViewXml(vehicleBaseInfoXmlId, plate, receivingCarTicketItem);
      context.reply(
        MessageFactory.text("", {
          embed,
        })
      );
    }
  } catch (err) {
    logger.warn("车牌号处理异常", err);
  }
};

function getFuelCapacityValue(fuelCapacity: string | number | undefined): string {
  let numericValue: number;

  // 如果 mileage 是数字类型，直接取值
  if (typeof fuelCapacity === "number") {
    numericValue = fuelCapacity;
  }
  // 如果是字符串类型，尝试提取数字部分
  else if (typeof fuelCapacity === "string") {
    const match = fuelCapacity.match(/[\d.]+/);
    if (match) {
      numericValue = parseFloat(match[0]);
    } else {
      return "0";
    }
  } else {
    return "0";
  }

  // 判断是否大于 100
  return numericValue > 100 ? "0" : numericValue.toString();
}
