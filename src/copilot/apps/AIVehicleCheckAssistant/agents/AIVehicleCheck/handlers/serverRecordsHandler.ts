import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { createVehicleRecordsViewXml} from "../richtext";
import { Types } from "mongoose";
import { IVehicleRecordsInfoPageResponse } from "../interface";
import { oneClient } from "../utils/OneClient";

export const serverRecordsHandler: IHandlerFunction = async (context) => {
  // 取出車牌號
    const receivingCarTicketItem = context.slots.receivingCarTicketItem;
    // 取出車牌號
    const plate = !receivingCarTicketItem ? null : receivingCarTicketItem.plate;
    console.log("====1号车间车牌号解析-plateTemp==" + plate);
    if (!plate || plate.length === 0) {
      context.reply(MessageFactory.markdown("请先上传车牌号" ));
      return context.break();
    }

    const carId = !receivingCarTicketItem ? null : receivingCarTicketItem.carId;
    if (!carId || carId.length === 0) {
      context.reply(MessageFactory.markdown("车牌号在1号车间系统中不存在，请先创建车辆" ));
      return context.break();
    }

    // 根据车辆ID查询服务记录
    const recordsInfoPage = await oneClient.getOpenVehicleRecordsInfo(carId, 20) as IVehicleRecordsInfoPageResponse;
    if(recordsInfoPage != null && recordsInfoPage.data != null && recordsInfoPage.data.list != null && recordsInfoPage.data.list.length > 0){
      const vehicleBaseInfoXmlId = new Types.ObjectId().toString();
      const embed = createVehicleRecordsViewXml(vehicleBaseInfoXmlId, receivingCarTicketItem.plate, recordsInfoPage.data);
      context.reply(MessageFactory.text("", {
        embed,
      }));
    }
};