import { IHandlerFunction, MessageFactory } from "@casstime/copilot-core";
import { createOneIndexDisplayViewXml } from "../richtext";

export const inquiryStartHandler: IHandlerFunction = async (context) => {
  // 清楚缓存信息
  context.clearSlots(["plate"]);
  context.clearSlots(["receivingCarTicketItem"]);
  context.clearSlots(["receivingCarTicketAdd"]);

  // 首页提示
  const embed = createOneIndexDisplayViewXml();
  context.reply(
    MessageFactory.text("1号车间欢迎你", {
      embed,
    })
  );
};
