import { React, View, Text, StyleSheet } from "@casstime/copilot-xml";
import { IRreceivingCarTicketItem} from "../../interface/index";

/**
 * 展示拍照接车相关提示信息
 */
export function CenterDisplayExpandComp({
  id,
  plate,
  receivingCarTicket
}: {
  id: string;
  plate: string;
  receivingCarTicket: IRreceivingCarTicketItem;
}) {
  const {
    mileage = 0,
    fuelCapacity = '' ,
    display_check_items = [],
    interior_check_items = [],
  } = receivingCarTicket;

  if (!plate) return null;

  return (
    <View style={styles.headContent}>
      <Text style={{ fontWeight: "bold" }}>【{plate}】请拍照接车</Text>
      {
        mileage && mileage >= 0   ? (
          <Text>已记录 里程{mileage}KM \ 油量{fuelCapacity}%</Text>
        ) : <Text>拍摄中控获取里程和油量</Text>
      }    

      <View style={{ height: 20 }} />
      {display_check_items.length > 0 ? (
        <View>
          <Text>外观检查，可继续拍照记录</Text>
          {display_check_items.map((displayCheckItem, displayIndex) => {
            return (
              <View style={styles.headContent} key={displayIndex}>
                <Text style={{ fontWeight: "bold" }}>
                  {displayIndex + 1}、{displayCheckItem.position_name || '无'}
                </Text>
                
                {displayCheckItem.display_check_result?.length > 0 ? (
                  displayCheckItem.display_check_result.map((displayCheckResult, displayCheckResultIndex) => {
                    return (
                      <View style={styles.headContent} key={displayCheckResultIndex}>
                        <Text>{displayCheckResult.desc}</Text>
                      </View>
                    );
                  })
                ) : (
                  <Text>暂无外观检查结果</Text>
                )}
              </View>
            );
          })}
        </View>
      ) : (
        <Text>拍摄有损坏的外观图片，记录外观情况</Text>
      )}

      <View style={{ height: 20 }} />
      {interior_check_items.length > 0 ? (
      <View>
        <Text>内饰检查，可继续拍照记录</Text>
        {interior_check_items.map((interiorCheckItem, interiorIndex) => {
          return (
            <View style={styles.headContent} key={interiorIndex}>
              <Text style={{ fontWeight: "bold" }}>
                {interiorIndex + 1}、{interiorCheckItem.position_name || '无'} {interiorCheckItem.check_value_code == 1 ? '异常' :'正常'}
              </Text>
              <Text >
                {interiorCheckItem.check_value_code == 1 ? interiorCheckItem.repair_description :''}
              </Text>
            </View>
          );
        })}
      </View>
    ) : (
      <Text>拍摄有损坏的内饰图片，记录内饰情况</Text>
    )}
    {/* 添加按钮 */}
    <View style={styles.buttonContainer}>
      <Text style={styles.button}>完成检查</Text>
    </View>
    </View>
  );
}

const styles = StyleSheet.create({
  headView: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 16,
  },
  headImage: {
    width: 72,
    height: 72,
    marginRight: 20,
  },
  headContent: {
    flex: 1,
    marginRight: 10,
  },
  headContentText: {
    fontWeight: "bold",
    fontSize: 30,
    numberOfLines: 2,
  },
  fold: {
    borderRadius: 16,
    backgroundColor: "#F7F8FA",
  },
  marginTopView: {
    backgroundColor: "#F7F8FA",
    borderRadius: 16,
    padding: 16,
  },
  headerText: {
    fontSize: 32,
    lineHeight: 48,
  },
  trigger: {
    position: "absolute",
    right: 0,
    bottom: 0,
  },
  triggerView: {
    flexDirection: "row",
    alignItems: "center",
  },
  triggerText: {
    color: "#646566",
    fontSize: 22,
    lineHeight: 40,
  },
  triggerImage: {
    width: 15,
    height: 10,
  },
  containerView: {
    backgroundColor: "#F7F8FA",
    padding: 10,
    borderRadius: 16,
  },
  contentView: {
    flexDirection: "row",
    alignItems: "center",
  },
  contentImage: {
    width: 30,
    height: 30,
    marginRight: 12,
  },
  contentText: {
    color: "#979899",
    flex: 1,
    flexWrap: "wrap",
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: 10,
  },

  button: {
    flex: 1,
    padding: 10,
    margin: 5,
    backgroundColor: '#007AFF',
    color: 'white',
    textAlign: 'center',
    borderRadius: 8,
  },
});
