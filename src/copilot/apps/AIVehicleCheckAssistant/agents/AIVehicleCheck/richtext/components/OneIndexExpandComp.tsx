import { React, View, Text, StyleSheet } from "@casstime/copilot-xml";


export function OneIndexExpandComp() {

  return (
    <View style={styles.headContent}>
      <Text style={{ fontWeight: "bold" }}>您好！我是1号车间接车小助手，您可以</Text>
      <Text>1、车牌号接车开单；</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  headView: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 16,
  },
  headImage: {
    width: 72,
    height: 72,
    marginRight: 20,
  },
  headContent: {
    flex: 1,
    marginRight: 10,
  },
  headContentText: {
    fontWeight: "bold",
    fontSize: 30,
    numberOfLines: 2,
  },
  fold: {
    borderRadius: 16,
    backgroundColor: "#F7F8FA",
  },
  marginTopView: {
    backgroundColor: "#F7F8FA",
    borderRadius: 16,
    padding: 16,
  },
  headerText: {
    fontSize: 32,
    lineHeight: 48,
  },
  trigger: {
    position: "absolute",
    right: 0,
    bottom: 0,
  },
  triggerView: {
    flexDirection: "row",
    alignItems: "center",
  },
  triggerText: {
    color: "#646566",
    fontSize: 22,
    lineHeight: 40,
  },
  triggerImage: {
    width: 15,
    height: 10,
  },
  containerView: {
    backgroundColor: "#F7F8FA",
    padding: 10,
    borderRadius: 16,
  },
  contentView: {
    flexDirection: "row",
    alignItems: "center",
  },
  contentImage: {
    width: 30,
    height: 30,
    marginRight: 12,
  },
  contentText: {
    color: "#979899",
    flex: 1,
    flexWrap: "wrap",
  },
});
