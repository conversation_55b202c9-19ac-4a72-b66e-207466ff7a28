import { React, View, Text, StyleSheet } from "@casstime/copilot-xml";
import { IRreceivingCarTicketAdd} from "../../interface/index";

export function ViewVehicleAddComp({
  id,
  receivingCarTicketAdd
}: {
  id: string;
  receivingCarTicketAdd: IRreceivingCarTicketAdd;
}) {
  const {
    base_info = {
      receiving_car_ticket_no: "",
    },
    car_info = {
      plate: "",
    },
  } = receivingCarTicketAdd;

  const content = !base_info ? null :  (
      <View style={styles.headContent}>
           <Text style={{ fontWeight: "bold" }}>【{car_info.plate}】 已完成接车，单号：{base_info.receiving_car_ticket_no}</Text>
           <Text>车主油保养卡和洗车卡，请选择是否直接开单</Text>
            {/* 添加按钮 */}
            <View style={styles.buttonContainer}>
              <Text style={styles.button}>开洗车单</Text>
              <Text style={styles.button}>开保养单</Text>
            </View>
        </View>
    );

  return content;
}

const styles = StyleSheet.create({
  headView: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 16,
  },
  headImage: {
    width: 72,
    height: 72,
    marginRight: 20,
  },
  headContent: {
    flex: 1,
    marginRight: 10,
  },
  headContentText: {
    fontWeight: "bold",
    fontSize: 30,
    numberOfLines: 2,
  },
  fold: {
    borderRadius: 16,
    backgroundColor: "#F7F8FA",
  },
  marginTopView: {
    backgroundColor: "#F7F8FA",
    borderRadius: 16,
    padding: 16,
  },
  headerText: {
    fontSize: 32,
    lineHeight: 48,
  },
  trigger: {
    position: "absolute",
    right: 0,
    bottom: 0,
  },
  triggerView: {
    flexDirection: "row",
    alignItems: "center",
  },
  triggerText: {
    color: "#646566",
    fontSize: 22,
    lineHeight: 40,
  },
  triggerImage: {
    width: 15,
    height: 10,
  },
  containerView: {
    backgroundColor: "#F7F8FA",
    padding: 10,
    borderRadius: 16,
  },
  contentView: {
    flexDirection: "row",
    alignItems: "center",
  },
  contentImage: {
    width: 30,
    height: 30,
    marginRight: 12,
  },
  contentText: {
    color: "#979899",
    flex: 1,
    flexWrap: "wrap",
  },
  buttonContainer: {
  flexDirection: 'row',
  marginTop: 10,
  },

  button: {
    flex: 1,
    padding: 10,
    margin: 5,
    backgroundColor: '#007AFF',
    color: 'white',
    textAlign: 'center',
    borderRadius: 8,
  },
});
