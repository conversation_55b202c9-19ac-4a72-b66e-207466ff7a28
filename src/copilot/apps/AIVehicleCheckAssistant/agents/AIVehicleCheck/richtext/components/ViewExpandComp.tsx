import { React, View, Text, StyleSheet } from "@casstime/copilot-xml";
import { ICustomerVehicleBaseItem} from "../../interface/index";

export function ViewExpandComp({
  id,
  customerVehicleBase
}: {
  id: string;
  customerVehicleBase: ICustomerVehicleBaseItem;
}) {
  const {
    customer_name = "",
    preferences = "",
    plate = "",
    custom_model = "",
    vin = "",
    mileage = 0,
    package_card_total = 0,
    business_opportunity_total = 0,
    repair_record_total = 0,
    repair_record_details = [],
  } = customerVehicleBase;

  const content = !plate ? null :  (
      <View style={styles.headContent}>
          <Text> 车牌: "{plate} {customer_name}"已存在</Text>
          <Text> 车型: {custom_model}</Text>
          <Text> 车架号: {vin}</Text>
          <Text> 里程: {formatMileage(mileage)}</Text>
          <Text/>
          <Text style={{ fontWeight: "bold" }}>和车主相关的如下关键信息需要特别关注：</Text>
          <Text>{package_card_total}个套餐卡</Text>
          <Text/>
          <Text>{business_opportunity_total}个商机</Text>
          <Text/>
          <Text> 个人喜好:</Text>
          <Text> {preferences}</Text>
          <Text/>
          <Text style={{ fontWeight: "bold" }}>最近1次服务记录：</Text>
          {repair_record_details.length > 0 ? (
            // 循环显示服务记录
            repair_record_details.map((record, recordIndex) => {
              return (
                <View style={styles.headContent} key={recordIndex}>
                <Text>工单号：{record.order_no || '无'}</Text>
                <Text>日期：{formatDate(record.common_serve_time)}</Text>
                <Text>里程：{formatMileage(record.mileage)}</Text>
                <Text>服务项目：</Text>

                  {record.gs_projects?.length > 0 ? (
                    record.gs_projects.map((project, labourIndex) => {
                      return (
                        <View style={styles.headContent}  key={labourIndex}>
                          <Text>{labourIndex + 1}、{project.project_name}</Text>

                            {project.project_part_list?.length > 0 ? (
                              project.project_part_list.map((part, partIndex) => {
                                return (
                                  <View style={styles.headContent} key={partIndex}>
                                  <Text>--{part.project_name}*{part.project_qty}{part.project_unit} {part.total}元</Text>
                                </View>
                                );
                              })) : ( <Text> 暂无配件项目列表</Text>)
                            }

                        </View>
                      );
                    })) : ( <Text> 暂无工时项目列表</Text>)
                  }
                </View>
              );
              
            })) : (<Text> 暂无维修记录</Text>)}

            {/* 添加按钮 */}
            <View style={styles.buttonContainer}>
              <Text style={styles.button}>更多维修记录（{repair_record_total}）</Text>
              <Text style={styles.button}>接车</Text>
            </View>
        </View>
    );

  return content;
}

// 格式化里程
const formatMileage = (mileage?: string | number): string => {
  if (mileage === null || mileage === undefined ) return '未记录';
  return `${mileage} KM`;
};

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return '未知';

  let timestamp = parseInt(dateStr, 10);
  if (timestamp.toString().length === 10) {
    // 秒级时间戳，转成毫秒
    timestamp *= 1000;
  }

  const date = new Date(timestamp);
  if (isNaN(date.getTime())) return dateStr;

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

const styles = StyleSheet.create({
  headView: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 16,
  },
  headImage: {
    width: 72,
    height: 72,
    marginRight: 20,
  },
  headContent: {
    flex: 1,
    marginRight: 10,
  },
  headContentText: {
    fontWeight: "bold",
    fontSize: 30,
    numberOfLines: 2,
  },
  fold: {
    borderRadius: 16,
    backgroundColor: "#F7F8FA",
  },
  marginTopView: {
    backgroundColor: "#F7F8FA",
    borderRadius: 16,
    padding: 16,
  },
  headerText: {
    fontSize: 32,
    lineHeight: 48,
  },
  trigger: {
    position: "absolute",
    right: 0,
    bottom: 0,
  },
  triggerView: {
    flexDirection: "row",
    alignItems: "center",
  },
  triggerText: {
    color: "#646566",
    fontSize: 22,
    lineHeight: 40,
  },
  triggerImage: {
    width: 15,
    height: 10,
  },
  containerView: {
    backgroundColor: "#F7F8FA",
    padding: 10,
    borderRadius: 16,
  },
  contentView: {
    flexDirection: "row",
    alignItems: "center",
  },
  contentImage: {
    width: 30,
    height: 30,
    marginRight: 12,
  },
  contentText: {
    color: "#979899",
    flex: 1,
    flexWrap: "wrap",
  },
  buttonContainer: {
  flexDirection: 'row',
  marginTop: 10,
  },

  button: {
    flex: 1,
    padding: 10,
    margin: 5,
    backgroundColor: '#007AFF',
    color: 'white',
    textAlign: 'center',
    borderRadius: 8,
  },
});
