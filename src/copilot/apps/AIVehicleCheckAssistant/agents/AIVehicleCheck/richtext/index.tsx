import { React } from "@casstime/copilot-xml";
import { renderToXML } from "@casstime/copilot-xml";
import { ViewExpandComp } from "./components/ViewExpandComp";
import { ViewRecordsComp } from "./components/ViewRecordsComp";
import { CenterDisplayExpandComp } from "./components/CenterDisplayExpandComp";
import { OneIndexExpandComp } from "./components/OneIndexExpandComp";
import { ViewVehicleAddComp } from "./components/ViewVehicleAddComp";
import { ICustomerVehicleBaseItem, IVehicleRecordsInfoResponse, IRreceivingCarTicketItem, IRreceivingCarTicketAdd} from "../interface/index";

export function createFinishVehicleBaseInfoViewXml(id: string, receivingCarTicketAdd: IRreceivingCarTicketAdd) {
  const content = renderToXML(
    <ViewVehicleAddComp id={id} receivingCarTicketAdd={receivingCarTicketAdd} ></ViewVehicleAddComp>
  );
  return {
    type: "richtext",
    content,
  };
}

export function createVehicleBaseInfoViewXml(id: string, customerVehicleBase: ICustomerVehicleBaseItem) {
  const content = renderToXML(
    <ViewExpandComp id={id} customerVehicleBase={customerVehicleBase} ></ViewExpandComp>
  );
  return {
    type: "richtext",
    content,
  };
}

export function createVehicleRecordsViewXml(id: string, plate: string, vehicleRecords: IVehicleRecordsInfoResponse) {
  const content = renderToXML(
    <ViewRecordsComp id={id} plate={plate} vehicleRecords={vehicleRecords} ></ViewRecordsComp>
  );
  return {
    type: "richtext",
    content,
  };
}

export function createVehicleCenterDisplayViewXml(id: string, plate: string, receivingCarTicket: IRreceivingCarTicketItem) {
  const content = renderToXML(
    <CenterDisplayExpandComp id={id} plate={plate} receivingCarTicket={receivingCarTicket}></CenterDisplayExpandComp>
  );
  return {
    type: "richtext",
    content,
  };
}

export function createOneIndexDisplayViewXml() {
  const content = renderToXML(
    <OneIndexExpandComp ></OneIndexExpandComp>
  );
  return {
    type: "richtext",
    content,
  };
}
