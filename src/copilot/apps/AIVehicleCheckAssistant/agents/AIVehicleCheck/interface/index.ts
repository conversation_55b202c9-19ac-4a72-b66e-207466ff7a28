// 假设接口返回结构如下（请根据实际情况调整）
export interface IVehicleInfoResponse {
  aud?: number;
  data?: ICustomerVehicleBaseItem; // 根据实际结构调整字段
  code?: number;
  message?: string;
}

// 客户车辆基本信息
export interface ICustomerVehicleBaseItem {
  customer_id: string; // 客户ID
  customer_store_item_id: string; // 门店客户ID
  customer_name: string; // 客户名称
  customer_mobile?: string; // 客户电话
  customer_type_id?: number; 
  preferences?: string; // 客户喜好
  plate: string; // 车牌号
  car_id: string; // 车辆ID
  custom_line?: string; // 车系
  custom_model?: string; // 车型全称
  vin?: string; // 车架号
  mileage?: number; // 里程
  package_card_total?: number; // 套餐卡总数
  business_opportunity_total?: number; // 商机总数
  repair_record_total?: number; // 维修记录数
  repair_record_details?: IRepairRecordDetailItem[];
}

// 假设接口返回结构如下（请根据实际情况调整）
export interface IVehicleRecordsInfoPageResponse {
  aud?: number;
  data?: IVehicleRecordsInfoResponse; // 根据实际结构调整字段
  code?: number;
  message?: string;
}

export interface IVehicleRecordsInfoResponse {
  total?: number; // 总记录数
  list?: IRepairRecordDetailItem[]; // 根据实际结构调整字段
}

// 服务记录
export interface IRepairRecordDetailItem {
  id: string; // 工单ID
  order_no: string; // 工单号
  common_serve_time: string; // 接车时间
  mileage : number; // 里程
  gs_projects: IProjectObjectItem[] // 工时
  pj_projects: IProjectObjectItem[] // 配件
}
// 服务项目
export interface IProjectObjectItem {
  id: string; // 项目ID
  parent_id : string;  // 配件项目父级ID
  project_name : string; // 项目名称
  project_qty : number;  // 项目数量
  project_unit : string; // 项目单位
  price : number; // 项目单价
  original_price : number; // 项目原价
  total : number; // 项目总价
  discount : number; // 项目折扣
  reduce_money : number; // 项目优惠金额
  project_part_list : IProjectObjectItem[]; // 项目配件
}

// 接车单
export interface IRreceivingCarTicketItem {
  carId : string;
  plate : string;
  mileage?: number;
  fuelCapacity?: string;
  fuelConsumption?: string;
  display_check_items?: IDisplayCheckItem[];
  interior_check_items?: IInteriorCheckItem[];
}

export interface IDisplayCheckItem {
  position_code : number;
  position_name : string;
  display_check_result: IDisplayCheckResult[];
}

export interface IDisplayCheckResult {
  code : number;
  desc : string;
}

export interface IInteriorCheckItem {
  position_code : number;
  position_name : string;
  check_value_code: number;
  repair_description: string;
}


// 接车单
export interface IRreceivingCarTicketAdd {
  delete_images_version : number;
  base_info : IServiceBaseInfoAdd;
  car_info: IServiceCarInfoAdd;
  customer_info: IServiceCustomerInfoAdd;
  display_check_result: IDisplayCheckResultAdd;
  interior_check_items: IInteriorCheckItem[];
  images: IReceivingCarTicketImageAdd[];
}

export interface IServiceBaseInfoAdd {
  receiving_car_ticket_id:string; // 接车单id
  receiving_car_ticket_no:string; // 接车单id
  bill_type: number;  // 工单类型
  start_time?: string; // 接车时间
  repair_man: string; // 送修人名称
  repair_mobile?: string; // 送修人手机
  service_advisor?: string; // 接待员工名称
  employee_id?: string // 接待员工id
  mileage?: number; // 进厂里程
  rest_oil?: number; // 剩余油量
}

export interface IServiceCarInfoAdd {
  car_id: string; // 车辆id
  car_no: string; // 车牌号
  plate: string; // 车牌号
  mileage?: number; // 里程
  vin?: string; // 车架号
}

export interface IServiceCustomerInfoAdd {
  customer_id: string; // 客户id
  customer_store_item_id: string; // 客户门店id
  customer_name: string; // 客户名称
  customer_mobile?: string; // 客户手机号
  customer_type_id?: number; // 客户类型1=个人客户，2=单位客户
  print_customer_mobile?: string;
  preferences?: string; // 客户偏好
}

export interface IDisplayCheckResultAdd{
  display_check_items : IDisplayCheckItemAdd[];
  other_check_comment : string;
}

export interface IDisplayCheckItemAdd {
  position_id : string;
  check_values : number[];
}

// 假设接口返回结构如下（请根据实际情况调整）
export interface ISaveVehicleInfoResponse {
  aud?: number;
  data?: IServiceBaseInfoAdd; // 根据实际结构调整字段
  code?: number;
  message?: string;
}

export interface IReceivingCarTicketImageAdd {
  url: string; // 图片url
  type: number; // 接车图片类型：0：其他接车图片，1：外观常规检查图片，2：外观其他检查图片，3：内饰检查图片
  width: number;  // 宽度
}
