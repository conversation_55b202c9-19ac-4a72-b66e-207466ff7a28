import { IClassifier } from "@casstime/copilot-core";
import { AIVehicleCheckIntents } from "../intent";
import logger from "@/common/logger";

export const intentClassifier: IClassifier = {
  id: "INTENT_CLASSIFIER",
  async classify(context) {
    try {
      const { lastMessage } = context;
      console.log("====1号车间处理器===" + lastMessage);

      if (lastMessage.type === "text") {
        const text = lastMessage.content;
        if (text.indexOf("更多维修记录") > -1) {
          return AIVehicleCheckIntents.更多维修记录;
        } else if (text.indexOf("接车") > -1) {
          return AIVehicleCheckIntents.接车;
        } else if (text.indexOf("完成检查") > -1) {
          return AIVehicleCheckIntents.完成检查;
        } else if (text.indexOf("重新开始") > -1) {
          return AIVehicleCheckIntents.重新开始;
        } else if (isLicensePlate(text)) {
          return AIVehicleCheckIntents.车牌;
        } else {
          return AIVehicleCheckIntents.其它;
        }
      }
      return AIVehicleCheckIntents.其它;
    } catch (err) {
      logger.warn("意图分类失败", err);
      return AIVehicleCheckIntents.其它;
    }
  },
};

function isLicensePlate(text: string): boolean {
  const pattern =
    /^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[A-Za-z]{1}[A-HJ-NP-Za-z0-9]{4,5}[A-HJ-NP-Za-z0-9挂学警港澳]{1})/;
  return pattern.test(text);
}
