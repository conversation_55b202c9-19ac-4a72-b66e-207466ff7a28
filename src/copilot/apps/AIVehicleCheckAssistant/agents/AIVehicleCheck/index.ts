import { AgentName } from "@/copilot/constants";
import { Agent } from "@casstime/copilot-core";
import logger from "@/common/logger";
import { intentClassifier } from "./classifiers/intentClassifier";
import { AIVehicleCheckIntents } from "./intent";
import {
  plate<PERSON>and<PERSON>,
  centerConsoleHandler,
  outAppearanceHandler,
  inAppearanceHandler,
  otherHandler,
  serverRecordsHandler,
  pickUpCarHandler,
  finishCarHandler,
  moveHandler,
  inquiryStartHandler,
  fallbackHandler,
} from "./handlers";

/**
 * Agent
 */
const agent = new Agent(AgentName.AIVehicleCheckAgent);

/**
 * 第一次进入Agent
 */
agent.onEnter(() => {
  logger.info("进入 AIVehicleCheckAgent");
});

/**
 * 离开当前Agent
 */
agent.onLeave(() => {
  logger.info("离开 AIVehicleCheckAgent");
});

/**
 * 设置意图解析器
 */
agent.registerIntentClassifier(intentClassifier, true);

agent.handle(AIVehicleCheckIntents.更多维修记录, serverRecordsHandler);

agent.handle(AIVehicleCheckIntents.接车, pickUpCarHandler);

agent.handle(AIVehicleCheckIntents.完成检查, finishCarHandler);

agent.handle(AIVehicleCheckIntents.车牌, plateHandler);

agent.handle(AIVehicleCheckIntents.仪表, centerConsoleHandler);

agent.handle(AIVehicleCheckIntents.外观, outAppearanceHandler);

agent.handle(AIVehicleCheckIntents.内饰, inAppearanceHandler);

agent.handle(AIVehicleCheckIntents.其它, otherHandler);

agent.handle(AIVehicleCheckIntents.重新开始, moveHandler);

agent.handleCommand(AIVehicleCheckIntents.inquiryStart, inquiryStartHandler);

agent.handleFallback(fallbackHandler);

export default agent;
