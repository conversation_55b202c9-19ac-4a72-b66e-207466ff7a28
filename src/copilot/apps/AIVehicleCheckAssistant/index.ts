import { Application } from "@casstime/copilot-core";
import { AppName } from "@/common/enums";
import AIVehicleCheckAgent from "./agents/AIVehicleCheck";

const app = new Application(AppName.AIVehicleCheckAssistant);

const DEFAULT_SCENE = "FALLBACK";

app.setSceneClassifier({
  id: "AIVehicleCheckClassifier",
  classify: async () => {
    return DEFAULT_SCENE;
  },
});


app.route(DEFAULT_SCENE, AIVehicleCheckAgent);

export default app;
