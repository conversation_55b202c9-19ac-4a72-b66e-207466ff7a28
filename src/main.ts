import fastify from "fastify";
import mongoose from "mongoose";
import { getEurekaClient } from "./common/infra/eureka";
import { bindRoutes } from "./routes";
import { bindHooks } from "./common/hooks";
import { getEnvConfig, isDev } from "./common/utils";
import { loadSpringConfig, SpringConfigOptioins } from "@casstime/config";
import { apolloConfig, config } from "@casstime/apollo-config";

const server = fastify({ logger: true });

bindHooks(server);

bindRoutes(server);

const MONGODB_URL = getEnvConfig("MONGODB_URL") as string;
const PORT = +(getEnvConfig("PORT") || 22345);

async function start() {
  await apolloConfig();
  console.log("当前配置信息为", config);
  await loadSpringConfig({
    endpoint: config.get("SPRING_CLOUD_CONFIG_ENDPOINT"),
    env: (config.util.getEnv("NODE_ENV") || "alpha") as SpringConfigOptioins["env"],
    name: ["__common_feign_client_", "__common_eureka_", "terminal-api"],
  });

  await mongoose.connect(MONGODB_URL);
  console.log("已连接数据库：", MONGODB_URL);
  server.listen({ port: PORT, host: "0.0.0.0" }, (err, address) => {
    if (err) {
      console.error(err);
      process.exit(1);
    }
    console.log(`Server listening at ${address}`);
    if (!isDev()) {
      getEurekaClient().start();
    }
  });

  const gracefulShutdown = async () => {
    try {
      await getEurekaClient().stop();
      await server.close();
    } finally {
      process.exit(0);
    }
  };

  // 处理系统信号
  process.on("SIGTERM", gracefulShutdown);
  process.on("SIGINT", gracefulShutdown);
  // 捕获未catch Promise
  process.on("unhandledRejection", async (err) => {
    console.error("[unhandleRejection] 未处理Promise异常", {
      err,
    });
  });
}

start();
