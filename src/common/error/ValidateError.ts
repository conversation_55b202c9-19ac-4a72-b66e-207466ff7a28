export enum ValidateErrorCode {
  MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD", // 表单中某些必填字段未填写
  INVALID_VALUE_TYPE = "INVALID_VALUE_TYPE", // 表单字段的值类型不正确
  VALUE_OUT_OF_RANGE = "VALUE_OUT_OF_RANGE", // 表单字段的值超出了允许的范围
  FORMAT_ERROR = "FORMAT_ERROR", // 表单字段的值格式不正确
  DUPLICATE_VALUE = "DUPLICATE_VALUE", // 表单中某些字段的值重复，而这些字段应该是唯一的
  INCONSISTENCY_ERROR = "INCONSISTENCY_ERROR", // 表单中某些字段之间存在不一致性
  DEPENDENCY_ERROR = "DEPENDENCY_ERROR", // 某些字段的值依赖于其他字段的值，但未满足依赖关系
  LENGTH_ERROR = "LENGTH_ERROR", // 表单字段的值长度不符合要求
  ILLEGAL_CHARACTERS = "ILLEGAL_CHARACTERS", // 表单字段包含不允许的字符
}

class ValidateError<T = object> extends Error {
  static isValidateError<T>(err: unknown): err is ValidateError<T> {
    return err instanceof ValidateError;
  }
  static create<T>(code: ValidateErrorCode, message: string, data?: T) {
    return new ValidateError(code, message, data);
  }
  readonly code: string;
  readonly data: T;
  private constructor(code: ValidateErrorCode, message: string, data = {} as T) {
    super(message);
    this.code = code;
    this.data = data;
    this.name = "ValidateError";
  }
}

export default ValidateError;
