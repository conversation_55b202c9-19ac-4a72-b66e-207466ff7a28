import { ErrorCode, TeamCode } from "../enums";
import { HttpError } from "./HttpError";

class ErrorFactory {
  public static createError<T>(
    errorCode: ErrorCode,
    options?: { message?: string; data?: T; teamCode?: number }
  ): HttpError {
    return HttpError.create(errorCode, {
      errorCode,
      teamCode: options?.teamCode || TeamCode.TerminalTeam,
      data: options?.data,
    });
  }
}

export default ErrorFactory;
