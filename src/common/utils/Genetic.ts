/**
 * 遗传算法 TypeScript 实现
 * 基于 Sub Protocol 的 genetic-js 库
 */

import { sleep } from "./async";

// 类型定义
export type Entity = any;
export type Population = Array<{ fitness: number; entity: Entity }>;

export interface Stats {
  maximum: number; // 最大值
  minimum: number; // 最小值
  mean: number; // 平均值
  stdev: number; // 标准差
}

export interface GeneticConfiguration {
  size: number; // 种群大小
  crossover: number; // 交叉概率
  mutation: number; // 变异概率
  iterations: number; // 迭代次数
  fittestAlwaysSurvives: number; // 始终存留的最优个体数量
  maxResults: number; // 返回结果的最大数量
  skip: number; // 通知间隔
}

// 深克隆工具函数
function Clone<T>(obj: T): T {
  if (obj === null || typeof obj !== "object") {
    return obj;
  }
  return JSON.parse(JSON.stringify(obj));
}

// 优化策略
const Optimize = {
  Maximize: (a: number, b: number): boolean => a >= b, // 最大化
  Minimize: (a: number, b: number): boolean => a < b, // 最小化
};

// 单个个体的选择方法
const Select1 = {
  Tournament2: function (this: GeneticInstance, pop: Population): Entity {
    const n = pop.length;
    const a = pop[Math.floor(Math.random() * n)];
    const b = pop[Math.floor(Math.random() * n)];
    return this.optimize(a.fitness, b.fitness) ? a.entity : b.entity;
  },

  Tournament3: function (this: GeneticInstance, pop: Population): Entity {
    const n = pop.length;
    const a = pop[Math.floor(Math.random() * n)];
    const b = pop[Math.floor(Math.random() * n)];
    const c = pop[Math.floor(Math.random() * n)];
    let best = this.optimize(a.fitness, b.fitness) ? a : b;
    best = this.optimize(best.fitness, c.fitness) ? best : c;
    return best.entity;
  },

  Fittest: function (pop: Population): Entity {
    return pop[0].entity;
  },

  Random: function (pop: Population): Entity {
    return pop[Math.floor(Math.random() * pop.length)].entity;
  },

  RandomLinearRank: function (this: GeneticInstance, pop: Population): Entity {
    this.internalGenState["rlr"] = this.internalGenState["rlr"] || 0;
    return pop[Math.floor(Math.random() * Math.min(pop.length, this.internalGenState["rlr"]++))].entity;
  },

  Sequential: function (this: GeneticInstance, pop: Population): Entity {
    this.internalGenState["seq"] = this.internalGenState["seq"] || 0;
    return pop[this.internalGenState["seq"]++ % pop.length].entity;
  },
};

// 两个个体的选择方法
const Select2 = {
  Tournament2: function (this: GeneticInstance, pop: Population): [Entity, Entity] {
    return [Select1.Tournament2.call(this, pop), Select1.Tournament2.call(this, pop)];
  },

  Tournament3: function (this: GeneticInstance, pop: Population): [Entity, Entity] {
    return [Select1.Tournament3.call(this, pop), Select1.Tournament3.call(this, pop)];
  },

  Random: function (this: GeneticInstance, pop: Population): [Entity, Entity] {
    return [Select1.Random.call(this, pop), Select1.Random.call(this, pop)];
  },

  RandomLinearRank: function (this: GeneticInstance, pop: Population): [Entity, Entity] {
    return [Select1.RandomLinearRank.call(this, pop), Select1.RandomLinearRank.call(this, pop)];
  },

  Sequential: function (this: GeneticInstance, pop: Population): [Entity, Entity] {
    return [Select1.Sequential.call(this, pop), Select1.Sequential.call(this, pop)];
  },

  FittestRandom: function (this: GeneticInstance, pop: Population): [Entity, Entity] {
    return [Select1.Fittest.call(this, pop), Select1.Random.call(this, pop)];
  },
};

const MAX_BLOCK_TIME = 500;
// 主要的遗传算法类
class GeneticInstance {
  // 必须实现的函数
  public fitness: (entity: Entity) => number = null!;
  public seed: () => Entity = null!;
  public optimize: (a: number, b: number) => boolean = null!;
  public select1: (population: Population) => Entity = null!;

  // 可选实现的函数
  public mutate: ((entity: Entity) => Entity) | null = null;
  public crossover: ((mother: Entity, father: Entity) => [Entity, Entity]) | null = null;
  public select2: ((population: Population) => [Entity, Entity]) | null = null;
  public generation: ((population: Population, generation: number, stats: Stats) => boolean | void) | null = null;
  public notification:
    | ((population: Population, generation: number, stats: Stats, isFinished: boolean) => void)
    | null = null;

  public unique: ((population: Population) => Population) | null = null;

  // 配置和状态
  public configuration: GeneticConfiguration = {
    size: 250,
    crossover: 0.9,
    mutation: 0.2,
    iterations: 100,
    fittestAlwaysSurvives: 1,
    maxResults: 100,
    skip: 0,
  };

  public userData: Record<string, any> = {};
  public internalGenState: Record<string, any> = {};
  public entities: Entity[] = [];

  // 核心方法
  public async start(): Promise<void> {
    // 根据概率应用变异
    const mutateOrNot = (entity: Entity): Entity => {
      return Math.random() <= this.configuration.mutation && this.mutate ? this.mutate(Clone(entity)) : entity;
    };
    let startAt = Date.now();
    // 初始化种群
    this.entities = [];
    for (let i = 0; i < this.configuration.size; i++) {
      this.entities.push(Clone(this.seed()));
    }

    // 运行遗传算法指定的迭代次数
    for (let i = 0; i < this.configuration.iterations; i++) {
      // 每代重置内部状态
      this.internalGenState = {};

      // 计算适应度并排序种群
      const pop = this.entities
        .map((entity) => ({
          fitness: this.fitness(entity),
          entity: entity,
        }))
        .sort((a, b) => (this.optimize(a.fitness, b.fitness) ? -1 : 1));

      // 计算统计信息
      const mean = pop.reduce((a, b) => a + b.fitness, 0) / pop.length;
      const stdev = Math.sqrt(
        pop.map((a) => (a.fitness - mean) * (a.fitness - mean)).reduce((a, b) => a + b, 0) / pop.length
      );

      const stats: Stats = {
        maximum: pop[0].fitness,
        minimum: pop[pop.length - 1].fitness,
        mean: mean,
        stdev: stdev,
      };

      // 调用代回调（如果提供）
      const continueEvolution = this.generation
        ? this.generation(pop.slice(0, this.configuration.maxResults), i, stats)
        : true;
      const isFinished =
        (typeof continueEvolution !== "undefined" && !continueEvolution) || i === this.configuration.iterations - 1;

      // 如果需要，发送通知
      if (this.notification && (isFinished || this.configuration.skip === 0 || i % this.configuration.skip === 0)) {
        this.notification(pop.slice(0, this.configuration.maxResults), i, stats, isFinished);
      }

      // 如果完成则停止
      if (isFinished) {
        break;
      }
      // 同步执行时长超过500MS,则暂停执行一个周期（防止过长时间阻塞）
      const now = Date.now();
      if (now - startAt > MAX_BLOCK_TIME) {
        startAt = now;
        await sleep(0);
      }
      // 创建下一代
      const newPop: Entity[] = [];
      let uniqPop = pop;
      if (this.unique) {
        uniqPop = this.unique(pop);
      }

      // 可选地保留最优个体
      const survives = this.configuration.fittestAlwaysSurvives || 0;
      if (survives) {
        newPop.push(...uniqPop.slice(0, survives).map((item) => item.entity));
      }

      // 填充新种群
      while (newPop.length < this.configuration.size) {
        if (
          this.crossover &&
          Math.random() <= this.configuration.crossover &&
          newPop.length + 1 < this.configuration.size
        ) {
          // 应用交叉
          const parents = this.select2!(uniqPop);
          const children = this.crossover(Clone(parents[0]), Clone(parents[1])).map(mutateOrNot);
          newPop.push(children[0], children[1]);
        } else {
          // 仅选择并可能变异
          newPop.push(mutateOrNot(this.select1(uniqPop)));
        }
      }

      // 用新种群替换旧种群
      this.entities = newPop;
    }
  }

  public evolve(
    config: Partial<GeneticConfiguration>,
    userData: Record<string, any>,
    callback: (population: Population, generation: number, stats: Stats, isFinished: boolean) => void
  ): Promise<{ pop: Population; generation: number; stats: Stats; isFinished: boolean }> {
    // 应用配置
    for (const key in config) {
      if (Object.prototype.hasOwnProperty.call(config, key)) {
        (this.configuration as any)[key] = (config as any)[key];
      }
    }

    // 应用用户数据
    if (userData) {
      for (const key in userData) {
        if (Object.prototype.hasOwnProperty.call(userData, key)) {
          this.userData[key] = userData[key];
        }
      }
    }

    // 返回一个在算法完成时解析的 Promise
    return new Promise<{ pop: Population; generation: number; stats: Stats; isFinished: boolean }>((resolve) => {
      // 存储原始通知函数
      const originalNotification = this.notification;

      // 覆盖通知函数以在完成时解析 Promise
      this.notification = (pop, generation, stats, isFinished) => {
        // 如果存在，则调用原始通知
        if (originalNotification) {
          originalNotification.call(this, pop, generation, stats, isFinished);
        }
        callback(pop, generation, stats, isFinished);
        // 在算法完成时解析 Promise
        if (isFinished) {
          resolve({ pop, generation, stats, isFinished });
        }
      };

      // 开始进化过程
      this.start();
    });
  }
}

// 主要的遗传算法对象
export const Genetic = {
  create: (): GeneticInstance => new GeneticInstance(),
  Optimize,
  Select1,
  Select2,
  Clone,
};
