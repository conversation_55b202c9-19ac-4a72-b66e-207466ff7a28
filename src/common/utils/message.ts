import { IFormMessage, IMessage } from "@casstime/copilot-core";
import { $parse, IXmlNode } from "@casstime/copilot-xml";
import _ from "lodash";

function stringifyForm(form: IFormMessage) {
  if (form.formName === "inquiryForm") {
    return `已创建询价单: ${form.formData?.inquiryId}`;
  }
  if (form.formName === "inquiryResult") {
    return `正在报价，请稍候...`;
  }
  return "";
}

export function trimTags(text: string) {
  return text.replace(/<[^>]+>/g, "");
}

function xmlNodeToString(node: IXmlNode): string {
  if (node.type === "literal") {
    return node.value;
  }
  if (node.type === "text") {
    return node.children.map(xmlNodeToString).join("\t");
  }
  if (node.type === "view" || node.type === "fragment") {
    return node.children?.map(xmlNodeToString).join("\n") || "\n";
  }
  if (node.type === "link") {
    const val = node.children.map(xmlNodeToString).join("");
    return `[${val}](${node.props?.to})`;
  }
  if (node.type === "table") {
    const [header, ...rows] = node.children;
    return (
      xmlNodeToString(header) +
      "\n| " +
      header.children?.map(() => "---").join(" | ") +
      " |\n" +
      rows.map(xmlNodeToString).join("")
    );
  }

  if (node.type === "row") {
    return (
      "| " +
      node.children
        .map(xmlNodeToString)
        .map((item) => item.trim().replace(/\n/, "<br>"))
        .join(" | ") +
      " |\n"
    );
  }
  if (node.type === "col") {
    return node.children.map(xmlNodeToString).join("").replace(/\n/g, " ") + " ";
  }

  if (node.type === "image") {
    return `![image](${node.props.uri})`;
  }

  // 不输出按钮
  if (node.type === "button") {
    return "";
  }

  return "";
}

export function stringifyRichText(xml: string) {
  const ast = $parse(xml);
  const output = ast
    .map(xmlNodeToString)
    .join("\n")
    .replace(/\n\s*\n/g, "\n");
  const trimImage = output.replace(/\!\[image\]\(.*?\)/, "").trim();
  if (trimImage) {
    return trimImage;
  }
  return output.trim();
}

type WithRole =
  | boolean
  | {
      system: string;
      user: string;
    };

export function stringifyMessage(message: IMessage, withRole: WithRole = true) {
  let roles = { user: "用户", system: "采购助手" };
  if (typeof withRole === "object") {
    roles = withRole;
  }

  const role = message.fromUser === "system" ? roles.system : roles.user;
  let content = "";
  switch (message.type) {
    case "text":
      content = trimTags(message.content);
      break;
    case "voice":
      content = message.content || "";
      break;
    case "image":
      content = `![image](${Math.random().toString(36).slice(2)})`;
      break;
    case "video":
      content = `![video](${Math.random().toString(36).slice(2)})`;
      break;
    case "command":
      break;
    case "form":
      content = stringifyForm(message);
      break;
    case "richtext":
      content = stringifyRichText(message.content);
      break;
    case "markdown":
      content = trimTags(message.content);
      break;
    case "system":
      content = trimTags(message.content);
      break;
  }
  if (message.embed) {
    if (content.trim()) {
      content += "\n";
    }
    content += `<pre>\n${stringifyRichText(message.embed.content)}\n</pre>`;
  }
  const prefix = withRole ? `${role}: ` : "";
  return `${prefix}${content}`;
}

export function stringifyHistory(
  historyMessages: IMessage[],
  maxCount = 5,
  withRole: { system: string; user: string } = { system: "采购助手", user: "用户" }
) {
  return _.orderBy(historyMessages, ["createdAt"], ["asc"])
    .slice(-maxCount)
    .map((item) => stringifyMessage(item, withRole))
    .filter((item) => ![`${withRole.system}:`, `${withRole.user}:`].includes(item.trim()))
    .join("\n");
}
