import { isTimeBetween } from "../time";

describe(isTimeBetween.name, () => {
  const data = [
    {
      startTime: "2023-01-01 00:00:00",
      endTime: "2023-01-01 23:59:59",
      time: "2023-01-01 12:00:00",
      expected: true,
    },
    {
      startTime: "2023-01-01 00:00:00",
      endTime: "2023-01-01 23:59:59",
      time: "2023-01-02 12:00:00",
      expected: false,
    },
    {
      startTime: "2023-01-01 00:00:00",
      endTime: "2023-01-01 23:59:59",
      time: "2023-01-01 00:00:00",
      expected: false,
    },
  ];
  data.forEach((item) => {
    it(`should return ${item.expected} when startTime is ${item.startTime}, endTime is ${item.endTime}, time is ${item.time}`, () => {
      const result = isTimeBetween(item.time, [item.startTime, item.endTime]);
      expect(result).toBe(item.expected);
    });
  });
});
