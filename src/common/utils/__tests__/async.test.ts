import { waitAsyncIterableStream, waitForPromises } from "../async";

describe(waitAsyncIterableStream.name, () => {
  it("should wait for the stream to complete and return the result", async () => {
    const iterableStream = new ReadableStream({
      async start(controller) {
        controller.enqueue("Hello");
        controller.enqueue(" ");
        controller.enqueue("World");
        controller.close();
      },
    });

    const result = await waitAsyncIterableStream(iterableStream);
    expect(result).toEqual(["Hello", " ", "World"]);
  });
});

describe(waitForPromises.name, () => {
  it("should resolve all promises and return a resolved object", async () => {
    const input = {
      a: Promise.resolve(1),
      b: Promise.resolve("two"),
      c: Promise.resolve(true),
    };

    const output = await waitForPromises(input);

    expect(output).toEqual({
      a: 1,
      b: "two",
      c: true,
    });
  });

  it("should throw if any promise rejects", async () => {
    const input = {
      a: Promise.resolve(1),
      b: Promise.reject(new Error("error")),
      c: Promise.resolve(true),
    };

    await expect(waitForPromises(input)).rejects.toThrow("error");
  });
});
