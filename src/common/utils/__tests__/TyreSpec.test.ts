import { TyreSpec } from "../TyreSpec";

describe("TyreSpec", () => {
  test("当输入为空时，应返回null", () => {
    const tyreSpec = new TyreSpec("");
    expect(tyreSpec.parse().length).toBe(0);
  });

  test("当输入不符合模式时，应返回null", () => {
    const data = ["123XYZ", "1,250.00", "￥250.15"];
    for (const item of data) {
      const tyreSpec = new TyreSpec(item);
      expect(tyreSpec.parse().length).toBe(0);
    }
  });

  test("当输入尺寸不在合理范围，应返回null", () => {
    const tyreSpec = new TyreSpec("23810-0R020）");
    expect(tyreSpec.parse().length).toBe(0);
  });

  test("当输入符合模式时，应返回正确结果", () => {
    const tyreSpec = new TyreSpec("155/80R13");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("155");
    expect(result.flatnessRate).toBe("80");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("13");
  });

  test("当sectionWidth小于150时，应返回null", () => {
    const tyreSpec = new TyreSpec("149/80R13");
    expect(tyreSpec.parse()).toHaveLength(0);
  });

  test("当typeCode包含大写字母时，应返回正确结果", () => {
    const tyreSpec = new TyreSpec("155/80ZR13 XYZ");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.typeCode).toBe("ZR");
  });

  test("当输入全为数字时，返回正确结果", () => {
    const tyreSpec = new TyreSpec("1558013");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("155");
    expect(result.flatnessRate).toBe("80");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("13");
  });
  test("当输入用横线分割时，返回正确结果", () => {
    const tyreSpec = new TyreSpec("155-80-13");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("155");
    expect(result.flatnessRate).toBe("80");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("13");
  });

  test("当输入用空格分割时，返回正确结果", () => {
    const tyreSpec = new TyreSpec("155 80 13");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("155");
    expect(result.flatnessRate).toBe("80");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("13");
  });

  test("当输入用.分割时，返回正确结果", () => {
    const tyreSpec = new TyreSpec("155.45.18");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("155");
    expect(result.flatnessRate).toBe("45");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("18");
  });

  test("当typeCode包含小写字母时，应返回正确结果", () => {
    const tyreSpec = new TyreSpec("米其林155/80r13 xyz");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.typeCode).toBe("R");
  });

  test("包含多条轮胎规格时，返回多条结果", () => {
    const tyreSpec = new TyreSpec("155/80R13 255/80ZR13");
    const results = tyreSpec.parse();
    expect(results).toHaveLength(2);
    expect(results[1].sectionWidth).toBe("255");
  });
  test("没有扁平比时", () => {
    const results = TyreSpec.parse("155R13C");
    expect(results).toHaveLength(1);
    expect(TyreSpec.stringify(results[0])).toBe("155R13C");
  });
  test("尺寸支持小数点", () => {
    const results = TyreSpec.parse("245/35R17.5");
    expect(results).toHaveLength(1);
    expect(TyreSpec.stringify(results[0])).toBe("245/35R17.5");
  });
  test("允许多个空格", () => {
    const results = TyreSpec.parse("325/40  R22  114Y");
    expect(results).toHaveLength(1);
    expect(TyreSpec.stringify(results[0])).toBe("325/40R22");
  });
  test("严格解析，必须轮胎规格开头", () => {
    const results = TyreSpec.strictParse(" 245/35R20");
    expect(results).toHaveLength(0);
  });
  test("轮翼尺寸后面通过除了C|LT以外的非数字分割", () => {
    const results = TyreSpec.strictParse("245 65 17AT胎");
    expect(results).toHaveLength(1);
    expect(TyreSpec.stringify(results[0])).toBe("245/65R17");
  });
  test("中文数字处理", () => {
    const results = TyreSpec.strictParse("二二五五五十七马牌轮胎");
    expect(results).toHaveLength(1);
    expect(TyreSpec.stringify(results[0])).toBe("225/55R17");
  });
  test("flatnessRate为'00'的时候需要校验", () => {
    const results = TyreSpec.strictParse("2600020");
    expect(results).toHaveLength(0);
  });
  test("sectionWidth必须以5/0/6结尾", () => {
    expect(TyreSpec.includesTyreSpec("21711-JE30A")).toBe(false);
  });
  test("长串字符不是轮胎规格", () => {
    expect(TyreSpec.includesTyreSpec("21511JE30A")).toBe(false);
  });
  test("支持逗号分割", () => {
    const tyreSpec = new TyreSpec("固铂255，60，18");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("255");
    expect(result.flatnessRate).toBe("60");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("18");
  });

  test("支持混合分割", () => {
    const tyreSpec = new TyreSpec("固铂255 60。18");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("255");
    expect(result.flatnessRate).toBe("60");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("18");
  });
  test("去掉R\\d后面的空格", () => {
    const tyreSpec = new TyreSpec("255/60R1 8");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("255");
    expect(result.flatnessRate).toBe("60");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("18");
  });
  test("255+50+20", () => {
    const tyreSpec = new TyreSpec("255+50+20");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("255");
    expect(result.flatnessRate).toBe("50");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("20");
  });
  test("支持全角斜杠255／50R20", () => {
    const tyreSpec = new TyreSpec("255／50R20");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("255");
    expect(result.flatnessRate).toBe("50");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("20");
  });
  test("米其林PR1MACY245/40R19", () => {
    const tyreSpec = new TyreSpec("米其林PR1MACY245/40R19");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("245");
    expect(result.flatnessRate).toBe("40");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("19");
  });
  test("185R14 6PR", () => {
    const tyreSpec = new TyreSpec("185R14 6PR");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("185");
    expect(result.flatnessRate).toBe(undefined);
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("14");
  });
  test("225/60R18100H", () => {
    const tyreSpec = new TyreSpec("225/60R18100H");
    const [result] = tyreSpec.parse()!;
    expect(result).not.toBeNull();
    expect(result.sectionWidth).toBe("225");
    expect(result.flatnessRate).toBe("60");
    expect(result.typeCode).toBe("R");
    expect(result.rimDiameter).toBe("18");
  });
  const data = [
    {
      text: "30659046",
      output: false,
    },
    {
      text: "2254518",
      output: true,
    },
    {
      text: "145/80R18",
      output: true,
    },
  ];
  test.each(data)("$text", ({ text, output }) => {
    expect(TyreSpec.includesTyreSpec(text)).toBe(output);
  });
});
