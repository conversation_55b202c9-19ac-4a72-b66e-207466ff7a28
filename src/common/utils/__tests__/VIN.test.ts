import { validateVINCheckBit } from "../VIN";

it("有效Vin码", () => {
  const data = ["1C6SRFU92NN383404", "LBVFP3906BSE35574", "WDDNG5EB1BA383216"];
  for (const item of data) {
    expect(validateVINCheckBit(item)).toBe(true);
  }
});

it("Vin码长度不是17位、存在非法字符", () => {
  const data = ["1C65RFU92N", "OBVFP3906BSE35574", "QDDNG5EB1BA383216", ""];
  for (const item of data) {
    expect(validateVINCheckBit(item)).toBe(false);
  }
});

it("Vin码校验位验证不通过", () => {
  const data = ["1C65RFU92NN383404", "1BVFP3906BSE35574", "1DDNG5EB1BA383216"];
  for (const item of data) {
    expect(validateVINCheckBit(item)).toBe(false);
  }
});
