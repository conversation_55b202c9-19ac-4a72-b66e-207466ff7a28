import { extractTyreBrands, findTyre<PERSON><PERSON>, ITyreBrand } from "../tyre";

describe(extractTyreBrands.name, () => {
  const data: { text: string; output: ITyreBrand[] }[] = [
    {
      text: "米其林 205/55R17",
      output: [{ name: "米其林", code: "MICHELIN" }],
    },
    {
      text: "马牌和PIREllI",
      output: [
        { name: "马牌", code: "CONTINENTA" },
        { name: "倍耐力", code: "PIRELLI" },
      ],
    },
  ];

  test.each(data)("should extract tyre brand names", ({ text, output }) => {
    const result = extractTyreBrands(text);
    expect(result).toEqual(output);
  });
});

describe(findTyreBrand.name, () => {
  const data: { nameOrCode: string; output: ITyreBrand | undefined }[] = [
    {
      nameOrCode: "米其林",
      output: { name: "米其林", code: "MICHELIN" },
    },
    {
      nameOrCode: "CONTINENTA",
      output: { name: "马牌", code: "CONTINENTA" },
    },
  ];
  test.each(data)("should find tyre brand by name or code", ({ nameOrCode, output }) => {
    const result = findTyreBrand(nameOrCode);
    expect(result).toEqual(output);
  });
});
