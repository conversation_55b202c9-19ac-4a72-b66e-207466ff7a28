import { trimTags, stringifyRichText } from "../message";

it("trimTags", () => {
  expect(trimTags('<s font="12px">hello</s>')).toBe("hello");
  expect(trimTags("<s>hello</s> world")).toBe("hello world");
  expect(trimTags("<s>hello</s> <s>world</s>")).toBe("hello world");
  expect(trimTags("<s>hello</s> <s>world</s> <s>!</s>")).toBe("hello world !");
  expect(trimTags("<s>hello</s> <s>world</s> <s>!</s> <s>.</s>")).toBe("hello world ! .");
});

describe("stringifyRichText", () => {
  it("should handle literal nodes", () => {
    expect(stringifyRichText("hello")).toBe("hello");
  });

  it("should handle button/text nodes", () => {
    expect(stringifyRichText("<text>helloworld</text>")).toBe("helloworld");
  });

  it("should handle view/fragment nodes", () => {
    expect(stringifyRichText("<fragment><view>line1</view></view>line2</view></fragment>")).toBe("line1\nline2");
  });

  it("should handle link nodes", () => {
    expect(stringifyRichText('<link to="https://example.com">click</link>')).toBe("[click](https://example.com)");
  });

  it("should handle table nodes", () => {
    const xml = `
      <table>
        <row>
          <col>Header1</col>
          <col>Header2</col>
        </row>
        <row>
          <col>Row1Col1</col>
          <col>Row1Col2</col>
        </row>
      </table>
    `;
    expect(stringifyRichText(xml.trim())).toBe(
      "| Header1 | Header2 |\n" + "| --- | --- |\n" + "| Row1Col1 | Row1Col2 |"
    );
  });

  it("should handle image nodes", () => {
    expect(stringifyRichText('<image uri="https://example.com/image.png"/>')).toBe(
      "![image](https://example.com/image.png)"
    );
  });

  it("should handle nested nodes", () => {
    const xml = `
      <view>
        <text>Title</text>
        <link to="/details">Details</link>
        <table>
          <row>
            <col>Item</col>
            <col>Price</col>
          </row>
        </table>
      </view>
    `;
    expect(stringifyRichText(xml.trim())).toBe(
      "Title\n" + "[Details](/details)\n" + "| Item | Price |\n" + "| --- | --- |"
    );
  });

  it("should handle empty input", () => {
    expect(stringifyRichText("")).toBe("");
  });
});
