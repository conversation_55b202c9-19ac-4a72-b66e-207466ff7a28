import { extract<PERSON>hars, has<PERSON>hineseC<PERSON>, hasVinCode, isCellphone, isNormalChars } from "../string";

it("判断字符串内是否包含Vin码", () => {
  const data = [
    { str: "1HGCM82633A654321", result: true },
    { str: "车架号1HGCM82633A654321", result: true },
    { str: "123456789012345678", result: false },
    { str: "火花塞", result: false },
  ];
  for (const item of data) {
    expect(hasVinCode(item.str)).toBe(item.result);
  }
});

it("isNormalChars", () => {
  const input: [string, boolean][] = [
    ["test", true],
    ["向、", false],
    ["向东12", true],
    ["1", true],
  ];
  for (const [str, result] of input) {
    expect(isNormalChars(str)).toBe(result);
  }
});

it("isCellphone", () => {
  const input: [string, boolean][] = [
    ["13345678901", true],
    ["1234567890", false],
    ["23456789012", false],
  ];
  for (const [str, result] of input) {
    expect(isCellphone(str)).toBe(result);
  }
});

describe("过滤中文、英文、数字字符", () => {
  const data = [
    {
      input: "发动机？",
      output: "发动机",
    },
    {
      input: "机油格、前杠",
      output: "机油格前杠",
    },
    {
      input: "255/50 R20",
      output: "25550R20",
    },
    {
      input: '"！！！"',
      output: "",
    },
    {
      input: "",
      output: "",
    },
  ];
  test.each(data)("extractChars %s", ({ input, output }) => {
    expect(extractChars(input, "CHAR_CN_EN_NUM")).toBe(output);
  });
});

describe(hasChineseChar.name, () => {
  const data = [
    { str: "1234567890", result: false },
    { str: "abcde）", result: false },
    { str: "中文测试", result: true },
    { str: "123abc中文", result: true },
  ];
  for (const item of data) {
    test(item.str, () => {
      expect(hasChineseChar(item.str)).toBe(item.result);
    });
  }
});
