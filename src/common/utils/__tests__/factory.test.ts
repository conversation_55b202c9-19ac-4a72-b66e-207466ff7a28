import { makeSingleton } from "../factory";

describe(makeSingleton.name, () => {
  it("should return the same instance", () => {
    const createInstance = jest.fn(() => ({}));
    const singleton = makeSingleton(createInstance);

    const instance1 = singleton();
    const instance2 = singleton();

    expect(instance1).toBe(instance2);
    expect(createInstance).toHaveBeenCalledTimes(1);
  });

  it("should create a new instance if the function is different", () => {
    const createInstance1 = jest.fn(() => ({}));
    const createInstance2 = jest.fn(() => ({}));

    const singleton1 = makeSingleton(createInstance1);
    const singleton2 = makeSingleton(createInstance2);

    const instance1 = singleton1();
    const instance2 = singleton2();

    expect(instance1).not.toBe(instance2);
    expect(createInstance1).toHaveBeenCalledTimes(1);
    expect(createInstance2).toHaveBeenCalledTimes(1);
  });
});
