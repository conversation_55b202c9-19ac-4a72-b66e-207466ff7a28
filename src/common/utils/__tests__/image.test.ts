import { rotateOssImage } from "../image";

describe("rotateOssImage OSS图片旋转函数", () => {
  test("基础URL无query参数", () => {
    const url = "https://example.com/image.jpg";
    const rotatedUrl = rotateOssImage(url, 90);
    expect(rotatedUrl).toBe("https://example.com/image.jpg?x-oss-process=image%2Fresize%2Cl_1280%2Frotate%2C90");
  });

  test("URL已有其他query参数", () => {
    const url = "https://example.com/image.jpg?width=100&height=100";
    const rotatedUrl = rotateOssImage(url, 180);
    expect(rotatedUrl).toBe(
      "https://example.com/image.jpg?width=100&height=100&x-oss-process=image%2Fresize%2Cl_1280%2Frotate%2C180"
    );
  });

  test("URL已有旋转参数", () => {
    const url = "https://example.com/image.jpg?x-oss-process=image/rotate,90";
    const rotatedUrl = rotateOssImage(url, 90);
    expect(rotatedUrl).toBe("https://example.com/image.jpg?x-oss-process=image%2Fresize%2Cl_1280%2Frotate%2C180");
  });

  test("旋转角度叠加超过360度", () => {
    const url = "https://example.com/image.jpg?x-oss-process=image/rotate,270";
    const rotatedUrl = rotateOssImage(url, 180);
    expect(rotatedUrl).toBe("https://example.com/image.jpg?x-oss-process=image%2Fresize%2Cl_1280%2Frotate%2C90");
  });

  test("旋转角度为0", () => {
    const url = "https://example.com/image.jpg";
    const rotatedUrl = rotateOssImage(url, 0);
    expect(rotatedUrl).toBe("https://example.com/image.jpg?x-oss-process=image%2Fresize%2Cl_1280");
  });
});
