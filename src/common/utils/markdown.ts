import marked, { Tokens } from "marked";

export function extractMarkdownTokens(markdown: string, filter: (token: marked.Token) => boolean) {
  return marked.lexer(markdown).filter(filter);
}

function _convertMarkdownTableToJSON(markdown: string) {
  // 提取所有表格 Token
  const tables = extractMarkdownTokens(markdown, (token) => token.type === "table") as Tokens.Table[];

  // 合并所有表格数据
  const result = [];
  for (const table of tables) {
    for (const row of table.rows) {
      const obj: Record<string, string> = {};
      table.header.forEach((header, index) => {
        obj[header.text] = (row[index] || "").text.trim();
      });
      result.push(obj);
    }
  }

  return result;
}

export function convertMarkdownTableToJSON(markdown: string) {
  const result = _convertMarkdownTableToJSON(markdown);
  if (result.length) {
    return result;
  }
  // 解析table失败，去除多余行再次解析
  const lines = markdown.split("\n");
  const filterLines = lines.filter((line) => /^\s{0,}\|/.test(line));
  return _convertMarkdownTableToJSON(filterLines.join("\n"));
}
