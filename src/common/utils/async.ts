/**
 * 同 Promise.withResolvers
 * @see {https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/withResolvers}
 */
export const withResolvers = <T>() => {
  let resolve!: (value: T) => void;
  let reject!: (reason: unknown) => void;
  const promise = new Promise<T>((res, rej) => {
    resolve = res;
    reject = rej;
  });
  return { promise, resolve, reject };
};

/**
 * 使用 resolve 和 reject 构造回调函数
 * @param resolve
 * @param reject
 * @returns
 */
export const callbackify =
  <T>(resolve: (value: T) => void, reject: (reason: unknown) => void) =>
  (err: unknown, result?: T) => {
    if (err) {
      reject(err);
    } else {
      resolve(result!);
    }
  };

/**
 * 等待stream完成， 返回结果
 * @param iterableStream
 * @returns
 */
export async function waitAsyncIterableStream<T>(iterableStream: ReadableStream<T> & AsyncIterable<T>): Promise<T[]> {
  const result: T[] = [];
  for await (const item of iterableStream) {
    result.push(item);
  }
  return result;
}

// 定义一个类型，用于表示包含 Promise 的键值对对象
export type PromiseRecord<T> = {
  [K in keyof T]: Promise<T[K]>;
};

// 等待对象中的所有 Promise 完成，并返回解析后的结果对象
export async function waitForPromises<T extends Record<string, unknown>>(promises: PromiseRecord<T>): Promise<T> {
  const entries = Object.entries(promises) as Array<[keyof T, Promise<T[keyof T]>]>;

  const resolved: Partial<T> = {};

  for (const [key, promise] of entries) {
    resolved[key] = await promise;
  }

  return resolved as T;
}

/**
 * 等待一段时间，setTimeout Promise 封装，值小于等于 0 毫秒时，使用 queueMicrotask
 * @param ms
 * @returns
 */
export const sleep = (ms: number) => {
  if (ms <= 0) {
    return new Promise<void>((resolve) => queueMicrotask(resolve));
  }
  return new Promise((resolve) => setTimeout(resolve, ms));
};
