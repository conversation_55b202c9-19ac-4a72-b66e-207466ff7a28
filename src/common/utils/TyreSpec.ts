import _ from "lodash";
import Nzh from "nzh";

export interface ITyreSpec {
  sectionWidth: string;
  flatnessRate: string;
  typeCode: string;
  rimDiameter: string;
}
/**
 * 轮胎规格解析器
 * @example
 * ```
 * const result = TyreSpec.parse("205/55 R16");
 * console.log(result);
 * const text = TypeSpec.stringify(result);
 * console.log(text);
 * ```
 */
export class TyreSpec {
  /**
   * 规格解析
   * @param text
   * @returns
   */
  static parse(text: string) {
    if (!text) {
      return [];
    }
    return new TyreSpec(text).parse();
  }

  /**
   * 严格的规格解析，必须轮胎规格开头
   * @param text
   * @returns
   */
  static strictParse(text: string) {
    if (!text) {
      return [];
    }
    return new TyreSpec(text).parse(true);
  }

  /**
   * 判断是否包含轮胎规格
   * @param text
   * @returns
   */
  static includesTyreSpec(text: string) {
    return TyreSpec.parse(text).length > 0;
  }

  /**
   * 序列化轮胎规格
   * @param spec
   * @returns
   */
  static stringify(spec: ITyreSpec | null) {
    if (!spec) {
      return "";
    }
    const { headCode, middleCode, tailCode } = this.parseTypeCode(spec.typeCode);

    const flatness = spec.flatnessRate ? `/${spec.flatnessRate}` : "";
    return `${headCode}${spec.sectionWidth}${flatness}${middleCode}${spec.rimDiameter}${tailCode}`;
  }

  static parseTypeCode(typeCode: string) {
    let headCode = "";
    let middleCode = "";
    let tailCode = "";
    if (typeCode.includes("R") || typeCode.includes("Z")) {
      middleCode = typeCode;
    } else if (typeCode === "C") {
      tailCode = "C";
    } else if (typeCode === "LT/C") {
      headCode = "LT";
      tailCode = "C";
    } else {
      headCode = typeCode;
    }
    middleCode = middleCode || "R";
    return { headCode, middleCode, tailCode };
  }

  private text: string;
  constructor(text: string) {
    this.text = this.normalize(text);
  }

  private normalize(text: string) {
    return (
      text
        // 支持全角斜杠
        .replace(/／/g, "/")
        // R1 8 -> R18
        .replace(/R\s?(\d)\s(\d)/, "R$1$2")
        // 中文后紧接着规格的场景，固铂225/45R18 -> 固铂 225/45R18
        .replace(/([^0-9])(\d{3}\/\d{2}R\d{2})/, "$1 $2")
        // 处理类似 225/60R18100H -> 225/60R18 100H
        .replace(/(R\d{2})(\d{2,3}[A-Z])/gi, "$1 $2")
        // 支持逗号顿号分割轮胎规格
        .replace(/^(\D*)(\d{3})[、，,.。+]?\s?(\d{2})[、，,。.+]?\s?(\d{2})(\D*)$/gi, "$1$2-$3-$4$5")
    );
  }

  private reg =
    /\b(P|LT)?([12345]\d{2})\s*([—－./-]?\s*(\d{2}))?\s*([—－/.ZRFP-]+)?\s*(\d{2}\.?\d?)\s*(C|LT)?[^0-9]*\b/gi;

  private zhCNNumberReg = /[一二三四五六七八九十]{7,9}/g;

  parse(strict = false): ITyreSpec[] {
    // 超过10个连续字符，不是轮胎
    if (/^[A-Z0-9-]{10,}$/.test(this.text)) {
      return [];
    }
    if (this.text.includes("￥") || /,\d{3}\.\d{2}\b/.test(this.text)) {
      return [];
    }
    // 处理中文数字字符串
    const zhCNNumber = [...this.text.matchAll(this.zhCNNumberReg)].map((match) => this.transition(match[0])).join(" ");
    const strictReg = new RegExp("^" + this.reg.source, this.reg.flags);
    const regex = strict ? strictReg : this.reg;
    const matches = [...this.text.matchAll(regex), ...zhCNNumber.matchAll(regex)];

    return matches
      .map((match) => {
        const headCode = match[1] || "";
        const middleCode = (match[5] || "").replace(/[^ZRF]/g, "");
        const tailCode = match[7] || "";
        let typeCode = tailCode || headCode || middleCode || "R";
        typeCode = typeCode.toUpperCase();
        if (typeCode === "C" && headCode === "LT") {
          typeCode = "LT/C";
        }
        const result = {
          sectionWidth: match[2],
          flatnessRate: match[4],
          typeCode,
          rimDiameter: match[6],
        };

        if (/^0/.test(result.rimDiameter)) {
          return null;
        }
        // 简单校规格验合法性
        const sectionWith = +result.sectionWidth;
        const flatnessRate = +result.flatnessRate;
        const rimDiameter = +result.rimDiameter;
        // 轮胎宽度以6/5/0结尾
        if (!_.inRange(sectionWith, 144, 500) || /[^056]$/.test(result.sectionWidth)) {
          return null;
        }
        if (result.flatnessRate && !_.inRange(flatnessRate, 21, 150)) {
          return null;
        }
        if (!_.inRange(rimDiameter, 11, 50)) {
          return null;
        }

        const isTypeCode = /^[A-Z]+$/.test(typeCode);
        result.typeCode = isTypeCode ? typeCode : "R";
        return result;
      })
      .filter(Boolean) as ITyreSpec[];
  }

  transition(text: string) {
    const nzhcn = Nzh.cn;
    const texts: string[] = [];
    if (text.length === 7) {
      // 分割成 3 2 2
      const segment = this.segmentation(text, [3, 2, 2])
        .map((item) => String(nzhcn.decodeS(item)))
        .join("");
      texts.push(segment);
    } else if (text.length === 8) {
      // 分割成 3 2 3
      const segment1 = this.segmentation(text, [3, 2, 3])
        .map((item) => String(nzhcn.decodeS(item)))
        .join("");
      texts.push(segment1);
      // 分割成 3 2 3
      const segment2 = this.segmentation(text, [3, 3, 2])
        .map((item) => String(nzhcn.decodeS(item)))
        .join("");
      texts.push(segment2);
    } else if (text.length === 9) {
      // 分割成 3 3 3
      const segment = this.segmentation(text, [3, 3, 3])
        .map((item) => String(nzhcn.decodeS(item)))
        .join("");
      texts.push(segment);
    }
    return texts.join(" ");
  }

  segmentation(text: string, nums: number[]) {
    const texts: string[] = [];
    nums.reduce((pre, cur) => {
      texts.push(text.substring(pre, pre + cur));
      return pre + cur;
    }, 0);
    return texts;
  }
}
