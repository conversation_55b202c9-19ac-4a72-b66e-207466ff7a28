/**
 * 阿里云OSS图片处理，返回旋转后的图片URL
 * @param imageUrl
 * @param rotation
 * @returns
 */
export function rotateOssImage(imageUrl: string, rotation: number): string {
  // 1. 分割URL和query部分
  const [urlBody, queryString] = imageUrl.split("?");

  // 2. 将query部分转换为对象，"x-oss-process=image/rotate,90"
  // TODO：暂时只处理旋转图片，后续如果需要处理其他参数，需要增加逻辑处理
  const queryParams = new URLSearchParams(queryString);
  const queryObject = Object.fromEntries(queryParams.entries());
  const [, oldRotate] = (queryObject["x-oss-process"] || "").split("rotate,");
  const oldRotateNum = Number(oldRotate || 0);
  const newRotate = isNaN(oldRotateNum) ? rotation : (oldRotateNum + rotation) % 360;

  // 3. 处理query对象
  // 图片长边超过 4096 阿里云旋转会报错，所以需要通过 l_1280 将图片长边缩小到 1280
  if (newRotate) {
    queryObject["x-oss-process"] = `image/resize,l_1280/rotate,${newRotate}`;
  } else {
    queryObject["x-oss-process"] = `image/resize,l_1280`;
  }

  // 4. 将处理后的query重新拼接回URL
  const newQueryString = new URLSearchParams(queryObject).toString();

  return `${urlBody}?${newQueryString}`;
}
