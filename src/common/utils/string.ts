import { Types } from "mongoose";

/**
 * 字符串是否包含vin码
 * @param str
 * @returns
 */
export function hasVinCode(str: string): boolean {
  return /\b[A-Z0-9]{17}\b/i.test(str);
}

/**
 * mongodb数据库Id 生成器
 * @returns
 */
export function createObjectId() {
  return new Types.ObjectId().toString();
}

/**
 * 判断字符串是否都是中文、英文、数字、下划线
 * @param str
 * @returns
 */
export function isNormalChars(str: string) {
  return /^[\u4e00-\u9fa5_a-zA-Z0-9_]+$/.test(str);
}

/**
 * 判断字符串是否是手机号（完整匹配）
 * @param str
 * @returns
 */
export function isCellphone(str: string) {
  return /^[1][3456789][0-9]{9}$/.test(str);
}

type CHAR_TYPE = "CHAR_CN_EN_NUM";

/**
 * 提取特定类型的字符串
 * @param str
 * @param charType
 * @returns
 */
export function extractChars(str: string, charType: CHAR_TYPE) {
  switch (charType) {
    case "CHAR_CN_EN_NUM":
      return str.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, "");
    default:
      return str;
  }
}

/**
 * 是否包含中文
 * @param str
 * @returns
 */
export function hasChineseChar(str: string) {
  return /[\u4e00-\u9fa5]/.test(str);
}
