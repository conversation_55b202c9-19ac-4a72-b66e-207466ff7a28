import _ from "lodash";
import { TyreBrandMap } from "./TyreBrandMap";
import { TyreSpec } from "./TyreSpec";

export interface ITyreBrand {
  name: string;
  code: string;
}

/**
 * 从给定的文本中提取轮胎品牌信息。
 *
 * @param text - 包含轮胎品牌信息的文本。
 * @returns 匹配的轮胎品牌数组，按在文本中出现的顺序排序。
 */
export function extractTyreBrands(text: string): ITyreBrand[] {
  text = text.toUpperCase();
  // 纠正文本中的拼写错误
  const correctRules = [["普力斯通", "普利司通"]];
  for (const [input, replacer] of correctRules) {
    text = text.replace(input, replacer);
  }

  const items: { index: number; brand: ITyreBrand }[] = [];
  Object.entries(TyreBrandMap)
    .map(([name, code]) => {
      return [name.toUpperCase(), code.toUpperCase()];
    })
    .forEach(([name, code]) => {
      const nameIndex = text.indexOf(name);
      const codeIndex = text.indexOf(code);
      const index = Math.max(nameIndex, codeIndex);
      if (index === -1) return;
      items.push({ index, brand: { name, code } });
    });
  return _.orderBy(items, "index", "asc").map((item) => item.brand);
}

/**
 * 根据名称或代码查找轮胎品牌。
 *
 * @param nameOrCode - 轮胎品牌的名称或代码。
 * @returns 匹配的轮胎品牌对象，如果没有找到则返回 null。
 */
export function findTyreBrand(nameOrCode: string) {
  const brand = Object.entries(TyreBrandMap).find(([name, code]) => name === nameOrCode || code === nameOrCode);
  return brand ? { name: brand[0], code: brand[1] } : null;
}

/**
 * 解析轮胎规格
 * @param text
 * @returns
 */
export function parseTyreSpec(text: string) {
  return TyreSpec.parse(text).map((spec) => {
    return { treadWidth: spec.sectionWidth, ratio: spec.flatnessRate, meridian: spec.typeCode, size: spec.rimDiameter };
  });
}
