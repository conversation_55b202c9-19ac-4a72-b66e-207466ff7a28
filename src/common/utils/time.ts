import dayjs from "dayjs";

type TimeLike = string | number | Date | null | undefined;

/**
 * 判断时间是否在某个时间段内(且不等于边界值)
 * @param time
 * @param [startTime, endTime]
 * @returns
 */
export const isTimeBetween = (time: TimeLike, [startTime, endTime]: [TimeLike, TimeLike]) => {
  const currentTime = dayjs(time);
  const start = dayjs(startTime);
  const end = dayjs(endTime);
  return currentTime.isAfter(start) && currentTime.isBefore(end);
};

type TimeFormat = "YYYY-MM-DD";
export function formatTime(time: TimeLike, format: TimeFormat) {
  return dayjs(time).format(format);
}
