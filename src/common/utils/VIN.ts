/**
 * 校验车架号（VIN码）的校验位
 * @param {string} vin 车架号（17位字符）
 * @returns {boolean} 校验位是否有效
 */
export function validateVINCheckBit(vin: string = "") {
  const VIN = vin.toUpperCase();
  const illegalLength = VIN.length !== 17;
  const hasIllegalChar = /[OIQ]/.test(VIN);
  if (illegalLength || hasIllegalChar) {
    return false;
  }

  // 定义字符对应值表
  const charMap = {
    A: 1,
    B: 2,
    C: 3,
    D: 4,
    E: 5,
    F: 6,
    G: 7,
    H: 8,
    J: 1,
    K: 2,
    L: 3,
    M: 4,
    N: 5,
    P: 7,
    R: 9,
    S: 2,
    T: 3,
    U: 4,
    V: 5,
    W: 6,
    X: 7,
    Y: 8,
    Z: 9,
  };
  // 定义加权值表（第1位到第17位的加权值）
  const weights = [8, 7, 6, 5, 4, 3, 2, 10, 0, 9, 8, 7, 6, 5, 4, 3, 2];

  let sum = 0;

  for (let i = 0; i < VIN.length; i++) {
    const char = VIN[i].toUpperCase() as keyof typeof charMap; // 转换为大写
    let value = 0;

    // 如果是数字，直接使用数字值
    if (/[0-9]/.test(char)) {
      value = parseInt(char, 10);
    } else if (char in charMap) {
      value = charMap[char];
    }
    // 计算加权和
    sum += value * weights[i];
  }

  // 计算校验位
  const remainder = sum % 11;
  const checkBit = remainder === 10 ? "X" : remainder.toString();

  // 获取VIN码中的校验位（第9位）
  const vinCheckBit = VIN[8];

  // 校验位是否匹配
  if (checkBit === vinCheckBit) {
    return true;
  }

  return false;
}
