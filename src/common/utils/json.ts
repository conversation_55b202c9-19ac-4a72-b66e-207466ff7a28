import { parseJsonMarkdown as _parseJsonMarkdown } from "@langchain/core/output_parsers";
import { jsonrepair } from "jsonrepair";

/**
 * 从markdown中解析出json，暂时使用`langchain`的实现
 */
export const parseJsonMarkdown = _parseJsonMarkdown;

/**
 * 解析不完整的json字符串
 */
export const parseUnCompletedJsonMarkdown = <T = unknown>(text: string): null | T => {
  text = text.trim();
  if (!text) {
    return null;
  }
  try {
    const repairText = jsonrepair(text);
    return JSON.parse(repairText);
  } catch {
    return null;
  }
};
