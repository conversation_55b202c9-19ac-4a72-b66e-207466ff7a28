import { AgentName } from "@/common/enums";

import { IAction, ICommandAction, INLU } from "@casstime/copilot-core";

export class ActionFactory {
  // 生成 command 按钮
  static command(text: string, command: string, others?: Partial<ICommandAction>): IAction {
    return {
      type: "command",
      text,
      command,
      theme: "secondary", // 默认值
      ...others,
    };
  }

  // 生成 nlu 按钮
  static nlu(text: string, nlu: INLU & { agentName: AgentName }, theme: IAction["theme"] = "secondary"): IAction {
    // nlu 按钮都需要带上 agentName, 默认为 inquiryAgent
    if (!nlu.agentName) {
      nlu.agentName = AgentName.inquiryAgent;
    }
    return {
      type: "nlu",
      text,
      nlu,
      theme,
    };
  }
}
