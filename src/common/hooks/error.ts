import { FastifyError, FastifyReply, FastifyRequest } from "fastify";
import { ResponseFactory } from "./wrapper";
import { ErrorCode } from "../enums";
import { HttpError } from "../error";
import logger from "../logger";

/**
 * @deprecated 请使用 HttpError `import { HttpError } from "@/common/error";`
 * 自定义业务异常
 */
export const CustomError = HttpError;

export const errorHandler = (error: FastifyError, request: FastifyRequest, reply: FastifyReply) => {
  logger.warn("接口异常：", error.message, error.stack);
  // 参数校验异常
  if (error.validation) {
    reply.status(200).send(
      ResponseFactory.error({
        errorCode: ErrorCode.VALIDATION_ERROR,
        data: error.validation?.[0],
        message: "参数校验异常",
      })
    );
    return;
  }
  // 业务异常
  if (HttpError.isHttpError(error)) {
    reply.status(error.httpCode).send(
      ResponseFactory.error({
        errorCode: error.errorCode,
        message: error.message,
        data: error.data,
      })
    );
    return;
  }
  // 未捕获异常
  reply.status(500).send(
    ResponseFactory.error({
      errorCode: ErrorCode.SERVER_ERROR,
      message: error.message,
    })
  );
};
