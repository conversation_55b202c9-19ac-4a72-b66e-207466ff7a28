import { FastifyInstance, FastifyRequest } from "fastify";
import { error<PERSON>and<PERSON> } from "./error";
import { wrapper<PERSON>and<PERSON> } from "./wrapper";
import { initializeRequestMeta } from "../asyncStore";
import { isDev, parseUserAgent, Version } from "../utils";
import { readFileSync } from "node:fs";
import { VersionEnum } from "../enums";

function loadDebugData(request: FastifyRequest) {
  if (process.argv.join("").endsWith(".ts")) {
    const data = JSON.parse(readFileSync("./data/debug.json", "utf-8"));
    return data;
  }
  return request;
}

export const bindHooks = (server: FastifyInstance) => {
  server.addHook("onRequest", (request, reply, done) => {
    if (isDev()) {
      const debugData = loadDebugData(request);
      request.headers = debugData.headers;
    }
    const headers = request.headers;
    const { appVersion, platform } = parseUserAgent(headers["user-agent"] || "");
    initializeRequestMeta({ headers, appVersion: new Version(appVersion || VersionEnum.MAX_VERSION), platform }, done);
  });
  server.addHook("preHandler", async (request) => {
    if (isDev() && request.routerPath.includes("/hook")) {
      const debugData = loadDebugData(request);
      Object.assign(request.body as object, {
        companyId: debugData.companyId,
      });
      Object.assign((request.body as { data: object }).data, {
        fromUser: debugData.userLoginId,
      });
    }
  });
  server.addHook("preSerialization", wrapperHandler);

  server.setErrorHandler(errorHandler);
};
