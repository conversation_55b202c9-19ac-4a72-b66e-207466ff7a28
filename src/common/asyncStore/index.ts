import { AsyncLocalStorage } from "node:async_hooks";
import { Version } from "../utils";

export type RequestMeta = {
  headers: Record<string, unknown>;
  platform?: string;
  appVersion?: Version;
};

const asyncLocalStorage = new AsyncLocalStorage<RequestMeta>();

export const useRequestMeta = () => {
  return asyncLocalStorage.getStore() || { headers: {} };
};

export const initializeRequestMeta = (context: RequestMeta, next: () => void) => {
  return asyncLocalStorage.run(context, next);
};
