import { useRequestMeta } from "../asyncStore";
import httpClient from "./http.client";

interface Options {
  uri: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  json?: unknown;
  headers?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type DEFAULT = any;
/**
 * 适配 TerminalApi V2 的 HttpClient。默认返回出参，出错抛异常
 */
export class HttpClientForV2 {
  private async request<T = DEFAULT>(options: Options): Promise<T> {
    // terminal-api 相关接口自动注入鉴权信息
    if (options.uri.includes("/terminal-api-v2")) {
      const meta = useRequestMeta();
      options.headers = {
        ...options.headers,
        ...meta.headers,
      };
    }
    const { err, result } = await httpClient.request<T, unknown>(options.uri, options);
    if (err) {
      throw err;
    }
    return result as T;
  }

  get<T = DEFAULT>(path: string, params?: Partial<Options>): Promise<T> {
    return this.request<T>({
      uri: path,
      method: "GET",
      ...params,
    });
  }

  post<T = DEFAULT>(path: string, params?: Partial<Options>): Promise<T> {
    return this.request<T>({
      uri: path,
      method: "POST",
      ...params,
    });
  }

  put<T = DEFAULT>(path: string, params?: Partial<Options>): Promise<T> {
    return this.request<T>({
      uri: path,
      method: "PUT",
      ...params,
    });
  }

  delete<T = DEFAULT>(path: string, params?: Partial<Options>): Promise<T> {
    return this.request<T>({
      uri: path,
      method: "DELETE",
      ...params,
    });
  }

  patch<T = DEFAULT>(path: string, params?: Partial<Options>): Promise<T> {
    return this.request<T>({
      uri: path,
      method: "PATCH",
      ...params,
    });
  }
}
