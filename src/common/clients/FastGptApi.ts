import { fetchEventSource } from "@/common/infra/fetch-event-source";
import logger from "@/common/logger";

interface IFastGptRequest {
  query: string;
  inputs: object;
  platform: "fastgptChat";
  conversationId?: string;
  user: string;
  imgUrl?: string;
  extra?: {
    buyerUserLoginId?: string;
    sellerUserLoginId?: string;
    storeId?: string;
    inquiryId?: string;
    orderId?: string;
  };
}

type MessageEvent = {
  event: "message";
  answer: string;
  /** 单位是s */
  created_at: number;
};

type AgentMessageEvent = {
  event: "flowNodeStatus";
  answer: string;
};

type MessageEndEvent = {
  event: "message_end";
};

export interface IFastGptFlowNode {
  status: string;
  name: string;
}

//  建议获取
export interface IFastGptSuggestReq {
  shareId: string;
  outLinkUid: string;
  messages: IFastGptSuggestMessage[];
}

export interface IFastGptSuggestMessage {
  role: string;
  content: string;
}

export type ResponseEvent = MessageEvent | AgentMessageEvent | MessageEndEvent;

interface FastGptApiOptions {
  baseUrl: string;
  apiKey: string;
}

type Data<T = never> = Promise<{
  result: "success";
  data?: T;
}>;

class FastGptApi {
  private apiKey: string;
  private baseUrl: string;
  constructor(options: FastGptApiOptions) {
    this.apiKey = options.apiKey;
    this.baseUrl = options.baseUrl;
  }

  get headers() {
    return {
      "Content-Type": "application/json",
      Authorization: `Bearer ${this.apiKey}`,
    };
  }
  async sendChatMessage(request: IFastGptRequest, onEvent: (data: ResponseEvent) => void) {
    logger.info("sendChatMessage", request);
    return new Promise((resolve, reject) => {
      fetchEventSource(`${this.baseUrl}/chat`, {
        method: "POST",
        headers: {
          ...this.headers,
        },
        body: JSON.stringify(request),
        openWhenHidden: true,
        onmessage(ev) {
          logger.info("onmessage", ev.data);
          try {
            onEvent(JSON.parse(ev.data));
          } catch (err) {
            logger.error("onmessage error", err, ev);
          }
        },
        onerror(err) {
          console.error("EventSource error:", err);
          reject(err);
        },
        onclose() {
          console.log("EventSource closed");
        },
      })
        .then(resolve)
        .catch(reject);
    });
  }
}

export default FastGptApi;
