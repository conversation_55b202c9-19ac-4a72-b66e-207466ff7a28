import { HttpClient, HttpClientRes } from "@casstime/node-http-client";
import { get } from "@casstime/node-http-cls";
import logger from "@/common/logger";
import localConfig from "config";
import { isDev } from "../utils/env";
import { utilsClient } from "@/copilot/apps/GarageAssistant/agents/inquiry/clients/UtilsClient";
import type { RequestPromiseOptions } from "request-promise";
import { config, apolloConfig } from "@casstime/apollo-config";

const client = new HttpClient({
  baseUrl: localConfig.get("API_INTRA_BASE_URL"),
  defaultTimeout: 20 * 1000, // copilot请求接口默认超时时间
  defaultHeadersProvider(url) {
    if (/^https?:/.test(url)) {
      return {};
    }
    return get("PASS_THROUGH_HEADERS") || {};
  },

  // 打印外部请求的 accessLog
  onPostResponse(options, httpRes) {
    const totalTime = (httpRes.timingPhases && httpRes.timingPhases.total) as number;

    logger.info(
      {
        type: "access",
        method: options.method,
        url: options.uri,
        ...httpRes.timingPhases,
        latency: Math.round(totalTime),
        statusCode: httpRes.statusCode,
      },
      "%s %s %dms %d",
      options.method,
      options.uri,
      totalTime,
      httpRes.statusCode
    );
    // 调试环境不打日志，以免终端被大量的请求日志淹没
    if (!isDev()) {
      logger.info("request body %o , response body %o", options.json, httpRes.result);
    }
  },
});

const originRequest = client.request;

const serviceClients = {
  terminalApiV2: {
    startsUrl: "/terminal-api-v2",
    baseUrl: localConfig.get("V2_BASE_URL"),
  },
};
apolloConfig().then(() => {
  // 从 Apollo 配置中获取 V2_BASE_URL
  // serviceClients.terminalApiV2.baseUrl = config.get("V2_BASE_URL") || localConfig.get("V2_BASE_URL");
  serviceClients.terminalApiV2.baseUrl = "http://hwbeta-api-kunlun.intra.casstime.com/terminal-api-v2-inner-service"
});

client.request = async <T, E>(uri: string, options: RequestPromiseOptions) => {
  options = options || {};
  if (uri.startsWith(serviceClients.terminalApiV2.startsUrl)) {
    const uriPath = uri.replace(serviceClients.terminalApiV2.startsUrl, "");
    uri = `${serviceClients.terminalApiV2.baseUrl}${uriPath}`;

    options.headers = {
      "User-Agent": "copilot-server",
      ...options.headers,
    };
  }
  const res = await originRequest.apply(client, [uri, options]);
  if (uri.includes("terminal-api-v2")) {
    utilsClient.checkLogin(res.result);
  }
  return res as HttpClientRes<T, E>;
};

export default client;
