import config from '@casstime/config';
import { Eureka } from '@casstime/eureka';

export const getEurekaClient = () => {
  const serviceUrls = config
    .get<string>('eureka.client.serviceUrl.defaultZone')
    .split(',')
    .map(uri => `${uri}apps`);

  const eurekaClient = new Eureka({
    serviceUrls,
    appId: config.get('EUREKA_INSTANCE_ID'),
    port: config.get('PORT'),
    registerWithEureka: config.util.getEnv('NODE_ENV') !== 'development',
    ipAddr: process.env.POD_IP,
  });
  return eurekaClient;
};
