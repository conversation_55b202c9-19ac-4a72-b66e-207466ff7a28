import { FastifyInstance } from "fastify";
import { bindCopilot } from "../copilot";
import { handler as messages<PERSON>and<PERSON>, opts as messageOpts } from "./messages";
import { handler as message<PERSON>ist<PERSON>and<PERSON>, opts as messageListOpts } from "./messageList";
import { handler as getMessagesAmountHand<PERSON>, opts as getMessagesAmountOpts } from "./getMessagesAmount";
import { handler as getAppNames } from "./getAppNames";
import { handler as updateMessage<PERSON>and<PERSON>, opts as updateMessageOpts } from "./updateMessage";
import { handler as createConfigureReplyHandler, opts as createConfigureReplyOpts } from "./createConfigureReply";
import { handler as getConfigureReplysHandler, opts as getConfigureReplysOpts } from "./getConfigureReplys";
import { handler as updateConfigureReplys<PERSON>and<PERSON>, opts as updateConfigureReplysOpts } from "./updateConfigureReply";
import { handler as getConfigureIntentsHand<PERSON> } from "./getConfigureIntents";
import { handler as getInquiryId<PERSON>ist<PERSON>and<PERSON>, opts as getInquiryIdListOpts } from "./getInquiryIdList";
import { handler as getAsyncCommandHandler, opts as getAsyncCommandOpts } from "./getAsyncCommand";
import { handler as updateRecommendPlanHandler, opts as updateRecommendPlanOpts } from "./updateRecommendPlan";
import { handler as updateIntelligentPlanHandler, opts as updateIntelligentPlanOpts } from "./updateIntelligentPlan";
import { handler as updatePurchasePlanHandler, opts as updatePurchasePlanOpts } from "./updatePurchasePlan";
import { handler as createDialogueHandler, opts as createDialogueOpts } from "./createDialogue";
import { handler as makePlanHandler, opts as makePlanOpts } from "./makePlan";
import { handler as quoteTriggerHandler, opts as quoteTriggerOpts } from "./quoteTrigger";
import { handler as getQuoteRecommendPlanHandler, opts as getQuoteRecommendPlanOpts } from "./quoteRecommendPlan";
import { handler as getMessageReporterHandler, opts as getMessageReporterOpts } from "./getMessageReporter";
import { handler as getRepairScenarios, opts as getRepairScenariosOpts } from "./getRepairScenarios";
import { handler as createPlansHandler, opts as createPlansHandlerOpts } from "./createPlans";
import { handler as getPlansDetailHandler, opts as getPlansDetailOpts } from "./getPlansDetail";

import healthHandler from "./health";
import { qaRoutes } from "./qa";

export const bindRoutes = (server: FastifyInstance) => {
  bindCopilot("/copilot/hook", server);

  server.register(qaRoutes, { prefix: "/copilot/qa" });

  server.get("/health", healthHandler);
  // 根据 createAt 拉取历史消息
  server.get("/copilot/messages", messageOpts, messagesHandler);
  // 根据 _id 拉取历史消息
  server.get("/copilot/messageList", messageListOpts, messageListHandler);
  server.post("/copilot/message", updateMessageOpts, updateMessageHandler);
  // 统计owner下的消息amount
  server.post("/copilot/messages/amount", getMessagesAmountOpts, getMessagesAmountHandler);
  // 拉取48小时内采购助手的询价单id
  server.post("/copilot/inquiryids", getInquiryIdListOpts, getInquiryIdListHandler);
  // 获取所有appname
  server.get("/copilot/appnames", {}, getAppNames);
  // 获取 dialogueId
  server.post("/copilot/dialogues", createDialogueOpts, createDialogueHandler);

  // 获取需要执行的commands数组
  server.get("/copilot/async_command/:id", getAsyncCommandOpts, getAsyncCommandHandler);

  // 更新推荐方案数据
  server.post("/copilot/recommend_plan", updateRecommendPlanOpts, updateRecommendPlanHandler);
  server.post("/copilot/intelligent_plan", updateIntelligentPlanOpts, updateIntelligentPlanHandler);
  server.post("/copilot/purchase_plan", updatePurchasePlanOpts, updatePurchasePlanHandler);
  server.post("/copilot/make_plan", makePlanOpts, makePlanHandler);
  // 报价更新触发制作方案
  server.post("/copilot/quote_trigger", quoteTriggerOpts, quoteTriggerHandler);
  server.get("/copilot/quote_recommend_plans", getQuoteRecommendPlanOpts, getQuoteRecommendPlanHandler);

  // 配置消息的增改查
  server.post("/copilot/configure_reply", createConfigureReplyOpts, createConfigureReplyHandler);
  server.get("/copilot/configure_replys", getConfigureReplysOpts, getConfigureReplysHandler);
  server.put("/copilot/configure_reply", updateConfigureReplysOpts, updateConfigureReplysHandler);
  server.get("/copilot/configure_reply/intents", {}, getConfigureIntentsHandler);

  // 查询reporter消息上报
  server.get("/copilot/message_reporter", getMessageReporterOpts, getMessageReporterHandler);

  // 查事故场景
  server.post("/copilot/repair_scenarios", getRepairScenariosOpts, getRepairScenarios);

  // 制作推荐方案
  server.post("/copilot/plans", createPlansHandlerOpts, createPlansHandler);
  // 查询方案详情
  server.get("/copilot/plans/detail/:id", getPlansDetailOpts, getPlansDetailHandler);
};
