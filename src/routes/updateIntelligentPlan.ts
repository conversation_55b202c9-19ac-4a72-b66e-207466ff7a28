import { HttpError } from "@/common/error";
import { IIntelligentPlanTaskParams, TaskType } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { TaskPollingStatus } from "@/models";
import logger from "@/common/logger";
import { FastifyRequest } from "fastify";
import assert from "assert";
import { isError } from "@/common/utils";
import { purchasePlanService } from "@/service";

export interface IParamsSchema {
  Body: {
    inquiryId: string; //询价单号
    recommendContentsGroupId: string; // 方案组id
    turns: number; //当前方案轮次
    finished: boolean; //暂无报价，提前结束任务
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["inquiryId"],
      properties: {
        inquiryId: { type: "string" },
        recommendContentsGroupId: { type: "string" },
        turns: { type: "number" },
        finished: { type: "boolean" },
      },
    },
  },
};

/** 更新推荐方案数据 */
export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<object> => {
  const { inquiryId, turns: newTurns, recommendContentsGroupId, finished } = request.body;
  try {
    const item = await TaskPollingStatus.findOne({
      taskId: inquiryId,
      taskType: TaskType.INTELLIGENT_PLAN,
    });
    if (!item) {
      throw new HttpError({ message: `${JSON.stringify(request.body)}: 没有该轮询任务` });
    }
    const params = { ...(item?.params || {}) } as IIntelligentPlanTaskParams;
    if (finished) {
      // 方案无报价，提前结束
      params.finished = true;
    } else {
      const { turns = 0 } = params;
      /** 轮次没有变化，报错 */
      if (turns === newTurns) {
        throw new HttpError({ message: `${JSON.stringify(request.body)}: 方案轮次没有变化` });
      }
      params.turns = newTurns;
      params.recommendContentsGroupId = recommendContentsGroupId;
    }

    await purchasePlanService.saveLlmOrMlPlan(item, params);
    const updateRes = await TaskPollingStatus.updateOne(
      { taskId: inquiryId, taskType: TaskType.INTELLIGENT_PLAN },
      {
        $set: { params },
      }
    );
    logger.info(
      `${item._id}: 更新结果:${JSON.stringify(updateRes)}, 回调入参: ${JSON.stringify(
        request.body
      )}, 更新推荐方案数据:${JSON.stringify(params)}}`
    );
    return { isSuccess: true };
  } catch (e) {
    assert(isError(e), "e is not Error");
    throw new HttpError({ message: e.message });
  }
};
