import { FastifyRequest } from "fastify";
import assert from "assert";
import { isError } from "@/common/utils";
import { HttpError } from "@/common/error";
import { purchasePlanService } from "@/service";

export interface IParamsSchema {
  Querystring: {
    inquiryId: string;
  };
}

export const opts = {
  schema: {
    querystring: {
      type: "object",
      required: ["inquiryId"],
      properties: {
        inquiryId: { type: "string" },
      },
    },
  },
};

/** 查询提交方案 */
export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<object> => {
  const { inquiryId } = request.query;
  try {
    return await purchasePlanService.getQuoteRecommendPlans(inquiryId);
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new HttpError({ message: e.message });
  }
};
