import { FastifyReply, FastifyRequest } from "fastify";
import { CopilotMessage } from "@/models";
import { CustomError } from "@/common/hooks/error";
import { IFormMessage } from "@casstime/copilot-core";
import assert from "assert";
import { isError } from "@/common/utils";

export interface IParamsSchema {
  Body: {
    userId: string;
    app: string;
    companyId: string;
    hours: number;
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["userId", "app", "companyId"],
      properties: {
        userId: { type: "string" },
        app: { type: "string" },
        companyId: { type: "string" },
        // 默认限制查询时间48小时内
        hours: { type: "number", default: 48 },
      },
    },
  },
};

/** 获取48小时询价单id */
export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<object> => {
  const { userId, app, companyId, hours } = request.body;
  try {
    const timestamp = new Date().getTime() - hours * 60 * 60 * 1000;
    const formMessages = await CopilotMessage.find({
      type: { $nin: ["command", "echo", "system"] as any },
      createdAt: { $gt: timestamp },
      owner: `${app}:${userId}:${companyId}`,
    })
      .lean()
      .exec();

    const inquiryIds: string[] = [];
    // 过滤得到inquiryId和demandId
    formMessages.map((formMsg) => {
      const { extra = {}, type } = formMsg;
      if (type === "form") {
        // 表单消息
        const { inquiryId, demandId } = (formMsg as IFormMessage)?.formData || {};
        if (inquiryId) {
          inquiryIds.push(inquiryId);
        }
        if (demandId) {
          inquiryIds.push(demandId);
        }
      } else {
        // 其他消息
        if (extra?.inquiryId) {
          inquiryIds.push(extra?.inquiryId);
        } else if (extra?.demandId) {
          inquiryIds.push(extra?.demandId);
        }
      }
    });
    // 使用new Set进行去重
    return { inquiryIds: [...new Set(inquiryIds)] };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new CustomError({ message: e.message });
  }
};
