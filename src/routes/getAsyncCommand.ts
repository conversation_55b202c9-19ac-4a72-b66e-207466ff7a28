import { FastifyRequest } from "fastify";
import { Types } from "mongoose";
import { HttpError } from "@/common/error";
import { AsyncCommand, IAsyncCommand } from "@/models/AsyncCommand";
import assert from "assert";
import { isError } from "@/common/utils";

export interface IParamsSchema {
  Params: {
    id: string;
  };
}

export const opts = {
  schema: {
    params: {
      type: "object",
      properties: {
        id: { type: "string" },
      },
    },
  },
};

/** 查询执行commands消息 */
export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<IAsyncCommand> => {
  try {
    const { id } = request.params;
    const _id = Types.ObjectId(id);
    const result = await AsyncCommand.findOne({ _id }).lean().exec();
    const commands = (result?.commands || []).map((item) => ({ ...item, type: "command", text: "" }));
    return { commands };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new HttpError({ message: e.message });
  }
};
