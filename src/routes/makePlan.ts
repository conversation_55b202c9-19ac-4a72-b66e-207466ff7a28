import logger from "@/common/logger";
import { FastifyRequest } from "fastify";
import { Types } from "mongoose";
import { PlanRecommender } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender";
import {
  IPurchasePlanItem,
  MarkerType,
} from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/interfaces/plan";
import { createDataProvider } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/factory";
import { inquiryClient } from "@/clients/inquiry";
import { handleRecommendResult } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/handlers";
import { MessageReporter } from "@/messages";

export interface IParamsSchema {
  Body: {
    inquiryId: string; //询价单号
    taskItemId: string; //任务项id
    type: string; //类型
  };
}

export interface IMakePlanResponseDTO {
  taskItemId: string;
  scene: string;
  contentForJson: string;
}
interface IContentDTO {
  demandId: string;
  scenario: string;
  recommendProgrammes: IPurchasePlanItem[];
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["inquiryId", "taskItemId"],
      properties: {
        inquiryId: { type: "string" },
        taskItemId: { type: "string" },
        type: { type: "string", default: MarkerType.ENUMMAKER }, // 默认枚举算法
      },
    },
  },
};

/** 制作采购方案 */
export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<{ isSuccess: true }> => {
  makePlanRecommend(request.body);
  return { isSuccess: true };
};

async function makePlanRecommend(body: { inquiryId: string; taskItemId: string; type: string }) {
  const { inquiryId, taskItemId, type } = body;
  const makerType = type as MarkerType;
  const id = new Types.ObjectId().toString();
  const messageReporter = new MessageReporter(id);
  try {
    const planRecommender = await PlanRecommender.create({
      reporter: messageReporter,
      inquiryId,
      makerType,
      dataProvider: createDataProvider(inquiryId),
    });
    // 推荐方案
    const recommendPromises = [
      planRecommender.recommend({
        input: "优先考虑品质、品牌，其次考虑时效、价格，给我推荐最优方案",
      }),
      planRecommender.recommend({
        input: "要原厂的，品质最优的",
      }),
      planRecommender.recommend({
        input: "要便宜的，价格最优的",
      }),
    ];
    const recmmendResults = await Promise.all(recommendPromises);
    const { recommendPurchasePlans, quoteMatches, planWeight } = handleRecommendResult(recmmendResults);
    const recommendPurchaseReasonPlans = await planRecommender.planReasonGenerator.generate({
      input: "优先考虑品质、品牌，其次考虑时效、价格，给我推荐最优方案",
      recommendPurchasePlans,
      quoteMatches,
      planWeight,
    });
    const content: IContentDTO = {
      demandId: inquiryId,
      scenario: "GARAGE_ASSISTANT_SENTINEL",
      recommendProgrammes: recommendPurchaseReasonPlans,
    };
    const plan = {
      taskItemId,
      scene: "GARAGE_ASSISTANT_SENTINEL_PROGRAMMES",
      contentForJson: JSON.stringify(content),
    };
    const result = await inquiryClient.makePlanCallback(plan);
    logger.info("makePlan succeed", body, plan, result);
  } catch (e) {
    logger.error("makePlan error", e);
  }
}
