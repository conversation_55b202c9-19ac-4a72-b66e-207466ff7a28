import { FastifyRequest } from "fastify";
import { HttpError } from "@/common/error";
import { Dialogue, IDialogue } from "@/models";
import { AppMap } from "@/common/enums";
import assert from "assert";
import { isError } from "@/common/utils";

export interface IBodySchema {
  Body: {
    app: string;
    displayName: string;
    businessId: string;
    userId: string;
    companyId: string;
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["app", "userId", "companyId"],
      properties: {
        app: { type: "string" },
        displayName: { type: "string" },
        userId: { type: "string" },
        companyId: { type: "string" },
        businessId: { type: "string" },
      },
    },
  },
};

export const handler = async (request: FastifyRequest<IBodySchema>): Promise<IDialogue> => {
  try {
    const { app, userId, companyId, displayName = AppMap[app as keyof typeof AppMap] } = request.body;
    const businessId = request.body?.businessId || "DEFAULT";
    const result = await Dialogue.findOneAndUpdate(
      { app, userId, companyId, businessId },
      {
        $set: {
          displayName,
        },
      },
      { upsert: true, new: true }
    ).exec();
    if (!result) {
      throw new HttpError({
        message: "Failed to create or update dialogue",
      });
    }
    return result;
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new HttpError({ message: e.message });
  }
};
