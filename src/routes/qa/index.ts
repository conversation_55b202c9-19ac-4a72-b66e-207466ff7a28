import { FastifyInstance } from "fastify";
import { handler as getQa<PERSON><PERSON><PERSON><PERSON><PERSON>, opts as getQaListOpts } from "./getQaList";
import { handler as createHandler, opts as createOpts } from "./createQA";
import { handler as updateHandler, opts as updateOpts } from "./updateQA";
import { handler as updateStatusHandler, opts as updateStatusOpts } from "./updateQAStatus";
import { handler as returnImAnswerHandler, opts as returnImAnswer } from "./returnImAnswer";
import { handler as supplierHandler, opts as supplierOpts } from "./returnImFastGptAnswer";

export const qaRoutes = async (server: FastifyInstance) => {
  server.get("/list", getQaListOpts, getQaListHandler);
  // 创建qa
  server.post("", createOpts, createHandler);
  // 修改qa
  server.put("", updateOpts, updateHandler);
  // 启用、禁用
  server.post("/status", updateStatusOpts, updateStatusHandler);
  // IM提效 返回IM QA 答案
  server.post("/im", returnImAnswer, returnImAnswerHandler);
  // IM，供应商ai推荐回复
  server.post("/im/supplier", supplierOpts, supplierHandler);
};
