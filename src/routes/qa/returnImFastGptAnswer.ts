import { FastifyRequest } from "fastify";
import config from "@casstime/config";
import { HttpClientForV2 } from "@/common/clients/HttpClientForV2";
import { HttpError } from "@/common/error";
import { ErrorCode, TeamCode } from "@/common/enums";
import { INewFastGptParamsSchema, IFastGptRequestData, IFastGptResponseData } from "@/interfaces/newFastGpt";
import logger from "@/common/logger";

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["messages", "user"],
      properties: {
        messages: {
          type: "array",
          items: {
            type: "object",
          },
        },
        user: { type: "string" },
        conversationId: { type: "string" },
        extra: {
          type: "object",
          properties: {
            buyerUserLoginId: { type: "string" },
            sellerUserLoginId: { type: "string" },
            storeId: { type: "string" },
            orderId: { type: "string" },
            inquiryId: { type: "string" },
          },
        },
      },
    },
  },
};

const httpClient = new HttpClientForV2();
const fastGptApiHeaders = {
  "Content-Type": "application/json",
  Authorization: `Bearer ${config.get("FASTGPT_SUPPLIER_KEY")}`,
};

/**
 * 处理FastGPT请求的处理函数
 * @param request - Fastify请求对象
 * @returns 返回处理后的FastGPT响应内容
 *
 * @remarks
 * 入参类型: {@link IFastGptRequestData}
 * 出参类型: {@link IFastGptResponseData}
 */
export const handler = async (request: FastifyRequest<INewFastGptParamsSchema>) => {
  const { messages, user, conversationId, extra = {} } = request.body;
  try {
    // 构建FastGPT请求数据
    const requestData: IFastGptRequestData = {
      messages,
      variables: {
        user,
        extra,
      },
      chatId: conversationId,
      responseChatItemId: conversationId,
      shareId: config.get("NEW_FASTGPT_SHARE_ID"),
      retainDatasetCite: false,
      detail: false,
      stream: false,
    };
    // 发送请求到FastGPT API
    const res = await httpClient.post<IFastGptResponseData>(
      `${config.get("NEW_FASTGPT_BASE_URL")}/api/v2/chat/completions`,
      {
        headers: fastGptApiHeaders,
        json: requestData,
      }
    );
    if (res?.choices?.length) {
      return {
        content: res.choices[0].message?.content,
      };
    } else {
      return {
        content: "",
      };
    }
  } catch (e) {
    logger.error(`returnImFastGptAnswer error: ${e}`);
    throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ImTeam });
  }
};
