import { HttpError } from "@/common/error";
import { qaService } from "@/copilot/apps/GarageAssistant/agents/inquiry/services/QAService";
import { generateImQAText } from "@/copilot/apps/GarageAssistant/generator";
import assert from "assert";
import { FastifyRequest } from "fastify";
import { isError } from "@/common/utils";

export interface IParamsSchema {
  Body: {
    input: string;
    history: string[];
    scoreThreshold: number;
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["input", "history", "scoreThreshold"],
      properties: {
        input: { type: "string" },
        history: { type: "array", items: { type: "string" } },
        scoreThreshold: { type: "number" },
      },
    },
  },
};

const markdownToText = (text: string) => {
  return (
    text
      // 去除代码块
      .replace(/```[\s\S]*?```/g, "")
      // 去除行内代码
      .replace(/`([^`]+)`/g, "$1")
      // 去除图片
      .replace(/!\[.*?\]\(.*?\)/g, "")
      // 去除链接，只保留文本
      .replace(/\[([^\]]+)\]\([^)]+\)/g, "$1")
      // 去除标题
      .replace(/^#{1,6}\s+/gm, "")
      // 去除加粗
      .replace(/(\*\*|__)(.*?)\1/g, "$2")
      // 去除斜体
      .replace(/(\*|_)(.*?)\1/g, "$2")
      // 去除无序列表
      .replace(/^\s*[-*+]\s+/gm, "")
      // 去除引用
      .replace(/^\s*>+\s?/gm, "")
      // 去除分割线
      .replace(/^(-{3,}|\*{3,}|_{3,})$/gm, "")
      .trim()
  );
};

export const handler = async (request: FastifyRequest<IParamsSchema>) => {
  try {
    const { input, history = [] } = request.body;
    let relatedQA = [];
    if (input.includes("\n")) {
      // 通过\n分割 input
      const inputArr = input.split("\n");
      const qaTask = inputArr.map((item) => qaService.searchRelatedQA(item || ""));
      let qaResult = await Promise.all(qaTask);
      if (qaResult?.length > 3) {
        qaResult = qaResult.map((item) => item.slice(0, 1));
      } else {
        qaResult = qaResult.map((item) => item.slice(0, 2));
      }
      relatedQA = qaResult.flat();
    } else {
      relatedQA = await qaService.searchRelatedQA(input || "");
      relatedQA = relatedQA.slice(0, 3);
    }
    const filteredQA = relatedQA.filter((item) => item.score >= request.body.scoreThreshold);
    if (filteredQA?.length === 0) {
      return { content: "" };
    }
    const historyStr = history.length > 0 ? history.join("\n") : "";
    const answer = await generateImQAText(historyStr, input, filteredQA);
    if (answer && answer.content) {
      return { content: markdownToText(answer.content.toString()) };
    }
    return { content: "" };
  } catch (e) {
    assert(isError(e), "e is not Error");
    throw new HttpError({ message: e.message });
  }
};
