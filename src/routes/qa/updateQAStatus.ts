import { qdrantClient } from "@/clients/qdrant";
import { HttpError } from "@/common/error";
import { embedQuery } from "@/clients/llm";
import { QAAnswer, QAQuestion } from "@/models";
import assert from "assert";
import { FastifyRequest } from "fastify";
import { Types } from "mongoose";

export interface IParamsSchema {
  Body: {
    answerId: string;
    enabled: boolean;
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["answerId", "enabled"],
      properties: {
        answerId: { type: "string" },
        enabled: { type: "boolean" },
      },
    },
  },
};

/** 更新QA状态:
 * 启用:创建向量
 * 禁用:删除向量
 *  */
export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<object> => {
  const { answerId = "67ce7e92ff6041775b4b156e", enabled = true } = request.body;
  try {
    const answer = await QAAnswer.findById(answerId);
    if (!answer) {
      throw new HttpError({ message: "数据不存在" });
    }
    if (enabled) {
      const embedAnswer = await embedQuery(answer.answer);
      await qdrantClient.upsert({
        collectionName: "answer",
        id: answer.vectorId,
        vector: embedAnswer,
        mongoId: answer._id.toString(),
      });
      answer.enabled = enabled;
      await answer.save();
      const questions = await QAQuestion.find({ answerId: Types.ObjectId(answerId) });
      await Promise.all(
        questions.map(async (item) => {
          const embedQuestion = await embedQuery(item.question);
          await qdrantClient.upsert({
            collectionName: "question",
            id: item.vectorId,
            vector: embedQuestion,
            mongoId: item._id.toString(),
          });
        })
      );
    } else {
      await qdrantClient.delete({
        collectionName: "answer",
        ids: [answer.vectorId],
      });
      answer.enabled = enabled;
      await answer.save();
      const questions = await QAQuestion.find({ answerId: Types.ObjectId(answerId) });
      await qdrantClient.delete({
        collectionName: "question",
        ids: questions.map((item) => item.vectorId),
      });
    }

    return { isSuccess: true };
  } catch (e) {
    assert(e instanceof Error);
    throw new HttpError({ message: e.message });
  }
};
