import { qdrantClient } from "@/clients/qdrant";
import { HttpError } from "@/common/error";
import { embedQuery } from "@/clients/llm";
import { QAAnswer, QAQuestion } from "@/models";
import assert from "assert";
import { FastifyRequest } from "fastify";
import { v4 } from "uuid";

export interface IParamsSchema {
  Body: {
    id: string;
    answer: string;
    questions: {
      id: string;
      question: string;
    }[];
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["id", "answer", "questions"],
      properties: {
        id: { type: "string" },
        answer: { type: "string" },
        questions: {
          type: "array",
          items: {
            type: "object",
            properties: {
              question: { type: "string" },
              id: { type: "string" },
            },
          },
        },
      },
    },
  },
};

export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<object> => {
  try {
    const { id, answer, questions } = request.body;
    const answerItem = await QAAnswer.findById(id);
    if (!answerItem) {
      throw new HttpError({ message: "数据不存在" });
    }
    if (answerItem.answer !== answer) {
      // 修改了答案
      if (answerItem.enabled) {
        const embedAnswer = await embedQuery(answer);
        await qdrantClient.upsert({
          collectionName: "answer",
          id: answerItem.vectorId,
          vector: embedAnswer,
          mongoId: answerItem._id.toString(),
        });
      }
      answerItem.answer = answer;
      await answerItem.save();
    }
    const questionItems = await QAQuestion.find({ answerId: id });
    const oldQuestion: Record<string, boolean> = {};
    questionItems.forEach((item) => {
      oldQuestion[item._id.toString()] = true;
    });
    const promises = questions.map(async (question) => {
      if (question.id) {
        // 查询是否修改了问题
        const existQuestion = questionItems.find((item) => item._id.toString() === question.id);
        if (existQuestion && existQuestion.question !== question.question) {
          // 修改了问题
          existQuestion.question = question.question;
          await existQuestion.save();
          if (answerItem.enabled) {
            const embedQuestion = await embedQuery(question.question);
            await qdrantClient.upsert({
              collectionName: "question",
              id: existQuestion.vectorId,
              vector: embedQuestion,
              mongoId: existQuestion._id.toString(),
            });
          }
        }
        oldQuestion[question.id] = false;
      } else {
        // 新增问题
        const questionItem = await QAQuestion.create({
          question: question.question,
          vectorId: v4(),
          answerId: answerItem._id,
        });
        if (answerItem.enabled) {
          const embedQuestion = await embedQuery(question.question);
          await qdrantClient.upsert({
            collectionName: "question",
            id: answerItem.vectorId,
            vector: embedQuestion,
            mongoId: questionItem._id.toString(),
          });
        }
      }
    });
    await Promise.all(promises);
    // 删除旧的问题
    const tobeDeleteIds = Object.keys(oldQuestion).filter((k) => oldQuestion[k]);
    await Promise.all(
      tobeDeleteIds.map(async (key) => {
        const questionItem = questionItems.find((item) => item._id.toString() === key);
        if (!questionItem) {
          return;
        }
        await qdrantClient.delete({
          collectionName: "question",
          ids: [questionItem.vectorId],
        });
        await questionItem.deleteOne();
      })
    );

    return { isSuccess: true };
  } catch (e) {
    assert(e instanceof Error);
    throw new HttpError({ message: e.message });
  }
};
