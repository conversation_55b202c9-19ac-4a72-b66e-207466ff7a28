import { qdrantClient } from "@/clients/qdrant";
import { HttpError } from "@/common/error";
import { isError } from "@/common/utils";
import { embedQuery } from "@/clients/llm";
import { QAAnswer, QAQuestion } from "@/models";
import assert from "assert";
import { FastifyRequest } from "fastify";
import { v4 } from "uuid";
export interface IParamsSchema {
  Body: {
    questions: string[];
    answer: string;
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["questions", "answer"],
      properties: {
        questions: { type: "array", items: { type: "string" } },
        answer: { type: "string" },
      },
    },
  },
};

export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<object> => {
  try {
    const { answer, questions } = request.body;
    const [embedAnswer, ...embedQuestions] = await Promise.all([
      embedQuery(answer),
      ...questions.map((question) => embedQuery(question)),
    ]);

    const answerItem = await QAAnswer.create({
      answer,
      vectorId: v4(),
    });
    await qdrantClient.upsert({
      collectionName: "answer",
      id: answerItem.vectorId,
      vector: embedAnswer,
      mongoId: answerItem._id.toString(),
    });

    await Promise.all(
      questions.map(async (question, idx) => {
        const questionItem = await QAQuestion.create({
          question,
          vectorId: v4(),
          answerId: answerItem._id,
        });
        await qdrantClient.upsert({
          collectionName: "question",
          id: questionItem.vectorId,
          vector: embedQuestions[idx],
          mongoId: questionItem._id.toString(),
        });
      })
    );
    return { isSuccess: true };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new HttpError({ message: e.message });
  }
};
