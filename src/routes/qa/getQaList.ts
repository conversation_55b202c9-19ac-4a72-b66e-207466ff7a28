import { FastifyRequest } from "fastify";
import { FilterQuery } from "mongoose";
import { IQAAnswer, QAAnswer, QAQuestion } from "@/models";
import { HttpError } from "@/common/error";
import _ from "lodash";
import assert from "assert";
import { isError } from "@/common/utils";

export interface IParamsSchema {
  Querystring: {
    pageSize?: number;
    page?: number;
    question?: string;
    answer?: string;
    status?: "enabled" | "disabled";
  };
}

export const opts = {
  schema: {
    querystring: {
      type: "object",
      required: [],
      properties: {
        pageSize: { type: "number" },
        page: { type: "number" },
        question: { type: "string" },
        answer: { type: "string" },
        status: { type: "string" },
      },
    },
  },
};

export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<object> => {
  const { page = 1, pageSize = 10, answer = "", question = "", status } = request.query;
  try {
    const condition: FilterQuery<IQAAnswer> = {
      answer: new RegExp(answer, "i"),
    };
    if (status) {
      condition.enabled = status === "enabled";
    }

    const result = await QAAnswer.aggregate([
      {
        $match: condition,
      },
      {
        $lookup: {
          from: QAQuestion.collection.name,
          localField: "_id",
          foreignField: "answerId",
          as: "questions",
        },
      },
      {
        $addFields: {
          matchedQuestions: question
            ? {
                $filter: {
                  input: "$questions",
                  as: "question",
                  cond: {
                    $regexMatch: {
                      input: "$$question.question",
                      regex: question,
                      options: "i",
                    },
                  },
                },
              }
            : "$questions",
        },
      },
      {
        $match: {
          matchedQuestions: { $ne: [] },
        },
      },
      {
        $facet: {
          content: [
            {
              $skip: (page - 1) * pageSize,
            },
            {
              $limit: pageSize,
            },
          ],
          totalContent: [
            {
              $count: "total",
            },
          ],
        },
      },
    ]);

    const content = _.get(result, "[0].content", []);
    const total = _.get(result, "[0].totalContent[0].total", 0);
    return { content, total, page, pageSize };
  } catch (e) {
    assert(isError(e), "e is not Error");
    throw new HttpError({ message: e.message });
  }
};
