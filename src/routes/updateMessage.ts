import { FastifyReply, FastifyRequest } from "fastify";
import mongoose from "mongoose";
import { CopilotMessage, CopilotMessageDoc, ICopilotMessage } from "../models";
import { CustomError } from "../common/hooks/error";
import { IFormMessage, IMessage } from "@casstime/copilot-core";
import assert from "assert";
import { isError } from "@/common/utils";

export interface IParamsSchema {
  Body: {
    id: string;
    message: IFormMessage;
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["id"],
      properties: {
        id: { type: "string" },
        message: { type: "object" },
      },
    },
  },
};

/** 修改消息 */
export const handler = async (request: FastifyRequest<IParamsSchema>, reply: FastifyReply): Promise<object> => {
  const { id, message } = request.body;
  try {
    const item = await CopilotMessage.findOne({ _id: id });
    if (!item) {
      throw new CustomError({ message: "消息不存在" });
    }
    await CopilotMessage.updateOne(
      { _id: mongoose.Types.ObjectId(id) },
      {
        formData: message.formData,
      }
    );
    return {};
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new CustomError({ message: e.message });
  }
};
