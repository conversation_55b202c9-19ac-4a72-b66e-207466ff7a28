import { FastifyRequest } from "fastify";
import assert from "assert";
import _ from "lodash";
import { isError } from "@/common/utils";
import { IMessageReporter, MessageReporter } from "@/models/MessageReporter";
import { HttpError } from "@/common/error";

export interface IParamsSchema {
  Querystring: {
    messageId: string;
  };
}

export const opts = {
  schema: {
    querystring: {
      type: "object",
      required: ["messageId"],
      properties: {
        messageId: { type: "string" },
      },
    },
  },
};

interface IMessageReporterNode {
  phases: IMessageReporter[];
  name: string;
  duration: number;
  children: IMessageReporterNode[];
}

export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<object> => {
  const { messageId } = request.query;
  try {
    const messageList = await MessageReporter.find({ messageId }).sort({ timestamps: 1 }).lean();
    const groupMsgs = _.groupBy(messageList, "reporterId");
    const messageReporters = Object.values(groupMsgs);
    return {
      reporters: messageReporters,
    };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new HttpError({ message: e.message });
  }
};
