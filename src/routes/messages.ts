import { FastifyReply, FastifyRequest } from "fastify";
import { CopilotMessage, CopilotMessageDoc, ICopilotMessage } from "../models";
import { FilterQuery } from "mongoose";
import { CustomError } from "../common/hooks/error";
import Version from "../common/utils/Version";
import { isError, parseUserAgent } from "../common/utils";
import { VersionEnum } from "../common/enums";
import { FormName, MessageType } from "../interfaces/inquiry";
import { AppName } from "@/copilot/constants";
import assert from "assert";

export interface IParamsSchema {
  Querystring: {
    userId: string;
    companyId: string;
    app: string;
    startstamp?: number;
    endstamp?: number;
    pageSize?: number;
    /** 对搜索结果的排序 */
    sort?: "asc" | "desc";
  };
}

export const opts = {
  schema: {
    querystring: {
      type: "object",
      required: ["userId"],
      properties: {
        userId: { type: "string" },
        companyId: { type: "string" },
        app: { type: "string" },
        startstamp: { type: "number" },
        endstamp: { type: "number" },
        pageSize: { type: "number" },
        sort: { type: "string", enum: ["desc", "asc"] },
      },
    },
  },
};

/** 获取历史消息 */
export const handler = async (request: FastifyRequest<IParamsSchema>, reply: FastifyReply): Promise<object> => {
  const { userId, companyId, app, startstamp, endstamp, pageSize = 10, sort: _sort } = request.query;
  try {
    let condition: FilterQuery<ICopilotMessage> = {};
    if (app === AppName.GarageAssistant) {
      condition = {
        // 默认过滤不展示的消息
        type: {
          $nin: ["command", "echo", "system"] as any,
        },
        // 采购助手需要兼容旧记录
        $or: [{ owner: `${userId}:${companyId}` }, { owner: `${app}:${userId}:${companyId}` }],
      };
    } else {
      condition = {
        type: {
          $nin: ["command", "echo", "system"] as any,
        },
        owner: `${app}:${userId}:${companyId}`,
      };
    }

    let sort = -1;
    if (startstamp || endstamp) {
      condition.createdAt = {};
      if (startstamp) {
        condition.createdAt["$gt"] = startstamp;
        if (!sort) sort = 1;
      }
      if (endstamp) {
        condition.createdAt["$lt"] = endstamp;
        if (!sort) sort = -1;
      }
    }

    const result = await CopilotMessage.find(condition).sort({ createdAt: sort }).limit(pageSize);
    if (_sort === "desc") {
      result.sort((b, a) => +new Date(a.createdAt || "") - +new Date(b.createdAt || ""));
    } else {
      result.sort((a, b) => +new Date(a.createdAt || "") - +new Date(b.createdAt || ""));
    }

    return result;
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new CustomError({ message: e.message });
  }
};
