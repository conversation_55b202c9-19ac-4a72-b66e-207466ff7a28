import { FastifyRequest } from "fastify";
import assert from "assert";
import { isError } from "@/common/utils";
import { HttpError } from "@/common/error";
import { purchasePlanService } from "@/service";

export interface IParamsSchema {
  Body: {
    inquiryId: string;
    turn: number;
    makerType?: string;
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["inquiryId", "turn"],
      properties: {
        inquiryId: { type: "string" },
        turn: { type: "number" },
        makerType: { type: "number" },
      },
    },
  },
};

/** 更新报价，触发制作方案 */
export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<object> => {
  const { inquiryId, turn, makerType } = request.body;
  try {
    purchasePlanService.makeRecommendPlan(inquiryId, turn, makerType);
    return { isSuccess: true };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new HttpError({ message: e.message });
  }
};
