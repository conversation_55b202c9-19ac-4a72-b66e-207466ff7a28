import { FastifyReply, FastifyRequest } from "fastify";
import { CopilotMessage, ICopilotMessage } from "../models";
import { FilterQuery } from "mongoose";
import { CustomError } from "../common/hooks/error";
import _ from "lodash";
import assert from "assert";
import { isError } from "@/common/utils";

interface IParamsSchema {
  Body: {
    app: string; // GARAGE_ASSISTANT
    userId: string;
    companyId: string;
    startTime: number; // 开始时间戳
    endTime: number; // 结束时间戳
    page: number; //页码
    pageSize: number; // 每页数量
    sort: string; // asc升序(默认)， desc降序
    hasInteraction: boolean; // 是否有交互
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      properties: {
        app: { type: "string" },
        userId: { type: "string" },
        companyId: { type: "string" },
        startTime: { type: "number" },
        endTime: { type: "number" },
        page: { type: "number", default: 1 },
        pageSize: { type: "number", default: 20 },
        sort: { type: "string", default: "asc" },
        hasInteraction: { type: "boolean", default: "false" },
      },
    },
  },
};
/** 获取历史消息 */
export const handler = async (request: FastifyRequest<IParamsSchema>, reply: FastifyReply): Promise<object> => {
  const {
    userId,
    companyId,
    app,
    startTime = new Date().getTime() - 24 * 60 * 60 * 1000,
    endTime = new Date().getTime(),
    page,
    pageSize,
    sort: _sort,
    hasInteraction,
  } = request.body;
  try {
    // 统计消息的数量
    const condition: FilterQuery<ICopilotMessage> = {};
    if (hasInteraction) {
      condition.toUser = "system";
    }
    const sort = _sort === "desc" ? -1 : 1;
    condition.type = { $nin: ["command", "echo", "system"] as any };

    // 1-查询某个用户的聊天记录
    if (app && userId && companyId) {
      condition.createdAt = {
        $gt: startTime, // 大于 startTime
        $lte: endTime, // 小于或等于 endTime
      };
      const owner = `${app}:${userId}:${companyId}`;
      condition.owner = owner;
      const singleResult = await CopilotMessage.find(condition).sort({ createdAt: sort }).lean();
      return { list: [{ owner, amount: singleResult.length }] };
    }
    if (app) {
      condition.app = app;
    }
    // 2-聚合查询，按用户分组，获取每个用户在时间范围内的消息
    const allResults = await CopilotMessage.aggregate([
      {
        $match: {
          ...condition,
          createdAt: {
            $gt: new Date(startTime), // 大于 startTime
            $lte: new Date(endTime), // 小于或等于 endTime
          },
        },
      },
      {
        $group: {
          _id: "$owner", // 根据 owner 分组
          amount: { $sum: 1 }, // 统计组内消息数量
          app: { $first: "$app" }, // 从分组中获取第一个app值
          fromUser: { $first: "$fromUser" }, // 从分组中获取第一个fromUser值
          toUser: { $first: "$toUser" }, // 从分组中获取第一个toUser值
        },
      },
      {
        $sort: { amount: sort }, // 按 amount 字段降序排序
      },
      {
        $facet: {
          metadata: [{ $count: "total" }], // 计算总数
          data: [
            { $skip: (page - 1) * pageSize },
            { $limit: pageSize },
            {
              $project: {
                _id: 0,
                owner: "$_id",
                app: 1,
                fromUser: 1,
                toUser: 1,
                amount: 1,
              },
            },
          ],
        },
      },
    ]);

    return {
      list: _.get(allResults, "[0].data", []),
      total: _.get(allResults, "[0].metadata[0].total", 0),
      page,
      pageSize,
    };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new CustomError({ message: e.message });
  }
};
