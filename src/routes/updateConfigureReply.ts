import { FastifyReply, FastifyRequest } from "fastify";
import mongoose from "mongoose";
import { ConfigureReply, IConfigureReply } from "../models";
import { CustomError } from "../common/hooks/error";
import assert from "assert";
import { isError } from "@/common/utils";

export interface IParamsSchema {
  Body: {
    id: string;
    configureReply: IConfigureReply;
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["id"],
      properties: {
        id: { type: "string" },
        configureReply: { type: "object" },
      },
    },
  },
};

/** 修改消息 */
export const handler = async (request: FastifyRequest<IParamsSchema>, reply: FastifyReply): Promise<object> => {
  const { id, configureReply } = request.body;
  try {
    const item = await ConfigureReply.findById(id);
    if (!item) {
      throw new CustomError({ message: "消息不存在" });
    }
    await ConfigureReply.updateOne(
      { _id: mongoose.Types.ObjectId(id) },
      {
        ...configureReply,
      }
    );
    return { isSuccess: true };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new CustomError({ message: e.message });
  }
};
