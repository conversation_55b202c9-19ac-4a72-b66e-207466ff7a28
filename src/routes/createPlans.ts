import logger from "@/common/logger";
import { FastifyRequest } from "fastify";
import { Types } from "mongoose";
import { PlanRecommender } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender";
import { MarkerType } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/interfaces/plan";
import { createDataProvider } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/factory";
import { handleRecommendResult } from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/handlers";
import { MessageReporter } from "@/messages";
import { RecommendPlanGroup } from "@/models";

interface IBodyDTO {
  inquiryId: string; //询价单号
  expertAdvice?: string; // 专家意见
  type?: string; //类型
  firstRecommend?: boolean; // 首推
}

export interface IBodySchema {
  Body: IBodyDTO;
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["inquiryId"],
      properties: {
        inquiryId: { type: "string" },
        expertAdvice: { type: "string" },
        type: { type: "string", default: MarkerType.ENUMMAKER }, // 默认枚举算法
        firstRecommend: { type: "boolean", default: false }, // 默认非首推
      },
    },
  },
};

/** 制作方案 */
export const handler = async (request: FastifyRequest<IBodySchema>): Promise<{ isSuccess: true; id: string }> => {
  const id = new Types.ObjectId().toString();
  createPlans(request.body, id);
  return { isSuccess: true, id };
};

async function createPlans(body: IBodyDTO, id: string) {
  const { inquiryId, expertAdvice, type, firstRecommend } = body;
  const makerType = type as MarkerType;
  const messageReporter = new MessageReporter(id);
  try {
    const planRecommender = await PlanRecommender.create({
      reporter: messageReporter,
      inquiryId,
      makerType,
      dataProvider: createDataProvider(inquiryId),
    });
    // 推荐方案
    const recommendPromises = [
      planRecommender.recommend({
        input: "优先考虑品质、品牌，其次考虑时效、价格，给我推荐最优方案",
        expertAdvice,
      }),
    ];
    if (firstRecommend) {
      ["要原厂的，品质最优的", "要便宜的，价格最优的"].forEach((input) => {
        planRecommender.recommend({
          input,
          expertAdvice,
        });
      });
    }
    const recommendResults = await Promise.all(recommendPromises);
    const { recommendPurchasePlans } = handleRecommendResult(recommendResults);
    const recommendPlanItem = await RecommendPlanGroup.create({
      recommendContentsGroupId: id,
      makerType,
      inquiryId,
      plans: recommendPurchasePlans,
      firstRecommend,
    });
    logger.info("createPlans succeed", inquiryId, id, type, recommendPlanItem);
  } catch (e) {
    logger.error("createPlans error", e);
  }
}
