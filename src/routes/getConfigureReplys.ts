import { FastifyReply, FastifyRequest } from "fastify";
import { ConfigureReply, ConfigureReplyDoc } from "../models";
import { CustomError } from "../common/hooks/error";
import { FilterQuery } from "mongoose";
import assert from "assert";
import { isError } from "@/common/utils";

export interface IParamsSchema {
  Querystring: {
    intents?: string;
    sort?: "asc" | "desc";
  };
}

export const opts = {
  schema: {
    querystring: {
      type: "object",
      properties: {
        intents: { type: "string" },
        sort: { type: "string", enum: ["desc", "asc"] },
      },
    },
  },
};

/** 查询消息 */
export const handler = async (request: FastifyRequest<IParamsSchema>, reply: FastifyReply): Promise<object> => {
  const { intents, sort: _sort } = request.query;
  const sort = _sort === "desc" ? -1 : 1;
  const condition: FilterQuery<ConfigureReplyDoc> = {};
  if (intents) {
    const intentList = intents?.split(",");
    condition.intent = { $in: intentList };
  }
  try {
    const result = await ConfigureReply.find(condition).sort({ priority: sort });
    return { configureReplys: result };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new CustomError({ message: e.message });
  }
};
