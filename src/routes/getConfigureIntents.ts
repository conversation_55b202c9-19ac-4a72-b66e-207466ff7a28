import { ConfigureReply } from "../models";
import { CustomError } from "../common/hooks/error";
import assert from "assert";
import { isError } from "@/common/utils";

/** 查询所有有效意图 */
export const handler = async (): Promise<object> => {
  try {
    const result = await ConfigureReply.aggregate([
      { $match: { isValid: true } },
      { $group: { _id: "$intent" } },
      { $project: { intent: "$_id", _id: 0 } },
    ]);
    return { configureIntents: result.map((item) => item.intent) };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new CustomError({ message: e.message });
  }
};
