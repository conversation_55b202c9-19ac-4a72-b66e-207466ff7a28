import { FastifyReply, FastifyRequest } from "fastify";
import { ConfigureReply, IConfigureReply } from "../models";
import { CustomError } from "../common/hooks/error";
import assert from "assert";
import { isError } from "@/common/utils";

export interface IParamsSchema {
  Body: IConfigureReply;
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["intent", "replyMessage"],
      properties: {
        intent: { type: "string" },
        priority: { type: "number" },
        isValid: { type: "boolean" },
        replyMessage: { type: "object" },
      },
    },
  },
};

/** 新增消息 */
export const handler = async (request: FastifyRequest<IParamsSchema>, reply: FastifyReply): Promise<object> => {
  try {
    const msg = await ConfigureReply.create(request.body);
    if (!msg) {
      throw new CustomError({ message: "新增消息失败" });
    }
    return { isSuccess: true };
  } catch (e) {
    assert(isError(e), "e is not Error");
    throw new CustomError({ message: e.message });
  }
};
