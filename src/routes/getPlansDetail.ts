import { FastifyRequest } from "fastify";
import { HttpError } from "@/common/error";
import assert from "assert";
import { isError } from "@/common/utils";
import { IRecommendPlanGroup, RecommendPlanGroup } from "@/models";

export interface IParamsSchema {
  Params: {
    id: string;
  };
}

export const opts = {
  schema: {
    params: {
      type: "object",
      properties: {
        id: { type: "string" },
      },
    },
  },
};

/** 查询方案详情 */
export const handler = async (
  request: FastifyRequest<IParamsSchema>
): Promise<{ hasPlan: boolean; recommendPlanGroup?: IRecommendPlanGroup }> => {
  try {
    const { id } = request.params;
    const recommendPlanGroup = await RecommendPlanGroup.findOne({ recommendContentsGroupId: id }).lean().exec();
    if (!recommendPlanGroup) {
      return {
        hasPlan: false,
      };
    }
    return { hasPlan: true, recommendPlanGroup };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new HttpError({ message: e.message });
  }
};
