import { FastifyRequest } from "fastify";
import assert from "assert";
import { isError } from "@/common/utils";
import { partsMindClient } from "@/clients/parts-mind";
import { IRepairScenariosRes } from "@/clients/parts-mind/interface";
import { HttpError } from "@/common/error";

export interface IParamsSchema {
  Body: {
    brandCode: string;
    vehicleTypeCode: string;
    stdCodes: string[];
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["brandCode", "stdCodes"],
      properties: {
        brandCode: { type: "string" },
        vehicleTypeCode: { type: "string" },
        stdCodes: { type: "array", items: { type: "string" } },
      },
    },
  },
};

/** 获取48小时询价单id */
export const handler = async (
  request: FastifyRequest<IParamsSchema>
): Promise<{ content: IRepairScenariosRes["accidents"] }> => {
  const { brandCode, vehicleTypeCode, stdCodes } = request.body;

  try {
    const result = await partsMindClient.getRepairScenarios({ brandCode, vehicleTypeCode, stdCodes });
    return { content: result.accidents };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new HttpError({ message: e.message });
  }
};
