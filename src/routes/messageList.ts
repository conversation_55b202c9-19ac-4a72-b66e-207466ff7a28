import { FastifyRequest } from "fastify";
import { CopilotMessage, ICopilotMessage } from "../models";
import { FilterQuery } from "mongoose";
import { CustomError } from "../common/hooks/error";
import { AppName } from "@/copilot/constants";
import assert from "assert";
import { isError } from "@/common/utils";

export interface IParamsSchema {
  Querystring: {
    userId: string;
    companyId: string;
    app: string;
    startMsgId?: string;
    endMsgId?: string;
    pageSize?: number;
    /** 对搜索结果的排序 */
    sort?: "asc" | "desc";
    filterType?: string;
    dialogueId?: string;
  };
}

export const opts = {
  schema: {
    querystring: {
      type: "object",
      required: ["userId"],
      properties: {
        userId: { type: "string" },
        companyId: { type: "string" },
        app: { type: "string" },
        startMsgId: { type: "string" },
        endMsgId: { type: "string" },
        pageSize: { type: "number" },
        sort: { type: "string", enum: ["desc", "asc"] },
        filterType: { type: "string" },
        dialogueId: { type: "string" },
      },
    },
  },
};

/** 获取历史消息 */
export const handler = async (request: FastifyRequest<IParamsSchema>): Promise<object> => {
  const { userId, companyId, app, startMsgId, endMsgId, pageSize = 10, sort, filterType, dialogueId } = request.query;
  // 默认过滤不展示的消息
  let filterTypes: any = ["command", "record"];
  if (filterType) {
    filterTypes = filterType.split(",");
  }
  try {
    let condition: FilterQuery<ICopilotMessage> = {};
    if (app === AppName.GarageAssistant) {
      condition = {
        type: {
          $nin: filterTypes,
        },
        // 采购助手需要兼容旧记录
        $or: [{ owner: `${userId}:${companyId}` }, { owner: `${app}:${userId}:${companyId}` }],
      };
    } else {
      condition = {
        type: {
          $nin: filterTypes,
        },
        owner: `${app}:${userId}:${companyId}`,
      };
    }

    if (dialogueId) {
      condition.dialogueId = dialogueId;
    }

    if (startMsgId || endMsgId) {
      condition.createdAt = {};
      if (startMsgId) {
        const msg = await CopilotMessage.findOne({ _id: startMsgId });
        condition.createdAt["$gt"] = msg?.createdAt;
      }
      if (endMsgId) {
        const msg = await CopilotMessage.findOne({ _id: endMsgId });
        condition.createdAt["$lt"] = msg?.createdAt;
      }
    }

    const result = await CopilotMessage.find(condition).sort({ createdAt: -1 }).limit(pageSize);
    if (sort === "desc") {
      result.sort((b, a) => +new Date(a.createdAt || "") - +new Date(b.createdAt || ""));
    } else {
      result.sort((a, b) => +new Date(a.createdAt || "") - +new Date(b.createdAt || ""));
    }

    return { messageList: result };
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new CustomError({ message: e.message });
  }
};
