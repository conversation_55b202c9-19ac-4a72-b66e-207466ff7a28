import { HttpError } from "@/common/error";
import { IRecommendPlanTaskParams } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import { TaskPollingStatus } from "@/models";
import logger from "@/common/logger";
import { FastifyReply, FastifyRequest } from "fastify";
import _ from "lodash";
import assert from "assert";
import { isError } from "@/common/utils";

export interface IParamsSchema {
  Body: {
    inquiryId: string; //询价单号
    inquiryProgrammeGroupId: string; //方案组id
    turns: number; //当前方案轮次
    finished: boolean; //是否已完成
    inquiryProgrammeIds: string[]; //方案id数组
  };
}

export const opts = {
  schema: {
    body: {
      type: "object",
      required: ["inquiryId"],
      properties: {
        inquiryId: { type: "string" }, // 询价单号
        inquiryProgrammeGroupId: { type: "string" }, // 方案组id
        turns: { type: "number" }, // 当前方案轮次
        finished: { type: "boolean" }, // 是否已完成
        inquiryProgrammeIds: { type: "array", items: { type: "string" } }, // 方案id数组
      },
    },
  },
};

/** 更新推荐方案数据 */
export const handler = async (request: FastifyRequest<IParamsSchema>, reply: FastifyReply): Promise<object> => {
  const { inquiryId, finished, turns: newTurns, inquiryProgrammeIds = [], inquiryProgrammeGroupId } = request.body;
  try {
    const item = await TaskPollingStatus.findOne({
      taskId: inquiryId,
    });
    if (!item) {
      throw new HttpError({ message: "没有该轮询任务" });
    }
    const params = { ...(item?.params || {}) } as IRecommendPlanTaskParams;
    const { hasSentInquiryProgrammeIds = [], turns } = params;
    let hasSent = false;
    if (inquiryProgrammeIds.length === 0) {
      hasSent = true;
    } else if (inquiryProgrammeIds.length === 1) {
      /** inquiryProgrammeIds只有一个时，判断上一次发送的数组是否包含这个id，包含则标记已发送 */
      hasSent = hasSentInquiryProgrammeIds.includes(inquiryProgrammeIds[0]);
    } else {
      /** inquiryProgrammeIds 有值且与已发送的ids不完全相等 */
      hasSent = _.xor(inquiryProgrammeIds, hasSentInquiryProgrammeIds).length === 0;
    }
    /** 没发送过才是有效的轮次，否则不更新轮次 */
    const validTurns = hasSent ? turns : newTurns;
    const newParams = {
      ...params,
      finished,
      inquiryId,
      turns: validTurns,
      inquiryProgrammeIds,
      inquiryProgrammeGroupId,
    };
    logger.info("更新推荐方案数据", { params: newParams });
    await TaskPollingStatus.updateOne(
      { taskId: inquiryId },
      {
        $set: { params: newParams },
      }
    );
    return {};
  } catch (e) {
    assert(isError(e), "e is not an Error");
    throw new HttpError({ message: e.message });
  }
};
