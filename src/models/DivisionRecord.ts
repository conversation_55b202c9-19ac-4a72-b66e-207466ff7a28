import mongoose, { Schema, Document } from "mongoose";

export type IDivisionRecord = {
  type: string;
  entityId: string;
  divisionName: string;
};

const schema = new Schema<IDivisionRecord>(
  {
    type: { type: String, required: true },
    entityId: { type: String, required: true },
    divisionName: { type: String, required: true },
  },
  {
    timestamps: true,
    toJSON: {
      transform(doc, ret) {
        ret.id = ret._id;
      },
    },
  }
);

export type DivisionRecordDoc = Document<IDivisionRecord>;

export const DivisionRecord = mongoose.model<IDivisionRecord>("DivisionRecord", schema);
