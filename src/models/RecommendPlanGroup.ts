import {
  IPurchasePlanItem,
  PurchasePlanFeatureEnum,
} from "@/copilot/apps/AIPurchasePlanAssistant/agents/AIPurchasePlan/planrecommender/interfaces/plan";
import mongoose, { Schema, Document } from "mongoose";

export interface IPurchasePlan extends IPurchasePlanItem {
  // 特征
  features: PurchasePlanFeatureEnum[];
  // 显示的特征
  showFeature: string;
  // 发货地
  locationName?: string;
  // 方案价格
  price: number;
  // 时效描述
  durationDesc: string;
}

export type IRecommendPlanGroup = {
  inquiryId: string;
  // taskpollingstatuses 表的params.recommendContentsGroupId
  recommendContentsGroupId?: string;
  // 是否首推，用户主动触发的方案为false
  firstRecommend: boolean;
  makerType: string;
  plans: IPurchasePlan[];
};

export const RecommendPlanItemSchema = new Schema({
  name: { type: String, required: false },
  type: { type: String, required: false },
  source: { type: String, required: false },
  storeId: { type: String, required: false },
  features: { type: [String], required: false },
  showFeature: { type: String, required: false },
  price: { type: Number, required: false, default: 0 },
  durationDesc: { type: String, required: false },
  locationName: { type: String, required: false },
  storeName: { type: String, required: false },
  facilityId: { type: String, required: false },
  facilityName: { type: String, required: false },
  reason: { type: String, required: false },
  standardItemResults: {
    type: [Object],
  },
  weightedScore: { type: Number, required: false },
});

const RecommendPlanGroupSchema = new Schema(
  {
    inquiryId: { type: String, required: true },
    recommendContentsGroupId: { type: String, required: false },
    firstRecommend: { type: Boolean, required: true, default: false },
    makerType: { type: String, required: false },
    plans: { type: [RecommendPlanItemSchema], required: true },
  },
  {
    timestamps: true,
    toJSON: {
      transform(doc, ret) {
        ret.id = ret._id;
      },
    },
  }
);

export type RecommendPlanGroupDoc = Document<IRecommendPlanGroup>;

export const RecommendPlanGroup = mongoose.model<IRecommendPlanGroup>("RecommendPlanGroup", RecommendPlanGroupSchema);
