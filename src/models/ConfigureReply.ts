import mongoose, { Schema, Document } from "mongoose";
import { IMessage } from "@casstime/copilot-core";

export type IConfigureReplyDTO = IConfigureReply & { _id?: Schema.Types.ObjectId }

export type IConfigureReply = {
  intent: string;
  priority: number;
  hours: number;
  isValid: boolean;
  useAI: boolean;
  replyMessage: IMessage;
  replyTimes: mongoose.Types.ObjectId[];
};

const schema = new Schema<IConfigureReply>(
  {
    intent: { type: String, required: true },
    priority: { type: Number, default: 0 },
    hours: { type: Number, default: 0 },
    isValid: { type: Boolean, default: true },
    useAI: { type: Boolean, default: false },
    replyMessage: Object,
    replyTimes: [{ type: Schema.Types.ObjectId, ref: 'ReplyTime' }]
  },
  {
    timestamps: true,
    toJSON: {
      transform(doc, ret, options) {
        ret.id = ret._id;
      },
    },
  }
);

export type ConfigureReplyDoc = Document<IConfigureReply>;

export const ConfigureReply = mongoose.model<IConfigureReply>("ConfigureReply", schema);
