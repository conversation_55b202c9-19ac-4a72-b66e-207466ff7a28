import mongoose, { Schema, Document } from "mongoose";
import { QAAnswer } from "./QAAnswer";

export interface IQA {
  question: string;
  questionId: string;
  answer: string;
  answerId: string;
}

export type IQAQuestion = {
  question: string;
  answerId: string;
  vectorId: string;
  enabled: boolean;
  priority: number;
};

const QAQuestionSchema = new Schema(
  {
    question: { type: String, required: true },
    answerId: { type: Schema.Types.ObjectId, ref: QAAnswer.collection.name, required: true },
    vectorId: { type: String, required: true },
    enabled: { type: Boolean, default: true },
    priority: { type: Number, default: 100 },
  },
  {
    timestamps: true,
    toJSON: {
      transform(doc, ret) {
        ret.id = ret._id;
      },
    },
  }
);

export type QAQuestionDoc = Document<IQAQuestion>;

export const QAQuestion = mongoose.model<IQAQuestion>("QAQuestion", QAQuestionSchema);
