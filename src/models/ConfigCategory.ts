import mongoose, { Schema, Document } from "mongoose";

export type IConfigCategory = {
  name: string;
  description?: string;
  aliases?: string;
  examples?: string[];
};

const schema = new Schema(
  {
    name: { type: String, required: true, unique: true },
    description: { type: String, required: false },
    aliases: { type: [String], required: false },
    examples: { type: [String], required: false },
  },
  {
    timestamps: true,
  }
);

export type ConfigCategoryDoc = Document<IConfigCategory>;

export const ConfigCategory = mongoose.model<IConfigCategory>("ConfigCategory", schema);
