import mongoose, { Schema, Document } from "mongoose";

export type IUserSelectRecords = {
  owner: string; // 标识
  type: UserSelectRecordType; // 记录类型
  options: string[]; // 选项
  selected: string; // 选中项
  imageUrl?: string; // 图片url
};

export enum UserSelectRecordType {
  SIMILAR_PARTS = "SIMILAR_PARTS",
}

const schema = new Schema<IUserSelectRecords>(
  {
    owner: { type: String, required: true },
    type: { type: UserSelectRecordType, required: true },
    options: { type: Array, required: true },
    selected: { type: String, required: true },
    imageUrl: { type: String },
  },
  {
    timestamps: true,
    toJSON: {
      transform(doc, ret) {
        ret.id = ret._id;
      },
    },
  }
);

export type UserSelectRecordsDoc = Document<IUserSelectRecords>;

export const UserSelectRecords = mongoose.model<IUserSelectRecords>("UserSelectRecords", schema);
