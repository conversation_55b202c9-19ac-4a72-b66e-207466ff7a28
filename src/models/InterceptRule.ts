import { IMessage, INLU } from "@casstime/copilot-core";
import mongoose, { Schema, Document } from "mongoose";

type Response = IMessage | INLU;
/**
 * 拦截规则，用于拦截用户消息，提前匹配回复
 */
export type IInterceptRule = {
  /**
   * 应用
   */
  app: string;

  /**
   * 规则描述
   */
  description: string;

  /**
   * 匹配方式
   * keywordsIn: 关键词匹配，只要包含一个关键词就匹配成功
   * textIn: values 中包含文本就匹配成功
   * regexIn: values 是正则表达式，某个表达式能够匹配中消息
   */
  operator: "keywordsIn" | "textIn" | "regexIn";

  /**
   * 匹配规则
   */
  value: string[];

  /**
   * 回复类型
   * message: 固定消息回复
   * nlu: 为当前消息添加nlu
   */
  responseType: "message" | "nlu";

  /**
   * 响应内容
   */
  response: Response;

  /**
   * 优先级
   */
  priority: number;

  /**
   * 索引字符串
   */
  chars: string;

  /**
   * 是否可用
   */
  enabled: boolean;
};

const schema = new Schema(
  {
    app: { type: String, required: true },
    description: { type: String, required: true },
    operator: { type: String, required: true },
    value: { type: mongoose.SchemaTypes.Mixed, required: true },
    responseType: { type: String, required: true },
    response: { type: Object, required: true },
    priority: { type: Number, default: 0 },
    chars: { type: String, required: true },
    enabled: { type: Boolean, default: true },
  },
  {
    timestamps: true,
  }
);

schema.index({
  chars: "text",
});

export type InterceptRuleDoc = Document<IInterceptRule>;

export const InterceptRule = mongoose.model<IInterceptRule>("InterceptRule", schema);
