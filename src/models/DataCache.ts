import mongoose, { Schema, Document } from "mongoose";

export type IDataCache = {
  /** 用途_接口_id */
  key: string;
  /** 拍平成字符串 */
  value: string;
};

const schema = new Schema(
  {
    key: { type: String, required: true },
    value: { type: String, required: true },
  },
  {
    timestamps: true,
  }
);

export type DataCacheDoc = Document<IDataCache>;

export const DataCache = mongoose.model<IDataCache>("DataCache", schema);
