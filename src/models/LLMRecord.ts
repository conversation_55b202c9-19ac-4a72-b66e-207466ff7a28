import mongoose, { Schema, Document } from "mongoose";

export type ILLMRecord = {
  runId: string;
  prompts?: string;
  generations?: string;
};

const schema = new Schema(
  {
    runId: { type: String, required: true },
    prompts: { type: String, required: false },
    generations: { type: String, required: false },
  },
  {
    timestamps: true,
  }
);

export type LLMRecordDoc = Document<ILLMRecord>;

export const LLMRecord = mongoose.model<ILLMRecord>("LLMRecord", schema);
