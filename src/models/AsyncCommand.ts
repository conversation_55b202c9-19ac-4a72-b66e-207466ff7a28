import { IAsyncCommandItem } from "@/copilot/apps/GarageAssistant/agents/inquiry/interface";
import mongoose, { Schema, Document } from "mongoose";

export type IAsyncCommand = {
  commands: IAsyncCommandItem[];
  updatedAt?: Date;
  createdAt?: Date;
};

const schema = new Schema<IAsyncCommand>(
  {
    commands: { type: Array, required: true },
  },
  {
    timestamps: true,
    toJSON: {
      transform(doc, ret) {
        ret.id = ret._id;
      },
    },
  }
);

export type AsyncCommandDoc = Document<IAsyncCommand>;

export const AsyncCommand = mongoose.model<IAsyncCommand>("AsyncCommand", schema);
