import mongoose, { Schema, Document } from "mongoose";

export type ITaskPollingStatus = {
  owner: string; // 会话标识
  taskType: string; // 询价类型：TYRE-轮胎，WHOLE_CAR_PARTS-全车件询价
  taskId: string; // 轮询任务id
  done: boolean; // 轮询任务是否结束
  params?: object; // 参数
  app?: string;
  userId?: string;
  companyId?: string;
  updatedAt?: Date;
  createdAt?: Date;
  dialogueId?: string; // 对话Id
  id?: string;
};

const schema = new Schema<ITaskPollingStatus>(
  {
    owner: { type: String, required: true },
    taskType: { type: String, required: true },
    taskId: { type: String, required: true },
    done: { type: Boolean, default: false },
    params: { type: Object },
    app: { type: String },
    userId: { type: String },
    companyId: { type: String },
    dialogueId: { type: String },
  },
  {
    timestamps: true,
    toJSON: {
      transform(doc, ret) {
        ret.id = ret._id;
      },
    },
  }
);

export type TaskPollingStatusDoc = Document<ITaskPollingStatus>;

export const TaskPollingStatus = mongoose.model<ITaskPollingStatus>("TaskPollingStatus", schema);
