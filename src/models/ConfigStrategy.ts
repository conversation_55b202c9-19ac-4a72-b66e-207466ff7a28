import mongoose, { Schema, Document } from "mongoose";

export enum StrategyScemeEnum {
  fallback_reply = 'fallback_reply',
  welcome = 'welcome',
  guide = 'guide'
}

export enum StrategyTypeEnum {
  image = 'image',
  video = 'video',
  text = 'text'
}

export enum StrategyMsgTypeEnum {
  fastgpt = 'fastgpt',
  fixed = 'fixed'
}

export type IStrategyContent = {
  /** 消息类型 image 图片、video视频、text文本 */
  type: StrategyTypeEnum;
  /** 类型 fastgpt：FASTGPT生成、fixed: 固定文案 */
  msgType?: StrategyMsgTypeEnum;
  /** 文本内容 */
  content?: string[];
  /** 嵌入内容 */
  embed?: object;
  /** 图片url */
  imageUrl?: string;
  /** 视频url */
  videoUrl?: string;
  /** 视频首帧图 */
  thumb?: string;
  /** 视频时长 */
  duration?: string;
  /** 是否使用AI优化文案 */
  isAiOptimization?: boolean;
  /** AI优化prompts */
  prompts?: string;
  /** 扩展字段 */
  extra?: string;
}

export type IConfigStrategy = {
  title?: string;
  sceme?: StrategyScemeEnum;
  content?: string;
  categoryName?: string;
  actions?: string[][];
  featureToggle?: string;
  priority?: number;
  enabled?: boolean;
};

const schema = new Schema(
  {
    title: { type: String, required: true },
    sceme: { type: StrategyScemeEnum, required: true },
    content: { type: String, required: true },
    categoryName: { type: String, required: false },
    actions: { type: [[String]], required: false },
    featureToggle: { type: String, required: false },
    priority: { type: Number, required: false },
    enabled: { type: Boolean, required: false },
  },
  {
    timestamps: true,
  }
);

export type ConfigStrategyDoc = Document<IConfigStrategy>;

export const ConfigStrategy = mongoose.model<IConfigStrategy>("ConfigStrategy", schema);
