import mongoose, { Schema, Document } from "mongoose";

export type IConfigAction = {
  _id: string;
  text: string;
  action: string;
  description?: string;
};

const schema = new Schema(
  {
    text: { type: String, required: true, unique: true },
    action: { type: String, required: true },
    description: { type: String, required: false },
  },
  {
    timestamps: true,
  }
);

export type ConfigActionDoc = Document<IConfigAction>;

export const ConfigAction = mongoose.model<IConfigAction>("ConfigAction", schema);
