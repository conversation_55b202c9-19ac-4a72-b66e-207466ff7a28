import mongoose, { Schema, Document } from "mongoose";

export type IRecommendPlanGroupRead = {
  inquiryId: string;
  recommendPlanGroupId: string;
  // 已读列表
  dialogueIds: string[];
};

const RecommendPlanGroupReadSchema = new Schema(
  {
    inquiryId: { type: String, required: true, unique: true, index: true },
    recommendPlanGroupId: { type: Schema.Types.ObjectId, required: true },
    dialogueIds: { type: [String], required: false },
  },
  {
    timestamps: true,
    toJSON: {
      transform(doc, ret) {
        ret.id = ret._id;
      },
    },
  }
);

export type RecommendPlanGroupReadDoc = Document<IRecommendPlanGroupRead>;

export const RecommendPlanGroupRead = mongoose.model<IRecommendPlanGroupRead>(
  "RecommendPlanGroupRead",
  RecommendPlanGroupReadSchema
);
