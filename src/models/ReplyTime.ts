import mongoose, { Schema, Document, Types } from "mongoose";

export type IReplyTime = {
  userLoginId: string;
  configureReplyId: Types.ObjectId;
  time: number;
  updatedAt?: Date;
  createdAt?: Date;
};

const schema = new Schema<IReplyTime>(
  {
    userLoginId: { type: String, required: true },
    configureReplyId: { type: Schema.Types.ObjectId, ref: 'ConfigureReply', required: true },
    time: { type: Number, required: true },
  },
  {
    timestamps: true,
    toJSON: {
      transform(doc, ret, options) {
        ret.id = ret._id;
      },
    },
  }
);

export type ReplyTimeDoc = Document<IReplyTime>;

export const ReplyTime = mongoose.model<IReplyTime>("ReplyTime", schema);
