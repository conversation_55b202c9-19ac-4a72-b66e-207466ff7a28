import mongoose, { Schema, Document } from "mongoose";

export type IQAAnswer = {
  answer: string;
  vectorId: string;
  enabled: boolean;
};

const QAAnswerSchema = new Schema(
  {
    answer: { type: String, required: true },
    vectorId: { type: String, required: true },
    enabled: { type: Boolean, required: true, default: true },
  },
  {
    timestamps: true,
    toJSON: {
      transform(doc, ret) {
        ret.id = ret._id;
      },
    },
  }
);

export type QAAnswerDoc = Document<IQAAnswer>;

export const QAAnswer = mongoose.model<IQAAnswer>("QAAnswer", QAAnswerSchema);
