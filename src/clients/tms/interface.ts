export interface IWholionLogisticsPayload {
  postalAddressId: string;
  userLoginId: string;
  garageCompanyId: string;
  businessType: string;
  terminal: string;
  products?: IProductItem[];
}

export interface IWholionLogisticsEtaDTO {
  productId?: string;
  defaultEta?: string;
  etaLabels?: IEtaLabelDetailDTO[];
}

interface IEtaLabelDetailDTO {
  logisticsServiceType?: string; // 物流服务名【专送|班车送|拼单送|...】（用于展示）
  etaShow?: string; // ETA预计送达时长（用于展示）
  distributionTime?: number; // 配送时长（单位分钟）
}

export interface IProductItem {
  facilityId: string;
  productId: string;
  storeId: string;
  inquiryId: string;
  quantity: number;
  price: string;
  invoiceType: string;
}
