import httpClient from "../../common/clients/http.client";
import { IWholionLogisticsEtaDTO, IWholionLogisticsPayload } from "./interface";

export class TMSClient {
  async getWholionLogistics(wholionLogisticsPayload: IWholionLogisticsPayload) {
    const { result } = await httpClient.post<IWholionLogisticsEtaDTO[]>(
      "/tms-service/logistics/wholion/eta-label/show/v2",
      {
        body: wholionLogisticsPayload,
      }
    );
    return result;
  }
}
