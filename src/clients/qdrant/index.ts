import { QdrantClient as _QdrantClient } from "@qdrant/js-client-rest";
import config from "@casstime/config";
import { IQdResult } from "./interface";

class QdrantClient {
  questionCollectionName = "copilot_server_qa_question";
  answerCollectionName = "copilot_server_qa_answer";
  qdrant: _QdrantClient;
  constructor() {
    const qdrant = new _QdrantClient({
      url: config.get("QDRANT_URL"),
      prefix: config.get("QDRANT_URL_PREFIX"),
      checkCompatibility: false,
    });
    this.qdrant = qdrant;
  }

  async searchQuestion({ vector, limit = 5 }: { vector: number[]; limit: number }) {
    const result = await this.qdrant.search(this.questionCollectionName, {
      vector,
      limit,
    });
    return result as IQdResult[];
  }
  async searchAnswer({ vector, limit = 5 }: { vector: number[]; limit: number }) {
    const result = await this.qdrant.search(this.answerCollectionName, {
      vector,
      limit,
    });
    return result as IQdResult[];
  }
  async upsert({
    id,
    vector,
    mongoId,
    collectionName,
  }: {
    id: string;
    vector: number[];
    mongoId: string;
    collectionName: "question" | "answer";
  }) {
    const name = collectionName === "question" ? this.questionCollectionName : this.answerCollectionName;
    await this.qdrant.upsert(name, {
      points: [
        {
          id,
          vector,
          payload: {
            mongoId,
          },
        },
      ],
    });
  }
  async delete({ ids, collectionName }: { ids: string[]; collectionName: "question" | "answer" }) {
    const name = collectionName === "question" ? this.questionCollectionName : this.answerCollectionName;
    await this.qdrant.delete(name, {
      points: ids,
    });
  }
  async setPayload({
    id,
    mongoId,
    collectionName,
  }: {
    id: string;
    mongoId: string;
    collectionName: "question" | "answer";
  }) {
    const name = collectionName === "question" ? this.questionCollectionName : this.answerCollectionName;
    await this.qdrant.setPayload(name, {
      points: [id],
      payload: { mongoId },
    });
  }
}

export const qdrantClient = new QdrantClient();

export const qdrant = qdrantClient.qdrant;
