import httpClient from "../../common/clients/http.client";
import { ErrorCode } from "../../common/enums";
import { TerminalRes } from "../../interfaces/client";
import { IParsePartNamesBatchNLPRes, IParsePartNamesNLPRes } from "./interface";

export class NLPClient {
  async extractPartNameEntities(text: string) {
    const { result } = await httpClient.post<TerminalRes<IParsePartNamesNLPRes>>("/nlp-node/nlp/entities/extract", {
      body: { text, fixParts: true },
    });
    return {
      errorCode: result?.code === 200 ? ErrorCode.SUCCESS : result?.code,
      data: result?.data?.content || [],
      message: result?.errorMessage,
    };
  }

  async extractPartNamesEntitiesBatch(texts: string[]) {
    const { result } = await httpClient.post<TerminalRes<IParsePartNamesBatchNLPRes>>(
      "/nlp-node/nlp/entities/extract/batch",
      {
        body: { texts, fixParts: true },
      }
    );
    return {
      errorCode: result?.code === 200 ? ErrorCode.SUCCESS : result?.code,
      data: result?.data.content || [],
      message: result?.errorMessage,
    };
  }
}
