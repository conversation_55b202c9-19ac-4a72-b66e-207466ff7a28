export interface IGetCategoryTypeDTO {
    itemId?: string
    itemName?: string
    parentItemId?: string
    parentItemName?: string
    categoryType?: string
    standName?: string
  }
  export interface IBizcatParams{
    categoryId: string,
    needParent: string, // 是否需要父分类，N-不需要，Y-需要，默认是N
    standardNameCodes:string[]
  }

  export interface IGetBizCatTypeDTO{
    bizCatHeadCode: string,
    bizCatHeadId: number,
    categoryCode: string,
    id: number,
    itemName: string
    parentId: number,
    parentItem: IParentItem
    productId: string,
    sourceType: string
    standName: string,
    standardNameCode: string
  }
  interface IParentItem{
    id: number,
    bizCatHeadId: number,
    itemName: string
    parentId: number,
  }