// tslint:disable
/**
 * 商品库存
 */
export type ProductInventory = {
    /**
     * 库存数量
     */
    availableToPromiseTotal?: number;
    /**
     * 是否为默认仓库
     */
    defaultFacility?: string;
    /**
     * 仓库id
     */
    facilityId?: string;
    /**
     * 仓库名称
     */
    facilityName?: string;
    /**
     * 仓库所在地geoId
     */
    geoId?: string;
    /**
     * 库存数量，数值需要校验，故用string
     */
    inventoryValue?: string;
    /**
     * 公司id
     */
    partyId?: string;
    /**
     * 商品Id
     */
    productId?: string;
    /**
     * 店铺id
     */
    productStoreId?: string;
    /**
     * 安全库存数量
     */
    safeToPromiseTotal?: number;
    /**
     * 是否展示实际库存数量
     */
    showInvOrNo?: string;
    /**
     * 状态id,没有用到
     */
    statusId?: string;
};
