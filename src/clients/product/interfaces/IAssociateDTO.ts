export interface IKeywordDTO {
  /** 公司ID */
  companyId: string;
  /** 关键字 */
  keyword?: string;
  /** 商场类型 */
  mallType?: string;
  /** 用户登录ID */
  userLoginId: string;
}

export interface IAssociateListItem {
  /** 联想词 */
  associate?: string;
  /** 商品数量统计 */
  count?: number;
}

export interface IStoreListItem {
  /** 店铺log */
  imageUrl?: string;
  /** 店铺商品总数 */
  productCount?: number;
  /** 店铺id */
  storeId?: string;
  /** 店铺名称 */
  storeName?: string;
}

export interface IData {
  /** 联想词VO */
  associateList?: IAssociateListItem[];
  /** 店铺VO List */
  storeList?: IStoreListItem[];
}

export interface IKeywordResponse {
  code?: number;
  /** 店铺和联想词数据VO */
  data?: IData;
  errorMessage?: string;
}