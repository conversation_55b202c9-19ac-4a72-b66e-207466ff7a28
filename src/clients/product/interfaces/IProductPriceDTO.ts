export interface IStepPricesItem {
  /** 不含税价 */
  excludingTaxPrice?: string;
  /** 阶梯-起始数量 */
  fromNum?: number;
  /** 开票类型, YES-开票, NO-不开票, BOTH-两种都支持 */
  openInvoiceType?: string;
  /** 价格 */
  price?: string;
  /** 序号 */
  sequence?: number;
  /** 含税价 */
  taxInclusivePrice?: string;
  /** 阶梯-终止数量 */
  thruNum?: number;
}

export interface IDataItem {
  /** 划线价 */
  crossedPrice?: string;
  /** 不含税价，价格文案特性开关关+开票类型为不开票和两种都支持，返回不含税价；其他不返回 */
  excludingTaxPrice?: string;
  /** 开票类型, YES-开票, NO-不开票, BOTH-两种都支持 */
  openInvoiceType?: string;
  /** 价格，开票类型为开票和两种都支持，取含税价；只支持不开票时，取不含税价 */
  price?: string;
  /** 4s价格 */
  price4s?: string;
  /** 商品id */
  productId?: string;
  /** 阶梯价 */
  stepPrices?: IStepPricesItem[];
  /** 含税价，价格文案特性开关关+开票类型为开票和两种都支持，返回含税价；其他不返回 */
  taxInclusivePrice?: string;
}

export interface IListResponse {
  code?: number;
  data?: IDataItem[];
  errorMessage?: string;
}

export interface  IGetProductPricesPayload {
  geoId?: string;
  productIds?: string[];
  pageType?: string;
  companyId?: string;
  userLoginId?: string;
}