export interface IAttrsItem {
  /** 属性名称 */
  attrName?: string;
  /** 属性值列表 */
  attrValues?: string[];
  /** 商城筛选项属性展示类型：MORE-更多、DIRECT-直接展示 */
  showType?: string;
}

export interface IBrandsItem {
  /** 品牌id */
  brandId?: string;
  /** 品牌Logo */
  brandLogo?: string;
  /** 品牌名称 */
  brandName?: string;
}

export interface IChildrenItem {
  /** 分类id */
  categoryId?: string;
  /** 分类名称 */
  categoryName?: string;
  /** 子级分类 */
  children?: IChildrenItem[];
}

export interface IStoresItem {
  /** 品牌商标签：目前只有博世（BOSCH） */
  brandVip?: string;
  /** 店铺logo */
  imageUrl?: string;
  /** 店铺id */
  storeId?: string;
  /** 店铺等级，查询店铺等级接口提供 */
  storeLevel?: number;
  /** 店铺名称 */
  storeName?: string;
  /** 供应商公司id */
  supplierCompanyId?: string;
}

export interface IProductCountVO {
  /** 属性统计 */
  attrs?: IAttrsItem[];
  /** 品牌统计 */
  brands?: IBrandsItem[];
  /** 分类统计 */
  categories?: IChildrenItem[];
  /** 关键字搜索，识别出选中的品牌id */
  checkBrandId?: string;
  /** 商家统计 */
  stores?: IStoresItem[];
}

export interface IAttrValuesDTO {
  attrValue?: string,
  attrValueShow?: string,
}

export interface IAttrShowItemDTO {
  attrName?: string,
  attrValues?: IAttrValuesDTO[],
}

export interface IAttrsDTO {
  columnNumber?: number,
  attrShowVOS?: IAttrShowItemDTO[]
}

export interface IStoreDTO {
  storeId?: string,
  storeName?: string,
  brandVip?: string,
  supplierCompanyId?: string,
}

export interface IProductListVOItemDTO {
  userLoginId?: string;
  garageCompanyId?: string;
  productId?: string;
  productName?: string;
  mainImgUrl?: string;
  description?: string;
  partNumber?: string;
  brandId?: string;
  brandName?: string;
  brandQualityId?: string;
  brandQualityName?: string;
  attrs?: IAttrsDTO[];
  store?: IStoreDTO;
  productTagTitle?: string;
  productTagType?: string;
  activityId?: string;
  unit?: string;
}

export interface IDataInfo {
  /** 商品统计VO */
  productCountVO?: IProductCountVO;
  /** 商品列表VO */
  productListVO?: IProductListVOItemDTO[];
  // productListVO?: [];
}

export interface IData {
  /** 搜索引擎商品聚合信息VO */
  data?: IDataInfo;
  pageNum?: number;
  pageSize?: number;
  totalElements?: number;
  totalPages?: number;
}

export interface ISearchProductNewResultDTO {
  code?: number;
  data?: IData;
  errorMessage?: string;
}