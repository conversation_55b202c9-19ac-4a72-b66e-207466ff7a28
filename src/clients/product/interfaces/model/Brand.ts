// tslint:disable
import { Serializable } from "./Serializable";
/**
 * 零件品牌
 */
export type Brand = {
    /**
     * 品牌别名
     */
    brandAlias?: string[];
    /**
     * 品牌id
     */
    brandCode?: string;
    /**
     * 修改时使用，作为Id使用
     */
    brandId?: string;
    /**
     * 品牌logo地址
     */
    brandLogo?: string;
    /**
     * 品牌名
     */
    brandName?: string;
    /**
     * 创建人
     */
    createdBy?: string;
    /**
     * 创建时间
     */
    createdDate?: string;
    id?: Serializable;
    /**
     * 手机端介绍页
     */
    introducePhonePage?: string;
    /**
     * 介绍页
     */
    introductoryPage?: string;
    /**
     * 是否原厂件
     */
    isOEM?: string;
    /**
     * 修改人
     */
    lastUpdateBy?: string;
    /**
     * 品牌默认品质  INTERNAL_BRAND,EXTERNAL_BRAND,ORIGINAL_BRAND,（ORIGINAL_OTHERS,OTHER_BRAND）后面是询价定制
     */
    quality?: string;
    /**
     * 品质
     */
    qualitys?: string[];
};
