// tslint:disable
export type ProductSearchRequest = {
    baseCategory?: string;
    brandCode?: string;
    brandQulityType?: string;
    capacity?: string;
    constituent?: string;
    createEndDate?: string;
    createStartDate?: string;
    deliveryAraeGeoId?: string;
    firstCategory?: string;
    generalStatus?: string;
    grade?: string;
    hasInventory?: boolean;
    internalName?: string;
    isOEM?: string;
    keyword?: string;
    needTax?: boolean;
    norm?: string;
    partNum?: string;
    productPartNum?: string;
    productSortEum?: "PRICE_ASC" | "PRICE_DESC" | "SALECOUNT_ASC" | "SALECOUNT_DESC" | "LASTUPDATEDTIME_DESC";
    productStatus?: string;
    productType?: string;
    rows?: number;
    secondCategory?: string;
    showSpecialSale?: string;
    start?: number;
    statusId?: string;
    storeId?: string;
    viscosity?: string;
};
