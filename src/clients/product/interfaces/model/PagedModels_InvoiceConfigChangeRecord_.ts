// tslint:disable
import { InvoiceConfigChangeRecord } from "./InvoiceConfigChangeRecord";
import { PageMetadata } from "./PageMetadata";
import { Sort } from "./Sort";
export type PagedModels_InvoiceConfigChangeRecord_ = {
    content?: InvoiceConfigChangeRecord[];
    first?: boolean;
    last?: boolean;
    metadata?: PageMetadata;
    number?: number;
    numberOfElements?: number;
    size?: number;
    sort?: Sort;
    totalElements?: number;
    totalPages?: number;
};
