// tslint:disable
export type InventoryDTO = {
    availableToPromiseTotal?: number;
    defaultFacility?: string;
    facilityId?: string;
    facilityName?: string;
    partyId?: string;
    productId?: string;
    productStoreId?: string;
    safeToPromiseTotal?: number;
    showInvOrNo?: string;
    statusId?: string;
};

export type IProductInfosDTO = {
    productId?: string,
    productName?: string,
    availableTotalQuantity?: number
    type?: string
    attrs: IProductAttribute[]
}

export type IProductItemInfoDTO = {
    storeId?: string,
    storeName?: string,
    productInfos?: IProductInfosDTO[]
}

export type IProductInfoDTO = {
    storeName?: string,
    productInfoList: IProductInfosDTO[]
}

export interface IProductAttribute {
    attrName?: string;
    attrType?: string;
    attrValue?: string;
    productId?: string;
  }
