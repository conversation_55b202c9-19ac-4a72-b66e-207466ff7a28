// tslint:disable
/**
 * 标名属性配置新增、修改接口入参
 */
export type CategoryAttrConfigDTO = {
    /**
     * 属性名称
     */
    attrName?: string;
    /**
     * 属性类型
     */
    attrType?: string;
    /**
     * 属性单位
     */
    attrUnit?: string;
    /**
     * 属性值
     */
    attrValue?: string;
    /**
     * 标名Id
     */
    categoryId?: string;
    /**
     * 标名
     */
    categoryName?: string;
    /**
     * 创建人
     */
    createdBy?: string;
    /**
     * 创建时间
     */
    createdDate?: string;
    /**
     * 属性Id
     */
    id?: string;
    /**
     * 是否在维修厂端显示
     */
    isCategoryShow?: string;
    /**
     * 是否必填
     */
    isRequired?: string;
    /**
     * 更新人
     */
    updatedBy?: string;
    /**
     * 更新时间
     */
    updatedDate?: string;
};
