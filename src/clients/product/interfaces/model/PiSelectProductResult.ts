// tslint:disable
/**
 * PI查询商品返回结果
 */
export type PiSelectProductResult = {
    /**
     * basicUnit
     */
    basicUnit?: string;
    /**
     * 商家编码(内部编码)
     */
    internalName?: string;
    /**
     * 原厂零件号
     */
    originalPartsNumber?: string;
    /**
     * 品牌ID
     */
    partBrandId?: string;
    /**
     * 零件号
     */
    partNumber?: string;
    /**
     * 车型名称
     */
    productGroupNumber?: string;
    /**
     * 商品名称
     */
    productName?: string;
    /**
     * 店铺ID
     */
    productStoreId?: string;
    /**
     * 返回码
     */
    resultCode?: string;
    /**
     * 返回信息
     */
    resultMessage?: string;
};
