// tslint:disable
export type SearchEngineProductBatchParam = {
    /**
     * 业务分类
     */
    baseCategory?: string;
    /**
     * 公司ID
     */
    companyId?: string;
    /**
     * 审核状态 PASS_AUDIT：审核通过，DENY_AUDIT：审核不通过，WAIT_AUDIT：待审核
     */
    generalStatus?: "PASS_AUDIT" | "DENY_AUDIT" | "WAIT_AUDIT";
    /**
     * 所在地区
     */
    geoId?: string;
    /**
     * 是否查询统计数据 Y为统计，其余则不统计
     */
    isCount?: "Y";
    /**
     * 关键字
     */
    keywords?: string[];
    /**
     * 关键词类型
     */
    keywordsType?: string[];
    /**
     * 排序方式 PRICE_ASC：价格升序，PRICE_DESC：价格降序，SALECOUNT_ASC：销量升序，SALECOUNT_DESC：销量降序
     */
    sort?: "PRICE_ASC" | "PRICE_DESC" | "SALECOUNT_ASC" | "SALECOUNT_DESC";
    /**
     * 上架状态 PRODUCT_ADDED：上架，PRODUCT_SHELVES：下架，PRODUCT_INIT：新建
     */
    statusId?: "PRODUCT_ADDED" | "PRODUCT_SHELVES" | "PRODUCT_INIT";
    /**
     * 允许查询的店铺列表，与storeNotIn存在关联时应该是取交集
     */
    storeIn?: string[];
    /**
     * 用户ID
     */
    userLoginId?: string;
};
