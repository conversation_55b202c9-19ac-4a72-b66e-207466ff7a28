// tslint:disable
import { ProductAttribute } from "./ProductAttribute";
import { ProductBaseCatagory } from "./ProductBaseCatagory";
import { BiddingSort } from "./BiddingSort";
import { Brand } from "./Brand";
import { ProductCalculatedInfo } from "./ProductCalculatedInfo";
import { ProductInventory } from "./ProductInventory";
import { OriginalParts } from "./OriginalParts";
import { ProductPrice } from "./ProductPrice";
import { ProductAudit } from "./ProductAudit";
import { ProductCategoryV2 } from "./ProductCategoryV2";
import { Product } from "./Product";
import { ProductVehicleInfo } from "./ProductVehicleInfo";
import { ProductResource } from "./ProductResource";
import { ProductStore } from "./ProductStore";
import { ThirdPlatInfo } from "./ThirdPlatInfo";
/**
 * 商品信息组合类，包括基本信息对象，价格对象，库存对象，品牌对象，店铺对象，原厂零件对象，对应的属性的列表，对应的资源列表
 */
export type ProductFull = {
    /**
     * 商品对应的属性
     */
    attrs?: ProductAttribute[];
    /**
     * 基础业务分类
     */
    baseCatagory?: ProductBaseCatagory;
    /**
     * 业务分类列表
     */
    baseCatagorys?: ProductBaseCatagory[];
    /**
     * 竞价排序对象
     */
    biddingSort?: BiddingSort;
    /**
     * 品牌
     */
    brand?: Brand;
    /**
     * 商品查看及购买统计
     */
    calculateInfo?: ProductCalculatedInfo;
    /**
     * 库存
     */
    inventory?: ProductInventory[];
    /**
     * 商品对应的原厂零件信息
     */
    originalParts?: OriginalParts;
    /**
     * 价格
     */
    price?: ProductPrice;
    /**
     * 常规件商品审核信息
     */
    productAuditInfo?: ProductAudit;
    /**
     * 商品分类
     */
    productCategory?: ProductCategoryV2[];
    /**
     * 商品ID
     */
    productId?: string;
    /**
     * 商品基本信息
     */
    productInfo?: Product;
    /**
     * 车型列表
     */
    productVehicle?: ProductVehicleInfo[];
    /**
     * 商品对应的图片，富文本等资源
     */
    resouces?: ProductResource[];
    /**
     * 销售车型ids
     */
    salesStyleIds?: string[];
    /**
     * 店铺
     */
    store?: ProductStore;
    /**
     * 第三方商品信息
     */
    thirdPlatInfo?: ThirdPlatInfo;
    /**
     * 新标名信息
     */
    standardName?: IStandardName
};

export interface IStandardName {
    standardName?: string,
    categoryCode?: string
}