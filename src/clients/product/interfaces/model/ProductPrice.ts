// tslint:disable
/**
 * 商品价格
 */
export type ProductPrice = {
    /**
     * 卖家税后价格
     */
    atPrice?: number;
    /**
     * 卖家税前价格
     */
    btPrice?: number;
    /**
     * 买家税后价格
     */
    buyerAtPrice?: number;
    /**
     * 买家税前价格
     */
    buyerBtPrice?: number;
    /**
     * 买家默认价格
     */
    buyerPrice?: number;
    /**
     * 默认对接价格
     */
    defaultPrice?: number;
    /**
     * 到店含税价
     */
    finalAtPrice?: number;
    /**
     * 到店不含税价
     */
    finalBtPrice?: number;
    /**
     * 卖家默认价
     */
    price?: number;
    /**
     * 4s价格
     */
    price4s?: number;
    /**
     * 默认对接价格2
     */
    priceGroup2?: number;
    /**
     * 默认对接价格3
     */
    priceGroup3?: number;
    /**
     * 默认对接价格4
     */
    priceGroup4?: number;
    /**
     * 默认对接价格5
     */
    priceGroup5?: number;
    /**
     * 价格策略类型
     */
    priceStrategyType?: string;
    /**
     * 税率
     */
    taxRate?: number;
};
