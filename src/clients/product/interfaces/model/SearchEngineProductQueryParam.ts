// tslint:disable
import { StandardAttribute } from "./StandardAttribute";
export type SearchEngineProductQueryParam = {
    /**
     * 业务分类
     */
    baseCategory?: string;
    /**
     * 未被过滤的博世授权经销商列表，获取全部经销商后与storeIn取交集
     */
    boschStoreIds?: string[];
    /**
     * 品质
     */
    branQuality?: string;
    /**
     * 品牌
     */
    brandId?: string;
    /**
     * 容量
     */
    capacity?: string;
    /**
     * 公司ID
     */
    companyId?: string;
    /**
     * 成分
     */
    constituent?: string;
    /**
     * 是否支持对项发票
     */
    enabledItemInvoice?: boolean;
    endDate?: string;
    /**
     * 一级分类
     */
    firstCategory?: string;
    /**
     * 只模糊查询零件号，不带出品牌件关系
     */
    fuzzyQueryPartNo?: string;
    /**
     * 商品名称
     */
    fuzzyQueryProductName?: string;
    /**
     * 审核状态 PASS_AUDIT：审核通过，DENY_AUDIT：审核不通过，WAIT_AUDIT：待审核
     */
    generalStatus?: "PASS_AUDIT" | "DENY_AUDIT" | "WAIT_AUDIT";
    /**
     * 所在地区
     */
    geoId?: string;
    /**
     * geoId-companyId，用于品牌品类授权区域过滤
     */
    geoIds?: string[];
    /**
     * 级别
     */
    grade?: string;
    /**
     * 是否有库存 Y为有库存 N为无库存
     */
    hasInventory?: "Y" | "N";
    /**
     * 商家编码
     */
    internalName?: string;
    /**
     * 是否查询统计数据 Y为统计，其余则不统计
     */
    isCount?: "Y";
    /**
     * 是否原厂 Y为原厂件 N为品牌件
     */
    isOEM?: "Y" | "N";
    /**
     * 关键字
     */
    keywords?: string;
    /**
     * 离我最近，Y是，N否
     */
    nearest?: "Y" | "N";
    /**
     * 当前页码
     */
    pageNum?: number;
    /**
     * 页码大小
     */
    pageSize?: number;
    /**
     * 零件号列表
     */
    partsList?: string[];
    /**
     * 零件号
     */
    partsNo?: string;
    /**
     * 预分析结果
     */
    preAnalyzedResult?: {};
    /**
     * 需要过滤掉商品ID列表
     */
    productIdNotIn?: string[];
    /**
     * 商品名称
     */
    productName?: string;
    /**
     * 订货商品关联的订货协议ID列表
     */
    purchaseAgreementIds?: string[];
    /**
     * 优先排序 如{“brandId:BMW”},表示结果中宝马品牌优先排序
     */
    rank?: {};
    /**
     * 销售车型id列表
     */
    salesStyleIds?: string[];
    /**
     * 二级分类
     */
    secondCategory?: string;
    /**
     * 排序方式 COMPREHENSIVE_SORT：综合排序, PRICE_ASC：价格升序，PRICE_DESC：价格降序，SALECOUNT_ASC：销量升序，SALECOUNT_DESC：销量降序;LAST_UPDATED_DATE_DESC，更新时间降序
     */
    sort?: "COMPREHENSIVE_SORT" | "PRICE_ASC" | "PRICE_DESC" | "SALECOUNT_ASC" | "SALECOUNT_DESC" | "LAST_UPDATED_DATE_DESC";
    /**
     * 特卖商品 Y：是特卖商品，否则搜全部
     */
    specialSaleStatus?: "Y" | "N";
    /**
     * 标名
     */
    standName?: string;
    /**
     * 标准属性，常规件商城根据标准属性过滤
     */
    standardAttributes?: StandardAttribute[];
    startDate?: string;
    /**
     * 上架状态 PRODUCT_ADDED：上架，PRODUCT_SHELVES：下架，PRODUCT_INIT：新建
     */
    statusId?: "PRODUCT_ADDED" | "PRODUCT_SHELVES" | "PRODUCT_INIT";
    /**
     * 只看订货，Y是，N否
     */
    stockProduct?: "Y" | "N";
    /**
     * 允许查询的店铺列表，与storeNotIn存在关联时应该是取交集
     */
    storeIn?: string[];
    /**
     * 需要过滤掉的店铺列表，与storeNotIn存在关联时应该是取交集
     */
    storeNotIn?: string[];
    /**
     * vin查询带出轮胎标准属性，外层list是or关系，里层list是and关系
     */
    tyreAttributes?: StandardAttribute[][];
    /**
     * 用户ID
     */
    userLoginId?: string;
    /**
     * 粘度
     */
    viscosity?: string;
    standardNameCode?: string[];
    categoryCode?: string[];
    /**
     * APP
     */
    platform?: string;
};

export interface IGetProductListReq {
  isFilterOrderGoods?: boolean;
  keywords?: string;
  platform?: 'PLATFORM_APP' | 'PLATFORM_PC' | 'APP';
  hasInventory?: "Y" | "N";
  storeIn?: string[];
  pageNum?:number;
  pageSize?: number;
  sort?: "COMPREHENSIVE_SORT" | "PRICE_ASC" | "PRICE_DESC" | "SALECOUNT_ASC" | "SALECOUNT_DESC" | "LAST_UPDATED_DATE_DESC";
  userLoginId?: string;
  companyId?: string;
  userAddressGeoId?: string;
}
