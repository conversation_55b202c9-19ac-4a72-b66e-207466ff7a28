// tslint:disable
/**
 * 销售车型信息
 */
export type VehicleSalesModel = {
    ac?: string;
    autoAc?: string;
    brandCode?: string;
    brandName?: string;
    country?: string;
    cylinderArrangement?: string;
    cylinderVolume?: string;
    cylinders?: string;
    displacement?: string;
    doors?: string;
    driveMode?: string;
    emissionStandard?: string;
    engine?: string;
    engineLocation?: string;
    frontBrake?: string;
    frontFoglamp?: string;
    frontRim?: string;
    frontTyre?: string;
    fuelGrade?: string;
    fuelType?: string;
    gearNumber?: string;
    guidingPrice?: string;
    hidHeadlamp?: string;
    horsepower?: string;
    idlingYear?: string;
    induction?: string;
    ksSalesStyleId?: string;
    listingMonth?: string;
    listingYear?: string;
    manufacturer?: string;
    model?: string;
    panoramicSunroof?: string;
    powerKw?: string;
    powerSteering?: string;
    producedYear?: string;
    productionStatus?: string;
    rearBrake?: string;
    rearRim?: string;
    rearTyre?: string;
    rearWiper?: string;
    rimsMaterial?: string;
    salesStyle?: string;
    salesStyleId?: string;
    seats?: string;
    series?: string;
    spareWheel?: string;
    sunroof?: string;
    transmissionDesc?: string;
    transmissionType?: string;
    valvesPerCylinder?: string;
    vehicleAttributes?: string;
    vehicleType?: string;
    wheelbase?: string;
    yearStyle?: string;
};
