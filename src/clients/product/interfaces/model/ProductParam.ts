// tslint:disable
import { FacilityParam } from "./FacilityParam";
import { PriceParam } from "./PriceParam";
import { ValidateResult } from "./ValidateResult";
/**
 * 商品对外接口、商品（价格、库存）批量更新时的参数封装类
 */
export type ProductParam = {
    acea?: string;
    api?: string;
    authentication?: string;
    /**
     * 品牌类型BrandTypeEnum:原厂、原厂配套、自主品牌
     */
    brandType?: string;
    capacity?: string;
    constituent?: string;
    /**
     * 操作类型CurdTypeEnum,NOTE:现在只有传DELETE时才有意义，传其他值会忽略
     */
    crudType?: string;
    /**
     * 商家内部skuid
     */
    customerProductId?: string;
    /**
     * 商品描述
     */
    description?: string;
    /**
     * 仓库库存
     */
    facility?: FacilityParam[];
    fieldPositions?: {};
    grade?: string;
    hths?: string;
    ilsac?: string;
    /**
     * 商家编码
     */
    internalName?: string;
    model?: string;
    norm?: string;
    /**
     * 原厂品牌ID
     */
    oeBrandId?: string;
    /**
     * 原厂品牌
     */
    oeBrandName?: string;
    /**
     * 原厂零件号
     */
    oePartsNo?: string;
    origin?: string;
    partType?: string;
    /**
     * 零件号
     */
    partsNo?: string;
    /**
     * 价格
     */
    price?: PriceParam[];
    price4s?: string;
    /**
     * 品牌
     */
    productBrandName?: string;
    /**
     * 品质
     */
    productBrandQualityId?: string;
    productCategory?: string;
    /**
     * 来源
     */
    productCreateSource?: string;
    /**
     * 商品id
     */
    productId?: string;
    /**
     * 商品等级ProductLevelEnum：全新件、下线件、拆车件
     */
    productLevel?: string;
    /**
     * 零件名称
     */
    productName?: string;
    /**
     * 商品状态ProductStatusEnum：新建、未上架、已上架
     */
    productStatus?: string;
    /**
     * 商品类型，实际应该用productPartType更准确,其他地方productType表示货源类型
     */
    productType?: string;
    series?: string;
    sportViscosity?: string;
    standardName?: string;
    standardPartsNo?: string;
    suite?: boolean;
    temp?: string;
    validateResult?: ValidateResult;
    /**
     * 适用车型
     */
    vehicles?: string;
    viscosity?: string;
};
