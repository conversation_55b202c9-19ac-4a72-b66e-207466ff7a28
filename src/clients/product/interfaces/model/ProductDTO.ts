// tslint:disable
export type ProductDTO = {
    /**
     * 金额uomTypeId
     */
    amountUomTypeId?: string;
    /**
     * 是否自动创建关键字
     */
    autoCreateKeywords?: boolean;
    /**
     * 物料清单
     */
    billOfMaterialLevel?: number;
    /**
     * 品牌英文名
     */
    brandName?: string;
    /**
     * 品牌品质
     */
    brandQualityId?: string;
    /**
     * 是否有运费
     */
    chargeShipping?: string;
    /**
     * 评论
     */
    comments?: string;
    /**
     * 配置Id
     */
    configId?: string;
    /**
     * 创建者
     */
    createdByUserLogin?: string;
    /**
     * 创建时间
     */
    createdDate?: string;
    /**
     * 创建时间
     */
    createdStamp?: string;
    /**
     * 创建时间
     */
    createdTxStamp?: string;
    /**
     * 客户产品Id
     */
    customerProductId?: string;
    /**
     * 默认发货箱TypeId
     */
    defaultShipmentBoxTypeId?: string;
    /**
     * 深度uomId
     */
    depthUomId?: string;
    /**
     * 商品描述信息
     */
    description?: string;
    /**
     * 详细图片url
     */
    detailImageUrl?: string;
    /**
     * 细节屏幕
     */
    detailScreen?: string;
    /**
     * 直径uomId
     */
    diameterUomId?: string;
    /**
     * 仓库Id
     */
    facilityId?: string;
    /**
     * 定额
     */
    fixedAmount?: number;
    /**
     * 高度uomId
     */
    heightUomId?: string;
    /**
     * 是否包装
     */
    inShippingBox?: string;
    /**
     * 是否包含促销活动
     */
    includeInPromotions?: boolean;
    /**
     * 商家编码
     */
    internalName?: string;
    /**
     * 商家编码
     */
    internalNameTrim?: string;
    /**
     * 介绍日期
     */
    introductionDate?: string;
    /**
     * 库存消息
     */
    inventoryMessage?: string;
    /**
     * 是否是变化的
     */
    isVariant?: boolean;
    /**
     * 是否是虚拟的
     */
    isVirtual?: boolean;
    /**
     * 大图像url
     */
    largeImageUrl?: string;
    /**
     * 最后修改者
     */
    lastModifiedByUserLogin?: string;
    /**
     * 最后修改日期
     */
    lastModifiedDate?: string;
    /**
     * 更新时间
     */
    lastUpdatedStamp?: string;
    /**
     * 更新时间
     */
    lastUpdatedTxStamp?: string;
    /**
     * 商品描述信息
     */
    longDescription?: string;
    /**
     * lotId写入
     */
    lotIdFilledIn?: string;
    /**
     * 制造商partId
     */
    manufacturerPartyId?: string;
    /**
     * 中等图像url
     */
    mediumImageUrl?: string;
    /**
     * 订单数量
     */
    orderDecimalQuantity?: string;
    /**
     * 原始地址Id
     */
    originGeoId?: string;
    /**
     * 原始图片url
     */
    originalImageUrl?: string;
    /**
     * 零件号
     */
    partNumber?: string;
    /**
     * 零件类型
     */
    partType?: string;
    /**
     * 零件号
     */
    partsnoTrim?: string;
    /**
     * 碎片包含
     */
    piecesIncluded?: number;
    /**
     * 价格明细文字
     */
    priceDetailText?: string;
    /**
     * 主要产品类别Id
     */
    primaryProductCategoryId?: string;
    /**
     * 商品新增来源
     */
    productCreateSource?: string;
    /**
     * 商品深度
     */
    productDepth?: number;
    /**
     * 商品直径
     */
    productDiameter?: number;
    /**
     * 商品高度
     */
    productHeight?: number;
    /**
     * 商品Id
     */
    productId?: string;
    /**
     * 产品型号
     */
    productModel?: string;
    /**
     * 商品名称
     */
    productName?: string;
    /**
     * 商品等级
     */
    productRating?: number;
    /**
     * 店铺id
     */
    productStoreId?: string;
    /**
     * 商品类型Id
     */
    productTypeId?: string;
    /**
     * 商品重量
     */
    productWeight?: number;
    /**
     * 商品宽度
     */
    productWidth?: number;
    /**
     * 数量包含
     */
    quantityIncluded?: number;
    /**
     * 数量uomId
     */
    quantityUomId?: string;
    /**
     * 等级类型枚举
     */
    ratingTypeEnum?: string;
    /**
     * 发布日期
     */
    releaseDate?: string;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 替换零件号
     */
    replacePartsNum?: string;
    /**
     * 需要金额
     */
    requireAmount?: string;
    /**
     * 需要库存
     */
    requireInventory?: string;
    /**
     * 要求方法枚举Id
     */
    requirementMethodEnumId?: string;
    /**
     * 保留最大
     */
    reservMaxPersons?: number;
    /**
     * 保留
     */
    reserve?: string;
    /**
     * 是否可回收
     */
    returnable?: string;
    /**
     * 无法销售
     */
    salesDiscWhenNotAvail?: string;
    /**
     * 销售终止日期
     */
    salesDiscontinuationDate?: string;
    /**
     * 运输深度
     */
    shippingDepth?: number;
    /**
     * 运输高度
     */
    shippingHeight?: number;
    /**
     * 运输宽度
     */
    shippingWidth?: number;
    /**
     * 小图片url
     */
    smallImageUrl?: string;
    /**
     * 规格
     */
    specifications?: string;
    /**
     * 状态Id
     */
    statusId?: string;
    /**
     * 支持停止日期
     */
    supportDiscontinuationDate?: string;
    /**
     * 是否应税
     */
    taxable?: string;
    /**
     * 虚拟变化方法枚举
     */
    virtualVariantMethodEnum?: string;
    /**
     * 重量
     */
    weight?: number;
    /**
     * 重量uomTypeId
     */
    weightUomId?: string;
    /**
     * 宽度uomId
     */
    widthUomId?: string;
};
