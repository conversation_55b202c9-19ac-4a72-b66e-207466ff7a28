// tslint:disable
import { ProductParam } from "./ProductParam";
/**
 * 用于商品批量更新时接收参数。批量更新包括：对外接口对接、excel导入、页面批量导入
 */
export type ProductBatchRequest = {
    /**
     * 是否格式化校验通过
     */
    formatPass?: boolean;
    /**
     * 验证不通过数据
     */
    invalidProductParams?: ProductParam[];
    /**
     * 原始入参数据
     */
    productParams?: ProductParam[];
    /**
     * 店铺id
     */
    productStoreId?: string;
    /**
     * redis key 如果存在，则该对象从redis获取
     */
    redisKey?: string;
    /**
     * 用户id
     */
    userLoginId?: string;
    /**
     * 用户名称
     */
    userLoginName?: string;
};
