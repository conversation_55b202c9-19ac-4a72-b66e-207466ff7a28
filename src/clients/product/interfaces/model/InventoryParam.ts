// tslint:disable
export type InventoryParam = {
    /**
     * 品牌id
     */
    brandId?: string;
    /**
     * 仓库id
     */
    facilityId?: string;
    /**
     * 是否有库存,为true时有库存，为false时无库存
     */
    hasInventory?: boolean;
    /**
     * 商家编码
     */
    internalName?: string;
    /**
     * 库存是否低于安全库存值，为true时才会作为过滤条件
     */
    lessThanSafeInventory?: boolean;
    /**
     * 最大库存
     */
    maxNum?: number;
    /**
     * 最小库存
     */
    minNum?: number;
    /**
     * 第几页
     */
    pageNum?: number;
    /**
     * 每页显示条数
     */
    pageSize?: number;
    /**
     * 零件号
     */
    partsNo?: string;
    /**
     * 店铺id
     */
    storeId?: string;
};
