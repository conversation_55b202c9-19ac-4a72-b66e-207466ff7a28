export interface IProductMallFilterAddress {
  provinceGeoId: string;          // 省geoId
  cityGeoId: string;              // 市geoId
  districtGeoId: string;          // 县/区geoId
}

type FilterEnum =
  'SUPPLIER_AREA_AUTH' |            // 供应商报价区域黑白名单
  'SUPPLIER_GARAGE_STORE_AUTH' |    // 供应商&维修厂，店铺黑白名单/店铺禁用
  'SUPPLIER_BRAND_CATEGORY_AUTH' |  // 供应商：品牌分类黑白名单
  'PRODUCT_INVENTORY' |             // 过滤库存为0商品
  'PRODUCT_STATUS' |                // 只保留上架的商品，其他状态过滤
  'PRODUCT_AUDIT';                  // 只保留审核通过的商品，其他状态过滤

export interface IProductMallFilterPayload {
  userLoginId: string;
  garageCompanyId: string;
  productIds: string[];
  filterEnums?: FilterEnum[];
  userAddress?: IProductMallFilterAddress;
}
