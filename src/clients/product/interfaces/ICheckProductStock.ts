export interface ICheckProductStockPayloadDTO {
  demandId: string; // 询价单号
  quotationProducts: IQuotationProductItem[]; // 报价结果id
}

interface IQuotationProductItem {
  defaultFacilityId: string; // 仓库id
  quantity: number; // 数量
  quotationProductId: string; // 报价结果id
}

export interface ICheckProductStockResponseDTO {
  demandId: string; // 询价单号
  quotationProducts: IQuotationProduct[]; // 报价条目
  remark: string; // 备注
  state: string; // 该数据是否有效状态，Y表示有效，N表示无效
  statusId: string; // 询价单状态
}

interface IQuotationProduct {
  productId: string; // 商品ID
  quotationProductId: string; // 报价结果ID
  source: string; // 报价来源 系统报价：AUTO；人工报价：MANUALLY
  /**
   * 报价条目状态 
   * SALEABLE-可销售; DEMAND_EXPIRED-询价单已过期; DEMAND_CLOSED-询价单已关闭; NOT_FOUND-资源不存在; PRODUCT_OFF-已下架; OUT_OF_STOCK-库存不足; RE_DECODE-已重新译码; NOT_FULL_SALE-不满足整箱销售; BELOW_MINIMUM_SALES_QUANTITY-低于最小销售数量
   * 详细参见: com.casstime.ec.cloud.quote.enums.QuotationProductStateEnum
   */
  state: string;
  stateDesc: string; // 报价条目状态描述
  storeId: string; // 店铺ID
}