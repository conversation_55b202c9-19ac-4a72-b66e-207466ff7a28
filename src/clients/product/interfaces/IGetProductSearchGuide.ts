export interface IGetProductSearchGuideRequest {
  userLoginId: string;
  companyId: string;
  keyword: string;
  origin: string;
}
export interface IGetProductSearchGuideBtn {
  title?: string;  // 按钮文案'),
  url?: string;  // '路由链接')
}
export interface IGetProductSearchGuideResponse {
  titleBoldTexts?: string[];  // '标题加粗部分文案'),
  descBoldTexts?: string[];  // '描述加粗部分文案'),
  btns?: IGetProductSearchGuideBtn[];  // '引导提示跳转按钮集合'),
  desc?: string;  // '描述'),
  image?: string;  // '图标，没有配置时展示高保默认图标'),
  title?: string;  // 完整的标题'),
  type?: string;  // '场景'),
}