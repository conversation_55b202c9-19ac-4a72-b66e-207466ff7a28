// tslint:disable
export type ProductStepPrice = {
    /**
     * 阶梯-卖家税后价格
     */
    atPrice?: number;
    /**
     * 阶梯-卖家税前价格
     */
    btPrice?: number;
    /**
     * 阶梯-买家税后价格
     */
    buyerAtPrice?: number;
    /**
     * 阶梯-买家税前价格
     */
    buyerBtPrice?: number;
    /**
     * 阶梯-买家默认价格
     */
    buyerPrice?: number;
    /**
     * 默认对接价格
     */
    defaultPrice?: number;
    /**
     * 到店含税价
     */
    finalAtPrice?: number;
    /**
     * 到店不含税价
     */
    finalBtPrice?: number;
    /**
     * 阶梯-起始数量
     */
    fromNum?: number;
    /**
     * 开票类型
     */
    openInvoiceType?: string;
    /**
     * 阶梯-卖家默认价
     */
    price?: number;
    /**
     * 序号
     */
    sequence?: number;
    /**
     * 阶梯-终止数量
     */
    thruNum?: number;
};
