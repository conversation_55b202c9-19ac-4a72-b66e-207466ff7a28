export interface ICategoryPayloadDTO {
  categoryId?: number; // 分类id
  needParent?: string; // 需要上级分类数据不 'Y'
  categoryCodes?: string[]; // 分类code数组
}

export interface IParentFirstItemDTO {
  id?: number; // 一级分类id
  bizCatHeadId?: number;
  itemName?: string; // 一级分类名称
  parentId?: number; // 父级分类id
}

export interface IPparentItemDTO {
  id?: number; // 二级分类id
  bizCatHeadId?: number;
  itemName?: string; // 二级分类名称
  parentId?: number; // 父级分类id
  parentItem?: IParentFirstItemDTO; // 上级分类数据
}

export interface ICategoryResDTO {
  id?: number; // 三级分类id
  bizCatHeadId?: number;
  itemName?: string; // 三级分类名称
  sourceType?: string; // 三级分类类型
  standName?: string; // 三级分类标准名称
  categoryCode?: string; // 三级分类code
  parentId?: number; // 父级分类id
  parentItem?: IPparentItemDTO; // 上级分类数据
}
