import { ProductFull } from './model';

export interface IProductFull {
  [key: string]: ProductFull
}

export interface IProductListIem {
  productId: string;
  productName: string;
  internalName: string;
  productPartNum: string;
  productBrandId: string;
  productBrandName: string;
  isOEM: string;
  statusId: string;
  inventory: IInventory[];
  storeId: string;
  originalPartNum: string[];
  productImgUri: string;
  productVehicle?: string;
  price: number;
  isShowGift: boolean;
  isShowCoupon: boolean;
  isShowDiscount: boolean;
  isAnniversary?: boolean;
  hasFollowed?: boolean;
}

export interface IInventory {
  facilityName: string;
  facilityId: string;
  inventoryValue: string;
  geoId: string;
  availableToPromiseTotal: number;  // 库存数
}
