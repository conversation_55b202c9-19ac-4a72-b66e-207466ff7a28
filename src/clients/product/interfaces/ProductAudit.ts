// tslint:disable
import { Serializable } from "./Serializable";
/**
 * 常规件商城审核实体类
 */
export type ProductAudit = {
    /**
     * 审核id
     */
    auditId?: string;
    /**
     * 审核原因
     */
    auditReason?: string;
    /**
     * 审核状态
     */
    auditStatus?: "WAIT_AUDIT" | "PASS_AUDIT" | "DENY_AUDIT";
    /**
     * 创建人
     */
    createdBy?: string;
    /**
     * 创建时间
     */
    createdDate?: string;
    /**
     * 查询返回商品审核状态(用于和auditStatus比对)
     */
    generalStatus?: string;
    id?: Serializable;
    /**
     * 创建时间
     */
    lastUpdateCreatedBy?: string;
    /**
     * 最后修改时间
     */
    lastUpdateCreatedDate?: string;
    /**
     * 商品id
     */
    productId?: string;
};
