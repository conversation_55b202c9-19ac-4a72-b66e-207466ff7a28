export interface IShoppingMallPayloadSchemaDTO {
  /**
   * 页码
   */
  pageNum?: number;
  /**
   * 分页大小
   */
  pageSize?: number;
  /**
   * 排序类型
   */
  orderType?: string;
  /**
   * 是否只看订货
   */
  isFilterOrderGoods?: boolean;
  /**
   * 收货地址geoId(街道)
   */
  userAddressGeoId?: string;
  /**
   * 市级别geoId
   */
  cityGeoId?: string;
  /**
   * 收货地址ID
   */
  addressId?: string;
  /**
   * 搜索关键字,支持vin码搜索
   */
  keywords: string;
  /**
   * 一级分类
   */
  firstCategory?: string;
  /**
   * 二级分类
   */
  secondCategory?: string;
  /**
   * 品牌Id
   */
  brandId?: string;
  /**
   * 筛选条件属性值入参集合
   */
  standardAttributes?: IStandardAttributesItemDTO[];
  /**
   * 品牌名称
   */
  brandCode?: string;
  /**
   * 销售车型ID
   */
  salesStyleId?: string;
  /**
   * 制造商
   */
  manufacturer?: string;
  /**
   * 车型
   */
  model?: string;
  /**
   * 销售款
   */
  salesStyle?: string;
  /**
   * 车系
   */
  series?: string;
  /**
   * 年款
   */
  yearStyle?: string;
  /**
   * 轮胎数据标识
   */
  tyreFlag?: string;
}

export interface IStandardAttributesItemDTO {
  /**
   * 属性名
   */
  attrName: string;
  /**
   * 属性值
   */
  attrValue: string;
}

export interface IVehicleDTO {
  /**
   * 品牌名称
   */
  brand?: string;
  /**
   * 销售车型ID
   */
  ksSalesStyleId?: string;
  /**
   * 制造商
   */
  manufacturer?: string;
  /**
   * 车型
   */
  model?: string;
  /**
   * 销售款
   */
  salesStyle?: string;
  /**
   * 车系
   */
  series?: string;
  /**
   * 年款
   */
  yearStyle?: string;
  /**
   * 轮胎数据标识
   */
  tyreFlag?: string;
}

export interface IVehicleResponseDTO {
  /**
   * 品牌名称
   */
  partsList?: string[];
  /**
   * 销售车型ID
   */
  ksSalesStyleIds?: string[];
  /**
   * 制造商
   */
  tyreSizes?: string[];
}

export interface IShoppingMallParamsDTO {
  /**
   * 收货地址geoId(街道)
   */
  userAddressGeoId: string;
  /**
   * 市级别geoId
   */
  cityGeoId?: string;
  /**
   * 收货地址ID
   */
  addressId?: string;
  /**
   * 公司id
   */
  companyId: string;
  /**
   * 用户id
   */
  userLoginId: string;
}

export interface IAttributesItem {
  attrName?: string;
  attrValue?: string;
  attrValues?: string[];
}