export interface IProductVehicle {
  productId: string;
  vehicleInfo: string;
}

export interface ICalculateInfo {
  productId: string;
  totalQuantityOrdered: number;
  totalTimesViewed: number;
  averageCustomerRating: number;
}

export interface IAttr {
  productId: string;
  attrName: string;
  attrValue: string;
  attrType: string;
}

export interface IResouce {
  resouceType: string;
  objectInfo: string;
}

export interface IBrand {
  brandCode: string;
  brandName: string;
  isOEM: string;
  quality: string;
}

export interface IStore {
  productStoreId: string;
  storeName: string;
  companyName: string;
  title: string;
  subtitle: string;
  homePage: string;
  enableTmsShip: string;
  customerService?: any;
}

export interface IInventory {
  facilityName: string;
  facilityId: string;
  inventoryValue: string;
}

export interface IPrice {
  taxRate: number;
  price: number;
  btPrice: number;
  atPrice: number;
  buyerPrice: number;
  buyerAtPrice: number;
  buyerBtPrice: number;
  defaultPrice: number;
}

export interface IProductInfo {
  productId: string;
  primaryProductCategoryId: string;
  internalName: string;
  internalNameTrim: string;
  brandName: string;
  productName: string;
  statusId: string;
  productStoreId: string;
  partNumber: string;
  partsnoTrim: string;
  partType: string;
  description: string;
  longDescription: string;
  specifications: string;
  smallImageUrl?: string;
}

export interface IOriginalParts {
  originalPartsNo?: string;
  brandCode?: string;
  brandNameCN?: string;
  partsName?: string;
  price4s?: number;
}

export interface IProductDetail {
  productId: string;
  productInfo: IProductInfo;
  price?: IPrice;
  inventory?: IInventory[];
  store?: IStore;
  brand?: IBrand;
  resouces?: IResouce[];
  attrs?: IAttr[];
  calculateInfo?: ICalculateInfo;
  productVehicle?: IProductVehicle[];
  originalParts?: IOriginalParts;
}

export interface IProductOriginalParts {
  [productId: string]: IProductOriginalPartsItem[];
}

export interface IProductOriginalPartsItem {
  productId: string;
  originalPartsNo: string;
  originalBrandId: string;
  originalBrandName: string;
}

export interface IProductFollowDTO {
  customerGroup?: boolean;  // 博世授权全供应商
  isFiltered?: boolean;     // 是否被过滤,true-被过滤 false-未被过滤
  isFollowed?: boolean;     // 是否关注,true-已关注 false-未关注
  productId?: string;       // 商品Id
}
