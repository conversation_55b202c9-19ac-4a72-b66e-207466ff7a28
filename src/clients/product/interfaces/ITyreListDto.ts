export interface ISearchEngineSpdFeatureDTO {
  featureCode?: string;
  featureName?: string;
  value?: string;
}

export interface ITyreListReqDTO {
  pageNum?: number;
  pageSize?: number;
  searchType?: string;
  keywords?: string;
  brandCode?: string;
  aeCodeTrim?: string;
  features?: ISearchEngineSpdFeatureDTO[];
  userGeoId?: string;
  cityId?: string;
  sortType?: string;
  userLoginId?: string;
  companyId?: string;
  isExcludeLabel?: boolean;
}

export interface ISpdParamDTO {
  id?: string; // spdId
  brandCode?: string;
  aeCode?: string;
}

export interface ITyreMerchantReqDTO {
  userLoginId?: string;
  companyId?: string;
  userGeoId?: string;
  sortType?: string;
  orderByType?: string;
  spdParams?: ISpdParamDTO[];
}

export interface ITyreListPageReqDTO {
  pageNum?: number;
  pageSize?: number;
  searchType?: string;
  keywords?: string;
  brandCode?: string;
  userLoginId?: string;
  companyId?: string;
  userGeoId?: string;
  aeCodeTrim?: string;
  features?: ISearchEngineSpdFeatureDTO[];
}

export interface ISearchEngineSpdPictureDTO {
  seqNum?: number;
  type?: string;
  typeCode?: string;
  url?: string;
}

export interface IProductSalesLimitDTO {
  type?: string;
  value?: number;
}

export interface IStepPricesDTO {
  sequence?: number;
  fromNum?: number;
  thruNum?: number;
  price?: number;
}

export interface IProductPriceDTO {
  productId?: string;
  priceType?: string;
  hasPriceTax?: boolean;
  price?: number;
  stepPrices?: IStepPricesDTO[];
}

export interface IProductInventoriesDTO {
  productId?: string;
  facilityId?: string;
  facilityName?: string;
  facilityGeoId?: string;
  facilityLongitude?: number;
  facilityLatitude?: number;
  facilityDistance?: number;
  inventoryValue?: number;
}

export interface ILabelDTO {
  label?: string;
  type?: string;
}

export interface ISecKillInfoDTO {
  placeId?: string;
  id?: string;
  sessionId?: string;
  totalNumber?: number;
}

export interface IPromotionInfoDTO {
  placeId?: string;
  id?: string;
}

export interface IGroupBuyInfoDTO {
  groupBuyId?: string;
}

export interface IProductActivityDTO {
  type?: string;
  secKillInfo?: ISecKillInfoDTO;
  promotionInfo?: IPromotionInfoDTO;
  groupBuyInfo?: IGroupBuyInfoDTO;
}

export interface IMerchantListDTO {
  spdId?: string;
  spdBrandCode?: string;
  spdAeCode?: string;
  productId?: string;
  productDisplayName?: string;
  productStatusId?: string;
  productSalesCount?: number | string;
  productName?: string;
  productNameExtend?: string;
  productDesc?: string;
  productSalesLimit?: IProductSalesLimitDTO;
  productPrice?: IProductPriceDTO;
  productInventories?: IProductInventoriesDTO[];
  productActivity?: IProductActivityDTO;
  storeId?: string;
  storeName?: string;
  labelList?: ILabelDTO[];
  weightPoint?: number;
}

export interface ISearchEngineSpdDTO {
  id?: string;
  brandCode?: string;
  brandName?: string;
  brandQualityCode?: string;
  brandQualityName?: string;
  aeCode?: string;
  aeCodeTrim?: string;
  aeName?: string;
  stdName?: string;
  stdNameCode?: string;
  categoryName?: string;
  categoryCode?: string;
  features?: ISearchEngineSpdFeatureDTO[];
  pictures?: ISearchEngineSpdPictureDTO[];
  merchantList?: IMerchantListDTO[];
}

export interface ISpdProductResultDTO {
  id?: string;
  brandCode?: string;
  aeCode?: string;
  products?: IMerchantListDTO[];
}

export interface INearbyBusinessesDTO {
  storeId?: string;
  storeName?: string;
  distance?: number;
  facilityName?: string;
}

export interface IKeywordsAnalyseResultDTO {
  keywords?: string;
  brandCodes?: string[];
  partsNumTrims?: string[];
  tyreSpecs?: string[];
}

export interface ITyreListResDTO {
  pageNum?: number;
  pageSize?: number;
  totalPages?: number;
  totalElements?: number;
  spds?: ISearchEngineSpdDTO[];
  nearbyBusinesses?: INearbyBusinessesDTO[];
  keywordsAnalyseResult?: IKeywordsAnalyseResultDTO;
}

export interface IUnitMapDTO {
  D?: string;
  M?: string;
  W?: string;
  Y?: string;
}

export interface ILabelDataDTO {
  [key: string]: ILabelDTO[];
}

export interface IAfterSalePolicysDTO {
  areaCode?: string;
  userLoginId: string;
  companyId: string;
  businessType: string;
  queryLines: unknown[];
}

export interface IPinkagesReqDTO {
  businessType: string;
  userLoginId: string;
  companyId: string;
  pinkageSkuList: unknown[];
}
