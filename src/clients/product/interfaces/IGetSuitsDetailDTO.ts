export interface IGetSuitsDetailPayload {
  /** 商品ID */
  productId: string;
  /** 版本号 */
  version?: string;
  /** 快照类型 */
  snapshotType?: string;
  /** 报价结果id */
  quotationProductId?: string;
}

export interface ISuiteImageItem {
  imageId?: number;
  imageIdx?: number;
  imageName?: string;
  imageTypeName?: string;
  imageTypeCode?: number;
  imageUrl?: string;
}

export interface IOeListItem {
  oeCode?: string;
  oeCodeTrim?: string;
  oeBrandCode?: string;
  oeBrandName?: string;
}

export interface IProductsItem {
  partName?: string;
  brandCode?: string;
  partCode?: string;
  partCodeTrim?: string;
  remark?: string;
  quantity?: number;
  mainFlag?: boolean;
  recommend?: boolean;
  oeList?: IOeListItem[];
  productImage?: ISuiteImageItem[];
}

export interface ISuiteDetail {
  requestId?: string;
  suiteId?: string;
  brandCode?: string;
  storeId?: string;
  suiteCode?: string;
  suiteName?: string;
  suiteTypeCode?: string;
  suiteTypeName?: string;
  dataSource?: string;
  remark?: string;
  suiteImage?: ISuiteImageItem[];
  products?: IProductsItem[];
}

export interface IProductResponseItem {
  productId?: string;
  version?: string;
  suiteDetail?: ISuiteDetail;
}