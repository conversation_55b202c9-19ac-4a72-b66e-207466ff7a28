export interface IAttributesItem {
  attrName?: string;
  attrValue?: string;
  attrValues?: string[];
}

export interface IRank {
  item?: string;
}

export interface ISearchEngineProductSchemaDTO {
  /** 属性筛选：标准属性 */
  attributes?: IAttributesItem[];
  /** 品牌筛选：品牌id */
  brandId?: string;
  /** 识别出有选中品牌，页面切回全部需要传页面全部品牌 */
  brandIds?: string[];
  /** 公司id */
  companyId?: string;
  /** 小狮过滤：是否支持小狮物流 */
  enableLogistics?: boolean;
  /** 分类筛选：一级分类 */
  firstCategory?: string;
  /** 收货地址geoId(街道) */
  geoId?: string;
  /** 关键字 */
  keywords?: string;
  /** vin码/车型查找：开思销售车型Id列表 */
  ksSalesStyleIds?: string[];
  /** 排序类型，COMPREHENSIVE-综合、PRICE_DESC(PRICE_ASC)-价格双向排序、SALE_COUNT-销量从高到低、DISTANCE-距离由近及远 */
  orderType?: string;
  /** 当前页码 */
  pageNum?: number;
  /** 页码大小 */
  pageSize?: number;
  /** vin码/车型查找：零件号列表 */
  partsList?: string[];
  /** 平台，PC APP ALL */
  platform: string;
  /** 优先排序，如{“brandId:BMW”}，表示结果中宝马品牌优先排序 */
  rank?: IRank;
  /** 分类筛选：二级分类(叶子节点) */
  secondCategory?: string;
  /** 订货过滤：只看订货 */
  stockProduct?: boolean;
  /** 商家过滤：店铺id */
  storeId?: string;
  /** vin码/车型查找：轮胎规格列表 */
  tyreSizes?: string[];
  /** 用户登录id */
  userLoginId?: string;
}
