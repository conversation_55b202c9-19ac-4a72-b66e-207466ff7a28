// tslint:disable
import { ProductPicture } from "./ProductPicture";
export type Product = {
    /**
     * 品牌ID
     */
    brandName?: string;
    /**
     * 品质id
     */
    brandQualityId?: string;
    /**
     * 创建时间
     */
    createdTxStamp?: string;
    /**
     * 商家内部skuid,商品对接接口中有使用
     */
    customerProductId?: string;
    /**
     * 描述
     */
    description?: string;
    /**
     * 明细图
     */
    detailImageUrl?: string;
    /**
     * 内部标示）商家编码
     */
    internalName?: string;
    /**
     * 去空格商家编码
     */
    internalNameTrim?: string;
    /**
     * 大图
     */
    largeImageUrl?: string;
    /**
     * 最后更新时间
     */
    lastUpdatedTxStamp?: string;
    /**
     * 详细描述
     */
    longDescription?: string;
    /**
     * 中图
     */
    mediumImageUrl?: string;
    /**
     * 原始图
     */
    originalImageUrl?: string;
    /**
     * 零件号
     */
    partNumber?: string;
    /**
     * 分类类型
     */
    partType?: "ORIGINAL_PARTS" | "BRAND_PARTS" | "OIL_PARTS";
    /**
     * 去空格零件号
     */
    partsnoTrim?: string;
    /**
     * 类别
     */
    primaryProductCategoryId?: string;
    /**
     * 来源
     */
    productCreateSource?: string;
    /**
     * 商品id
     */
    productId?: string;
    /**
     * 型号
     */
    productModel?: string;
    /**
     * 产品名称
     */
    productName?: string;
    /**
     * 图片列表
     */
    productPictureList?: ProductPicture[];
    /**
     * 店铺id
     */
    productStoreId?: string;
    /**
     * 货源类型
     */
    productTypeId?: "SPOT_GOODS";
    /**
     * 评论
     */
    remark?: string;
    /**
     * 替换件零件号
     */
    replacePartsNum?: string;
    /**
     * unused-要求金额
     */
    requireAmount?: string;
    /**
     * 要求库存（Y或N）
     */
    requireInventory?: string;
    /**
     * 小图
     */
    smallImageUrl?: string;
    /**
     * 规格
     */
    specifications?: string;
    /**
     * 产品状态：新建、上架、下架
     */
    statusId?: "PRODUCT_INIT" | "PRODUCT_ADDED" | "PRODUCT_SHELVES" | "PRODUCT_NONE";
};
