import { Inventory } from "./Inventory";
import { IStepPrice } from "./IStepPrice";
// tslint:disable
export type IInventoryDTO = {
    total?: number; // 可用库存数
    productId?: string;
    stepPrices?: IStepPrice[];// 阶梯价
    buyerPrice?: number; // 买家默认价（活动价）
    crossedPrice?: number;// 划线价
    inventories?: Inventory[]; // 库存列表
    price?: number; // 商品卖家默认价
    atPrice?: number; // 卖家含税价格
    btPrice?: number; // 卖家不含税价格
    buyerAtPrice?: number; // 买家含税价格
    buyerBtPrice?: number; // 买家不含税价格
    taxRate?: number; // 税率
    priceTag?: string; // 价格标签
    showPrice?: string; // 默认给前端的展示价格
    openInvoiceType?: "YES" | "NO" | "BOTH"; // 商家开票配置 YES:开票；NO:不开票;BOTH:都可以
};
