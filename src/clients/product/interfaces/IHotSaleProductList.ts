export interface IHotSaleProductList {
  "@class"?: string;
  content: IHotSaleProductItem[];
  first: boolean;
  last: boolean;
  number: number;
  numberOfElements: number;
  size: number;
  totalElements: number;
  totalPages: number;
}

export interface IHotSaleProductItem {
  productId: string;
  productFull: IProductFull;
}
export interface IProductFull {
  productId: string;
  productInfo: IProductInfo;
  price: IPrice;
  brand: IBrand;
  attrs: IAttr[];
  productVehicle: IProductVehicle[];
}

export interface IProductInfo {
  smallImageUrl: string; // 商品图片
  productName: string; // 商品名
}

export interface IPrice {
  price: number; // 税后价，若无税后价则为税前价
}

export interface IBrand {
  brandName: string; // 品牌名
}
export interface IAttr {
  attrValue: string;
  attrType: string;
}

export interface IProductVehicle {
  vehicleInfo: string; // 适用车型
}
