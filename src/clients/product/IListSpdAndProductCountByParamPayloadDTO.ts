export interface IFeaturesItem {
  featureCode?: string,
  featureName?: string,
  value?: string
}

export interface IListSpdAndProductCountByParamPayloadDTO {
  brandCode?: string,
  features?: IFeaturesItem[],
  spdType?: string,
  keywords?: string,
  aeCodeTrim?: string,
  categoryName?: string,
  pageNum?: number,
  pageSize?: number,
  searchType?: string,
  userGeoId?: string,
  sortType?: string,
  userLoginId?: string,
  companyId?: string,
}