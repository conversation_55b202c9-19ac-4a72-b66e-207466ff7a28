type IProductSalesLimit = unknown;

export interface ISpdTyreInfoDto {
  pageNum?: string;
  pageSize?: string;
  totalPages?: string;
  totalElements?: string;
  spds?: ISpds[];
}

interface ISpds {
  id?: string;
  brandCode?: string;
  brandName?: string;
  brandQualityCode?: string;
  brandQualityName?: string;
  aeCode?: string;
  aeCodeTrim?: string;
  aeName?: string;
  stdName?: string;
  stdNameCode?: string;
  categoryName?: string;
  categoryCode?: string;
  features?: IFeatures[];
  pictures?: IPictures[];
}

export interface IFeatures {
  featureCode?: string;
  featureName?: string;
  value?: string;
}

interface IPictures {
  seqNum?: number;
  type?: string;
  url?: string;
  type_code?: number;
}

export interface ITyreProduct {
  id?: string;
  brandCode?: string;
  aeCode?: string;
  products?: IProducts[];
}

export interface IProducts {
  spdId?: string;
  spdBrandCode?: string;
  spdAeCode?: string;
  productId?: string;
  productDisplayName?: string;
  productName?: string;
  productNameExtend?: string;
  productDesc?: string;
  productStatusId?: string;
  productPrice?: IProductPrice;
  productSalesLimit?: IProductSalesLimit;
  productInventories?: IProductInventories[];
  storeId?: string;
  storeName?: string;
  productSalesCount?: number | string;
}

interface IProductInventories {
  productId?: string;
  facilityId?: string;
  facilityName?: string;
  facilityLongitude?: number;
  facilityLatitude?: number;
  facilityDistance?: number;
  inventoryValue?: number;
}

interface IProductPrice {
  productId?: string;
  priceType?: string;
  hasPriceTax?: boolean;
  price?: number;
  stepPrices?: IStepPrices[];
}

interface IStepPrices {
  sequence?: number;
  fromNum?: number;
  thruNum?: number;
  price?: string | number;
}
