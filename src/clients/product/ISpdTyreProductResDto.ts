type IPlatformType = string;

export interface ISpdTyreProductResDto {
  orderByType?: string;
  userGeoId?: string;
  brandCode?: string;
  aeCodeTrim?: string;
  cityGeoId?: string;
  productId?: string;
  platform?: IPlatformType;
}

export interface ISpdParams {
  id?: string;
  brandCode?: string;
  aeCode?: string;
}

export interface ISpdTyreDto {
  userLoginId?: string;
  companyId?: string;
  orderByType?: string;
  userGeoId?: string;
  brandCode?: string;
  aeCodeTrim?: string;
  cityGeoId?: string;
  productId?: string;
  platform?: IPlatformType;
}
