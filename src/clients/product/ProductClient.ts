import querystring from "querystring";
import { TeamCode, ErrorCode } from "@/common/enums";
import { IAddCommonModelByUserId } from "./interfaces/IAddCommonModelByUserId";
import IBrandDataDto from "./interfaces/IBrandDataDto";
import { IGetKeywordsParticipleListPayload } from "./interfaces/IGetKeywordsParticipleList";
import { IHotSaleProductList } from "./interfaces/IHotSaleProductList";
import { IInventoryDTO } from "./interfaces/IInventoryDTO";
import { IMallBrandDto } from "./interfaces/IMallBrandDto";

import {
  CategoryAttrConfigDTO,
  CategoryIconData,
  CommonVehicleTypeDTO,
  EcCatagoryTree,
  MarketTreeDTO,
  OpenInvoiceConfig,
  PagedModels_ParticipleDTO_ as ParticipleDTO,
  PagedResult_SearchEngineProductResult_ as SearchProductResult,
  ProductFull,
  ProductInventoryBatchParam,
  SearchEngineProductQueryParam,
} from "./interfaces/model";
import { SearchEngineProductCount } from "./interfaces/model/SearchEngineProductCount";
import { IBizcatParams, IGetBizCatTypeDTO, IGetCategoryTypeDTO } from "./IGetCategoryTypeDTO";
import { IGetFollowProductsByUserLoginIdDTO } from "./IGetFollowProductsByUserLoginIdDTO";
import { IInventoryInfo } from "./IGetInventoryInfoByProductIdsResultDTO";
import { Brand } from "./interfaces/Brand";
import { IKeywordDTO, IKeywordResponse } from "./interfaces/IAssociateDTO";
import { IStandardResponse } from "./interfaces/IAttributeDTO";
import { ICategoryPayloadDTO, ICategoryResDTO } from "./interfaces/ICategoryDTO";
import { ICheckProductStockPayloadDTO, ICheckProductStockResponseDTO } from "./interfaces/ICheckProductStock";
import { ICheckItem } from "./interfaces/ICheckSalesAreaPayload";
import { IFilterProductParam } from "./interfaces/IFilterProductParam";
import { IGetProductSearchGuideRequest, IGetProductSearchGuideResponse } from "./interfaces/IGetProductSearchGuide";
import { IGetSuitsDetailPayload, IProductResponseItem } from "./interfaces/IGetSuitsDetailDTO";
import { IProductDetail } from "./interfaces/IProductDetail";
import { IProductInventoryResultDTO } from "./interfaces/IProductInventoryResultDTO";
import { IProductMallFilterPayload } from "./interfaces/IProductMallFilterPayload";
import { IProductOriginalParts } from "./interfaces/IProductOriginalParts";
import { IDataItem, IGetProductPricesPayload } from "./interfaces/IProductPriceDTO";
import { ISearchEngineProductSchemaDTO } from "./interfaces/ISearchEngineProductSchemaDTO";
import { ISearchProductNewResultDTO } from "./interfaces/ISearchProductNewResultDTO";
import { IVehicleDTO, IVehicleResponseDTO } from "./interfaces/IShoppingMallPayloadSchemaDTO";
import { IIsTyreTypeSearchPayloadDTO } from "./interfaces/IsTyreTypeDTO";
import {
  ISpdProductResultDTO,
  ITyreListPageReqDTO,
  ITyreListResDTO,
  ITyreMerchantReqDTO,
} from "./interfaces/ITyreListDto";
import { AssociateResult } from "./interfaces/model/AssociateResult";
import { PagedModels_ProductFollowDTO_ } from "./interfaces/model/PagedModels_ProductFollowDTO_";
import { ProductFollowDTO } from "./interfaces/model/ProductFollowDTO";
import { ProductFollowParam } from "./interfaces/model/ProductFollowParam";
import { ProductFollowResult } from "./interfaces/model/ProductFollowResult";
import { IProductFull } from "./interfaces/ProductFull";
import { IProductClickStatisticPayload } from "./IProductClickStatisticPayload";
import { IReserveInventoryFacilityParam } from "./IReserveInventoryFacilityParam";
import { IReserveInventoryFacilityResult } from "./IReserveInventoryFacilityResult";
import { HttpClientForV2 } from "@/common/clients/HttpClientForV2";
import { HttpError } from "@/common/error";
import logger from "@/common/logger";
import { IGetSortStoresResponse } from "./IInquiryStores";

type IStoreListItem = unknown;

export interface IGetHotProductsQuery {
  userLoginId: string;
  companyId: string;
  isAuth: boolean;
  pageSize?: number;
  pageNum?: number;
}

export type ProductRestColumn =
  | "ORIGINAL_PARTS"
  | "PRICE"
  | "DEFAULT_PRICE"
  | "INVENTORY"
  | "BRAND"
  | "STORE"
  | "LIST_IMAGE"
  | "VEHICLES"
  | "ATTRS"
  | "TEXTS"
  | "SALES_COUNT"
  | "CGJ_CATAGORY"
  | "STANDARD_NAME";

export default class ProductClient {
  httpClient = new HttpClientForV2();

  get logger() {
    return logger;
  }

  /**
   * 查询品牌的介绍页
   * @param brandIdList 品牌ID列表
   */
  public async postBrandDataList(brandIdList: string[]): Promise<IBrandDataDto[]> {
    try {
      const brandDataList: IBrandDataDto[] | null = await this.httpClient.post(
        "/product-service/product/restApi/v1/brand/infos",
        { json: brandIdList }
      );
      return brandDataList || [];
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, {
        teamCode: TeamCode.ProductTeam,
      });
    }
  }

  /**
   * 查询ae数据、车系
   */
  public async getProductSearchParam(searchProductQuery: IVehicleDTO): Promise<IVehicleResponseDTO> {
    try {
      const path = "/product-service/product/vehicle/getProductSearchParam";
      return await this.httpClient.post(path, { json: searchProductQuery });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, {
        teamCode: TeamCode.ProductTeam,
      });
    }
  }

  /**
   * 批量查询商品属性信息API
   */
  public async getAttributes(productIds: string[]): Promise<IStandardResponse> {
    try {
      const path = "/product-data-service/product/attr/standard/attribute";

      return await this.httpClient.post(path, { json: productIds });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, {
        teamCode: TeamCode.ProductTeam,
      });
    }
  }

  /**
   * 查询常规件商城列表--新接口
   */
  public async getShoppingMallProductListV2(
    searchProductPayload: ISearchEngineProductSchemaDTO
  ): Promise<ISearchProductNewResultDTO> {
    try {
      const path = "/product-service/product/searchEngine/new/combine/search";
      return await this.httpClient.post(path, { json: searchProductPayload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, {
        teamCode: TeamCode.ProductTeam,
      });
    }
  }

  /**
   * 查询常规件商城列表
   * @param brandIdList 品牌ID列表
   */
  public async getShoppingMallProductList(
    searchProductQuery: SearchEngineProductQueryParam
  ): Promise<SearchProductResult> {
    try {
      const path = "/product-service/product/searchEngine/listPageSearchEngineProductByParamFront";
      return await this.httpClient.post(path, { json: searchProductQuery });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, {
        teamCode: TeamCode.ProductTeam,
      });
    }
  }

  /**
   * 获取搜索结果的分类信息
   * 调搜索接口的同时，用相同的参数调该接口拿到分类信息，取品牌和二级分类信息返回给前端
   * @param searchProductQuery 和搜索接口参数完全相同
   */
  public async getShoppingMallProductCategory(
    searchProductQuery: SearchEngineProductQueryParam
  ): Promise<SearchEngineProductCount[]> {
    try {
      const path = `/product-service/product/searchEngine/countSearchEngineProductByParamFront`;
      return await this.httpClient.post(path, { json: searchProductQuery });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 查询商品相关属性
   * @param userLoginId
   * @param productIds
   * @param productAttributeQuery
   * @param userAddressGeoId 街道geoId
   * 需要查询商品信息数组组成元素: [ORIGINAL_PARTS:原厂件信息,
   * PRICE:价格信息, DEFAULT_PRICE:默认价格信息, INVENTORY:库存信息, BRAND:品牌信息,
   * STORE:店铺信息, LIST_IMAGE:列图信息, VEHICLES:适用车型信息, ATTRS:属性信息, TEXTS:文本信息]
   */
  public async getProductInfo(
    userLoginId: string,
    productIds: string[],
    productAttributeQuery?: ProductRestColumn[],
    userAddressGeoId?: string,
    companyId?: string
  ): Promise<IProductFull> {
    try {
      const path = "/product-service/product/restApi/v1/product/products";
      let getProductInfoPayload: any = {
        userLoginId,
        productIds,
        resultColumn: productAttributeQuery,
        filterPreFacility: true,
      };
      if (userAddressGeoId) {
        getProductInfoPayload = { ...getProductInfoPayload, userAddressGeoId };
      }
      getProductInfoPayload = companyId ? { ...getProductInfoPayload, companyId } : getProductInfoPayload;
      const result = await this.httpClient.post(path, { json: getProductInfoPayload });
      for (const key in result) {
        if (result[key].price && result[key].price.openInvoiceType === "NO") {
          result[key].price.buyerAtPrice = 0;
          result[key].price.atPrice = 0;
          if (Array.isArray(result[key].price.stepPrices)) {
            result[key].price.stepPrices = result[key].price.stepPrices.map((stepPrice: any) => {
              stepPrice.buyerAtPrice = 0;
              stepPrice.atPrice = 0;
              return stepPrice;
            });
          }
        }
      }
      return result;
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, {
        teamCode: TeamCode.ProductTeam,
      });
    }
  }

  /**
   * 校验商品库存数
   */
  public async checkProductStock(body: ICheckProductStockPayloadDTO[]): Promise<ICheckProductStockResponseDTO> {
    try {
      return await this.httpClient.post("/quote-service/quotation/product/order/validation/batch", { json: body });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, {
        teamCode: TeamCode.OrderTeam,
      });
    }
  }

  /**
   * 获取商城品牌
   * @param userLoginId 用户登录ID
   * @param garageCompanyId 公司ID
   */
  public async getBrands(): Promise<IMallBrandDto[]> {
    try {
      const brands: IMallBrandDto[] | null = await this.httpClient.get(`/product-service/product/vehicle/brands`);
      return brands || [];
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, {
        teamCode: TeamCode.ProductTeam,
      });
    }
  }

  /**
   * 查询商品对应的仓库信息（why不使用商品属性接口查库存信息而使用该接口？这里包含showInvOrNo供应商后台设置是否展示仓库库存）
   * @param productIds
   * @param userLoginId
   */
  public async getInventoryInfoByProductIds(
    productIds: string[],
    userLoginId: string,
    userAddressGeoId?: string
  ): Promise<IInventoryInfo> {
    try {
      const api = "/product-service/product/restApi/v1/product/inventorysV2";
      let params: any = { productIds, userLoginId };
      if (userAddressGeoId) {
        params = { ...params, userAddressGeoId };
      }
      return await this.httpClient.post(api, { json: params });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, {
        teamCode: TeamCode.ProductTeam,
      });
    }
  }

  /**
   * 根据商品id批量查询价格
   * @param payload
   */
  public async getProductPriceInfoV2(payload: IGetProductPricesPayload): Promise<IDataItem[]> {
    try {
      const path = "/product-service/product/restApi/v1/list/prices";
      const { data } = await this.httpClient.post(path, { json: payload });
      return data;
    } catch {
      return [];
    }
  }

  /**
   * 根据商品id批量查询价格
   * @param payload
   */
  public async getProductPriceInfo(payload: ProductInventoryBatchParam): Promise<IInventoryDTO[]> {
    try {
      if (payload.queries && payload.queries.length) {
        const path = "/product-service/product/restApi/v1/price/listProductPricesByRequest";
        return await this.httpClient.post(path, { json: payload });
      }
      return [];
    } catch {
      return [];
    }
  }

  /**
   * 根据商品id批量查询库存
   * @param payload
   */
  public async getProductInventoryInfo(payload: ProductInventoryBatchParam): Promise<IProductInventoryResultDTO[]> {
    try {
      if (payload.queries && payload.queries.length) {
        const path = "/product-service/product/restApi/v1/product/inventory/listProductInventoryResultsByRequest";
        return await this.httpClient.post(path, { json: payload });
      }
      return [];
    } catch {
      return [];
    }
  }

  public async getHotSaleProductList(
    userLoginId: string,
    companyId: string,
    pageNum: number,
    pageSize: number
  ): Promise<IHotSaleProductList> {
    try {
      const api = `/product-service/product/restApi/v1/productSale/productSalePage/active?userLoginId=${userLoginId}&companyId=${companyId}&pageNum=${pageNum}&pageSize=${pageSize}`;
      return await this.httpClient.get(api);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, {
        teamCode: TeamCode.ProductTeam,
      });
    }
  }

  public async getHotProductsPriceless(getHotProductsQuery: IGetHotProductsQuery): Promise<IHotSaleProductList> {
    try {
      const { userLoginId, companyId, pageNum, pageSize } = getHotProductsQuery;
      const api = `/product-service/product/restApi/v1/productSale/productSaleForAppPage/active?userLoginId=${userLoginId}&companyId=${companyId}&pageNum=${pageNum}&pageSize=${pageSize}`;
      return await this.httpClient.get(api);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, {
        teamCode: TeamCode.ProductTeam,
      });
    }
  }

  /**
   * 获取黑白名单和报价区域,即可下单的供应商
   */
  public async getAvailableStore(userLoginId: string, companyId: string, geoId?: string): Promise<string[]> {
    try {
      let queryParams: string = `?${querystring.stringify({ userLoginId, companyId })}`;
      if (geoId) {
        queryParams = `?${querystring.stringify({ userLoginId, companyId, geoId })}`;
      }
      const api = `/product-service/product/restApi/v1/other/aviliableStore${queryParams}`;
      return await this.httpClient.get(api);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  public async getAllCategories(id: string): Promise<MarketTreeDTO[]> {
    try {
      const api = `/product-service/product/bizcat/head/tree?id=${id}`;
      return await this.httpClient.get(api);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 增加用户常用车型
   * @param addCommonModelByUserIdPayload
   */
  public async addCommonModelByUserId(
    addCommonModelByUserIdPayload: CommonVehicleTypeDTO
  ): Promise<IAddCommonModelByUserId> {
    try {
      const api = "/product-service/product/vehicle/addOrUpdateType";
      return await this.httpClient.post(api, { json: addCommonModelByUserIdPayload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /*
   * 根据用户id获取用户常用车型
   */
  public async getVehiclesById(userLoginId: string): Promise<CommonVehicleTypeDTO[]> {
    try {
      const api = `/product-service/product/vehicle/type?userLoginId=${userLoginId}`;
      return await this.httpClient.get(api);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 根据id删除用户常用车型
   */
  public async deleteVehiclesById(markId: string) {
    try {
      const api = `/product-service/product/vehicle/deleteById?markId=${markId}`;
      return await this.httpClient.post(api);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 常规件商城联想词搜索
   * @param keyword 用户输入的关键词
   */
  public async getAssociateKeywords(keyword: string): Promise<string[]> {
    try {
      const path = `/product-service/product/searchEngine/listAssociate?keyword=${encodeURI(keyword)}`;
      return await this.httpClient.get(path);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 常规件商城联想词搜索(统计聚合)-新接口
   * @param keyword 用户输入的关键词
   */
  public async getListStoreAndAssociateByKeyword(payload: IKeywordDTO): Promise<IKeywordResponse> {
    try {
      const path = "/search-engine-service/hot/search/keyword/listStoreAndAssociateByKeyword";

      return await this.httpClient.post(path, { json: payload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 常规件商城联想词搜索(统计聚合)
   * @param keyword 用户输入的关键词
   */
  public async getAssociateKeywordsNew(keywords: string): Promise<AssociateResult[]> {
    try {
      return await this.httpClient.get(
        `/search-engine-service/hot/search/keyword/associateWordByHotSearch?keyWord=${encodeURI(keywords)}`
      );
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 获取常规件商城筛选条件类别
   */
  public async getShoppingCatagories(): Promise<EcCatagoryTree[]> {
    try {
      const path = "/product-service/product/catagorys/tree/market?groupId=3";
      return await this.httpClient.get(path);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 根据二级分类名称获取属性列表
   * @param categoryName 二级分类名称
   */
  public async getCategoryAttrs(categoryName: string): Promise<CategoryAttrConfigDTO[]> {
    try {
      const query = querystring.stringify({ categoryName });
      const path = `/product-service/product/product/categoryAttrConfig/listByCategoryNameForMall?${query}`;
      return await this.httpClient.get(path);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 获取通过关键字分词列表
   * @param payload
   */
  public async getKeywordsParticipleList(payload: IGetKeywordsParticipleListPayload): Promise<ParticipleDTO> {
    try {
      const path = "/product-service/product/search/participle/paged";
      return await this.httpClient.post(path, { json: payload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 查询用户最近的店铺发货地
   */
  public async getFacilityByStoreIds(storeIds: string, geoId: string) {
    try {
      const api = `/product-service/product/facility/distance?storeIdList=${storeIds}&geoId=${geoId}`;
      return await this.httpClient.get(api);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  public async getProductVehicle(productId: string) {
    try {
      const api = `product-service/product/vehicle/product/${productId}`;
      return await this.httpClient.get(api);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 通过productId获取原厂件零件号
   */
  public async getProductOriginalParts(productIds: string[]): Promise<IProductOriginalParts> {
    try {
      const api = "/product-service/product/restApi/v1/product/originalParts";
      return await this.httpClient.post(api, { json: productIds });
    } catch (e) {
      this.logger.warn("获取原厂件零件号失败", e);
      return {};
    }
  }

  /**
   * 商品点击量统计接口
   */
  public async addProductClickStatistic(payload: IProductClickStatisticPayload) {
    try {
      const api = "/advertisement-service/click/statistics";
      return await this.httpClient.post(api, { json: payload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 查询用户已关注的商品数量
   * @param userLoginId 用户Id
   * @param source 关注入口: ANNIVERSARY_CELEBRATION(周年庆)
   */
  public async getFollowedCountByUserLoginId(userLoginId: string, source?: string): Promise<number> {
    try {
      let path = `/product-service/product/follow/numByUserLoginId?userLoginId=${userLoginId}`;
      if (source) {
        path += `&source=${source}`;
      }
      return await this.httpClient.get(path);
    } catch (e) {
      this.logger.warn("查询用户已关注的商品数量失败", e);
      return 0;
    }
  }

  /**
   * 关注商品
   */
  public async followProduct(param: ProductFollowParam): Promise<ProductFollowResult> {
    try {
      const path = `/product-service/product/follow/followProduct`;
      return await this.httpClient.post(path, { json: param });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 取消关注商品(批量)
   */
  public async unfollowProduct(userLoginId: string, type: string, productIds: string[]): Promise<boolean> {
    try {
      const path = `/product-service/product/follow/batchCancel?type=${type}&userLoginId=${userLoginId}`;
      return await this.httpClient.post(path, { json: productIds });
    } catch (e) {
      this.logger.warn("取消关注商品(批量)失败", e);
      return false;
    }
  }

  /**
   * 查询商品是否已关注(批量)
   */
  public async isFollowedByProductIds(userLoginId: string, productIds: string[]): Promise<ProductFollowDTO[]> {
    try {
      const path = `/product-service/product/follow/isFollowed?userLoginId=${userLoginId}`;
      return await this.httpClient.post(path, { json: productIds });
    } catch (e) {
      return [];
    }
  }

  /**
   * 更新商品关注信息
   */
  public async updateFollowProduct(userLoginId: string, productId: string): Promise<boolean> {
    try {
      const path = `/product-service/product/follow/update?productId=${productId}&userLoginId=${userLoginId}`;
      return await this.httpClient.post(path);
    } catch (e) {
      this.logger.warn("更新商品关注信息失败", e);
      return false;
    }
  }

  /**
   * 查询用户关注商品列表
   */
  public async getFollowProductsByUserLoginId(
    param: IGetFollowProductsByUserLoginIdDTO
  ): Promise<PagedModels_ProductFollowDTO_> {
    try {
      const { userLoginId, companyId, pageNum, pageSize } = param;
      const path = `/product-service/product/follow/products?userLoginId=${userLoginId}&companyId=${companyId}&pageNum=${pageNum}&pageSize=${pageSize}`;
      return await this.httpClient.get(path);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  // 根据地区过滤指定商品列表
  public async checkSalesArea(payload: ICheckItem[]): Promise<ICheckItem[]> {
    try {
      const path = "/product-service/product/salesArea/check";
      return await this.httpClient.post(path, { json: payload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  // 根据地区过滤指定商品列表
  public async getProductsFilterByAreaId(payload: IFilterProductParam): Promise<string[]> {
    try {
      const path = "/product-service/product/filterByAreaId";
      return await this.httpClient.post(path, { json: payload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  // 过滤黑名单和区域过滤的接口
  public async getProductMallFilter(payload: IProductMallFilterPayload): Promise<string[]> {
    try {
      return await this.httpClient.post("/product-service/product/mall/filter", { json: payload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  // 获取商品最大库存
  public async getInventoryByStoreAndProduct(
    storeId: string,
    productId: string
  ): Promise<Array<{ availableToPromiseTotal: number }>> {
    try {
      return await this.httpClient.get(`/product-service/product/api/${storeId}/${productId}/inventory`);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  // 校验库存，
  public async checkInventoryFacility(
    payload: IReserveInventoryFacilityParam
  ): Promise<IReserveInventoryFacilityResult> {
    try {
      const path = "/product-service/inventory/restApi/v1/product/checkInventoryFacility";
      return await this.httpClient.post(path, { json: payload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  public async getProductBatchInfo(
    userLoginId: string,
    productIds: string[],
    resultColumn: string[]
  ): Promise<ProductFull[]> {
    try {
      return await this.httpClient.post("/product-service/product/listProductsByIds", {
        json: { userLoginId, productIds, resultColumn },
      });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  public async listAttrsAndBrandsByParam(categoryName: string) {
    try {
      const result = await this.httpClient.get(
        `/product-service/product/spd/searchEngine/listAttrsAndBrandsByParam?${querystring.stringify({ categoryName })}`
      );
      return result || {};
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  public async listSpdAndProductCountByParam(payload: any) {
    try {
      const result = await this.httpClient.post(
        "/product-service/product/spd/searchEngine/listSpdAndProductCountByParam",
        { json: payload }
      );
      return result || {};
    } catch {
      return {};
    }
  }

  public async isTyreType(isTyreTypePayload: IIsTyreTypeSearchPayloadDTO): Promise<string> {
    try {
      const { searchType } = await this.httpClient.post(
        "/product-service/product/spd/searchEngine/listChannelByParam",
        { json: isTyreTypePayload }
      );
      return searchType;
    } catch {
      return "OTHER";
    }
  }

  public async getNearbyBusinesses(userLoginId: string, companyId: string, geoId: string): Promise<IStoreListItem[]> {
    try {
      const { data } = await this.httpClient.post(
        "/product-service/product/spd/searchEngine/listNearbyBusinessesByParam",
        { json: { userLoginId, companyId, geoId } }
      );
      return data;
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  public async tyreListPage(tyreListPageReq: ITyreListPageReqDTO): Promise<ITyreListResDTO> {
    try {
      return await this.httpClient.post("/product-service/product/spd/searchEngine/listPageByParam", {
        json: tyreListPageReq,
      });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  public async tyreMerchant(tyreMerchantReq: ITyreMerchantReqDTO): Promise<ISpdProductResultDTO[]> {
    try {
      return await this.httpClient.post("/product-service/product/spd/searchEngine/listSpdProductsByParam", {
        json: tyreMerchantReq,
      });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  public async getProductById(userLoginId: string, productId: string, resultColumn: string): Promise<ProductFull> {
    try {
      return await this.httpClient.get(
        `/product-service/product/getProductById?userLoginId=${userLoginId}&productId=${productId}&resultColumn=${resultColumn}`
      );
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 根据标名查询商品分类（旧）
   */
  public async getCategoryTypeByStandName(businessId: string, standNames: string[]): Promise<IGetCategoryTypeDTO[]> {
    try {
      return await this.httpClient.post(`/product-service/product/base/standNames?businessId=${businessId}`, {
        json: standNames,
      });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 根据标名查询商品分类（新）
   */
  public async getBizCatTypeByStandNameCode(params: IBizcatParams): Promise<IGetBizCatTypeDTO[]> {
    try {
      return await this.httpClient.post("/product-service/product/bizcat/item/standardnamecode", { json: params });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 商品详情
   * @param userLoginId
   * @param productIds
   * @param resultColumn
   * @param companyId
   */
  public async getProductsDetails(
    userLoginId: string,
    productIds: string[],
    resultColumn: ProductRestColumn[],
    companyId: string
  ): Promise<{ [productId: string]: IProductDetail }> {
    const api = "/product-service/product/restApi/v1/product/products";
    return this.httpClient.post(api, {
      json: {
        userLoginId,
        productIds,
        resultColumn,
        companyId,
      },
    });
  }

  /**
   * 查询每一个楼层的icon图标
   * @param categoryIds
   * @return CategoryIconData[]分类以及icon链接的对象
   */
  public protalFloorIconsList(categoryIds: string[]): Promise<CategoryIconData[]> {
    const api = "/product-service/product/category/icons";
    return this.httpClient.post(api, { json: categoryIds });
  }

  public async getProductBrandStatistics(payload: unknown): Promise<Brand[]> {
    try {
      return await this.httpClient.post(`/product-service/product/searchEngine/listBrandsByParam`, { json: payload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 批量获取商家发票配置
   * @param productStoreId
   * @returns
   */
  public async getInvoiceServiceByStoreId(productStoreId: string[]): Promise<OpenInvoiceConfig[]> {
    try {
      const queryStr = querystring.stringify({
        productStoreId,
      });

      return await this.httpClient.get(`/product-service/invoiceconfig?${queryStr}`);
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 获取分类数据
   * @param categoryPayload
   * @returns
   */
  public async getCategoryCode(categoryPayload: ICategoryPayloadDTO): Promise<ICategoryResDTO[]> {
    try {
      return await this.httpClient.post("/product-service/product/bizcat/item/categorycode", { json: categoryPayload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /**
   * 获取套件明细
   * @param payload
   * @returns
   */
  public async getSuitesDetail(payload: IGetSuitsDetailPayload[]): Promise<IProductResponseItem[]> {
    try {
      return await this.httpClient.post("/product-service/product/listSnapshotProducts", { json: payload });
    } catch {
      return [];
    }
  }

  /**
   * 获取商品搜索引导
   * @param payload
   * @returns
   */
  public async getProductSearchGuide(payload: IGetProductSearchGuideRequest): Promise<IGetProductSearchGuideResponse> {
    try {
      return await this.httpClient.post("/search-engine-service/keyword/search-guide/tip", { json: payload });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.ProductTeam });
    }
  }

  /** 获取询价单商家 */
  public async getInquirySortStores(inquiryId: string): Promise<IGetSortStoresResponse[]> {
    try {
      return await this.httpClient.get(`/inquiry-sort-service/quotation-product/sort/${inquiryId}/store`);
    } catch {
      return [];
    }
  }
}
