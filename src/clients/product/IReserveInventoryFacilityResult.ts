import { IInventoryStatus } from './IInventoryStatus';

export interface IReserveInventoryFacilityResult {
  activityId?:	string; // 活动id
  companyId?:	string; // 公司id
  defaultFacilityId: string; // 默认仓库id
  facilityId?:	string; // 用于返回结果仓库id
  facilityName?: string; // 仓库名称
  orderId?: string; // 订单id
  orderItemSeqId?:	string; // 订单项次序号id
  productId: string; // 商品id
  quantity:	number; // 扣减数量
  transfer?:	boolean; // 是否调仓库扣减
  userLoginId?: string; // 用户id
  InventoryStatus: IInventoryStatus;
}
