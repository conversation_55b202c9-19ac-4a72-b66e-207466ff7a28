import httpClient from "../../common/clients/http.client";
import { IGetQuoteResult, IGetSPDOriginalPayload, IGetSPDOriginalRes } from "./interface";

export class QuoteClient {
  async getSPDOriginal(payload: IGetSPDOriginalPayload) {
    const { inquiryId, quotationProductIds } = payload;
    const { result } = await httpClient.post<IGetSPDOriginalRes[]>(
      `/quote-service/list/quotation-product/attritbute/${inquiryId}`,
      {
        body: quotationProductIds,
      }
    );
    return result;
  }

  async getQuoteResult(inquiryId: string) {
    const { result } = await httpClient.get<IGetQuoteResult[]>(`/quote-service/userneedsitem/${inquiryId}/quoteresult`);
    return result;
  }
}
