export interface IGetSPDOriginalPayload {
  inquiryId: string; // 询价单
  quotationProductIds: string[]; // 商品ids
}

export interface IGetSPDOriginalRes {
  quotationProductId: string; // 商品id
  isOriginalAssort: boolean; // 是否原厂配套  true为原厂配套  fasle为非原厂配套
  spdFeatureDetail: string; // SPD特征信息
}

export interface IGetQuoteResult {
  /** 商品报价id */
  userNeedsItemId: string;
  remark: string;
  inquiryId: string;
  /** 译码结果id */
  resolveResultId: string;
  /** 商家id */
  storeId: string;
  /** 商家名称 */
  storeName: string;
  storeServiceArea: string;
  /** 商品id */
  productId: string;
  productType: string;
  /** 译码结果零件号 */
  partsNum: string;
  /** 译码结果名称 */
  partsName: string;
  partType: string;
  /** 品牌id */
  brandId: string;
  /** 品牌名称 */
  brandName: string;
  /** 销售价格 */
  sellerPrice: number;
  /** 税前销售价格 */
  sellerBtPrice: number;
  taxRate: number;
  /** 价格 */
  price: number;
  /** 税前价格 */
  btPrice: number;
  btBoxFee: number;
  atBoxFee: number;
  afterSaleSnapshot: string;
  /** 品质id */
  partsBrandQuality: string;
  /** 数量 */
  quantity: number;
  sellStatus: string;
  source: string;
  arrivalTime: number;
  /** 仓库id */
  location: string;
  /** 仓库名称 */
  locationName: string;
  whetherProductSet: string;
  createdStamp: number;
  lastUpdatedStamp: number;
  createdBy: string;
  originalAssort: number;
  quotedTime: number;
  oeOriginalAssort: boolean;
  /** 是否有配套资质 */
  categoryOriginalAssort: boolean;
}

export interface IPlanQuoteItem extends IGetQuoteResult {
  /** 品质名称 */
  partsBrandQualityName: string;
  /** 品质展示名称 */
  qualityName: string;
  /** 品质和品牌名称 */
  partsBrandQualityAndBrandName: string;
  /** 商家简称 */
  positioningName: string;
  /** SPD特征详情 */
  spdFeatureDetail: string;
  /** 需求id */
  needId: string;
  /** 需求 */
  needsName: string;
  /** 商品报价id */
  quotationProductId: string;
  /** 译码结果id */
  decodeResultId: string;
  /** 译码结果id */
  standardItemId: string;
  /** 展示的价格 */
  showPrice: number;
}
