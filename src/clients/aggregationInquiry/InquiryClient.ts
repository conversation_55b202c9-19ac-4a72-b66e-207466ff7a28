import { HttpClientForV2 } from "@/common/clients/HttpClientForV2";
import { ErrorCode, TeamCode } from "@/common/enums";
import { HttpError } from "@/common/error";
import { IInquirybodyResponse } from "./interfaces/IPostnquiryV3DTO";

export class InquiryClient {
  httpClient = new HttpClientForV2();

  public async getInquiryDetailV3(inquiryId: string, userNeedIds?: string[]): Promise<IInquirybodyResponse> {
    try {
      return await this.httpClient.post("/aggregation-inquiry-service/inquirybody/detail", {
        json: {
          inquiryId,
          userNeedIds,
          platform: "APP",
        },
      });
    } catch {
      throw HttpError.create(ErrorCode.SERVER_ERROR, { teamCode: TeamCode.InquiryTeam });
    }
  }
}
