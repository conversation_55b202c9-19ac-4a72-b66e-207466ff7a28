export interface IDataResourcesItem {
  /** 创建人 */
  createdBy?: string;
  /** 创建时间 */
  createdStamp?: string;
  /** 附件ID */
  dataResourceId?: string;
  /** 修改时间 */
  lastUpdatedStamp?: string;
  /** 附件类型MimeTypeEnum */
  mimeTypeId?: string;
  /** 附件地址 */
  objectInfo?: string;
}

export interface IBrandConfigs {
  /** 品牌code */
  brandCode?: string;
  /** 品类code */
  categoryCode?: string;
  /** 品牌介绍 */
  description?: string;
  /** APP端品牌介绍落地页地址 */
  descriptionAppHref?: string;
  /** PC端品牌介绍落地页地址 */
  descriptionPcHref?: string;
  /** 良品率 */
  goodRate?: string;
  /** 绑定品牌介绍信息id */
  id?: string;
  /** OE号 */
  oe?: string;
  /** 销量 */
  sales?: number;
  /** APP端品牌介绍品类视频链接 */
  descriptionAppVideo?: string;
  /** APP端品牌介绍视频首帧图片链接 */
  appVideoFirstFrame?: string;
}

export interface ILabelsItem {
  /** 广告ID */
  adIds?: string[];
  /** 广告位置ID */
  adLocationId?: string;
  /** 标签浮窗文案 */
  floatingContent?: string;
  /** 标签ID */
  labelId?: string;
  /** 标签名称 */
  labelName?: string;
  /** 标签类型 */
  labelType?: string;
  /** 标签类型名 */
  labelTypeName?: string;
  /** 跳转url */
  url?: string;
}

export interface ILabelAd {
  /** 广告标签 */
  labels?: ILabelsItem[];
  /** 是否展示广告标识 */
  showAdvFlag?: boolean;
}

export interface ISortAd {
  /** 广告id */
  adIds?: string[];
  /** 广告位置ID */
  adLocationId?: string;
  /** 推广位置 */
  locationRank?: string;
  /** 是否展示广告标识 */
  showAdvFlag?: boolean;
}

export interface ISubLayersItem {
  /** 子分层品牌id */
  brandCode?: string;
  /** 品牌介绍信息出参 */
  brandConfigs?: IBrandConfigs;
  /** 子分层品牌名称 */
  brandName?: string;
  /** 标签广告 */
  labelAd?: ILabelAd;
  /** 子分层品质code */
  qualityCode?: string;
  /** 子分层品质名称 */
  qualityName?: string;
  /** 报价结果集合 */
  quotationProductIds?: string[];
  /** 是否展示广告标识 */
  showAdvFlag?: boolean;
  /** 排序广告 */
  sortAd?: ISortAd;
  /** 报价条目数量 大于X时分为主报价区和推荐区 小于等于X时平铺 */
  productExpandNum?: number;
  /** 子分层展开数量(不排除已采报价结果数量) */
  subLayerExpandNum?: number;
  /** 子分层id（译码结果id-分层code-零件品牌id-零件品质code） */
  subLayerId?: string;
  /** 子分层名称（零件品牌名称-零件品质名称） */
  subLayerName?: string;
  /** 品牌品质总分 */
  totalScore?: number;
  storeLayers?: { quotationProductIds: string[] }[];
  /** 品类维度的是否原厂配套 */
  isPartCategoryCertification?: boolean;
}

export interface ILayersItem {
  /** 分层代码 (值: ORIGINAL_LAYER-原厂; STRICT_LAYER-开思严选; OTHER_LAYER-其他; TOP_LAYER-超级置顶; BRAND_LAYER-品牌; LAYER01-优秀品牌; LAYER02-回收件) (详细参见: com.casstime.ec.cloud.aggregation.inquiry.values.enums.QuotationResultLayerCodeEnum) */
  layerCode?: string;
  /** 分层描述 */
  layerDescription?: string;
  /** 分层描述跳转链接 */
  layerDescriptionHref?: string;
  /** 品牌品质层展示数量 */
  layerExpandNum?: number;
  /** 分层id */
  layerId?: string;
  /** 分层名称 */
  layerName?: string;
  /** 子分层信息 */
  subLayers?: ISubLayersItem[];
}

export interface IQuotationProductsItem {
  /** 是否一定要展示 */
  alwaysShow?: boolean;
  /** 最快到货时间: xx天 */
  arrivalTime?: number;
  /** 税后钉箱费 */
  atBoxFee?: number;
  /** 买家含税总费用（免税报价+免税钉箱费） */
  atBuyerAndBoxFee?: number;
  /** 品牌id */
  brandId?: string;
  /** 品牌名 */
  brandName?: string;
  /** 品牌件规格备注 */
  brandPartsRemark?: string;
  /** 品牌件规格图片 */
  brandPartsUrls?: string[];
  /** 税前钉箱费 */
  btBoxFee?: number;
  /** 买家含税总费用（含税报价+含税钉箱费） */
  btBuyerAndBoxFee?: number;
  /** 免税价格 */
  btPrice?: number;
  /** 创建人 */
  createdBy?: string;
  /** 创建时间 */
  createdStamp?: string;
  /** 商品描述 */
  description?: string;
  /** 距离 */
  distance?: number;
  /** 维修厂公司id */
  garageCompanyId?: string;
  /** 是否为促销商品 */
  hotSale?: string;
  /** 询价单号 */
  inquiryId?: string;
  /** 是否已采 */
  isOrdered?: boolean;
  /** 是否系统报价无库存 */
  isQuotationNoInventory?: boolean;
  /** 是否替换件 true 是替换件 */
  isReplacement?: boolean;
  /** 是否显示零件号 */
  isShowPartsNum?: boolean;
  /** 标签广告 */
  labelAd?: ILabelAd;
  /** 最后更新时间 */
  lastUpdatedStamp?: string;
  /** 发货地id */
  location?: string;
  /** 发货地名称 */
  locationName?: string;
  /** 原厂配套标识.0否1是 */
  originalAssort?: number;
  /** 零件类型 */
  partType?: string;
  /** 分区 */
  partition?: string;
  /** 零件品牌品质 */
  partsBrandQuality?: string;
  /** 零件名 */
  partsName?: string;
  /** 零件号 */
  partsNum?: string;
  /** 销售价 */
  price?: number;
  /** 价格调节ID */
  priceAdjustmentRulesId?: string;
  /** 商品id */
  productId?: string;
  /** 套件编码 */
  productSetCode?: string;
  /** 套件id */
  productSetId?: string;
  /** 商品类型：FINISHED_GOODS or DISPATCH_GOODS */
  productType?: string;
  /** 数量 */
  quantity?: number;
  /** 报价单id */
  quotationId?: string;
  /** 报价结果id */
  quotationProductId?: string;
  /** 报出时间 */
  quotedTime?: number;
  /** 商家备注 */
  remark?: string;
  /** 译码结果id */
  resolveResultId?: string;
  /** 销售状态 */
  sellStatus?: string;
  /** 销售类型 */
  sellType?: string;
  /** 卖家免税价格 */
  sellerBtPrice?: number;
  /** 卖家免税销售价（免税报价+免税钉箱费） */
  sellerBtPriceAndBoxFee?: number;
  /** 卖家含税价格 */
  sellerPrice?: number;
  /** 卖家含税销售价（含税报价+含税钉箱费） */
  sellerPriceAndBoxFee?: number;
  /** 是否展示广告标识 */
  showAdvFlag?: boolean;
  /** 排序广告 */
  sortAd?: ISortAd;
  /** 来源 */
  source?: string;
  /** 报价结果店铺类别分数 */
  storeCategoryScore?: number;
  /** 店铺id */
  storeId?: string;
  /** 店铺名 */
  storeName?: string;
  /** 店铺服务范围 */
  storeServiceArea?: string;
  /** 供应商id */
  supplierCompanyId?: string;
  /** 税率 */
  taxRate?: number;
  /** 报价结果总分 */
  totalScore?: number;
  /** 是否套件 */
  whetherProductSet?: string;
  /** 商品快照版本号 */
  productVersion?: string;
  /** 展示类型 */
  displayType?: string;
}

export interface IDecodeResultsItem {
  /** 调节商品类型 */
  adjustmentProductType?: string;
  /** 钉箱费 */
  boxFee?: number;
  /** 品类code */
  categoryCode?: string;
  /** 4s店价格 */
  competitivePrice?: number;
  /** 4s价格版本 */
  competitiveVersion?: string;
  /** 创建人 */
  createdBy?: string;
  /** 译码贴图 */
  dataResources?: IDataResourcesItem[];
  /** 译码结果ID */
  decodeResultId?: string;
  /** 译码来源 */
  decodeSource?: string;
  /** 译码员id */
  decoderId?: string;
  /** 询价单ID */
  inquiryId?: string;
  /** 建议 */
  isSuggest?: string;
  /** 分层信息 */
  layers?: ILayersItem[];
  /** 是否需要打包 */
  needPackage?: string;
  /** 订单id */
  orderId?: string;
  /** 零件名称 */
  partsName?: string;
  /** 零件号 */
  partsNum?: string;
  /** 品质 */
  quality?: string;
  /** 数量 */
  quantity?: number;
  /** 该译码结果下的报价结果 */
  quotationProducts?: IQuotationProductsItem[];
  /** 推荐购买数量 */
  recommendAmount?: number;
  /** 报价条目 */
  referenceItemId?: string;
  /** 参考价格 */
  referencePrice?: number;
  /** 备注 */
  remark?: string;
  /** 销售类型 */
  sellType?: string;
  /** 来源 */
  source?: string;
  /** 旧标准名称 */
  standardName?: string;
  /** 标准名称code */
  standardNameCode?: string;
  /** 4S纠错状态key */
  status4sKey?: string;
  /** 4S纠错状态描述 */
  status4sValue?: string;
  /** 状态 */
  statusId?: string;
  /** 原始需求id */
  userNeedsId?: string;
  /** 特殊置顶条目 */
  specialTopItems?: ISpecialTopItem[];
}

interface ISpecialTopItem {
  promotionSourceId?: string;
  quotationProductId?: string;
}

export interface IUserNeedsItem {
  /** 调节商品类型 */
  adjustmentProductType?: string;
  /** 品类code */
  categoryCode?: string;
  /** 4S店价格 */
  competitivePrice?: string;
  /** 4s价格版本 */
  competitiveVersion?: string;
  /** 创建人 */
  createdBy?: string;
  /** 用户需求贴图 */
  dataResources?: IDataResourcesItem[];
  /** 该需求下的译码结果 */
  decodeResults?: IDecodeResultsItem[];
  /** 译码来源 */
  decodeSource?: string;
  /** 描述 */
  description?: string;
  /** 询价单ID */
  inquiryId?: string;
  isSuggest?: string;
  /** 需求ID */
  needId?: string;
  /** 是否需要打包 */
  needPackage?: string;
  /** 客户需求(配件名称) */
  needsName?: string;
  /** 订单ID */
  orderId?: string;
  pid?: string;
  /** 数量 */
  quantity?: number;
  /** 参考价格 */
  referencePrice?: number;
  /** 备注 */
  remark?: string;
  /** 销售类型 */
  sellType?: string;
  source?: string;
  /** 标准名称 */
  standardName?: string;
  /** 标准名称code */
  standardNameCode?: string;
  /** 状态 */
  statusId?: string;
  /** 单项退回 */
  itemReturnFlag?: string;
}

export interface IQualityLayerFoldQuantityItem {
  /** app展示数量 */
  appFoldQuantity?: number;
  /** pc展示数量 */
  pcFoldQuantity?: number;
  /** 品质分层 */
  qualityLayer?: string;
}

export interface ILabelAdsItem {
  /** 广告ID */
  adIds?: string[];
  /** 广告位置ID */
  adLocationId?: string;
  /** 标签浮窗文案 */
  floatingContent?: string;
  /** 标签ID */
  labelId?: string;
  /** 标签名称 */
  labelName?: string;
  /** 标签类型 */
  labelType?: string;
  /** 标签类型名 */
  labelTypeName?: string;
  /** 是否展示广告标识 */
  showAdvFlag?: boolean;
  /** 跳转url */
  url?: string;
  /** 标签样式 */
  labelStyle?: ILabelStyleDTO;
}

export interface IInquiryBodyQuoteResultListItem {
  /** 售后快照 */
  afterSaleSnapshot?: string;
  /** 是否一定要展示 */
  alwaysShow?: boolean;
  /** APP是否折叠 */
  appFold?: boolean;
  /** 最快到货时间: xx天 */
  arrivalTime?: number;
  /** 税后钉箱费 */
  atBoxFee?: number;
  /** 品牌id */
  brandId?: string;
  /** 品牌名 */
  brandName?: string;
  /** 品牌件规格备注 */
  brandPartsRemark?: string;
  /** 品牌件规格图片 */
  brandPartsUrls?: string[];
  /** 税前钉箱费 */
  btBoxFee?: number;
  /** 免税价格 */
  btPrice?: number;
  /** 创建人 */
  createdBy?: string;
  /** 创建时间 */
  createdStamp?: string;
  /** 商品描述 */
  description?: string;
  /** 距离 */
  distance?: number;
  /** 维修厂公司id */
  garageCompanyId?: string;
  /** 是否为促销商品 */
  hotSale?: string;
  /** 询价单号 */
  inquiryId?: string;
  /** 是否替换件 true 是替换件 */
  isReplacement?: boolean;
  /** 替换件类型 PLATFORM_REPLACE:平台替换件 STORE_REPLACE：店铺替换件 REPLACE_PART:替换件 */
  replacementType?: string;
  /** 标签广告列表 */
  labelAds?: ILabelAdsItem[];
  /** 最后更新时间 */
  lastUpdatedStamp?: string;
  /** 发货地id */
  location?: string;
  /** 发货地名称 */
  locationName?: string;
  /** 原厂配套标识.0否1是 */
  originalAssort?: number;
  /** 零件类型 */
  partType?: string;
  /** 分区 */
  partition?: string;
  /** 零件品牌品质 */
  partsBrandQuality?: string;
  /** 零件名 */
  partsName?: string;
  /** 零件号 */
  partsNum?: string;
  /** PC是否折叠 */
  pcFold?: boolean;
  /** 销售价 */
  price?: number;
  /** 价格调节ID */
  priceAdjustmentRulesId?: string;
  /** 商品id */
  productId?: string;
  /** 套件编码 */
  productSetCode?: string;
  /** 套件id */
  productSetId?: string;
  /** 商品类型：FINISHED_GOODS or DISPATCH_GOODS */
  productType?: string;
  /** 品质渠道 */
  qualityChannel?: string;
  /** 品质分层 */
  qualityLayer?: string;
  /** 数量 */
  quantity?: number;
  /** 报价单id */
  quotationId?: string;
  /** 报出时间 */
  quotedTime?: number;
  /** 商家备注 */
  remark?: string;
  /** 译码结果id */
  resolveResultId?: string;
  /** 销售状态 */
  sellStatus?: string;
  /** 销售类型 */
  sellType?: string;
  /** 卖家免税价格 */
  sellerBtPrice?: number;
  /** 卖家销售价 */
  sellerPrice?: number;
  /** 排序广告 */
  sortAd?: ISortAd;
  /** 来源 */
  source?: string;
  /** 报价结果店铺类别分数 */
  storeCategoryScore?: number;
  /** 店铺id */
  storeId?: string;
  /** 店铺名 */
  storeName?: string;
  /** 店铺服务范围 */
  storeServiceArea?: string;
  /** 供应商id */
  supplierCompanyId?: string;
  /** 税率 */
  taxRate?: number;
  /** 置顶方式 HOVER_TOP 悬浮置顶 ，SIMPLE_TOP 普通置顶  非置顶NULL */
  topConfig?: string;
  /** 置顶配置对应的配置id */
  topConfigId?: number;
  /** 排序总得分 */
  totalScore?: number;
  /** 报价结果id */
  userNeedsItemId?: string;
  /** 是否套件 */
  whetherProductSet?: string;
}

export interface IQuoteItemSortDTO {
  /** 不含税价格 */
  btPrice?: number;
  /** 调货天数 */
  dispatchCargoDays?: number;
  /** 收货地址到报价仓库的距离 */
  distance?: number;
  /** 喜好商家 */
  favoriteStore?: boolean;
  /** 维修厂是否要发票 */
  garageNeedsInvoice?: boolean;
  /** 是否为包装车 */
  installAssure?: boolean;
  /** 前置仓类型 */
  leadtimeWarehouseType?: string;
  /** 本地其他商家，为等级数字 */
  localStoreLevel?: number;
  /** 本地优势商家 */
  localSuperiorityStore?: boolean;
  /** 异地商家，为等级数字 */
  nationStoreLevel?: number;
  /** 异地优势商家 */
  nationSuperiorityStore?: boolean;
  /** 周边商家 */
  peripheryStore?: boolean;
  /** 含税价格 */
  price?: number;
  /** 零件质量等级类型 */
  qualityLevel?: string;
  /** 品质分区 */
  qualityZone?: string;
  /** sku报出率 */
  quoteRate?: number;
  /** 是否为替换件 */
  replacement?: boolean;
  /** 包退货天数 */
  salesReturnDays?: number;
  /** 末端S商家 */
  sameAreaStore?: boolean;
  /** sku的最高价格（不含税） */
  skuHighestBtPrice?: number;
  /** sku的最高价格 */
  skuHighestPrice?: number;
  /** sku的最低价格（不含税） */
  skuLowestBtPrice?: number;
  /** sku的最低价格 */
  skuLowestPrice?: number;
  /** 评分规则id */
  sortRuleId?: string;
  /** 报价结果店铺类别 */
  storeCategory?: string;
  /** 优势商家 */
  superioritySupplier?: boolean;
  /** 商家等级 */
  supplierGrade?: number;
  /** 商家质保月份 */
  supplierGuaranteePeriodMonth?: number;
  /** 技术支持 */
  technologySupport?: boolean;
  /** 交易次数 */
  tradeTimes?: number;
  /** 异常过高价格 */
  unusualHigherPrice?: number;
  /** 异常过低价格 */
  unusualLowerPrice?: number;
  /** 小狮配送时间，单位为分钟 */
  wholionTime?: number;
}

export interface IScoreDetail {
  /** 调货天数分数 */
  dispatchCargoDaysScore?: number;
  /** 距离分数 */
  distanceScore?: number;
  /** 喜好商家分数 */
  favoriteStoreScore?: number;
  /** 保装车分数 */
  installAssureScore?: number;
  /** 前置仓类型分数 */
  leadtimeWarehouseTypeScore?: number;
  /** 本地其他商家等级分数 */
  localStoreLevelScore?: number;
  /** 本地优势商家分数 */
  localSuperiorityStoreScore?: number;
  /** 异地商家等级分数 */
  nationStoreLevelScore?: number;
  /** 异地优势商家分数 */
  nationSuperiorityStoreScore?: number;
  /** 周边商家分数 */
  peripheryStoreScore?: number;
  /** 价格分数 */
  priceScore?: number;
  /** 零件质量等级分数 */
  qualityLevelScore?: number;
  /** 品质分区分数 */
  qualityZoneScore?: number;
  /** 报价结果多样性维度分数 */
  quotationMultipleDimensionScore?: number;
  /** 报出率分数 */
  quoteRateScore?: number;
  /** 包退货天数分数 */
  salesReturnDaysScore?: number;
  /** 末端S商家分数 */
  sameAreaStoreScore?: number;
  /** 报价结果店铺类别分数 */
  storeCategoryScore?: number;
  /** 优势商家分数 */
  superioritySupplierScore?: number;
  /** 商家等级分数 */
  supplierGradeScore?: number;
  /** 商家质保月份分数 */
  supplierGuaranteePeriodMonthScore?: number;
  /** 技术支持分数 */
  technologySupportScore?: number;
  /** 总分 */
  totalScore?: number;
  /** 交易次数分数 */
  tradeTimesScore?: number;
  /** 小狮配送时间分数 */
  wholionTimeScore?: number;
}
export interface IPromotionLabels {
  /** 标签code */
  labelCode?: string;
  /** 标签名称 */
  labelName?: string;
}
export interface IQuotationProductExtraSnapshotDTOListItem {
  /** 调节商品类型 */
  adjustmentProductType?: string;
  /** 是否一定要展示 */
  alwaysShow?: boolean;
  /** APP是否折叠 */
  appFold?: boolean;
  /** app展示数量 */
  appFoldQuantity?: number;
  /** 钉箱费 */
  boxFee?: number;
  /** 品类code */
  categoryCode?: string;
  /** 4s店价格 */
  competitivePrice?: number;
  /** 4s价格版本 */
  competitiveVersion?: string;
  /** 创建人 */
  createdBy?: string;
  /** 创建时间 */
  createdStamp?: string;
  /** 译码贴图 */
  dataResources?: IDataResourcesItem[];
  /** 译码结果ID */
  decodeResultId?: string;
  /** 译码来源 */
  decodeSource?: string;
  /** 译码员id */
  decoderId?: string;
  /** 距离 */
  distance?: number;
  /** 该译码结果下的报价结果 */
  inquiryBodyQuoteResultList?: IInquiryBodyQuoteResultListItem[];
  /** 该译码结果下的APP报价结果 */
  inquiryBodyQuoteResultListForApp?: IInquiryBodyQuoteResultListItem[];
  /** 询价单ID */
  inquiryId?: string;
  internalCode?: string;
  isSuggest?: string;
  /** 标签广告列表 */
  labelAds?: ILabelAdsItem[];
  /** 修改时间 */
  lastUpdatedStamp?: string;
  /** 是否需要打包 */
  needPackage?: string;
  /** 订单id */
  orderId?: string;
  /** 分区 */
  partition?: string;
  /** 零件名称 */
  partsName?: string;
  /** 零件号 */
  partsNum?: string;
  /** PC是否折叠 */
  pcFold?: boolean;
  /** pc展示数量 */
  pcFoldQuantity?: number;
  /** 品质 */
  quality?: string;
  /** 品质渠道 */
  qualityChannel?: string;
  /** 品质分层 */
  qualityLayer?: string;
  /** 品质展示顺序 */
  qualityLayerDisplaySequences?: string[];
  /** 品质分层展示数量 */
  qualityLayerFoldQuantity?: IQualityLayerFoldQuantityItem[];
  /** 数量 */
  quantity?: number;
  /** 报价商品id */
  quotationProductId?: string;
  /** 报价条目排序 */
  quoteItemSortDTO?: IQuoteItemSortDTO;
  /** 推荐购买数量 */
  recommendAmount?: number;
  /** 报价条目 */
  referenceItemId?: string;
  /** 参考价格 */
  referencePrice?: number;
  /** 备注 */
  remark?: string;
  /** 报价条目分数详情 */
  scoreDetail?: IScoreDetail;
  /** 销售类型 */
  sellType?: string;
  /** 排序广告 */
  sortAd?: ISortAd;
  source?: string;
  /** 旧标准名称 */
  standardName?: string;
  /** 标准名称code */
  standardNameCode?: string;
  /** 4S纠错状态key */
  status4sKey?: string;
  /** 4S纠错状态描述 */
  status4sValue?: string;
  /** 状态 */
  statusId?: string;
  /** 报价结果店铺类别分数 */
  storeCategoryScore?: number;
  /** 置顶方式 HOVER_TOP 悬浮置顶 ，SIMPLE_TOP 普通置顶  非置顶NULL */
  topConfig?: string;
  /** 置顶配置对应的配置id */
  topConfigId?: number;
  /** 排序总得分 */
  totalScore?: number;
  /** 原始需求id */
  userNeedsId?: string;
  /** 推广标签 */
  promotionLabels?: IPromotionLabels[];
}

export interface IDecodeResultNavigationItem {
  /** app展示数量 */
  appFoldQuantity?: number;
  /** 译码单ID */
  decodeResultId?: string;
  /** 标准名称 */
  partsName?: string;
  /** 零件号 */
  partsNum?: string;
  /** pc展示数量 */
  pcFoldQuantity?: number;
  /** 品质展示顺序 */
  qualityLayerDisplaySequences?: string[];
  /** 品质分层展示数量 */
  qualityLayerFoldQuantity?: IQualityLayerFoldQuantityItem[];
  /** 报价结果快照集合 */
  quotationProductExtraSnapshotDTOList?: IQuotationProductExtraSnapshotDTOListItem[];
  /** 报价结果数 */
  quoteResultNumber?: number;
  /** 译码结果的报价状态 */
  quoteStatus?: string;
  /** 是否展示已采 */
  showOrderedFlag?: boolean;
  /** 需求ID */
  userNeedId?: string;
}

export interface IUserNeedsNavigationItem {
  /** 按需求定位列表的译码结果 */
  decodeResultNavigation?: IDecodeResultNavigationItem[];
  /** 客户需求(配件名称) */
  needsName?: string;
  /** 客户需求添加来源 */
  source?: string;
  /** 需求ID */
  userNeedsId?: string;
}

export interface IInquirybodyResponse {
  /** 需求列表 */
  userNeeds?: IUserNeedsItem[];
  /** 按需求定位列表 */
  userNeedsNavigation?: IUserNeedsNavigationItem[];
}

export interface ILabelStyleDTO {
  /** 背景 */
  background?: IStyleBackgroundDTD;
  /** 边框 */
  border?: IStyleBorderDTD;
  /** 字体 */
  font?: IStyleFontDTD;
  /** 图标 */
  tagIcon?: IStyleTagIconDTD;
  /** 标签高度 */
  labelHeight?: number;
}

/**
 * 标签样式背景对象
 */
export interface IStyleBackgroundDTD {
  /** 背景颜色（16进制）比如：#cb3231 */
  color?: string;
  /** 背景透明度(范围：0~1) */
  opacity?: number;
}
/**
 * 标签样式边框对象
 */
export interface IStyleBorderDTD {
  /** 边框颜色(16进制 比如：#cb3231 */
  color?: string;
  /** 边框透明度(范围：0~1) */
  opacity?: number;
  /** 边框圆角 */
  radius?: number;
  /** 边框粗细 */
  size?: number;
}
/**
 * 标签样式字体对象
 */
export interface IStyleFontDTD {
  /** 字体颜色（16进制）比如：#cb3231 */
  color?: string;
  /** 字体透明度(范围：0~1) */
  opacity?: number;
  /** TO文字左右边距DO */
  paddingHorizon?: number;
  /** 字体大小 */
  size?: number;
  /** 是否加粗 */
  weight?: boolean;
}
/**
 * 标签样式图标对象
 */
export interface IStyleTagIconDTD {
  /** 图标链接 */
  iconUrl?: string;
  /** 仅展示图标 */
  isOnlyShowIcon?: boolean;
  onlyShowIcon?: boolean;
  /** 图标宽度 */
  iconWidth?: number;
  /** 图标高度 */
  iconHeight?: number;
}
