import config from "@casstime/config";
import httpClient from "../../common/clients/http.client";
import {
  IAccidentRelatedPartsParams,
  IAccidentRelatedPartsRes,
  IRepairScenariosParams,
  IRepairScenariosRes,
  ISimilarRepairScenariosRes,
} from "./interface";

const partsMindBaseUrl = config.get("PARTS_MIND_BASE_URL");

export class PartsMindClient {
  /** 查询维修场景 */
  async getRepairScenarios(body: IRepairScenariosParams) {
    try {
      const { result } = await httpClient.post<IRepairScenariosRes>(
        `${partsMindBaseUrl}/parts-mind/vehicle-repair-parts/query-repair-scenarios`,
        {
          body,
        }
      );
      return {
        accidents: result?.accidents || [],
        total: result?.total || 0,
      };
    } catch {
      return {
        accidents: [],
        total: 0,
      };
    }
  }
  /** 查询事故相关配件 */
  async getAccidentRelatedParts(body: IAccidentRelatedPartsParams) {
    try {
      const { result } = await httpClient.post<IAccidentRelatedPartsRes>(
        `${partsMindBaseUrl}/parts-mind/vehicle-repair-parts/query-related-parts`,
        {
          body,
        }
      );
      return {
        parts: result?.parts || [],
        total: result?.total || 0,
      };
    } catch {
      return {
        parts: [],
        total: 0,
      };
    }
  }
  /** 根据事故场景描述查询场景，相似搜索 */
  async getSimilarRepairScenarios(body: { accidentText: string }) {
    try {
      const { result } = await httpClient.post<ISimilarRepairScenariosRes>(
        `${partsMindBaseUrl}/parts-mind/vehicle-repair-parts/query-accident-scenario`,
        {
          body,
        }
      );
      return {
        scenarios: result?.scenarios || [],
        total: result?.total || 0,
      };
    } catch {
      return {
        scenarios: [],
        total: 0,
      };
    }
  }
}
