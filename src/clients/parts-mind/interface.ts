export interface IRepairScenariosParams {
  // 车品牌编码（必填）
  brandCode: string;
  // 车类型，如"轿车"、"SUV"（必填）
  vehicleTypeCode: string;
  // 配件标准编码列表（必填）
  stdCodes: string[];
  // 提示词
  prompt?: string;
}

export interface IRepairScenariosRes {
  accidents: {
    accidentCode: string;
    accidentText: string;
  }[];
  total: number;
}

export interface IAccidentRelatedPartsParams {
  accidentCode: string;
  brandCode: string;
  vehicleTypeCode: string;
}
export interface IAccidentRelatedPartsRes {
  parts: {
    brand_code: string;
    brand_name: string;
    vehicle_type: string;
    part_std_code: string;
    part_std_name: string;
    description: string;
  }[];
  total: number;
}

export interface ISimilarRepairScenariosRes {
  scenarios: {
    accidentCode: string;
    accidentText: string;
  }[];
  total: number;
}