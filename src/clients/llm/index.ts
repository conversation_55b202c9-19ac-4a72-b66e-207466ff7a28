import { OpenAI, ChatOpenAI, ChatOpenAIFields } from "@langchain/openai";
import config from "@casstime/config";
import { M3EEmbedding } from "./M3EEmbedding";

export const chatglm = new OpenAI(
  {
    temperature: 0.3,
    modelName: "glm-4-flashx",
    openAIApiKey: config.get("LLM_API_KEY"),
  },
  {
    baseURL: config.get("LLM_API_BASE_URL"),
  }
);

export const chatglm_chat = new ChatOpenAI(
  {
    temperature: 0.3,
    modelName: "glm-4-flashx",
    openAIApiKey: config.get("LLM_API_KEY"),
  },
  { baseURL: config.get("LLM_API_BASE_URL") }
);

export const doubao_chat = new ChatOpenAI(
  {
    temperature: 0.3,
    modelName: "Doubao-1.5-pro-32k",
    openAIApiKey: config.get("DOUBAO_API_KEY"),
  },
  { baseURL: config.get("DOUBAO_API_BASE_URL") }
);

export const doubaoDeepSeek_chat = new ChatOpenAI(
  {
    temperature: 0.3,
    modelName: "Doubao-DeepSeek-V3",
    openAIApiKey: config.get("DOUBAO_API_KEY"),
  },
  { baseURL: config.get("DOUBAO_API_BASE_URL") }
);

export const m3e_embedding = new M3EEmbedding();

/**
 * @deprecated 请使用 m3e_embedding.embedQuery
 */
export const embedQuery = async (query: string) => {
  return m3e_embedding.embedQuery(query);
};


export const getChatOpenAI = ({ fields }: { fields?: Partial<ChatOpenAIFields> }) => {
  return new ChatOpenAI(
    {
      temperature: 0.3,
      modelName: "gpt-4o",
      openAIApiKey: config.get("DOUBAO_API_KEY"),
      ...fields,
    },
    { baseURL: config.get("DOUBAO_API_BASE_URL") }
  );
}
