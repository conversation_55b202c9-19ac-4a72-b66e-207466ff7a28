import config from "@casstime/config";

export class M3EEmbedding {
  async embedQuery(query: string) {
    const response = await fetch(`${config.get("LLM_API_BASE_URL")}/embeddings`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${config.get("LLM_API_KEY")}`,
      },
      body: JSON.stringify({
        model: "m3e-large",
        input: [query],
      }),
    });
    const data = await response.json();
    return data.data[0].embedding;
  }
}
