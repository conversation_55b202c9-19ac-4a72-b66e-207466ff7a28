import logger from "@/common/logger";
import httpClient from "../../common/clients/http.client";
import { IGetStoresLevelBatchResponseDTO } from "./interface";
export class StoreClient {
  async getStoreLevelinfo(storeIds: string[]) {
    if (!storeIds.length) {
      return [];
    }
    try {
      const { result } = await httpClient.post<IGetStoresLevelBatchResponseDTO[]>(
        `/store-level-service/store-level/info/component`,
        {
          json: { storeIds },
        }
      );
      return result;
    } catch (error) {
      logger.error("店铺等级信息获取失败", error);
      return [];
    }
  }
}
