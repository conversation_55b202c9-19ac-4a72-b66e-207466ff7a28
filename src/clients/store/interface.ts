export interface IGetStoresLevelBatchParamsDTO {
  /** 每次获取的列表大小 */
  batchSize?: number;
  /** 变量(CHANGE)或全量(ALL) */
  batchType?: string;
  /** 顺序查询的最后一个店铺 */
  storeId?: string;
  /** 店铺ID列表 */
  storeIds?: string[];
}
export interface IGetStoresLevelBatchResponseDTO {
  /** 店铺排名信息变化数值（保留两位小数） */
  comparedScore?: number;
  /** 店铺排名信息变化类型：UP-上升；DOWN-下降；SAME-相同无变化 */
  comparedType?: string;
  /** 是否是新店铺 */
  isNewStore?: boolean;
  /** 计算周期/更新时间：2020.5.1-2020.5.31 */
  periodInfo?: string;
  /** 排名信息 */
  rankData?: IRankData[];
  /** 店铺赛道的编码 */
  storeChannelCode?: string;
  /** 店铺赛道的名称 */
  storeChannelName?: string;
  /** 店铺ID */
  storeId?: string;
  /** 店铺等级的编码 */
  storeLevelCode?: string;
  /** 店铺等级的名称 */
  storeLevelName?: string;
  /** 店铺等级的图标 */
  storeLevelUrl?: string;
  /** 店铺排名信息（保留两位小数） */
  storeRanking?: number;
}

export interface IRankData {
  beyondRanking?: string; // 店铺排名超越信息（保留两位小数）
  comparedScore?: string; // 店铺排名信息变化数值（保留两位小数）
  comparedType?: string; // 店铺排名信息变化类型：UP-上升或相同无变化；DOWN-下降
  isNewStore?: boolean; // 是否是新店铺
  periodInfo?: string; // 计算周期/更新时间：2020.5.1-2020.5.31
  rankData?: object; // 排名信息
  storeChannelCode?: string; // 店铺赛道的编码
  storeChannelName?: string; // 店铺赛道的名称
  storeId?: string; // 店铺ID
  storeLevelCode?: string; // 店铺等级的编码
  storeLevelName?: string; // 店铺等级的名称
  storeLevelUrl?: string; // 店铺等级的图标
  storeRanking?: string; // 店铺排名信息（保留两位小数）
}
