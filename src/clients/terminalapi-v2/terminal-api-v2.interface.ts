export interface V2Res<T> {
  errorCode?: number;
  message?: string;
  data: T;
}

export interface IGetCurrentUserData {
  appUser?: boolean; // (boolean, optional): 是否是app用户 ,
  applicationStamp?: number; // (number, optional): 申请、离职时间 ,
  authenticated?: string; // (string, optional): 是否已认证 = ['Y', 'N'],
  isAuthenticated?: boolean; // (boolean, optional): 是否已认证 ,
  isSimpleInquiryAllowed?: boolean; // (boolean, optional): 是否允许发布简易询价 ,
  cellphone?: string; // (string, optional): 手机号码 ,
  contactNumber?: string; // (string, optional): 固定电话号码 ,
  email?: string; // (string, optional): email地址 ,
  enabled?: boolean; // (boolean, optional): 该帐号是否可用 ,
  entryApplied?: boolean; // (boolean, optional): 是否处于待入职申请中 ,
  gender?: string; // (string, optional): 性别, 0:男,1:女 = ['1', '0'],
  joinParty?: boolean; // (boolean, optional): 是否加入公司 ,
  needChangePassword?: boolean; // (boolean, optional): 是否需要修改密码 ,
  nickname?: string; // (string, optional): 昵称 ,
  companyName?: string; // (string, optional): 公司名称 ,
  orgType?: string; // (string, optional): 用户组织类型
  portraitUrl?: string; // (string, optional): 用户头像Url ,
  position?: string; // (string, optional):
  registerCompleted?: boolean; // (boolean, optional): 注册流程是否整, 移动端需补全公司信息后转为true ,
  updateTimes?: number; // (number, optional): userLoginName修改次数 ,
  userLoginId?: string; // (string, optional): 当前userLoginId ,
  userLoginName?: string; // (string, optional): 当前userLoginName, 前端的登录帐号 ,
  userName?: string; // (string, optional): 用户名 ,
  garageCompanyId?: string; // (string, optional): 公司id ,
  status?: string; // (string, optional): 员工状态 ,
  userParentName?: string; // (string, optional): 主账号name ,
  userParentId?: string; // (string, optional): 主账号ID ,
  entryCompanyId?: string; // (string, optional): 待入职公司id ,
  address?: string; // (string, optional): 详细地址 ,
  cityGeoId?: string; // (string, optional): 公司所在地市ID ,
  countyGeoId?: string; // (string, optional): 公司所在地区/县ID ,
  provinceGeoId?: string; // (string, optional): 公司所在地省ID ,
  provinceGeoName?: string; // (string, optional): 省名称 ,
  cityGeoName?: string; // (string, optional): 市名称 ,
  countyGeoName?: string; // (string, optional): 县、区名称 ,
  villageGeoId?: string; // (string, optional): 街道ID ,
  villageGeoName?: string; // (string, optional): 街道名称 ,
  actualAddress?: string; // (string, optional): 完整的公司地址 ,
  companyCode?: string; // (string, optional): 公司Code ,
  isShowEvaluateEntrance: boolean; // (boolean): 是否显示评价入口 ,
  isSignOrderAgreement: boolean; // (boolean): 是否显示‘只看订货’入口 ,
  isShowQuoteStoreRemark: boolean; // (boolean): 是否显示询价设置中的商家评价， A26 不做默认为false ,
  isShowQuoteStoreLevel: boolean; // (boolean): 是否显示询价设置中的商家等级 ,
  accountId?: string; // (string, optional): accountId
}

export interface IOpenInvoiceTypeDTO {
  openInvoiceType?: string; // YES:开票；NO:不开票;BOTH:都可以;''未设置
  isRequireItemInvoice?: boolean; // 是否需要对项发票
}

export interface IAddressItem {
  id?: string; // id (string, optional)?: 收货地址ID ,
  receiverName?: string; // receiverName (string, optional)?: 收货人 ,
  provinceGeoId?: string; // provinceGeoId (string, optional)?: 省ID ,
  provinceGeoName?: string; // provinceGeoName (string, optional)?: 省名称 ,
  cityGeoId?: string; // cityGeoId (string, optional)?: 市ID ,
  cityGeoName?: string; // cityGeoName (string, optional)?: 市名称 ,
  countyGeoId?: string; // countyGeoId (string, optional)?: 县、区ID ,
  countyGeoName?: string; // countyGeoName (string, optional)?: 县、区名称 ,
  villageGeoId?: string; // villageGeoId (string, optional)?: 村、区ID ,
  villageGeoName?: string; // villageGeoName (string, optional)?: 村、区名称 ,
  address?: string; // address (string, optional)?: 详细地址 ,
  contactTel?: string; // contactTel (string, optional)?: 座机号码 ,
  contactNumber?: string; // contactNumber (string, optional)?: 手机号码 ,
  userLoginId?: string; // userLoginId (string, optional)?: 用户登录ID ,
  isComplete?: true; // isComplete (boolean, optional)?: 地址是否完善 ,
  isAccurate?: true; // isAccurate (boolean, optional)?: 地址是否精确，是否需要修正 ,
  isDefaultAddress?: true; // isDefaultAddress (boolean, optional)?: 是否默认地址 ,
  actualAddress?: string; // actualAddress (string, optional)?: 完整地址，所有地址拼接值
  latitude?: number; //纬度
  longitude?: number; //经度

  createdBy?: string; // createdBy (string, optional)?: 当前操作人 ,
  houseNumber?: string;
}
