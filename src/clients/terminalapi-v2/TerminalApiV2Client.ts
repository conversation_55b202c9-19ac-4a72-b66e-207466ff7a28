import { HttpClientForV2 } from "@/common/clients/HttpClientForV2";
import { IAddressItem, IGetCurrentUserData, IOpenInvoiceTypeDTO, V2Res } from "./terminal-api-v2.interface";

export class TerminalApiV2Client extends HttpClientForV2 {
  /**
   * 获取当前用户信息
   * @returns
   */
  getCurrentUser(): Promise<V2Res<IGetCurrentUserData>> {
    return this.get("/terminal-api-v2/users/_current");
  }
  /**
   * 获取当前用户的默认地址
   * @returns
   */
  getDefaultAddress(): Promise<V2Res<IAddressItem>> {
    return this.get("/terminal-api-v2/address/address");
  }

  /**
   * 查询用户询价开票信息
   * @returns {Promise}
   */
  getOpenInvoiceType(): Promise<V2Res<IOpenInvoiceTypeDTO>> {
    const url = "/terminal-api-v2/ai_chat/inquiry/open_invoice_type";
    return this.get(url);
  }
}
