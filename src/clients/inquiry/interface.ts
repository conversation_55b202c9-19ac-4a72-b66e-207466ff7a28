export interface IAddressInfos {
  /** 地址ID */
  addressId?: string;
  /** 区Id */
  countyGeoId?: string;
  /** 区名称 */
  countyGeoName?: string;
  /** '市ID' */
  geoCityId?: string;
  /** '市名称' */
  geoCityName?: string;
  /** 省份ID */
  geoProvinceId?: string;
  /** '省名称' */
  geoProvinceName?: string;
  /** 街道Id */
  villageGeoId?: string;
  /** 街道名字 */
  villageGeoName?: string;
}

export interface IInquiryBaseInfos {
  /** 询价单追加品质需求 */
  appendInquiryQualities?: string[];
  /** 云仓 */
  cloudBase?: string;
  /** 创建人 */
  createdBy?: string;
  /** 创建时间 */
  createdStamp?: string;
  /** 是否被领取 */
  customerServiceId?: string;
  /** 发布源 */
  customerServiceQq?: string;
  /** 过期时间 */
  expiredStamp?: string;
  /** 询价单总金额 */
  inquiryAmount?: number;
  /** 询价单id */
  inquiryId?: string;
  /** 询价单品质需求 */
  inquiryQualities?: string[];
  /** 询价单类型InquiryTypeEnum */
  inquiryType?: string;
  /** 保险公司编码 */
  insuranceCompanyCode?: string;
  /** 保险公司简称 */
  insuranceCompanyShortName?: string;
  /** 是否保险直供 */
  insuranceDirect?: boolean;
  /** 有无内部编码 */
  internalCode?: string;
  /** 发票类型 */
  invoiceType?: string;
  /** 是否中端车事故单 */
  isAccident?: boolean;
  /** 是否匿名; 0 : 实名 ; 1 : 匿名 */
  isAnonymous?: string;
  /** 1为新询价单，0为旧询价单 */
  isMarkInquiry?: number;
  /** 0不需要到店价，1需要到店价 */
  isNeedWholePrice?: number;
  /** 是否为新客询价单 */
  isNewInquiry?: string;
  /** 0为不需要对项发票，1为需要对项发票 */
  isRequireItemInvoice?: number;
  /** SkipDecodeEnum 1已跳过译码的 */
  isSkipDecode?: number;
  /** 修改时间 */
  lastUpdatedStamp?: string;
  /** 需求数量 */
  needCount?: number;
  /** 新版本 是：Y,否：其他 */
  newVersion?: string;
  /** 是否需要替换件 */
  noReplacement?: string;
  /** 前询价单 */
  oldInquiryId?: string;
  /** YES:开票；NO:不开 */
  openInvoiceType?: string;
  /** 品质 */
  quality?: string;
  /** 开始译码时间 */
  resolveBeginDate?: string;
  /** 译码结束时间 */
  resolveEndDate?: string;
  /** '解析状态:Y已解析N未解析C继续解析' */
  resolveStatus?: string;
  /** 译码员名字 */
  resolveUserName?: string;
  /** 询价单类型ID */
  shoppingListTypeId?: string;
  /** 译码单ID */
  sourceId?: string;
  /** 状态ID */
  statusId?: string;
  /** 联系人 */
  userName?: string;
}

export interface IInquiryCarInfos {
  /** 汽车品牌 */
  carBrandId?: string;
  /** 车品牌名称 */
  carBrandName?: string;
  /** 车型ID */
  carModelId?: string;
  /** 车型名称 */
  carModelName?: string;
  /** 底盘号 */
  chassisId?: string;
  /** '销售车型名称' */
  engineType?: string;
  /** epc车型ID */
  epcModelCode?: string;
  /** epc车型名称 */
  epcModelName?: string;
  /** 询价单id */
  inquiryId?: string;
  /** 车牌 */
  licensePlate?: string;
  /** 主机厂ID */
  locationId?: string;
  /** 主机厂名称 */
  locationName?: string;
  /** 出厂日期 */
  productionDate?: string;
  /** 车系英文名称 */
  seriesEn?: string;
  /** 车系ID */
  seriesId?: string;
  /** 车系中文名称 */
  seriesZh?: string;
  /** '销售车型ID' */
  vehicleType?: string;
  /** vin码 */
  vin?: string;
}

export interface IInquiryTagInfosItem {
  /** id */
  id?: number;
  /** 询价单号ID-->shoppingListId */
  inquiryId?: string;
  /** 标签类型 */
  tagType?: string;
  /** 标签值 */
  tagValue?: string;
}

export interface IInquiryUserInfos {
  /** '机号码 */
  contactNumber?: string;
  /** 主账号ID */
  corporateId?: string;
  /** 主账号名称 */
  corporateName?: string;
  /** 公司账号ID */
  garageCompanyId?: string;
  /** 公司名称 */
  garageCompanyName?: string;
  /** '集团用户ID' */
  groupUserId?: string;
  /** '集团用户名称' */
  groupUserName?: string;
  /** '联系人' */
  userName?: string;
}

export interface IInquiryResponse {
  /** 地址信息 */
  addressInfos?: IAddressInfos;
  /** 询价单自带信息 */
  inquiryBaseInfos?: IInquiryBaseInfos;
  /** 车辆信息 */
  inquiryCarInfos?: IInquiryCarInfos;
  /** 询价标签信息 */
  inquiryTagInfos?: IInquiryTagInfosItem[];
  /** 询价单(ShoppingList)的DTO，与ShoppingList一一对应 */
  inquiryUserInfos?: IInquiryUserInfos;
}

export enum InquiryStatus {
  UNQUOTE = "UNQUOTE", // 报价中
  QUOTE = "QUOTE", // 报价完成
  EXPIRED = "EXPIRED", // 已过期
}

export interface IInquiryInfo {
  /**
   * 是否允许追加
   */
  allowAppend?: string;
  /**
   * 汽车品牌编码
   */
  carBrandCode?: string;
  /**
   * 汽车品牌
   */
  carBrandId?: string;
  /**
   * 车品牌名称
   */
  carBrandName?: string;
  /**
   * 车型ID
   */
  carModelId?: string;
  /**
   * 车型名称
   */
  carModelName?: string;
  /**
   * 底盘号
   */
  chassisId?: string;
  /**
   * 手机号码
   */
  contactNumber?: string;
  /**
   * 主账号ID
   */
  corporateId?: string;
  /**
   * 主账号名称
   */
  corporateName?: string;
  /**
   * 区ID
   */
  countyGeoId?: string;
  /**
   * 区名称
   */
  countyGeoName?: string;
  /**
   * 创建人
   */
  createdBy?: string;
  /**
   * 创建人名称
   */
  createdName?: string;
  /**
   * 创建时间
   */
  createdStamp: number;
  /**
   * 已譯碼需求数量
   */
  decodedNeedCount?: number;
  /**
   * 期望到货时间
   */
  desiredTime?: number;
  /**
   * 启动跨区域首轮分配召回 1:是，0：否
   */
  enableCrossRegionalFirstRecall?: number;
  /**
   * 启动跨区域报价不满足召回 1:是，0：否
   */
  enableCrossRegionalQuoteRecall?: number;
  /**
   * 销售车型名称
   */
  engineType?: string;
  /**
   * epc车型ID
   */
  epcModelCode?: string;
  /**
   * epc车型名称
   */
  epcModelName?: string;
  /**
   * 过期时间
   */
  expiredStamp: number;
  /**
   * 维修厂ID
   */
  garageCompanyId?: string;
  /**
   * 维修厂名称
   */
  garageCompanyName?: string;
  /**
   * 市ID
   */
  geoCityId?: string;
  /**
   * 市名称
   */
  geoCityName?: string;
  /**
   * 省份ID
   */
  geoProvinceId?: string;
  /**
   * 省名称
   */
  geoProvinceName?: string;
  /**
   * 集团用户ID
   */
  groupUserId?: string;
  /**
   * 集团用户名称
   */
  groupUserName?: string;
  /**
   * 询价单号ID-->shoppingListId
   */
  id?: string;
  /**
   * 冷热数据
   */
  inquiryDataType?: string;
  /**
   * 询价方式
   */
  inquiryMethod?: string;
  /**
   * 询价单类型
   */
  inquiryType?: string;
  /**
   * 发票类型
   */
  invoiceType?: string;
  /**
   * 是否中端车事故单
   */
  isAccident?: boolean;
  /**
   * 是否匿名，0:实名,1:匿名
   */
  isAnonymous?: string;
  /**
   * 0为未译码完成，1为译码完成
   */
  isInquiryDecoded?: number;
  /**
   * 0不需要到店价，1需要到店价
   */
  isNeedWholePrice?: number;
  /**
   * 是否为新客询价单
   */
  isNewInquiry?: string;
  /**
   * 0为不需要对项发票，1为需要对项发票
   */
  isRequireItemInvoice?: number;
  /**
   * 0为非简易询价，1为简易询价
   */
  isSimpleInquiry?: number;
  /**
   * 已跳过译码的 SkipDecodeEnum
   */
  isSkipDecode?: number;
  /**
   * 原needCount
   */
  itemsNum?: number;
  /**
   * 最后更新时间
   */
  lastUpdatedStamp?: string;
  /**
   * 收货地址的纬度
   */
  latitude?: number;
  /**
   * 车牌号
   */
  licensePlate?: string;
  /**
   * 车系编码
   */
  locationId?: string;
  /**
   * 车系编码名称
   */
  locationName?: string;
  /**
   * 收货地址的经度
   */
  longitude?: number;
  /**
   * 不需要替换件
   */
  noReplacement?: string;
  /**
   * 开票类型YES:开票；NO:不开
   */
  openInvoiceType?: string;
  /**
   * 生产日期
   */
  productionDate?: string;
  /**
   * 询价需求品质
   */
  qualities?: string[];
  /**
   * 品质
   */
  quality?: string;
  /**
   * 译码开始时间
   */
  resolveBeginDate?: string;
  /**
   * 开始译码时间
   */
  resolveCreatedStamp?: string;
  /**
   * 译码结束时间
   */
  resolveEndDate?: string;
  /**
   * 译码结果ID，原sourceId
   */
  resolveId?: string;
  /**
   * 译码状态
   */
  resolveStatusId?: string;
  /**
   * 译码员id
   */
  resolveUserId?: string;
  /**
   * 译码员名称
   */
  resolveUserName?: string;
  /**
   * 车系英文名称
   */
  seriesEn?: string;
  /**
   * 车系id
   */
  seriesId?: string;
  /**
   * 车系中文名称
   */
  seriesZh?: string;
  /**
   * 简易询价需求类型列表
   */
  simpleItemTypeIds?: string[];
  /**
   * 原customerServiceQq
   */
  source?: string;
  /**
   * 状态ID
   */
  statusId?: string;
  /**
   * 疑似事故单方案
   */
  suspectedCustomizeScheme?: string;
  /**
   * 用户需求（前3个）
   */
  userNeed?: string;
  /**
   * 销售车型ID
   */
  vehicleType?: string;
  /**
   * 预览报价详情页权限
   */
  viewQuoteInfoAuthority?: boolean;
  /**
   * 街道ID
   */
  villageGeoId?: string;
  /**
   * 街道名称
   */
  villageGeoName?: string;
  /**
   * 车辆vin码
   */
  vin?: string;
  /**
   * 是否保险事故车
   */
  insuranceDirect?: boolean;
  /**
   * 保险公司简称名字
   */
  insuranceCompanyShortName?: string;
  /**
   * 保险公司代码
   */
  insuranceCompanyCode?: string;
  /**
   * 询价单属性
   */
  inquiryTags?: IInquiryTags[];
}

interface IInquiryTags {
  inquiryId: string;
  tagType: string;
  tagValue: string;
}

export interface IGetUserNeedsList {
  needId: string;
  shoppingListId: string;
  needsName: string;
  quantity: number;
  remark: string;
  createdBy: string;
  createdStamp: number;
  lastUpdatedStamp: number;
  statusId: string;
  source: string;
  adjustmentProductType: string;
  isSuggest: string;
}

export interface IGetDecodeResult {
  decodeResultId: string;
  inquiryId: string;
  partsNum: string;
  quantity: number;
  remark: string;
  partsName: string;
  statusId: string;
  createdBy: string;
  createdStamp: number;
  lastUpdatedStamp: number;
  competitivePrice: number;
  userNeedsId: string;
  source: string;
  sellType: string;
  adjustmentProductType: string;
  isSuggest: string;
  needPackage: string;
  decodeSource: string;
  decoderId: string;
  standardName: string;
  categoryCode: string;
  standardNameCode: string;
  recommendAmount: number;
}

/**
 * 询价单状态信息
 */
export interface IInquiryStatusDTO {
  inquiryId?: string;
  inquiryStatus?: string;
  carBrandId?: string;
  carModelName?: string;
  partInfo?: IUserNeedStoreDTO[];
}

export interface IUserNeedStoreDTO {
  storeId?: string;
  storeName?: string;
  userNeeds?: IUserNeedsItemDTO[];
}

export interface IUserNeedsItemDTO {
  userNeedsItemId?: string;
  userNeedName?: string; // 需求名称
  userNeedsItemStatus?: string; // 配件译码状态
}

export interface IInquiryStatusRequest {
  inquiryId: string;
  userNeedsItemIds?: string[];
}
