import { IMakePlanResponseDTO } from "@/routes/makePlan";
import httpClient from "../../common/clients/http.client";
import {
  IInquiryResponse,
  IGetUserNeedsList,
  IGetDecodeResult,
  IInquiryStatusDTO,
  IInquiryStatusRequest,
} from "./interface";

export class InquiryClient {
  async getInquiryInfo(inquiryId: string) {
    const { result } = await httpClient.get<IInquiryResponse>(`/inquiry-service/inquiry/${inquiryId}`);
    return result;
  }

  async makePlanCallback(json: IMakePlanResponseDTO) {
    const { result } = await httpClient.post("inquiry-programme-service/llm-task/debugging-items/callback", { json });
    return result;
  }

  async getUserNeedsList(inquiryId: string) {
    const { result } = await httpClient.post<IGetUserNeedsList[]>("/inquiry-service/inquirybody/userneeds/list", {
      json: { inquiryId },
    });
    return result;
  }

  async getDecodeResultList(inquiryId: string) {
    const { result } = await httpClient.post<IGetDecodeResult[]>("/decode-service/decode/result/list/filter", {
      json: { inquiryId },
    });
    return result;
  }

  async getListInquiryStatus(json: IInquiryStatusRequest[]) {
    const { result } = await httpClient.post<IInquiryStatusDTO[]>("/inquiry-service/api/v1/listInquiryStatus", {
      json,
    });
    return result;
  }
}
