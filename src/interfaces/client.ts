export interface TerminalRes<T> {
  data: T;
  errorCode: number;
  message?: string;
  teamCode?: number;
  code?: number;
  errorMessage?: string;
}

export interface EcServiceRes<T> {
  statusCode: number;
  result: T;
}

export interface MaindataRes<T> {
  message: string;
  status: number;
  data: T;
}

export enum ILabel {
  "VIN码" = "VIN码",
  "其他" = "其他",
  "工单" = "工单",
  "故障诊断" = "故障诊断",
  "油品" = "油品",
  "燃油格" = "燃油格",
  "爆炸图" = "爆炸图",
  "物流包裹" = "物流包裹",
  "系统截图" = "系统截图",
  "聊天截图" = "聊天截图",
  "证件" = "证件",
  "轮胎" = "轮胎",
  "配件" = "配件",
  "铭牌" = "铭牌",
}

export interface ICandidateDTO {
  label: string;
  prob: number;
}

export interface ImageClassifyRes {
  label?: ILabel;
  score?: number;
  labels?: string[];
  probs?: number[];
}

export interface IRecognizeVinResult {
  errorCode?: string;
  errorMassage?: string;
  result?: string;
  success: boolean;
}
export interface IRecognizePartsNamesResult {
  code: string;
  data: IHandleWritingDto[];
  errorMessage: string;
}
interface IPointDto {
  x?: string;
  y?: string;
}
export interface IHandleWritingDto {
  words?: string; // 需求名称
  location?: IPointDto[]; // 坐标
  isNeed?: boolean; // 是否是配件
}
export interface IImageParams {
  imageUrl: string;
  channel?: string;
  application?: string; // 应用：（新版本必传）MALL_MAINTENANCE_SHOP_PC 电商维修厂PC; MALL_MAINTENANCE_SHOP_APP 电商维修厂APP; DISTRIBUTION_STAGING_PC 配销宝工作台PC;  WORKSHOPONE_STAGING_PC 1号车间工作台PC
  scene?: string; // 场景：（新版本必传）COMMON_INQUIRY 常规询价; ACCIDENT_VEHICLE 事故车询价; FAST_DECODEING 快速译码 （配销宝）; WORK_ORDER_QUOTATION 工单报价（1号车间）;
  os?: string; // 操作系统：（新版本必传） iOS、Android、HarmonyOS、Windows、Mac、Linux
  [key: string]: any;
}

interface IGetMatchNameParam {
  name?: string;
  serialNum?: number;
}

export interface IMatchNameParams {
  needs?: IGetMatchNameParam[];
}

export interface IMatchNeedsData {
  name: string;
  similar: number;
  stdName: string;
  serialNum: number;
  threshold: number;
}

export interface IMatchNeedsResult {
  code: string;
  data: IMatchNeedsData[];
  errorMessage: string;
}

export interface IImageRotateSizeRes {
  label?: string;
  score?: number;
  labels?: string[];
  probs?: number[];
}

export interface IImageRecognizeVinRes {
  vins: string[];
}

export interface ISimilarPartsRes {
  distance: number;
  img_url: string;
  name: string;
}

interface IBoxBounding {
  x: number;
  y: number;
  width: number;
  height: number;
  angle: number;
  confidence: number;
  label: number;
}

export interface IProOcrData {
  text: string;
  boxes: IBoxBounding;
  format?: string;
  /** "text", "bar_code", "qr_code", "logo" */
  type: string;
  prob: number;
}
export interface IProOcrRes {
  data: IProOcrData[];
}
