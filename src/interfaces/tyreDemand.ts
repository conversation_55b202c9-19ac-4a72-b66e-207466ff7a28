export interface IOriginalItemsItemDTO {
  /** 前轮，后轮 */
  remark?: string;
  /** 轮胎规格名称 */
  description?: string;
  /** 品牌代码 */
  brandCode?: string;
  /** 品牌名称 */
  brandName: string;
  /** 花纹 */
  pattern?: string;
  /** 防爆标识 */
  explosionProofMark?: string;
}

export interface ITyreDemandResponse {
  /** 轮胎收货地址信息 */
  address?: string;
  /** 询价单号 */
  demandId?: string;
  /** 状态 (值: UNQUOTE-报价中; QUOTE-报价完成; EXPIRED-已过期) */
  statusId?: string;
  /** 询价需求 */
  originalItems?: IOriginalItemsItemDTO[];
  /** 用户电话号码 */
  phoneNum?: string;
  /** 用户名称 */
  userName?: string;
  /** vin */
  vin?: string;
  /** 车辆品牌Code */
  carBrandCode?: string;
  /** 车辆品牌名字 */
  carBrandName?: string;
  /** 车型名称 */
  saleModelName?: string;
  /** 车型code */
  saleModelCode?: string;
  /** 是否开票 */
  isOpenInvoice?: boolean;
  /** 发票类型 NORMAL普通发票，VAT增值税专用发票 */
  invoiceType?: string;
  /** 创建时间 */
  createdDate?: number;
}
