export interface INewFastGptParamsSchema {
  Body: {
    messages: Message[];
    user: string;
    conversationId?: string;
    extra: {
      buyerUserLoginId?: string;
      sellerUserLoginId?: string;
      storeId?: string;
      orderId?: string;
      inquiryId?: string;
      aftersaleId?: string;
    };
  };
}

// 输入数据结构
export interface IFastGptRequestData {
  messages?: Message[];
  variables?: Variables;
  responseChatItemId?: string;
  chatId?: string;
  shareId?: string;
  outLinkUid?: string;
  retainDatasetCite?: boolean;
  detail?: boolean;
  stream?: boolean;
}

interface IContent {
  type: string;
  text: string;
  image_url?: { url: string };
  url?: string;
  name?: string;
}

interface Message {
  role?: string;
  content?: string | IContent[];
}

interface Variables {
  user?: string;
  extra?: ExtraVariables;
}

interface ExtraVariables {
  orderId?: string;
  sellerUserLoginId?: string;
}

// 响应数据结构
export interface IFastGptResponseData {
  id?: string;
  model?: string;
  usage?: UsageStats;
  choices?: Choice[];
}

interface UsageStats {
  prompt_tokens?: number;
  completion_tokens?: number;
  total_tokens?: number;
}

interface Choice {
  message?: ResponseMessage;
  finish_reason?: string;
  index?: number;
}

interface ResponseMessage {
  role?: string;
  content?: string;
}
