// tslint:disable
/**
 * 特性开关返回对象
 */
export type Feature = {
    /**
     * 特性开关状态，true:打开,false:关闭
     */
    enabled?: boolean;
    /**
     * 特性开关ID
     */
    featureId?: string;
    /**
     * 特性开关名称
     */
    featureName?: string;
    /**
     * 特性开关策略状态码，开关出现多种策略时使用
     */
    featureStrategyCode?: string;
    /**
     * 特性开关策略状态码，开关出现多种策略时使用
     */
    selectedStrategyCode?: string;
    /**
     * 特性开关策略状态码，开关可能出现多种策略
     */
    strategyCodes?: string[];
    /**
     * 分组
     */
    computeResults?: IComputeResults[];

    reason?: string;
};

interface IComputeResults {
    strategyId?: string;
    enabled?: boolean;
    reason?: string;
}