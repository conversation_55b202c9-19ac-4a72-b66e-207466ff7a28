/** 询价单列表item */
export interface InquiryListItem {
  carBrandId?: string;
  carBrandName?: string;
  carModelName?: string;
  createdName?: string;
  createdStamp?: number;
  engineType?: string;
  inquiryId: string;
  statusDesc?: string;
  statusId?: string;
  userNeed?: string;
}

/** 车型信息 */
export interface ICarModel {
  model?: string;
  vinCode?: string;
  carBrandCode?: string;
  carBrandId?: string;
  carBrandName?: string;
  isDefaultBrand?: boolean;
  locationId?: string;
  locationName?: string;
  seriesId?: string;
  seriesZh?: string;
  seriesEn?: string;
  epcModelCode?: string;
  epcModelName?: string;
  garageBrandInquiryState?: string;
  isSelectedSecondHandQuality?: boolean;
  phoneIntroduceUrl?: string;
  brandLogo?: string;
  isSupport?: boolean;
  saleModelName?: string;
  saleModelCode?: string;
  engineType?: string;
  // 轿车/SUV
  vehicleTypeClass?: string;
}

/** 默认开票信息 */
export interface IInquiryInvoiceRes {
  isGroupUser?: boolean; //是否集团用户
  isRequireItemInvoice?: boolean; //是否需要对项发票
  openInvoiceType?: "YES" | "NO"; // 开票类型 = ['YES', 'NO'],
  isShowSuppliers?: boolean; // 是否展示指定供应商
  invoiceType?: string; //发票类型 = ['VAT', 'normal', 'BOTH']
}

/** 地址信息 */
export interface IAddressItem {
  id?: string; // id (string, optional)?: 收货地址ID ,
  receiverName?: string; // receiverName (string, optional)?: 收货人 ,
  provinceGeoId?: string; // provinceGeoId (string, optional)?: 省ID ,
  provinceGeoName?: string; // provinceGeoName (string, optional)?: 省名称 ,
  cityGeoId?: string; // cityGeoId (string, optional)?: 市ID ,
  cityGeoName?: string; // cityGeoName (string, optional)?: 市名称 ,
  countyGeoId?: string; // countyGeoId (string, optional)?: 县、区ID ,
  countyGeoName?: string; // countyGeoName (string, optional)?: 县、区名称 ,
  villageGeoId?: string; // villageGeoId (string, optional)?: 村、区ID ,
  villageGeoName?: string; // villageGeoName (string, optional)?: 村、区名称 ,
  address?: string; // address (string, optional)?: 详细地址 ,
  contactTel?: string; // contactTel (string, optional)?: 座机号码 ,
  contactNumber?: string; // contactNumber (string, optional)?: 手机号码 ,
  userLoginId?: string; // userLoginId (string, optional)?: 用户登录ID ,
  isComplete?: true; // isComplete (boolean, optional)?: 地址是否完善 ,
  isAccurate?: true; // isAccurate (boolean, optional)?: 地址是否精确，是否需要修正 ,
  isDefaultAddress?: true; // isDefaultAddress (boolean, optional)?: 是否默认地址 ,
  actualAddress?: string; // actualAddress (string, optional)?: 完整地址，所有地址拼接值
  latitude?: number; //纬度
  longitude?: number; //经度

  createdBy?: string; // createdBy (string, optional)?: 当前操作人 ,
  houseNumber?: string;
}

// 原始需求
export interface IUserNeedsItem {
  /** 原始名称 */
  originalNeed: string;
  /** 配件名称 */
  needsName: string;
  /** SELECT: 选择,
         ADD: 译码员新增,
         MANUALLY: 手动,
         ELECTRONIC_CATALOG: 电子目录,
         MANUAL_ENTRY: 人工录入,
         UPDATE_DECODE_ERROR: 修改原始需求,
         MANUALLY_DECODE_COPY: 复制人工译码,
         USER_ADD: 维修厂新增,
         AUXILIARY: 辅助译码 */
  inquirySource?: string;
  /** 数量 */
  quantity: number;
  /** 是否是快流件 */
  isFastOe: boolean;
  /** 译码名称 */
  oeName?: string;
  /** 译码结果 */
  oeCode?: string;
  /** 标准名称 */
  stdName?: string;
  /** 标准名称code */
  stdNameCode?: string;
  /** 品类code */
  categoryCode?: string;
  /** 是否有译码结果 */
  hasResolved: boolean;
  /** 4s价格 */
  competitivePrice?: number;
  /** 是否推荐(必填) */
  isSuggest: boolean;
  /** 标准名称 */
  desc?: string;
  /** 备注 */
  remark?: string;
  /** 再次询价原来的needid */
  oldOeItemId?: string;
  /** 译码备注 */
  resolveRemark?: string;
  /** 备注图片(必填) */
  imageUrls: string[];
  /** 品质类型: DEFAULT_QUALITY 默选品质-整单默认品质;DEFAULT_CATEGORY_QUALITY 默选品质-品类默认品质-已澄清品质;RECOMMEND_CATEGORY_QUALITY 默选品质-品类推荐品质-已澄清品质 */
  qualityType: string;
  /** 用户选中的品质 ID 数组 */
  publishQualityList: string[];
  /** 默认勾选的品质，"checked": true, 的选项 */
  qualityList: string[];

  // 新增中端车接口字段
  strBrandId?: string;
  stdQuantity?: number;
  stdRemark?: string;
  stdInquirySource?: string;
  userLoginName?: string;
  brandId?: string;

  invalidNeedType?: string; // 无效需求类型,不是则不传  NON_DEMAND 非需求;MISMATCH_DEMAND  需求不匹配
  traceRequests?: unknown[]; // 需求追踪请求信息
}

export interface IInquiryAdditionalImagesItem {
  /** 文件类型：PICTURE */
  mediaType?: string;
  /** PARTSLIST:配件清单;NAMEPLATE:铭牌图; HEADSTOCK:车头图;TAILSTOCK:车尾图; HAND_WRITE_NEEDS: 手写工单*/
  typeId: string;
  /** 资源链接 */
  url: string;
}

export interface IShoppingListTagItem {
  /** 整车代码标签:CAR_MODEL */
  tagType: string;
  /** 标签值 */
  tagValue: string;
}

export interface InquiryReParameter {
  /** 询价单Id,询价单退回重新编辑时传 */
  shoppingListId?: string;
  /** 询价单Id,询价单过期重新询价时传 */
  reInquiryId?: string;
  /** vin码 */
  vin: string;
  /** N表示需要替换件 Y表示不需要替换件 */
  noReplacement?: string;
  /** 品牌id */
  carBrandId: string;
  /** 品牌名称 */
  carBrandName: string;
  /** 车型 */
  carModelName?: string;
  /** 用户名 */
  userName: string;
  /** 联系电话 */
  contactNumber: string;
  /** 是否开票 */
  isOpenInvoice: boolean;
  /** 品质 */
  qualities?: string[];
  /** SYSTEMHANDLER系统分配, MANHANDLER自行指定, COMBINATIONHANDLER组合分配 */
  quotedType?: string;
  /** 指定供应商id */
  storeIds?: string[];
  /** 来源 */
  source: string;
  /** 是否要选择品牌 */
  isSelectBrandFlag: boolean;
  /** 是否匿名询价 */
  isAnonymous: boolean;
  /** vin码扫描图片 */
  vinPicture?: string;
  /** 原始需求 */
  userNeeds?: IUserNeedsItem[];
  /** 是否需要对项发票 */
  isRequireItemInvoice?: boolean;
  /** 公司所在地省ID */
  provinceGeoId?: string;
  /** 公司所在地市ID */
  cityGeoId?: string;
  /** 公司所在地区/县ID */
  countyGeoId?: string;
  /** 省名称 */
  provinceGeoName?: string;
  /** 市名称 */
  cityGeoName?: string;
  /** 县、区名称 */
  countyGeoName?: string;
  /** 街道ID */
  villageGeoId?: string;
  /** 街道名称 */
  villageGeoName?: string;
  /** 产地id */
  locationId?: string;
  /** 产地(国产/进口) 一汽奥迪 */
  locationName?: string;
  /** 车系 */
  seriesId?: string;
  /** 车系中文名 */
  seriesZh?: string;
  /** 车系英文名 */
  seriesEn?: string;
  /** 销售车型ID */
  saleModelCode?: string;
  /** 销售车型名称 */
  saleModelName?: string;
  /** 是否跳过译码 */
  isSkipDecode?: boolean;
  /** 需求图片类型（铭牌，车头，车尾）集合 */
  picDemand?: string[];
  /** （铭牌，车头，车尾）图片URl */
  picDemandUrls?: string[];
  /** 配件清单图片URL */
  partsListUrls?: string[];
  /** 是否是事故车 */
  isAccidentInquiry?: boolean;
  /** 事故车的备注 */
  remarks?: string;
  /** 收货地址的经度 */
  longitude?: number;
  /** 收货地址的纬度 */
  latitude?: number;
  /** 地址id */
  addressId?: string;
  /** 本单业务规则 QUICK_REPAIR（快修快保）、REPAIR（维修）、CUSTOMIZE（事故） */
  tagValue?: string;
  /** 是否保险直供 true：保险单 false：非保险单 */
  isInsuranceDirect?: boolean;
  /** 保险公司代码 insuranceDirect字段为true时 必传 */
  insuranceCompanyCode?: string;
  /** 保险公司简称 insuranceDirect字段为true时 必传 */
  insuranceCompanyShortName?: string;
  /** 图片集合 */
  inquiryAdditionalImages?: IInquiryAdditionalImagesItem[];
  /** 标签集合 */
  shoppingListTag?: IShoppingListTagItem[];
  /** 区分智能采购助手通道 */
  inquiryAttributeRequests?: IInquiryAttribute[];
  /** 询价类型 */
  type?: InquiryTypeEnum;
}

export interface IInquiryAttribute {
  attributeType?: string;
  attributeValue?: string;
}

export interface ITyreInquiryReParameter {
  /** 地址信息入参 */
  address?: IAddress;
  /** 需求类型 轮胎询价：TYRE_DEMAND */
  demandType?: string;
  /** 维修厂公司id */
  garageCompanyId?: string;
  /** 维修厂公司名称 */
  garageCompanyName?: string;
  /** 集团用户id */
  corporateId?: string;
  /** 集团用户名称 */
  groupUserName?: string;
  /** 是否需要开票 需要：YES，不需要：NO */
  openInvoiceType?: string;
  /** 原始需求项次 */
  originalItems?: IOriginalItemsItem[];
  /** 询价来源 值: NO1ROOM-1号车间; IOS-苹果客户端; LAUNCH-安卓客户端; YWTX-易维天下; ANDROID-安卓客户端; PC-PC端; PRERECORD-预录单; WEIXIN-微信端; ERP-ERP */
  source?: string;
  /** 用户id */
  userId?: string;
  /** 用户名称 */
  userName?: string;
  /** 车辆信息入参 */
  vehicle?: IVehicle;
  /** vin码 */
  vin?: string;
  /** 重新询价时,旧的询价单 */
  oldDemandId?: string;
  /** 询价人电话号码 */
  contactNumber?: string;
  /** MASTER_DATA_AUTO: vin码自动带出规格.MANUAL_ENTRY:人工选择规格 */
  specificationSource?: string;
  // 开思电商  CASSMALL   博世  BOSCH   星辰大海  XCDH   智能采购助手  INTELLIGENT_PROCUREMENT_ASSISTANT
  platformSource?: string;
}

export enum SpecificationSourceEnum {
  MANUAL_ENTRY = "MANUAL_ENTRY",
  MASTER_DATA_AUTO = "MASTER_DATA_AUTO",
  PHOTO_RECOGNIZE = "PHOTO_RECOGNIZE",
}

/** WHOLE_CAR_PARTS 全车件询价 | HK_MC_TW_INQUIRY 港澳台询价 */
export enum InquiryTypeEnum {
  WHOLE_CAR_PARTS = "WHOLE_CAR_PARTS",
  HK_MC_TW_INQUIRY = "HK_MC_TW_INQUIRY",
}

export interface IAddress {
  /** 详细收货地址 */
  addressDetail?: string;
  /** 收货地址id */
  addressId?: string;
  /** 市id */
  cityGeoId?: string;
  /** 市名字 */
  cityGeoName?: string;
  /** 收货人名称 */
  contactName?: string;
  /** 区id */
  countyGeoId?: string;
  /** 区名字 */
  countyGeoName?: string;
  /** 纬度 */
  latitude?: number;
  /** 经度 */
  longitude?: number;
  /** 移动电话 */
  mobileNumber?: string;
  /** 省id */
  provinceGeoId?: string;
  /** 省名字 */
  provinceGeoName?: string;
  /** 固定电话 */
  telephoneNumber?: string;
  /** 街道id */
  villageGeoId?: string;
  /** 街道名字 */
  villageGeoName?: string;
}

interface originalItemResourceDTO {
  resourceType: string;
  resourceValue: string;
}

export interface IOriginalItemsItem {
  /** 子午线 */
  meridian?: string;
  /** 需求数量 */
  quantity?: number;
  /** 扁平比 */
  ratio?: string;
  /** 扁平比code */
  ratioCode?: string;
  /** 轮胎规格 */
  specification?: string;
  /** 尺寸 */
  size?: string;
  /** 尺寸code */
  sizeCode?: string;
  /** 来源  (值: MANUAL_ENTRY-人工录入) */
  sourceId?: string;
  /** 胎面宽 */
  treadWidth?: string;
  /** 胎面宽code */
  treadWidthCode?: string;
  /** 轮胎方位 (值: TYRE_FRONT-前轮; TYRE_REAR-后轮; TYRE_COMMON-通用) */
  tyreOrientation?: string;
  originalItemResources?: originalItemResourceDTO;
}

export interface IVehicle {
  /** 车辆品牌Code */
  carBrandCode?: string;
  /** 车辆品牌名字 */
  carBrandName?: string;
  /** 车型id */
  carModelId?: string;
  /** 车型名称 */
  carModelName?: string;
  /** epc车型ID */
  epcModelCode?: string;
  /** epc车型名称 */
  epcModelName?: string;
  /** locationId */
  locationId?: string;
  /** locationName */
  locationName?: string;
  /** 销售车型id */
  saleModelCode?: string;
  /** 销售车型名称 */
  saleModelName?: string;
  /** 车系英文名 */
  seriesEn?: string;
  /** 车系id */
  seriesId?: string;
  /** 车系中文名 */
  seriesZh?: string;
  /** vin码 */
  vin?: string;
}

export interface ITireSpecificationsItem {
  specification?: string; // 规格
  size?: string; // 尺寸
  meridian?: string; // 子午线
  treadWidth?: string; // 胎面宽
  ratio?: string; // 扁平比
  tyreOrientation?: string; // 轮胎方位 【TYRE_FRONT：前轮、TYRE_REAR：后轮、TYRE_COMMON：前后通用】
  treadWidthCode?: string; // 胎面宽code
  ratioCode?: string; // 扁平比code
  sizeCode?: string; // 尺寸code
}

export interface ITireInquiryVehicleItem {
  vin?: string; // vin码
  carModelName?: string; // 车型名称
  carModelId?: string; // 车型id
  carBrandCode?: string; // 车辆品牌Code
  carBrandName?: string; // 车辆品牌名字
  epcModelCode?: string; // epc车型ID
  epcModelName?: string; // epc车型名称
  seriesId?: string; // 车系id
  seriesZh?: string; // 车系中文名
  seriesEn?: string; // 车系英文名
  saleModelCode?: string; // 销售车型id
  saleModelName?: string; // 销售车型名称
  brandLogo?: string; // 品牌logo
  locationId?: string; // locationId
  locationName?: string; // locationName
}

export interface IGetTyreInquiryDetailPayload {
  // 轮胎询价单id
  demandId: string;
  // 是否哨兵
  isSentry: boolean;
  // 是否需要发票
  openInvoiceType: boolean;
  /** 排序类型 (值: COMPREHENSIVENESS-综合排序; DURATION-时效排序; PRICE-价格排序) */
  sortType: string;
}

export interface IPropertyItem {
  propertyTypeName: string; // 规格类型名称
  propertyTypeCode: string; // 规格类型代码
  valueList: Array<{
    propertyValueCode?: string; // 规格值代码
    propertyValueName?: string; // 规格值
  }>;
}

export interface IGetOptionalTyreSpecificationsResponse {
  property: IPropertyItem[];
}

export enum PropertyTypeCodeEnum {
  TreadWidth = "AV", // 轮胎宽度
  RatioCode = "64", // 扁平比
  Size = "1J", // 尺寸
}

export interface IGetOldDemandIdResponse {
  demandId: string;
  carModelName: string;
  vin: string;
  isSuccess: boolean;
}

export interface IGetInquiryIsAllowAppendResponse {
  isAllowAppend: boolean; // 是否可以追加报价
  inquiryId?: string; // 询价单号
  carModelName?: string; // 车型信息
  isSimpleInquiry?: boolean; // 是否是简易询价
  carBrandId?: string; // 品牌id
  saleModelName?: string; // 销售车型名称
  carBrandName?: string; // 品牌名称
  epcModelCode?: string; // epc车型编码
  brandLogo?: string; // 品牌图标
  qualityShowType?: string; // 品质展示类型 WHOLE_QUALITY（整单品质）/  SINGLE_QUALITY（配件品质）
}

// 消息类型
export enum MessageType {
  Text = "text", // 文本
  Image = "image", // 文本
  Voice = "voice", // 语音
  Video = "video", // 视频
  Form = "form", // 表单
}

// 卡片类型
export enum FormName {
  InquiryForm = "inquiryForm", // 询价卡片
  InquiryResult = "inquiryResult", // 报价结果
}

export interface InquiryDetailParams {
  inquiryId: string;
  source: string;
  fromPage: string;
  isSentry?: boolean;
}

export interface ICustomInquiryDetailParams extends InquiryDetailParams {
  userLoginId?: string; //采购人用户id
  garageCompanyId?: string; //采购人所在维修厂id
}

export interface IAppendUserNeed {
  needsName: string;
  oeCode?: string;
  oeName?: string;
  stdName?: string;
  categoryCode?: string;
  stdNameCode?: string;
  inquirySource?: string;
  stdInquirySource?: string;
  quantity: number;
  isFastOe: boolean;
  isSuggest: boolean;
  imageUrls: string[];
  hasResolved?: boolean;
}

export interface IAppendInquiryParams {
  inquiryId: string;
  appendUserNeeds: IAppendUserNeed[];
}

export interface IAppendProxyInquiryParams extends IAppendInquiryParams {
  userLoginId: string;
}
