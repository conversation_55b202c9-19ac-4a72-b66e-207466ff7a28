import { IAction } from "@casstime/copilot-core";
import _ from "lodash";
const HOOK_URL = "http://127.0.0.1:22345/copilot/hook";

test("输入人工客服时，必须包含【人工客服】按钮", async () => {
  const res = await fetch(HOOK_URL, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      replyMode: "stream",
      data: {
        type: "text",
        content: "人工客服",
        fromUser: "test1",
      },
    }),
  });
  const lines = (await res.text()).trim().split("\n");
  const lastLine = _.last(lines) as string;
  const payload = JSON.parse(lastLine?.replace("data:", ""));
  const msg = _.last(payload.messages);
  const actions = _.get(msg, "actions", []) as IAction[][];
  expect(actions.flat().some((action) => action?.text === "人工客服")).toBe(true);
}, 10000);
