import fs from "fs";
import { parse } from "csv/sync";
import { intentClassifier } from "../src/copilot/apps/GarageAssistant/agents/inquiry/classifiers/intentClassifier";
import { Context } from "@casstime/copilot-core";
import { InquiryIntents } from "../src/copilot/apps/GarageAssistant/agents/inquiry/parsers/inquiryIntentClassifier";
import { parseEntities } from "../src/copilot/apps/GarageAssistant/agents/inquiry/parsers";

const content = fs.readFileSync("./data/询价Agent意图分类.csv", "utf-8");

const list = parse(content, {
  columns: true,
}) as { input: string; intent: string }[];

describe("inquiry intent classifier", () => {
  test.each(list)("should classify %s", async (item) => {
    const context = {
      lastMessage: {
        type: "text",
        content: item["input"],
      },
    } as Context;
    Object.assign(context, { entities: await parseEntities(context) });
    console.log(context, JSON.stringify(Object.keys(item), null, 2));
    const result = await intentClassifier.classify(context, Object.values(InquiryIntents));
    console.log("-->", result);
    const mapping = {
      [InquiryIntents.询报价]: InquiryIntents.买配件,
      [InquiryIntents.修改配件信息]: InquiryIntents.买配件,
    };
    let expected = item.intent;
    if (["买机油", "买配件", "买轮胎"].includes(expected)) {
      expected = InquiryIntents.买配件;
    }

    expect(mapping[result as string] || result).toBe(expected);
  });
});
