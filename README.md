# Copilot Server

AI助手应用服务,支持多应用多Agent

## 环境配置
1. Node.js 版本。由于使用了新特性，请使用 Node.js 20+
2. VSCode 插件。开发时，为确保格式一致，请确保安装了以下插件
    - [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
    - [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

## 常用命令

1. 本地调试
    ```sh
    yarn dev
    ```
2. 单元测试
   ```sh
   # 监听文件变化，执行单元测试，单次执行可不加 `--watch`
   yarn test --watch 
   ```
3. 生成依赖管理
    ```sh
    yarn gen-deps
    ```
    > 使用前需要安装 graphviz，macOS 用户可以使用 `brew install graphviz` 安装

## AI推荐
### MOCK线上数据
1. 查询`db.datacaches`集合，导出为 `datacache.csv`，置于 `data/mock` 目录下
2. 将 `createDataProvider` 中的注释删除，使其返回 `MockDataProvider`
3. 调试时，在`datacache.csv`中找一个询价单ID当成`businessId`