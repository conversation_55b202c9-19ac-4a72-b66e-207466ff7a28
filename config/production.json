{"EUREKA_SERVICE_URLS": "http://discovery.backup-intra.casstime.com/eureka/apps", "SPRING_CLOUD_CONFIG_ENDPOINT": "http://config-server.intra.cassmall.com/conf", "API_INTRA_BASE_URL": "http://api.intra.cassmall.com/", "LLM_API_BASE_URL": "http://************:3000/v1", "LLM_API_KEY": "sk-sbIEjx7Yp6bhcqHzAd2dF465Ac944d45B8777f8313Aa8951", "IMAGE_CLASSIFY": "http://***************:8502", "FASTGPT_BASE_URL_PROD": "http://ai-hwbeta.casstime.com", "FASTGPT_BASE_URL": "http://ai-hwbeta.casstime.com", "FASTGPT_AUTOPARTSFAQ_KEY": "fastgpt-vJnl9ri9gh9maxQzzbxYLbBfOeHNteNpy70ZDadnUlaBxyH9bvh80HUg", "FASTGPT_PARTEXPERTFAQ_KEY": "fastgpt-uu0GWNg2uXcjfJo3Vz4Qf5tEAdXmLe3OozBtIZLhHMQAnEweU6SHn", "APOLLO_HOST": "http://cassmall-hwprod-apollo-configservice.apollo:8080", "APOLLO_APPID": "com.casstime.bff.terminalApi", "APOLLO_CLUSTER": "default", "APOLLO_NAMESPACE": "copliot-server", "APOLLO_PUBLIC_NAMESPACE": "", "QDRANT_URL": "http://***************:8502", "PARTS_MIND_BASE_URL": "https://parts-mind.ops.casstime.net", "QDRANT_URL_PREFIX": "/qdrant", "V2_BASE_URL": "http://terminal-api-v2-srv.terminal.svc.cluster.local", "1CHEJIAN_BASE_URL": "https://wsaas-api.casstime.com/", "1CHEJIAN_APPKEY": "M3Z0qD1m2uHcHBDS2amH", "1CHEJIAN_APPSECRET": "ujNJV6BnGxQECWprFA68uZbaOSr9MBmk"}